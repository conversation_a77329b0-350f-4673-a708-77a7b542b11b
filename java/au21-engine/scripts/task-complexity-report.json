{"meta": {"generatedAt": "2025-05-01T09:04:58.921Z", "tasksAnalyzed": 10, "thresholdScore": 5, "projectName": "Your Project Name", "usedResearch": true}, "complexityAnalysis": [{"taskId": 1, "taskTitle": "Add OpenTelemetry dependencies to the project", "complexityScore": 2, "recommendedSubtasks": 4, "expansionPrompt": "Break down the process of adding OpenTelemetry dependencies to a Quarkus project into specific steps, including identifying the correct dependency versions, modifying build files, and verifying compatibility with Quarkus 3.20.", "reasoning": "This is a relatively straightforward dependency addition task with clear requirements. The complexity is low as it involves modifying build files with specific dependencies. Breaking it into 4 subtasks allows for proper verification steps."}, {"taskId": 2, "taskTitle": "Configure basic OpenTelemetry settings in application.properties", "complexityScore": 2, "recommendedSubtasks": 4, "expansionPrompt": "Detail the specific steps to configure OpenTelemetry in application.properties, including creating/modifying the file, adding each configuration property, testing the configuration, and documenting the changes.", "reasoning": "This is a simple configuration task with well-defined properties to add. The complexity is low as it involves adding specific properties to a configuration file. Four subtasks would cover property addition, verification, and documentation."}, {"taskId": 3, "taskTitle": "Configure OpenTelemetry exporters", "complexityScore": 4, "recommendedSubtasks": 5, "expansionPrompt": "Break down the process of configuring OpenTelemetry exporters into detailed steps, including setting up the OTLP exporter, configuring alternative exporters like Jaeger or Zipkin, setting up metrics exporters, configuring log exporters, and testing the exporter configurations.", "reasoning": "This task has moderate complexity as it involves configuring multiple exporters and understanding their specific requirements. It requires knowledge of different telemetry backends and their configuration options."}, {"taskId": 4, "taskTitle": "Implement request tracing for HTTP endpoints", "complexityScore": 5, "recommendedSubtasks": 6, "expansionPrompt": "Detail the steps to implement and verify HTTP request tracing, including creating a RequestFilter for custom attributes, implementing context propagation, testing with sample endpoints, handling error cases, and documenting the implementation.", "reasoning": "This task has moderate complexity as it involves understanding how the auto-instrumentation works and extending it with custom attributes. It requires coding a filter and ensuring proper context propagation across the application."}, {"taskId": 5, "taskTitle": "Implement database operation tracing", "complexityScore": 6, "recommendedSubtasks": 6, "expansionPrompt": "Break down the implementation of database operation tracing into specific steps, including verifying auto-instrumentation, adding custom spans for critical operations, configuring security settings, testing with various database operations, and optimizing performance.", "reasoning": "This task has higher complexity as it requires understanding both OpenTelemetry and database operations. It involves verifying auto-instrumentation and potentially adding custom spans, which requires deeper technical knowledge."}, {"taskId": 6, "taskTitle": "Implement custom business operation tracing", "complexityScore": 7, "recommendedSubtasks": 7, "expansionPrompt": "Detail the steps to implement custom business operation tracing, including identifying key business operations, designing span hierarchy, implementing span creation with proper error handling, adding business-specific attributes, testing with real scenarios, optimizing performance, and documenting best practices.", "reasoning": "This task has high complexity as it requires deep understanding of both the business domain and OpenTelemetry concepts. It involves writing custom code for span creation, proper error handling, and attribute management across multiple business operations."}, {"taskId": 7, "taskTitle": "Configure structured logging with OpenTelemetry", "complexityScore": 5, "recommendedSubtasks": 6, "expansionPrompt": "Break down the process of configuring structured logging with OpenTelemetry into specific steps, including setting up JSON logging format, configuring trace context inclusion, implementing SLF4J with MDC, testing log output with traces, handling special logging cases, and documenting the logging configuration.", "reasoning": "This task has moderate complexity as it involves integrating logging with tracing and ensuring proper context propagation. It requires understanding of both logging frameworks and OpenTelemetry concepts."}, {"taskId": 8, "taskTitle": "Implement distributed tracing for external service calls", "complexityScore": 8, "recommendedSubtasks": 7, "expansionPrompt": "Detail the steps to implement distributed tracing for external service calls, including identifying all external service clients, implementing context propagation for REST clients, handling manual HTTP clients, testing with mock services, implementing error handling, optimizing performance, and documenting the implementation.", "reasoning": "This task has high complexity as it involves ensuring trace context propagation across service boundaries. It requires understanding of HTTP clients, headers manipulation, and potentially dealing with different propagation formats."}, {"taskId": 9, "taskTitle": "Implement OpenTelemetry metrics collection", "complexityScore": 6, "recommendedSubtasks": 6, "expansionPrompt": "Break down the implementation of OpenTelemetry metrics collection into specific steps, including configuring metrics in application.properties, identifying key metrics to collect, implementing counters/gauges/histograms, integrating with Micrometer if needed, testing metrics collection, and documenting the metrics implementation.", "reasoning": "This task has moderate to high complexity as it involves understanding metrics concepts and implementing various metric types. It requires identifying meaningful metrics and properly implementing them across the application."}, {"taskId": 10, "taskTitle": "Create documentation and testing guide for OpenTelemetry integration", "complexityScore": 7, "recommendedSubtasks": 7, "expansionPrompt": "Detail the steps to create comprehensive documentation for the OpenTelemetry integration, including documenting configuration options, creating guides for custom spans, developing troubleshooting procedures, documenting performance considerations, creating security guidelines, developing example queries, and creating end-to-end testing scenarios.", "reasoning": "This task has high complexity as it requires comprehensive understanding of all OpenTelemetry components implemented. It involves creating detailed documentation covering multiple aspects including configuration, usage, troubleshooting, and best practices."}]}