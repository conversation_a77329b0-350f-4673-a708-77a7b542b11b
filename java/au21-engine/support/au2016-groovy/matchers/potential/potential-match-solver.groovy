package app.services.de.matchers.potential

import app.framework.AuLogger2
import groovy.transform.CompileStatic
import groovy.util.logging.Slf4j
import org.chocosolver.solver.Model
import org.chocosolver.solver.Solution
import org.chocosolver.solver.Solver
import org.chocosolver.solver.expression.discrete.arithmetic.ArExpression
import org.chocosolver.solver.variables.IntVar

import static app.services.de.matchers.potential.PotentialMatch_Model.*

// this is entirely independant of our models !!

@Slf4j
@CompileStatic
class PotentialMatch_Model {

  @CompileStatic
  static class TradingRange {
    final int min
    final int max

    TradingRange ( int min, int max ) {
      this.min = min
      this.max = max

      if (min < 0 || max < 0)
        throw new Exception ( 'Volume range cannot be negative!' )

      if (max < min)
        throw new Exception ("Max cannot be less than min!")
    }

    boolean isEmpty () { return min == 0 && max == 0 }
  }

  @CompileStatic
  static class TradingLimits {

    // ALL VOLUMES HERE ARE POSITIVE

    // SO, GOAL IS TO TRANSLATE max/min/desc into positive buy/sell limits

    // NOTE: see pdf: VolumeRanges.pdf, 2018.02.08

    final String username
    final TradingRange sell_range
    final TradingRange buy_range

    TradingLimits ( String username, int min_vol, int max_vol ) {

      this.username = username

      if (max_vol < 0) {
        // ie: this is a desc auction in which the max_vol is in the buy range:
        this.sell_range = new TradingRange ( 0, 0 )
        this.buy_range = new TradingRange ( -1 * max_vol, -1 * min_vol )

      } else if (min_vol > 0) {
        // ie: this is an asc auction in which the min_vol is in the sell range:
        this.sell_range = new TradingRange ( min_vol, max_vol )
        this.buy_range = new TradingRange ( 0, 0 )

      } else {
        // ie: max_vol is positive and min_vol is negative:
        this.sell_range = new TradingRange ( 0, max_vol )
        this.buy_range = new TradingRange ( 0, -1 * min_vol )
      }

    }
  }

  @CompileStatic
  static class Seller_Buyer_Capacity {
    final TradingLimits seller
    final TradingLimits buyer
    final int capacity

    // based on round price and counterparty credit limits:
    Seller_Buyer_Capacity ( TradingLimits seller, TradingLimits buyer, int capacity ) {
      if(capacity < 0)
        throw new Exception("capacity cannot be negative.")
      this.seller = seller
      this.buyer = buyer
      this.capacity = capacity
    }
  }

  @CompileStatic
  static class PotentialMatch_Input {

    final List<TradingLimits> traders
    final List<Seller_Buyer_Capacity> seller_buyer_capacities
    final int domain_max

    PotentialMatch_Input (
      List<TradingLimits> traders,
      List<Seller_Buyer_Capacity> seller_buyers,
      int domain_max ) {

      this.traders = traders
      this.seller_buyer_capacities = seller_buyers
      this.domain_max = domain_max
    }
  }

  @CompileStatic
  static class PotentialMatch_Solution {
    // all of these are set by the solver mutation function
    Map<TradingLimits, IntVar> seller_flows = [:]
    Map<TradingLimits, IntVar> buyer_flows = [:]
    Map<Seller_Buyer_Capacity, IntVar> seller_buyer_flows = [:]
    IntVar sell_buy_max_flow
    Solution solution
  }

}

@Slf4j
@CompileStatic
class PotentialMatch_Extensions {

  static int get_sell_potential ( PotentialMatch_Solution output, String seller_username ) {
    int total = 0
    output.seller_flows.each { TradingLimits t, IntVar int_var ->
      if (seller_username == t.username)
        total += int_var.value // ?? soln.getIntVal(int_var)
    }
    total
  }
  /*
  int get_buy_potential ( Potential_Match_Solution soln, Person seller ) {
    int total = 0
    soln.sell_seller_flows.each { Trader t, IntVar int_var ->
      if (buyer.person.username == t.username)
        total += int_var.value // ?? soln.getIntVal(int_var)
    }
    total
  }

  int get_seller_buyer_potential( Person seller, Person buyer){

    if(!(buyer && seller && buy_sell_link && buy_sell_link.instantiated))
      return 0

    seller_buyer_flows.find {Seller_Buyer sb, IntVar i ->
      sb.seller.username == seller.username
      sb.buyer.username == buyer.username
    }?.value?.value ?: 0   // ?? soln.getIntVal(int_var)

  }
  */

  static int getMax_flow ( PotentialMatch_Solution output ) {
    output.sell_buy_max_flow?.instantiated ?
      output.solution.getIntVal ( output.sell_buy_max_flow ) :
      0
  }

  static void print ( PotentialMatch_Solution output ) {
    //  println "max_flow: $pm.max_flow"
    def o = [
      sell_seller_flows :
        output.seller_flows.collect { TradingLimits t, IntVar int_var ->
          "$t.username: $int_var.value"
        },
      buy_buyer_flows   :
        output.buyer_flows.collect { TradingLimits t, IntVar int_var ->
          "$t.username: $int_var.value"
        },
      seller_buyer_flows:
        output.seller_buyer_flows.collect { Seller_Buyer_Capacity sb, IntVar int_var ->
          "seller: $sb.seller.username, buyer: $sb.buyer.username, $int_var.value"
        }
    ]
    log.info ( AuLogger2.to_string ( o ) )
  }
}

@Slf4j
@CompileStatic
class PotentialMatch_Mutators {

  static long solve_for_max_potential ( PotentialMatch_Input input ) {

    Map<TradingLimits, IntVar> seller_flows = [:]
    Map<TradingLimits, IntVar> buyer_flows = [:]
    Map<Seller_Buyer_Capacity, IntVar> seller_buyer_flows = [:]
    IntVar sell_buy_max_flow
    Solution solution

    final Model model = new Model ()

    input.traders.each { TradingLimits t ->

      // 1) seller elig: (n) so for 25 = 25
      seller_flows.put ( t, model.intVar ( 'sell_' + t.username, t.sell_range.min, t.sell_range.max ) )

      // 2) buy elig: (n) for 25 = 25
      buyer_flows.put ( t, model.intVar ( t.username + '_buy', t.buy_range.min, t.buy_range.max ) )

      // 3) counterparty elig: (n*(n-1)) for 25 = 600
      input.seller_buyer_capacities.each {
        seller_buyer_flows.put ( it,
          model.intVar ( it.seller.username + '_' + it.buyer.username, 0, it.capacity ) )
      }
    }

    Closure<ArExpression> add = { ArExpression acc, ArExpression link -> acc?.add ( link ) ?: link }

    // 4) conservation of flow at sell node ->seller nodes: n
    seller_flows.each { TradingLimits seller, IntVar seller_elig ->

      ( seller_buyer_flows
        .findAll { Seller_Buyer_Capacity sb, IntVar iv -> sb.seller == seller }
        .collect { Seller_Buyer_Capacity sb, IntVar iv -> iv } as List<ArExpression> )?.with {

        if (!it.empty) {
          seller_elig.eq ( it.inject ( add ) as ArExpression ).post ()
        }
      }

    }

    // 5) conservation of flow at buyer nodes -> buy node: n
    buyer_flows.each { TradingLimits buyer, IntVar buyer_elig ->

      ( seller_buyer_flows
        .findAll { Seller_Buyer_Capacity sb, IntVar iv -> sb.buyer == buyer }
        .collect { Seller_Buyer_Capacity sb, IntVar iv -> iv } as List<ArExpression> )?.with {

        if (!it.empty) {
          buyer_elig.eq ( it.inject ( add ) as ArExpression ).post ()
        }
      }
    }

    // 6) conservation of flow between buy_sell -> sell node and -> buy node:
    sell_buy_max_flow = model.intVar ( 'buy_sell', 0, input.domain_max )

    // conservation at the sell side:
    ( seller_flows.values ().toList () as List<ArExpression> ).with {
      if (!it.empty)
        sell_buy_max_flow.eq ( it.inject ( add ) as ArExpression ).post ()
    }

    // conservation at the buy side:
    ( buyer_flows.values ().toList () as List<ArExpression> ).with {
      if (!it.empty)
        sell_buy_max_flow.eq ( it.inject ( add ) as ArExpression ).post ()
    }

    // 7) Constraint traders to be buyer or seller, but not both:
    input.traders.each { TradingLimits t ->
      model.arithm (
        model.arithm ( seller_flows.get ( t ), '>', 0 ).reify (), '+',
        model.arithm ( buyer_flows.get ( t ), '>', 0 ).reify (), '<=', 1
      ).post ()

    }

    // 8) set objective:
    model.setObjective ( Model.MAXIMIZE, sell_buy_max_flow )

    // 9) Solve the problem
    try {
      long start = new Date ().time

      Solver solver = model.getSolver ()
      solution = new Solution ( model )
      // test.app.model.solver.plugMonitor(new GUI(test.app.model.solver));

      Number best_value

      solver.solve ()
      solution.record ()
      solver.printStatistics ()
      best_value = solver.getBestSolutionValue ()
      long duration = new Date ().time - start
      println ( 'duration: ' + duration + ' msec' )

//
//      while (solver.solve ()) {
//        solution.record ()
//        solver.printStatistics ()
//        best_value = solver.getBestSolutionValue ()
//        long duration = new Date ().time - start
//        println('duration: ' + duration + ' msec')
//      }

      /*
      log.info ( AuLogger2.to_string ( [
        best_value: best_value,
        duration  : duration + ' msec',
        solution  : solution,
        vars      : model.vars.collect { it }
      ] ) )
      */

      // max_solution = soln.getIntVal ( buy_sell_link )
      // use max_solution above

      return best_value.toLong ()

    } catch (Exception ex) {
      log.error ( ex.message )
    }

    return 0
  }

  /*
  static PotentialMatch_Solution solve_old ( PotentialMatch_Input input ) {

    PotentialMatch_Solution output = new PotentialMatch_Solution ()

    final Model model = new Model ()

    input.traders.each { Trader t ->

      // 1) seller elig: (n) so for 25 = 25
      output.seller_flows.put (
        t,
        model.intVar ( 'sell_' + t.username, 0, t.sell_eligibility ) )

      // 2) buy elig: (n) for 25 = 25
      output.buyer_flows.put (
        t,
        model.intVar ( t.username + '_buy', t.buy_min, t.buy_eligibility ) )

      // 3) counterparty elig: (n*(n-1)) for 25 = 600
      input.seller_buyer_capacities.each {
        output.seller_buyer_flows.put (
          it,
          model.intVar ( it.seller.username + '_' + it.buyer.username, 0, it.capacity )
        )
      }
    }

    def add = { ArExpression acc, ArExpression link -> acc ? acc.add ( link ) : link }

    // 4) conservation of flow at sell node ->seller nodes: n
    output.seller_flows.each { Trader seller, IntVar seller_elig ->

      ( output.seller_buyer_flows
        .findAll { Seller_Buyer_Capacity sb, IntVar iv -> sb.seller == seller }
        .collect { Seller_Buyer_Capacity sb, IntVar iv -> iv } as List<ArExpression> )?.with {

        if (!it.empty) {
          seller_elig.eq ( it.inject ( add ) ).post ()
        }
      }

    }

    // 5) conservation of flow at buyer nodes -> buy node: n
    output.buyer_flows.each { Trader buyer, IntVar buyer_elig ->

      ( output.seller_buyer_flows
        .findAll { Seller_Buyer_Capacity sb, IntVar iv -> sb.buyer == buyer }
        .collect { Seller_Buyer_Capacity sb, IntVar iv -> iv } as List<ArExpression> )?.with {

        if (!it.empty) {
          buyer_elig.eq ( it.inject ( add ) ).post ()
        }
      }
    }

    // 6) conservation of flow between buy_sell -> sell node and -> buy node:
    output.sell_buy_max_flow = model.intVar ( 'buy_sell', 0, input.domain_max )

    // conservation at the sell side:
    ( output.seller_flows.values ().toList () as List<ArExpression> ).with {
      if (!it.empty)
        output.sell_buy_max_flow.eq ( it.inject ( add ) ).post ()
    }

    // conservation at the buy side:
    ( output.buyer_flows.values ().toList () as List<ArExpression> ).with {
      if (!it.empty)
        output.sell_buy_max_flow.eq ( it.inject ( add ) ).post ()
    }

    // 7) Constraint traders to be buyer or seller, but not both:
    input.traders.each { Trader t ->
      model.arithm (
        model.arithm ( output.seller_flows.get ( t ), '>', 0 ).reify (), '+',
        model.arithm ( output.buyer_flows.get ( t ), '>', 0 ).reify (), '<=', 1
      ).post ()

    }

    // 8) set objective:
    model.setObjective ( Model.MAXIMIZE, output.sell_buy_max_flow )

    // 9) Solve the problem
    try {
      long start = new Date ().time

      Solver test.app.model.solver = model.getSolver ()
      output.solution = new Solution ( model )
      // test.app.model.solver.plugMonitor(new GUI(test.app.model.solver));

      test.app.model.solver.solve ()
      output.solution.record ()
      test.app.model.solver.printStatistics ()
      def best_value = test.app.model.solver.getBestSolutionValue ()
      long duration = new Date ().time - start
      log.info ( AuLogger2.to_string ( [
        best_value: best_value,
        duration  : duration + ' msec',
        solution  : output.solution,
        vars      : model.vars.collect { it }
      ] ) )

      // max_solution = soln.getIntVal ( buy_sell_link )
      // use max_solution above

    } catch (Exception ex) {
      log.error ( ex.message )
    }

    output
  }
  */

}