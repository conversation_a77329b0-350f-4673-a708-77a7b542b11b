package app.services.de.matchers.potential

import groovy.transform.CompileStatic
import groovy.util.logging.Slf4j

import static app.services.de.matchers.potential.PotentialMatch_Model.*

@Slf4j
@CompileStatic
class Potential_Match_Sample {

  static sample () {

    long start = new Date ().time

    List<TradingLimits> traders = []
    List<Seller_Buyer_Capacity> seller_buyer_flows = []
    int domain_max = 200

    ( 1..4 ).each { i ->
      traders.add (
        new TradingLimits ( 't' + i, 0, 50 ) )
    }

    int capacity = 0

    traders.each { TradingLimits seller ->
      traders.each { TradingLimits buyer ->
        if (seller != buyer) {
          capacity += 10
          seller_buyer_flows.add ( new Seller_Buyer_Capacity ( seller, buyer, capacity ) )
        }
      }
    }

    PotentialMatch_Input input = new PotentialMatch_Input (
      traders,
      seller_buyer_flows,
      domain_max )

    //   PotentialMatch_Solution output = PotentialMatch_Mutators.solve_old ( input )

    //   long end = new Date ().time

//    log.info 'sample potential: ' + output.sell_buy_max_flow.value + ', duration: ' + ( end - start ) + 'msec'

//    Network_Diagrammer.draw ( output )
  }

  static main ( args ) {
    // sample ()
    // sample ()
    sample ()
  }

}


