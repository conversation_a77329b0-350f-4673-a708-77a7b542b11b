package app.services.de.test.matchers.non_flow

import app.model.DeAuction
import app.model.DeMatch
import app.model.DeOrder
import app.model.DeRound
import app.model.DeTrader


// TODO: this is the old matcher, no?
class De_Matcher_Non_Flow {

  final DeAuction a
  final DeRound n
  final DeRound pen
  final long n_match_vol
  final long pen_match_vol
  final long match_increase
  final long total_buy_increase
  final long total_sell_decrease

  final def order_sorter = { DeOrder o1, DeOrder o2 -> o1.timestamp <=> o2.timestamp }

  long prorate ( long target, long individual_vol, long total_vol ) {
    total_vol == 0L ?
      0L :
      Math.floor ( (double) target * (double) individual_vol / (double) total_vol )
  }

  long sell_decrease ( DeTrader t ) { ( !pen ) ? 0L : pen.getOrder ( t ).sell_vol - n.getOrder ( t ).sell_vol }


  private class Trader_Award {
    // used to match up buyers and sellers
    DeOrder order
    String username
    boolean is_buyer
    long awarded_vol
    long unmatched_vol
  }


  De_Matcher_Non_Flow ( DeAuction a ) {

    /* 
     
     A) Auction continues if sell > buy.
     B) Awarded round is the last unless there is a pen with greater matched.
     C) Buyers and sellers are determined (not so straight forward, as sellers can become buyers
        - NB: ASSUMPTION THAT YOU HAVE TO GO THROUGH ZERO TO CHANGE FROM SELLER TO BUYER
              i.e: you can't be both here !!
     D) Award totals are calculated for either: single round, n award, or pen award
     E) Orders are matched.

     */

    this.a = a

    n = a.lastround
    pen = a.penultimate

    // check that the auction is closed!
    println "last round total sell: ${n.total_sell ()}, total buy: ${n.total_buy ()}"
    if (n.total_sell () > n.total_buy ())
      throw new Exception (
        "auction not ready to close, " +
          "final round buy of ${n.total_buy ()} " +
          "is less than sell of ${n.total_sell ()}" )

    n_match_vol = n.total_matched ()
    pen_match_vol = pen?.total_matched () ?: 0L
    match_increase = n_match_vol - pen_match_vol

    total_buy_increase = n.total_incremental_buy ()
    total_sell_decrease = pen ? ( pen.total_sell () - n.total_sell () ) : 0L

    // a) find the final round:
    a.awarded_round = ( pen && ( pen_match_vol > n_match_vol ) ) ? pen : n

    // b) calculate individual trader's match vol:

    a.traders*.awarded_volume = 0L

    // TODO: check that this works for equal volumes in the first, final or pen rounds

    if (a.awarded_round == n) {

      // NB: also handles case of SINGLE ROUND
      set_trader_awarded_volume_for_last_round ()

    } else if (a.awarded_round == pen) {

      set_trader_awarder_volume_for_penultimate ()

    } else {

      throw new Exception ( "Unable to award last or second last rounds." )
    }

    println "Awarded round: $a.awarded_round.round_number"

    // d) now we do the actual matching !

    // first some assertions:
    //List<DeTrader> buyers = a.awarded_round.orders.findAll { it.is_buy }.collect { a.getTrader(it) }
    //List<DeTrader> sellers = a.awarded_round.orders.findAll { it.is_sell }.collect { a.getTrader(it) }

    /*
    if (!buyers.empty) {
      long buyer_total = buyers*.awarded_volume.sum()
      println "buyer total: ${buyer_total}"
    }

    if (!sellers.isEmpty()) {
      long seller_total = sellers*.awarded_volume.sum()
      println "seller total: ${seller_total}"
    }
    */

    List<Trader_Award> trader_awards = a.traders.findAll { it.awarded_volume > 0 }.collect {
      new Trader_Award (
        order: a.awarded_round.getOrder ( it ),
        username: it.person.username,
        is_buyer: a.is_buyer ( it ),
        awarded_vol: it.awarded_volume,
        unmatched_vol: it.awarded_volume )
    }

    //List<DeOrder> sorted_orders = a.awarded_round.orders.sort { o1, o2 -> o1.timestamp <=> o2.timestamp }

    long remaining_unmatched = a.awarded_round.total_matched ()

    int pass = 0
    while (remaining_unmatched > 0) {

      pass++

      Trader_Award next_unmatched_buy_award = trader_awards.find {
        it.is_buyer && ( it.unmatched_vol > 0 ) // a.matched_buy_vol(a.getTrader(it))
      }

      Trader_Award next_unmatched_sell_award = trader_awards.find {
        !it.is_buyer && ( it.unmatched_vol > 0 ) // a.matched_sell_vol(a.getTrader(it))
      }

      DeMatch m = a.create_match (
        next_unmatched_buy_award.order,
        next_unmatched_sell_award.order,
        Math.min ( next_unmatched_buy_award.unmatched_vol, next_unmatched_sell_award.unmatched_vol ) )

      next_unmatched_buy_award.unmatched_vol -= m.match_volume
      next_unmatched_sell_award.unmatched_vol -= m.match_volume

      println "matched on pass $pass: $m.match_volume"

      remaining_unmatched -= m.match_volume

      println "remaining unmatched: $remaining_unmatched"
      println ()
    }

    println ( 'matches:' )
    a.lastround.matches.each { DeMatch m ->
      println ( 'buyer: ' + m.buy_order.username + ', seller: ' + m.sell_order.username + ', match vol: ' + m.match_volume )
    }

    // just to be sure:
    a.traders.each { DeTrader t ->
      long calculated_match = a.lastround.matches.findAll {
        ( it.buy_order.username == t.person.username ) || ( it.sell_order.username == t.person.username )
      }?.sum { it.match_volume }?.with { it as long } ?: 0L

      assert t.awarded_volume == calculated_match

    }

    println ()
  }


  private void set_trader_awarded_volume_for_last_round () {

    // - sellers get their last round vol
    // - buyers get their penultimate round vol + (weighted buy increase * match increase)

    final Set<DeTrader> buyers = []

    // first the penultimate buys:
    pen?.with {
      it.orders.findAll { it.is_buy }.each { DeOrder o ->
        DeTrader t = a.getTrader ( o )
        buyers << t // HashSet, so safe to add twice ???
        t.awarded_volume += pen.getOrder ( t ).buy_vol  // second last round
        // TODO: why += ?
      }
    }

    // next the incremental (or in the case of one round, the only):
    n.orders.each { DeOrder o ->

      DeTrader t = a.getTrader ( o )

      if (o.is_sell) { // sellers get all their last round vol
        t.awarded_volume = o.sell_vol

      } else if (o.is_buy) {
        buyers << t // set, so ok to add twice
        t.awarded_volume += prorate (
          match_increase,
          n.getOrder ( t ).incremental_buy,
          total_buy_increase )
      }
    }

    // true up FINAL ROUND for rounding, based on time:
    if (buyers.empty)
      return

    int total_remaining = n_match_vol - ( buyers*.awarded_volume.sum () as long )
    println "\nRemaining: $total_remaining \n"

    buyers.collect { n.getOrder ( it ) }.sort ( order_sorter ).each { DeOrder o ->

      if (total_remaining > 0) {

        println "remaining: $total_remaining"

        DeTrader t = a.getTrader ( o )

        long amount_to_add = Math.min ( total_remaining, ( o.buy_vol - t.awarded_volume ) )

        if (amount_to_add > 0) {

          println "amount to add: $amount_to_add"

          t.awarded_volume += amount_to_add
          total_remaining -= amount_to_add
        }
      }
    }
  }


  private void set_trader_awarder_volume_for_penultimate () {

    // - buyers get their penultimate round vol
    // - sellers get their final round vol + (weighted sell decrease * match decrease)

    final Set<DeTrader> sellers = []

    // first the lastround sells:
    n.with {
      it.orders.findAll { it.is_sell }.each { DeOrder o ->
        DeTrader t = a.getTrader ( o )
        sellers << t // set, so safe to add twice
        t.awarded_volume += n.getOrder ( t ).sell_vol // last round
      }
    }

    // next the incremental (or in the case of one round, the only):
    pen.orders.each { DeOrder o ->

      DeTrader t = a.getTrader ( o )

      if (o.is_buy) { // buyers get all their penultimate vol
        t.awarded_volume = o.buy_vol

      } else if (o.is_sell) { // prorate the decrease
        sellers << t // set, so ok to add twice
        t.awarded_volume += prorate (
          ( -match_increase ), // this will be a negative number so correct that
          pen.getOrder ( t ).sell_vol - (long) ( n.getOrder ( t )?.sell_vol ?: 0L ), // individual seller decrease
          total_sell_decrease )
      }
    }

    if (sellers.empty)
      return

    // true up PENULTIMATE ROUND for rounding, based on time:

    int total_remaining = pen_match_vol - ( sellers*.awarded_volume.sum () as long )
    println "\nRemaining: $total_remaining \n"

    sellers.collect { pen.getOrder ( it ) }.sort ( order_sorter ).each { DeOrder o ->

      if (total_remaining > 0) {

        println "remaining: $total_remaining"

        DeTrader t = a.getTrader ( o )

        long amount_to_add = Math.min ( total_remaining, ( o.sell_vol - t.awarded_volume ) )

        if (amount_to_add > 0) {

          println "amount to add: $amount_to_add"

          t.awarded_volume += amount_to_add
          total_remaining -= amount_to_add
        }
      }
    }
  }

}

