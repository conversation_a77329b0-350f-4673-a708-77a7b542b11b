package app.services.de.matchers.flow

import app.model.DeAuction
import app.model.DeMatch
import app.model.DeOrder
import app.model.DeRound
import app.model.DeTrader
import app.services.de.De_Getters
import app.services.de.De_Trader_Combo
import groovy.transform.CompileStatic
import org.psjava.algo.graph.flownetwork.FordFulkersonAlgorithm
import org.psjava.algo.graph.flownetwork.MaximumFlowAlgorithmResult
import org.psjava.algo.graph.pathfinder.BFSPathFinder
import org.psjava.ds.graph.CapacityEdge
import org.psjava.ds.graph.MutableCapacityGraph
import org.psjava.ds.math.Function
import org.psjava.ds.numbersystrem.IntegerNumberSystem
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import static app.services.de.De_Getters.*
import static groovy.json.JsonOutput.*

// note the workaround to get Slf4j and @Compile static to work
// https://github.com/grails/grails-core/issues/8967

//@Slf4j("LOG")
@CompileStatic
class De_Edge_Flow_Matcher {
  final static Logger log = LoggerFactory.getLogger(De_Edge_Flow_Matcher.class)

  static final String SOURCE = 'SOURCE'
  static final String SINK = 'SINK'

  static int last_max_flow

  @Override
  String toString () {
    return "De_Edge_Flow_Matcher{}";
  }

  static class AuEdge {
    final String from  // company name or SOURCE
    final String to // company name or SINK
    final int capacity
    Integer flow // will be set by algorithm
    final Date timestamp // can be null for non SOURCE or SINK

    AuEdge ( String from, String to, int capacity, Date timestamp = new Date () ) { // default to now (ie: no ranking)
      this.from = from
      this.to = to
      this.capacity = capacity
      this.timestamp = timestamp
    }

    String toString () { "$from -> $to (${flow?.toString () ?: '-'} / $capacity)" }
  }

  static List<DeMatch> match ( DeAuction a, DeRound r ) {

    // STEP ONE: draw_table AuEdges

    if (!r)
      throw new Exception ( "no round" )
    // TODO: should this use buy vol or potential max buy vol?

    List<DeOrder> buy_orders = sorted_buy_orders ( a, r )
    List<DeOrder> sell_orders = sorted_sell_orders ( a, r )

    log.info ( "\nsorted_buy_orders" )
    log.info prettyPrint ( toJson ( buy_orders ) )

    log.info "\nsorted_sell_orders"
    log.info prettyPrint ( toJson ( sell_orders ) )

    List<AuEdge> edges = []

    // 1) SINKS: they need to go in first
    buy_orders.each {
      edges << new AuEdge ( it.username, SINK, it.volume, it.priority_timestamp )
    }

    // 2) SOURCES:
    sell_orders.each {
      edges << new AuEdge ( SOURCE, it.username, it.volume, it.order_timestamp )
    }

    // 3) TRADER TO TRADER EDGES: starting with each buyer:
    buy_orders.each { DeOrder buy_order ->
      sell_orders.each { DeOrder sell_order ->
        int sell_vol = sell_order.volume
        int buy_vol = buy_order.volume

        int couter_party_vol_limit = de_counterparty_credit_vol_limit ( a, r,
          get_trader ( a, sell_order.username ),
          get_trader ( a, buy_order.username )
        )
        edges << new AuEdge ( sell_order.username, buy_order.username, couter_party_vol_limit )
      }
    }

    // STEP TWO: use AuEdges to draw_table a MutableCapacityGraph <String, Integer  >

    MutableCapacityGraph<String, Integer> g = MutableCapacityGraph.create ()
    // have to make sure that SOURCE and SINK are always added, otherwise wont find path!
    g.insertVertex ( SOURCE )
    g.insertVertex ( SINK )
    //log.info ( "\nCapacity Graph:" )
    edges.each { AuEdge e ->
     // log.info "Edge: [$e.timestamp] $e.from -> $e.to ($e.capacity)"
      g.insertVertex ( e.from )
      g.insertVertex ( e.to )
      g.addEdge ( e.from, e.to, e.capacity )
    }

    // STEP THREE: build the capacity graph

    long start = new Date ().time

    MaximumFlowAlgorithmResult<Integer, CapacityEdge<String, Integer>> result =
      FordFulkersonAlgorithm.
      // You should specify path finder. Basic one is finding by DFS.
      //  getInstance ( DFSPathFinder.instance ).
        getInstance ( BFSPathFinder.instance ).
        calc (
          g,
          SOURCE,
          SINK,
          IntegerNumberSystem.instance
        )

    last_max_flow = result.calcTotalFlow ()

    log.info ( "\nmax flow: ${last_max_flow}" )

    // Also, you can obtain the flows in each edges by retrieved flow function.

    // STEP FOUR: get the Max Flow solution graph:

    Function<CapacityEdge<String, Integer>, Integer> flow_function = result.calcFlowFunction ()

    log.info "took: ${new Date ().time - start} msec"

    // STEP FIVE: draw_table matches from any CounterParty Edges that have flow:

    //r.matches = []
    final List<DeMatch> matches = []

    // Report:
    g.vertices.each { v ->

      g.getEdges ( v ).each { e ->

        //log.info  "from: ${e.from ()}, to: ${e.to ()}, flow: ${flow_function.get ( e )}"

        log.info e.toString ()

        if (( e.from () != SOURCE ) && ( e.to () != SINK )) {
          DeTrader seller = get_trader ( a, e.from () )
          DeTrader buyer = get_trader ( a, e.to () )

          int vol = flow_function.get ( e )

          if (vol > 0) {
            DeOrder sell_order = De_Getters.get_order ( r, seller )
            DeOrder buy_order = De_Getters.get_order ( r, buyer )
            //   if (!n.matches.any { it.buy_order_id == buy_order.id && it.sell_order_id == sell_order.id }) {
            // because under this recursion, may draw_table match twice !
            //log.info  "matching: seller $sell_order.company_name, buyer: $buy_order.company_name, vol: $vol"

            DeMatch match = new DeMatch ( buy_order, sell_order, vol )
            //r.matches.add ( match )
            matches.add ( match )
          }
        }
      }
    }
    return matches
  }

  // ----------------------------------------------------------------------------------------------
  /*
  static De_Trader_Combo max_potential_flow ( DE a, DeRound r ) {

    int MSEC_THRESHOLD = 100

    long overall_start_time = new Date ().time

    Map<DeTrader, Map<Person, Integer  >> buyer_vols = [:]

    De_Trader_Combo best_combo = null
    MutableCapacityGraph<String, Integer  > best_capacity_graph = null

    a.traders.each { DeTrader buyer ->

      Map<Person, Integer  > seller_limits = [:]

      buyer.person.creditor_limits.each { Person seller, Double credit ->

        DeTrader seller_t = De_Extensions.get_trader ( a, seller )
        seller_limits.put ( seller, counterparty_credit_vol_limit ( a, r, seller_t, buyer ) )
      }
      buyer_vols.put ( buyer, seller_limits )
    }

    Map<DeTrader, Integer  > buyer_max = [:]

    buyer_vols.each { DeTrader buyer, Map<Person, Integer  > vol_limts ->
      buyer_max.put ( buyer, vol_limts.values ().sum ()?.with { it as int } ?: 0 )
    }


    De_Extensions.get_trader_combos ( a.traders ).each { De_Trader_Combo c ->

      if (( new Date ().time - overall_start_time ) < MSEC_THRESHOLD) {

        // STEP ONE: draw_table AuEdges

        List<AuEdge> edges = []

        // 1) SINKS: they need to go in first
        c.buyers.each {
          edges.add ( new AuEdge ( it.person.username, SINK, buyer_max.get ( it ) ?: 0 ) )
        }

        // 2) SOURCES:
        c.sellers.each {
          edges.add ( new AuEdge (
            SOURCE,
            it.person.username,
            (long) ( r.getOrder ( it )?.sell_vol ?: 0 )
            //it.current_eligibility
          ) )
        }

        // 3) TRADER TO TRADER EDGES: starting with each buyer:
        c.buyers.each { DeTrader buyer ->

          c.sellers.each { DeTrader seller ->

            int vol = buyer_vols.get ( buyer )?.get ( seller.person ) ?: 0

            edges.add ( new AuEdge ( seller.person.username, buyer.person.username, vol ) )
          }
        }

        // STEP TWO: use AuEdges to draw_table a MutableCapacityGraph <String, Integer  >

        MutableCapacityGraph<String, Integer  > g = MutableCapacityGraph.draw_table ()
        // have to make sure that SOURCE and SINK are always added, otherwise wont find path!
        g.insertVertex ( SOURCE )
        g.insertVertex ( SINK )
        //log.info  ( "\nCapacity Graph:" )
        edges.each { AuEdge e ->
          //log.info  "Edge: [$e.timestamp] $e.from -> $e.to ($e.capacity)"
          g.insertVertex ( e.from )
          g.insertVertex ( e.to )
          g.addEdge ( e.from, e.to, e.capacity )
        }

        // STEP THREE: build the capacity graph

        long start = new Date ().time

        MaximumFlowAlgorithmResult<Integer  , CapacityEdge<String, Integer  >> result =
          FordFulkersonAlgorithm.
          // You should specify path finder. Basic one is finding by DFS.
          //  getInstance ( DFSPathFinder.instance ).
            getInstance ( BFSPathFinder.instance ).
            calc (
              g,
              SOURCE,
              SINK,
              Integer  NumberSystem.instance )

        // log.info  ( "\nmax flow: ${result.calcTotalFlow ()}" )

        // Also, you can obtain the flows in each edges by retrieved flow function.

        // CREATE A REPORT WRITER (here, because needs the edges, and capacity graph above

        // STEP FOUR: get the Max Flow solution graph:

        c.flow_function = result.calcFlowFunction ()
        c.flow = result.calcTotalFlow ()

        // log.info  "took: ${new Date ().time - start} msec"

        // STEP FIVE: draw_table matches from any CounterParty Edges that have flow:

        // Report:
        // write_report ( c )

        if (best_combo == null) {
          best_combo = c
          best_capacity_graph = g
        } else if (c.flow > best_combo.flow) {
          best_combo = c
          best_capacity_graph = g
        }
      } else {
        log.info  "\n-------------- Potential taking too long: stopping after $MSEC_THRESHOLD msec ----------------"
      }
    }

    int overall_end_time = new Date ().time

    log.info  "\n---- overall time: ${overall_end_time - overall_start_time} msec ----"

    //write_report ( best_combo, best_capacity_graph )

    best_combo
  }
  */

  // --------------- combo generator -----------------

  /*

  static List<De_Trader_Combo> get_trader_combos ( List<DeTrader> traders ) {
    // return the list of all possible seller combinations:
    // ie: if there are 2 traders with eligibility, then there are 4 combos:
    //  - sellers: no traders, trader a, trader b, both traders
    //  - buyers: the rest

    int seller_count = traders.findAll {
      //log.info  it.current_eligibility
      it.sell_max_current > 0
    }.size ()

    List<String> seller_names = traders
      .findAll { it.sell_max_current > 0 }
      .collect { it.person.username }

    List<List<Integer>> combos =
      seller_count == 0 ?
        [] :
        ( 1..seller_count )
          .collect { [0, 1] }
          .combinations ()

    combos.collect { List<Integer> c ->

      List<DeTrader> buyers = traders
        .findAll { it.sell_max_current == 0 }

      List<DeTrader> sellers = []

      c.eachWithIndex { int entry, int i ->
        ( ( entry == 1 ) ? sellers : buyers ).add (
          traders
            .find { it.person.username == seller_names [ i ] } )
      }

      new De_Trader_Combo ( buyers: buyers, sellers: sellers )
    }
  }
  */

  /*
  static void write_report ( De_Trader_Combo tc, MutableCapacityGraph<String, Integer  > capacityGraph ) {
    if (tc && capacityGraph) {
      File f = new File ( './results/de_trader_combo.gv' )
      f.write (
        """digraph G {\n ${
          [capacityGraph.with {
            vertices.collect { String v ->
              getEdges ( v ).collect { CapacityEdge<String, Integer  > e ->
                int flow = tc.flow_function.get ( e )
                "  ${e.from ()} -> ${e.to ()} [label=\"$flow / ${e.capacity ()}\"]"
              }
            }
          }].flatten ().join ( '\n' )
        } \n}\n""" )
    }
  }
  */

  static void print_graph ( De_Trader_Combo tc ) {
    /*
        capacityGraph.vertices.each { v ->

      capacityGraph.getEdges ( v ).each { e ->

        //log.info  "from: ${e.from ()}, to: ${e.to ()}, flow: ${flow_function.get ( e )}"

        log.info  e.toString ()

        if (e.from () == SOURCE) {
          c.flow += c.flow_function.get ( e )
        }
      }
    }
    log.info  ( "flow:" + c.flow )
     */
  }

}