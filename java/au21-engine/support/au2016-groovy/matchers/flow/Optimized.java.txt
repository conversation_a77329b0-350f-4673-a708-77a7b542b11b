package app.services.de.matchers.flow;

import app.services.de.matchers.nutshell.EdgeInfo;

public class Optimized extends FlowNetwork {
    int[][] capacity;     // Contains all capacities.
    int[][] flow;         // Contains all flows.
    int[] previous;       // Contains predecessor information of path.
    int[] visited;        // Visited during augmenting path search.

    final int QUEUE_SIZE; // Size of queue will never be greater than n.
    final int queue[];    // Use circular queue in implementation.

    // Load up the information
    public Optimized(int n, int s, int t, Iterator<EdgeInfo> edges) {
        super(n, s, t);
        queue = new int[n];
        QUEUE_SIZE = n;
        capacity = new int[n][n];
        flow = new int[n][n];
        previous = new int[n];
        visited = new int[n];

        // Initially, flow is set to 0. Pull info from input.
        while (edges.hasNext()) {
            EdgeInfo ei = edges.next();
            capacity[ei.start][ei.end] = ei.capacity;
        }
    }


}