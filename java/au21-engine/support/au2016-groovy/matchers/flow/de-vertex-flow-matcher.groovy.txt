package app.services.de.test.matchers.flow

import app.model.DeAuction
import app.model.DeTrader
import org.psjava.algo.graph.flownetwork.FordFulkersonAlgorithm
import org.psjava.algo.graph.flownetwork.MaximumFlowAlgorithmResult
import org.psjava.algo.graph.pathfinder.BFSPathFinder
import org.psjava.ds.graph.CapacityEdge
import org.psjava.ds.graph.MutableCapacityGraph
import org.psjava.ds.misc.math.Function
import org.psjava.ds.numbersystrem.LongNumberSystem

import static app.services.de.test.matchers.flow._De_Vertex_Flow_Matcher.AuVertexType.*


// TODO: so, this one uses Vertices, but it seems that Edges might be better


// OLD:
class _De_Vertex_Flow_Matcher {

  enum AuVertexType {
    SOURCE, SELLER, BUYER, SINK
  }

  static class AuVertex {

    DeTrader trader
    AuVertexType type

    String toString () {
      if (type == SOURCE)
        return SOURCE.toString ()
      else if (type == SINK)
        return SINK.toString ()
      else
        return type.toString () + ': ' + trader.person.company_name
    }
  }

/*
class AuEdge {
  AuVertex from
  AuVertex to
  Long capacity
  long flow
}
*/

  static AuVertex source = new AuVertex ( type: SOURCE )
  static AuVertex sink = new AuVertex ( type: SINK )


  static MutableCapacityGraph<AuVertex, Long> create_capacity_graph ( DeAuction a ) {

    MutableCapacityGraph<AuVertex, Long> capacityGraph = MutableCapacityGraph.create ()

    capacityGraph.insertVertex ( source )
    capacityGraph.insertVertex ( sink )

    // 1. Trader vertices:

    List<AuVertex> seller_vertices = a.sellers.collect {
      new AuVertex (
        type: SELLER,
        trader: it
      )
    }

    List<AuVertex> buyer_vertices = a.buyers.collect {
      new AuVertex (
        type: BUYER,
        trader: it
      )
    }

    seller_vertices.each { capacityGraph.insertVertex ( it ) }
    buyer_vertices.each { capacityGraph.insertVertex ( it ) }

    // 2. set_de_auction_status the edges:

    seller_vertices.each { AuVertex seller_vx ->

      // a) source to seller edges:

      capacityGraph.addEdge (
        source,
        seller_vx,
        ( a.lastround.getOrder ( seller_vx.trader )?.sell_vol ?: 0L ) as Long
      )

      // b) seller to each buyer edges:

      buyer_vertices.each { AuVertex buyer_vx ->
        capacityGraph.addEdge (
          seller_vx,
          buyer_vx,
          a.current_counter_party_vol_limit ( a.lastround, seller_vx.trader, buyer_vx.trader )
        )
      }

    }

    // c) buyer to sink edges:

    buyer_vertices.each { AuVertex buyer_vx ->
      capacityGraph.addEdge (
        buyer_vx,
        sink,
        ( a.lastround.getOrder ( buyer_vx.trader )?.buy_vol ?: 0L ) as Long
      )
    }

    return capacityGraph

  }

// TODO: OLD
  static Function<CapacityEdge<AuVertex, Long>, Long> match ( MutableCapacityGraph<AuVertex, Long> capacityGraph ) {

    long start = new Date ().time

    // You should specify path finder. Basic one is finding by DFS.
    // getInstance ( DFSPathFinder.instance ).
    MaximumFlowAlgorithmResult<Long, CapacityEdge<AuVertex, Long>> result =
      FordFulkersonAlgorithm.
        getInstance ( BFSPathFinder.instance ).
        calc (
          capacityGraph,
          source,
          sink,
          LongNumberSystem.instance
        )

    println "took: ${new Date ().time - start} msec"

    println ( "max flow: ${result.calcTotalFlow ()}" )

    // Also, you can obtain the flows in each edges by retrieved flow function.

    Function<CapacityEdge<AuVertex, Long>, Long> flowFunction = result.calcFlowFunction ()

    // Report:
    capacityGraph.vertices.each { v ->
      capacityGraph.getEdges ( v ).each { e ->
        long flow = flowFunction.get ( e )
        println e.toString () + ": " + flow
        // println ( e + ": " + flowFunction.get ( e ))
      }
    };

    return flowFunction

  }

  // helper: // TODO: move to DeExtensions
  static List<CapacityEdge<AuVertex, Long>> getEdges ( MutableCapacityGraph<AuVertex, Long> capacityGraph, DeTrader t ) {
    capacityGraph.getEdges ( capacityGraph.vertices.find { it.trader == t } as AuVertex ).toList ()
  }

  static CapacityEdge<AuVertex, Long> getEdge ( MutableCapacityGraph<AuVertex, Long> capacityGraph, DeTrader seller, DeTrader buyer ) {
    getEdges ( capacityGraph, seller ).find { it.to ().trader && it.to ().trader == buyer }
  }

}