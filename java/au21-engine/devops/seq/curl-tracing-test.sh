#!/bin/bash

# Generate random trace and span IDs
TRACE_ID=$(openssl rand -hex 16)
SPAN_ID=$(openssl rand -hex 8)

# Current time in nanoseconds
START_TIME=$(date +%s%N)
END_TIME=$((START_TIME + 1000000000))  # 1 second later

# Create protobuf message (this is a simplified representation)
PROTO_MSG=$(printf '\x0a\x80\x01\x0a\x11\x0a\x0c\x73\x65\x72\x76\x69\x63\x65\x2e\x6e\x61\x6d\x65\x12\x01\x0a\x12\x6b\x0a\x69\x0a\x10%s\x12\x08%s\x1a\x09\x74\x65\x73\x74\x2d\x73\x70\x61\x6e\x20\x01\x28\x80\xb0\xb9\xc1\x92\xee\xf6\xd9\x2d\x30\x80\xb0\xb9\xd1\x92\xee\xf6\xd9\x2d\x4a\x1c\x0a\x10\x63\x75\x73\x74\x6f\x6d\x2e\x61\x74\x74\x72\x69\x62\x75\x74\x65\x12\x08\x0a\x06\x76\x61\x6c\x75\x65\x31' \
    $(echo -n $TRACE_ID | sed 's/\(..\)/\\x\1/g') \
    $(echo -n $SPAN_ID | sed 's/\(..\)/\\x\1/g'))

# Send the protobuf data to Seq
curl -v -X POST \
  "http://dev1.auctionologies.com:5341/ingest/otlp/v1/traces" \
  -H "Content-Type: application/x-protobuf" \
  --data-binary "$PROTO_MSG"
