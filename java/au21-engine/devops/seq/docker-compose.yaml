# https://docs.datalust.co/docs/using-gelf
# May 2, 2025: admin password: 'password'
version: '3'
services:
  seq-input-gelf:
    image: datalust/seq-input-gelf:latest
    platform: linux/arm64
    ports:
      - "12201:12201/udp"
    environment:
      # SEQ_ADDRESS: "http://dev1.auctionologies.com:5341"
      SEQ_ADDRESS: "http://dev1.auctionologies.com/seq/"
      GELF_ENABLE_DIAGNOSTICS: "True"
    restart: unless-stopped

# this is for running both on the same machine:
#version: '3'
#services:
#  seq-input-gelf:
#    image: datalust/seq-input-gelf:latest
#    depends_on:
#      - seq
#    ports:
#      - "12201:12201/udp"
#    environment:
#      SEQ_ADDRESS: "http://seq:5341"
#    restart: unless-stopped
#  seq:
#    image: datalust/seq:latest
#    ports:
#      - "5341:80"
#    environment:
#      ACCEPT_EULA: Y
#    restart: unless-stopped
#    volumes:
#      - ./seq-data:/data
