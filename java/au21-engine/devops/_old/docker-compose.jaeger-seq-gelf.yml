version: "3"
services:
  # Jaeger for distributed tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "16686:16686" # J<PERSON>ger UI
      - "14268:14268" # Receive legacy OpenTracing traces
      - "4317:4317"   # OTLP gRPC receiver
      - "4318:4318"   # OTLP HTTP receiver
      - "14250:14250" # Receive from external otel-collector
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    restart: unless-stopped

  # Seq for structured logging
  seq:
    image: datalust/seq:latest
    ports:
      - "5341:80"     # Web UI and ingestion
    environment:
      ACCEPT_EULA: Y
    restart: unless-stopped
    volumes:
      - ./seq-data:/data

  # Seq GELF input for log ingestion
  seq-input-gelf:
    image: datalust/seq-input-gelf:latest
    platform: linux/amd64
    depends_on:
      - seq
    ports:
      - "12201:12201/udp"
    environment:
      SEQ_ADDRESS: "http://seq:5341"
      GELF_ENABLE_DIAGNOSTICS: "True"
    restart: unless-stopped