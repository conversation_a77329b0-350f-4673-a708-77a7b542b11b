image: gradle:8.6.0-jdk17  # Base image

stages:
  - build
  - package

# Variables (Adjust if needed)
variables:
  APP_NAME: my-quarkus-app

build-app:
  stage: build
  script:
    - ./gradlew build
  artifacts:
    paths:
      - build/libs/*.jar
  cache:  # Gradle dependency caching
    paths:
      - .gradle/caches/
      - .gradle/wrapper/

build-image:
  stage: package
  image: docker:latest
  services:
    - docker:dind
  script:
    - docker build -t $APP_NAME:latest .
    # Optional: Push to GitLab Registry (or your registry of choice)
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker push $APP_NAME:latest
