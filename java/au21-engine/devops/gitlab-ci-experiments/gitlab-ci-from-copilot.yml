image: docker:latest

services:
  - docker:dind

stages:
  - build
  - push

variables:
  DOCKER_HOST: tcp://docker:2375/
  DOCKER_DRIVER: overlay2
  IMAGE_NAME: registry.gitlab.com/auctionologies/2024/2024-02-19-code-with-quarkus

before_script:
  - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" $CI_REGISTRY

build:
  stage: build
  script:
    - docker build -t $IMAGE_NAME .

push:
  stage: push
  script:
    - docker push $IMAGE_NAME
