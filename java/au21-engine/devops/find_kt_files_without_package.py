import os


def find_kt_files_without_package(directory):
    if not os.path.isdir(directory):
        print(f"Error: The directory '{directory}' does not exist.")
        return []

    no_package_files = []

    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.kt'):
                file_path = os.path.join(root, file)
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    for line in lines:
                        if line.strip():  # Ignore blank lines
                            first_non_blank_line = line.strip()
                            break
                    else:
                        first_non_blank_line = ''  # File is empty or only contains blank lines
                    if not first_non_blank_line.startswith('package'):
                        no_package_files.append(file_path)

    return no_package_files


source_directory = 'src'
files_without_package = find_kt_files_without_package(source_directory)

if files_without_package:
    print(
        "The following .kt files do not have a 'package' declaration at the top:")
    for file in files_without_package:
        print(file)
else:
    if os.path.isdir(
            source_directory):  # Only print this if the directory exists
        print("All .kt files have a 'package' declaration at the top.")
