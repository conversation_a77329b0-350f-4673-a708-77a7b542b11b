# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Task files
tasks.json
tasks/

# don't store this, it will come from the frontend build process
#src/main/resources/META-INF/resources

# exclude repomix xml files:
*repomix*.xml

# we don't want the databases:
db/

# Local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local


# IntelliJ
.idea
*.ipr
*.iml
*.iws
.kotlin/

# Gradle
.gradle/
build/
build-cache/
cache/

# Plugin directory
/.quarkus/cli/plugins/

# Eclipse
.project
.classpath
.settings/
bin/

# NetBeans
nb-configuration.xml

# Visual Studio Code
.vscode
.factorypath

# Dependencies
node_modules
.pnp
.pnp.js

# Testing
coverage

# Turbo
.turbo

# Vercel
.vercel

# Build Outputs
.next/
out/
build
dist

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Misc
.DS_Store
*.pem

# Vim
*.swp
*.swo

# patch
*.orig
*.rej

.aider*

# Added by <PERSON> Task Master
# Logs
logs
*.log
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific

