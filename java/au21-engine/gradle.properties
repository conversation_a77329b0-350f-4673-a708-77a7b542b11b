# gradle.properties

# Quarkus Platform Configuration (Keep existing)
quarkusPluginId=io.quarkus
quarkusPluginVersion=3.20.0
quarkusPlatformGroupId=io.quarkus.platform
quarkusPlatformArtifactId=quarkus-bom
quarkusPlatformVersion=3.20.0

# --- Gradle Performance Optimizations ---

# Enable Gradle Build Cache (Keep existing)
org.gradle.caching=true

# Enable Parallel Execution (Keep existing)
org.gradle.parallel=true

# Enable File System Watching (Added Optimization)
# Helps speed up incremental builds by avoiding unnecessary disk scanning
org.gradle.vfs.watch=true

# Kotlin Incremental Compilation Enhancement (Added Optimization)
# Uses classpath snapshots for more reliable incremental builds
kotlin.incremental.useClasspathSnapshot=true

# Gradle Daemon JVM Arguments (Keep existing)
org.gradle.jvmargs=-Xmx2g -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError

# --- Configuration Cache (Experimental - Enable with Caution) ---
# Quarkus compatibility has improved, but test thoroughly if you enable this.
# It offers significant potential speedups for subsequent builds.
# org.gradle.configuration-cache=true
# org.gradle.unsafe.configuration-cache-problems=warn
# Note: Removed the previous explicit 'org.gradle.configuration-cache=false'
