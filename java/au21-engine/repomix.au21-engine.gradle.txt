This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by Repomix.

================================================================
Directory Structure
================================================================
src/
  main/
    resources/
      application.properties
build.gradle.kts
gradle.properties
settings.gradle.kts

================================================================
Files
================================================================

================
File: src/main/resources/application.properties
================
#quarkus.test.continuous-testing=enabled

## ++++++++++++++++++++++++++++++++++++++++++++++++++++
# ALL CONFIGS: https://quarkus.io/guides/all-config
# GOOD REFERENCE: https://quarkus.io/guides/config-reference
# ++++++++++++++++++++++++++++++++++++++++++++++++++++

# ============================================
# Redis settings
# NOTE: using Lettuce, not Quarkus/Mutiny/Vertx
# ============================================
#
# quarkus.redis.hosts=redis://localhost:6379
# quarkus.redis.health.enabled=true
#REDIS_URL=redis://localhost:6379
#ENGINE_INPUT_CHANNEL=ENGINE_INPUT_CHANNEL
#ENGINE_OUTPUT_CHANNEL=ENGINE_OUTPUT_CHANNEL


# ============================================
# Objectdb settings
# ============================================
#
# (1) OBJECTDB_ACTIVATION_CODE:
# - If not set here, then it must be passed in as an environment variable.
# - it is specific to each host machine.

OBJECTDB_ACTIVATION_CODE=A812-7Y7H-GR1Y-1JT0-91KK

# (2) OBJECTDB_DB_HOME:
# - this is relative to: <user.home>
# - NB: with docker this is relative to: /root/ !!# (2) Docker:
# - ie: with docker, the volume to map is:  /root/<OBJECTDB_DB_HOME>
# - The database, logs, and config will all be under this directory relative to user home.
#
OBJECTDB_DB_HOME=db/objectdb
#
# (3) OBJECTDB_CONFIG_TEMPLATE_PATH
# - the configuration template is read at startup
# - variables are substituted and it is saved the location specified in OBJECTDB_CONFIG_GENERATED_PATH
# - this template is relative to src/main/resources:
OBJECTDB_CONFIG_TEMPLATE_PATH=config/objectdb/au21-engine.objectdb.template.xml
#
# (4) OBJECTDB_CONFIG_GENERATED_PATH:
# - generated from above template
# - save relative to <user.home>/<OBJECTDB_DB_HOME>
# - ie: for docker this is relative to: /root/<OBJECTDB_DB_HOME>/
OBJECTDB_CONFIG_GENERATED_PATH=config/objectdb/au21-engine.objectdb.generated.xml
#
# (5) OBJECTDB_URL:
# In addition to location, the type of database is determined by the OBJECTDB_URL:
#
# Jan 4, 2021 note: the default is now test.mem, use an environment variable
#   - override with eg: au21-engine.db
#   - or use client/server
#
# (a) if OBJECTDB_URL ends in .mem then an in-memory database is created,
#   - this is used for example in build.gradle under test {}
#
#OBJECTDB_URL=test.mem
#
# (b) else if OBJECTDB_URL starts with "objectdb://" then is client/server mode
#   - and is created under /db in the server directory
# OBJECTDB_URL=objectdb://localhost:6136/dev.odb;user=admin;password=admin
#
# (c) else an embedded database is created as follows
# - /db is prepended
# - and the db is created relative to: <user.home>/<OBJECTDB_DB_HOME>/<OBJECTDB_URL>/db
# - eg: if OBJECTDB_DB_HOME=objectdb,
# - and OBJECTDB_URL=au21-engine.odb
# - and this is a docker container,
# - then the database will be: /root/objectdb/db/au21-engine.odb
#
# CURRENT DEPLOYMENT: (use environment variable to override!)
#
OBJECTDB_URL=test.odb
# OBJECTDB_URL=au21-engine.odb

# (6) OBJECTDB_ACTIVATION_CODE:
#
# OBJECTDB_ACTIVATION_CODE is set in environment: ie: .env, or docker-compose file
# Ryzen March 9, 2021:
#OBJECTDB_ACTIVATION_CODE=A812-7Y7H-GR1Y-1JT0-91KK
#
# (7) client/server username/password:
# - only needed for client/server
# OBJECTDB_ADMIN_PASSWORD=
# OBJECTDB_ADMIN_USER=

# ============================================
## JACOCO: trying to get graceful shutdown
# ============================================

quarkus.shutdown.timeout=2

#------------------------------------------
# General Application Settings
#------------------------------------------
CREATE_SAMPLE_DB=true
%dev.HEARTBEAT=OFF
%prod.HEARTBEAT=ON

quarkus.live-reload.instrumentation=true

quarkus.http.port=4040
quarkus.http.test-port=4040
quarkus.http.enable-compression=true

quarkus.http.cors.enabled=true
%dev.quarkus.http.cors.origins=/.*/
%prod.quarkus.http.cors.origins=https://your-production-domain.com,http://localhost:4040
%test.quarkus.http.cors.origins=http://localhost:4040

%test.quarkus.http.auth.basic=false
%test.quarkus.security.users.embedded.enabled=false

# Add both paths if needed
quarkus.http.auth.permission.ws-permit.paths=/socket/*,/auction-socket/*
quarkus.http.auth.permission.ws-permit.policy=permit

quarkus.smallrye-graphql.ui.always-include=true

quarkus.http.access-log.enabled=false
quarkus.http.record-request-start-time=false

quarkus.application.name=au21-engine
quarkus.otel.exporter.otlp.traces.endpoint=http://localhost:4317
%test.quarkus.otel.traces.exporter=none


#------------------------------------------
# Logging Configuration
#------------------------------------------

# --- Default Logging Settings (Applied to all profiles unless overridden) ---
quarkus.log.level=INFO
quarkus.log.console.enable=true
# Default format is text, default JSON is disabled.


# --- Dev Profile Logging ---
# Inherits quarkus.log.level=INFO (or set %dev.quarkus.log.level=DEBUG if needed)
# Explicitly DISABLE the JSON console logging for the dev profile using the CORRECT key.
%dev.quarkus.log.console.json.enabled=false

# --- Test Profile Logging ---
# Inherits quarkus.log.level=INFO (or set %test.quarkus.log.level=DEBUG if needed)
# Explicitly DISABLE the JSON console logging for the test profile using the CORRECT key.
%test.quarkus.log.console.json.enabled=false

# --- Prod Profile Logging ---
# Inherits quarkus.log.level=INFO
# Explicitly ENABLE the JSON console logging ONLY for the prod profile using the CORRECT key.
%prod.quarkus.log.console.json.enabled=true
# Keep prod logs compact (not pretty-printed) for log aggregators.
%prod.quarkus.log.console.json.pretty-print=false
# Ensure the text format isn't applied to prod by the default setting (might be redundant now, but safe).
# Clear the text format for prod
%prod.quarkus.log.console.format=

# --- Category Specific Levels ---


# --- Category Specific Levels ---
# (Keep comments on separate lines)
# Categories are currently commented out as requested.
## Application specific code (DEBUG for dev/test)
#%dev,test.quarkus.log.category."au21.engine".level=INFO
## Add your specific package
#%dev,test.quarkus.log.category."org.acme".level=DEBUG
#
## Standard WebSocket implementation (DEBUG for dev/test when needed)
## Use DEBUG when troubleshooting WebSocket connection/lifecycle issues
#%dev,test.quarkus.log.category."io.undertow.websockets.jsr".level=INFO
#
## Vert.x HTTP (might be verbose, use DEBUG when needed)
#%dev,test.quarkus.log.category."io.quarkus.vertx.http".level=INFO
#
## Security (DEBUG useful for test profile)
#%test.quarkus.log.category."io.quarkus.security".level=DEBUG
## Usually INFO is enough unless debugging Elytron
#%test.quarkus.log.category."org.wildfly.security".level=INFO

# --- Optional File Logging (Example for Prod) ---
# %prod.quarkus.log.file.enable=true
# %prod.quarkus.log.file.path=/var/log/quarkus-app.log # Or your desired path
# %prod.quarkus.log.file.rotation.max-file-size=10M
# %prod.quarkus.log.file.rotation.max-backup-index=5
# %prod.quarkus.log.file.format=%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%c{1.}] (%t) %s%e%n
# %prod.quarkus.log.file.level=INFO # Log INFO and above to file in prod

================
File: build.gradle.kts
================
// build.gradle.kts
import com.github.benmanes.gradle.versions.updates.DependencyUpdatesTask
import org.jetbrains.kotlin.gradle.dsl.JvmTarget

// versions:
val axion_release = "1.15.0"
val asciitable = "0.3.2"
val classgraph = "4.8.179"
val combinatorics = "1.6.0"
val gson = "2.13.0"
val jackson_module_kotlin = "2.18.3"
val javax_persistence = "2.2.1"
val joda_time = "2.14.0"
val jsoniter = "0.9.23"
val jta = "1.1"
val jte = "3.2.0"
val kasechange_jvm = "1.4.1"
val kotest = "5.9.1"
val kotlinVersion = "2.0.21"
val kotlinx = "1.7.2"
val kotlinx_serialization_json = "1.8.1"
val micrometer = "1.14.6"
val mockk = "1.14.0"
val object_db = "2.9.0"
val opencsv = "5.10"
val plantuml_builder = "2.8"
val ps_java = "0.1.19"
val testcontainers = "1.19.8"
val ts_generator = "1.1.2"
val uri_test: String = "3.14.5"

plugins {
    kotlin("jvm") version "2.0.21"
    kotlin("plugin.allopen") version "2.0.21"
    kotlin("plugin.serialization") version "2.0.21"
    id("io.quarkus")
    id("com.github.ben-manes.versions") version "0.52.0"
    jacoco
}

repositories {
    mavenCentral()
    mavenLocal()
    maven { url = uri("https://m2.objectdb.com") }
    maven { url = uri("https://jitpack.io") }
}

val quarkusPlatformGroupId: String by project
val quarkusPlatformArtifactId: String by project
val quarkusPlatformVersion: String by project

dependencies {
    implementation(enforcedPlatform("$quarkusPlatformGroupId:$quarkusPlatformArtifactId:$quarkusPlatformVersion"))
    implementation("io.quarkus:quarkus-arc")
    implementation("io.quarkus:quarkus-container-image-jib")
    implementation("io.quarkus:quarkus-elytron-security-properties-file")
    implementation("io.quarkus:quarkus-kotlin")
    implementation("io.quarkus:quarkus-logging-json")
    implementation("io.quarkus:quarkus-opentelemetry")
    implementation("io.quarkus:quarkus-rest")
    implementation("io.quarkus:quarkus-rest-jackson")
    implementation("io.quarkus:quarkus-smallrye-graphql")
    implementation("io.quarkus:quarkus-websockets")

    // database
    implementation("org.eclipse.persistence:javax.persistence:$javax_persistence")
    implementation("javax.transaction:jta:$jta")
    implementation("com.objectdb:objectdb:$object_db")

    // Miscellaneous
    implementation("ch.ifocusit:plantuml-builder:$plantuml_builder")
    implementation("com.github.ntrrgc:ts-generator:$ts_generator")
    implementation("com.konghq:unirest-java:$uri_test")
    implementation("com.opencsv:opencsv:$opencsv")
    implementation("de.vandermeer:asciitable:$asciitable")
    implementation("gg.jte:jte:$jte")
    implementation("io.github.classgraph:classgraph:$classgraph")
    implementation("io.micrometer:micrometer-core:$micrometer")
    implementation("joda-time:joda-time:$joda_time")
    implementation("net.pearx.kasechange:kasechange-jvm:$kasechange_jvm")
    implementation("org.psjava:psjava:$ps_java")

    // json
    implementation("org.jetbrains.kotlinx:kotlinx-serialization-json:{kotlinx_serialization_json}")
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:$jackson_module_kotlin")
    implementation("com.google.code.gson:gson:$gson")
    implementation("com.jsoniter:jsoniter:$jsoniter")

    // testing
    testImplementation("io.quarkus:quarkus-junit5")
    testImplementation("io.quarkus:quarkus-jacoco")
    testImplementation("io.rest-assured:rest-assured")
    testImplementation("io.kotest:kotest-runner-junit5:$kotest")
    testImplementation("io.kotest:kotest-assertions-core:$kotest")
    testImplementation("io.kotest:kotest-assertions-json-jvm:$kotest")
    testImplementation("io.mockk:mockk:$mockk")
    testImplementation("com.github.shiguruikai:combinatoricskt:$combinatorics")
    testImplementation("org.testcontainers:testcontainers:$testcontainers")
}

group = "au24.engine"
version = "1.0.0-SNAPSHOT"

configurations.all {
    resolutionStrategy.eachDependency {
        if (requested.group == "org.jetbrains.kotlin" && requested.name.startsWith("kotlin-")) {
            useVersion(kotlinVersion)
        }
    }
}

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

allOpen {
    annotation("jakarta.ws.rs.Path")
    annotation("jakarta.enterprise.context.ApplicationScoped")
    annotation("jakarta.persistence.Entity")
    annotation("io.quarkus.test.junit.QuarkusTest")
}

tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile>().configureEach {
    finalizedBy("enhance")
    compilerOptions {
        jvmTarget.set(JvmTarget.JVM_21)
        javaParameters = true
    }
}

kotlin { jvmToolchain(21) }

tasks.withType<Test>().configureEach { // Using configureEach is generally good practice
    // --- Settings from Block 1 ---
    systemProperty("java.util.logging.manager", "org.jboss.logmanager.LogManager")

    // --- Settings from Block 2 ---
    useJUnitPlatform()
    failFast = true // Keep this if you want tests to stop on the first failure
    systemProperty("OBJECTDB_URL", "objectdb:test-set-in-build-gradle.mem") // Ensure this is the desired value for tests

    // --- New Test Logging Configuration ---
    testLogging {
        events("started", "passed", "skipped", "failed") // Log basic events
        exceptionFormat = org.gradle.api.tasks.testing.logging.TestExceptionFormat.SHORT // Use SHORT format for exceptions
        showStandardStreams = true // Show stdout/stderr from tests
        showStackTraces = true // Still show stack traces, but format controls length
        showCauses = true      // Show exception causes
    }
}

tasks.withType<JacocoReport> {
    afterEvaluate {
        classDirectories.setFrom(
            files(
                classDirectories.files.map {
                    fileTree(it) {
                        exclude("simulator/*", "exp/*", "optimizationExps/*")
                    }
                }
            )
        )
    }
}

tasks.jacocoTestReport { reports { xml.required.set(true) } }

val enhance by tasks.registering(JavaExec::class) {
    description = "Enhances Object Db model classes"
    mainClass.set("com.objectdb.Enhancer")
    args(
        "-cp", "src/main/kotlin",
        "au21.engine.domain.common.model.*",
        "au21.engine.domain.de.model.*",
        "au21.engine.framework.database.AuEntity",
        "au21.engine.framework.commands.CommandJson"
    )
    classpath = sourceSets.main.get().runtimeClasspath
    onlyIf {
        val skipEnhance = System.getenv("SKIP_ENHANCE")
        println("SKIP_ENHANCE=$skipEnhance")

        if (skipEnhance?.lowercase() == "true") {
            println("Skipping the 'enhance' task because SKIP_ENHANCE is set to 'true'.")
            println("To run the 'enhance' task, either unset SKIP_ENHANCE or set it to a value other than 'true'.")
            false
        } else {
            println("Running the 'enhance' task because SKIP_ENHANCE is not set to 'true'.")
            true
        }
    }
}

tasks.register("generateTypescript", JavaExec::class) {
    mainClass.set("au21.engine.generators.typescript.Ts_generatorKt")
    classpath = sourceSets.main.get().runtimeClasspath
}

tasks.named<DependencyUpdatesTask>("dependencyUpdates") {
    outputFormatter = "plain,json,html"
    outputDir = "build/dependencyUpdates"
    rejectVersionIf {
        if (candidate.group.startsWith("io.quarkus"))
            !candidate.version.startsWith("3.20.")
        else
            isNonStable(candidate.version) && !isNonStable(currentVersion)
    }
}

fun isNonStable(version: String): Boolean {
    val stableKeyword = listOf("RELEASE", "FINAL", "GA").any { version.uppercase().contains(it) }
    val regex = "^[0-9,.v-]+(-r)?$".toRegex()
    return !(stableKeyword || regex.matches(version))
}

tasks.register("whyKotlinStdlib") {
    doLast {
        configurations.compileClasspath.get().resolvedConfiguration.lenientConfiguration.allModuleDependencies
            .filter { it.moduleName == "kotlin-stdlib-jdk8" }
            .forEach { dep ->
                println("${dep.moduleName} ${dep.moduleVersion} is brought in by:")
                dep.parents.forEach { parent -> println(" - ${parent.name}") }
            }
    }
}

================
File: gradle.properties
================
# gradle.properties
quarkusPluginId=io.quarkus
quarkusPluginVersion=3.20.0
quarkusPlatformGroupId=io.quarkus.platform
quarkusPlatformArtifactId=quarkus-bom
quarkusPlatformVersion=3.20.0

# Gradle performance optimizations
org.gradle.caching=true
# Disabling configuration cache due to compatibility issues with Quarkus
org.gradle.configuration-cache=false
org.gradle.parallel=true
org.gradle.jvmargs=-Xmx2g -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError
org.gradle.unsafe.configuration-cache-problems=warn
n-cache-problems=warn

================
File: settings.gradle.kts
================
// settings.gradle.kts
pluginManagement {
    val quarkusPluginVersion: String by settings
    val quarkusPluginId: String by settings
    repositories {
        mavenCentral()
        gradlePluginPortal()
    }
    plugins { id(quarkusPluginId) version quarkusPluginVersion }
}
buildCache {
    local {
        // Removed debug println statement
        directory = File(rootDir, "build-cache")
    }
}
plugins {
    id("org.gradle.toolchains.foojay-resolver-convention") version "0.10.0"
}
rootProject.name = "au21-engine"



================================================================
End of Codebase
================================================================
