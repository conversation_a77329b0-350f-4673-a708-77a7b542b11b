export FRONTEND_PATH := '../au24-frontend/dist'
export RESOURCE_PATH := './src/main/resources/META-INF/resources'

# -------- Used by gitlab ci: --------

copy-frontend-dist-to-engine:
    mkdir -p "${RESOURCE_PATH}"
    rm -rf "${RESOURCE_PATH}"/*
    cp -a "${FRONTEND_PATH}"/. "${RESOURCE_PATH}"/

engine-build-ci:
	./gradlew clean build --build-cache --gradle-user-home cache/
	./gradlew jacocoTestReport --build-cache --gradle-user-home cache/
	cat build/reports/jacoco/test/html/index.html

# -------- DEV: --------

kill-port-4040:
    npx kill-port 4040

kill-port-8080:
    npx kill-port 8080

# -------- TEST: --------

test: kill-port-4040
    ./gradlew clean test

# -------- GRADLE: --------

dev:
    ./gradlew quarkusDev

upgrade_gradle:
    ./gradlew wrapper --gradle-version=8.10

check_for_dependency_updates:
    # revisions etc now specified in build.gradle.kts
    #./gradlew dependencyUpdates -Drevision=release
    ./gradlew dependencyUpdates

update_quarkus:
    ./gradlew quarkusUpdate
#    note: doesn't work on the build.gradle.kts, maybe can convert


# -------- QUARKUS: --------

open_quarkus_graphql-ui:
    open http://localhost:4040/q/graphql-ui/?query=query%20%7B%0A%20%20users%20%7B%0A%20%20%20%20id%0A%20%20%20%20username%0A%20%20%20%20password%0A%20%20%7D%0A%7D

open_quarkus_dev:
    open http://localhost:4040/q/dev

open_quarkus_graphql-schema:
    open http://localhost:4040/graphql/schema.graphql

# NOTE: for .http and insomnia etc this is the endpoint:
# http://localhost:4040/graphql

open_quarkus_graphql-schema-dev-ui:
    open http://localhost:4040/q/dev-ui/io.quarkus.quarkus-smallrye-graphql/graphql-schema


# -------- NODE_MODULES: --------

remove_node_modules_and_lock_files:
    find . -name 'node_modules' -type d -prune -exec rm -rf '{}' +
    find . -type f -name 'package-lock.json' -delete
    find . -type f -name 'yarn.lock' -delete
    find . -type f -name 'pnpm-lock.yaml' -delete

run-jaeger-with-docker-compose-not-working:
    cd devops/opentelemetry/ && docker-compose up

# JAEGER:
# 1/3: run it the first time, to create the 'jaeger' container
run-jaeger-with-docker:
    docker run -d --name jaeger \
        -e COLLECTOR_ZIPKIN_HTTP_PORT=9411 \
        -p 5775:5775/udp \
        -p 6831:6831/udp \
        -p 6832:6832/udp \
        -p 5778:5778 \
        -p 16686:16686 \
        -p 14268:14268 \
        -p 14250:14250 \
        -p 9411:9411 \
        jaegertracing/all-in-one:latest

# 2/3: stop the jaeger container:
stop-jaeger-with-docker:
    docker stop jaeger

# 3/3 re-start the jaeger container:
start-jaeger-with-docker:
    docker start jaeger

# SEQ logging
# needs the seq server and a seq-input-gelf container
seq_run:
    #!/usr/bin/env sh
    # actually it's running on dev1, wasn't stable on macos, didn't try the platform setting though
    # - NOTE: this might not be working,
    #   - the task below (seq_input_gelf_up) uses dev1 instead
    PH=$(echo 'secret' | docker run --rm -i datalust/seq config hash)

    mkdir -p /au/Downloads/seq/data

    docker run --name seq -d --restart unless-stopped \
     -e ACCEPT_EULA=Y \
     -e SEQ_FIRSTRUN_ADMINPASSWORDHASH="$PH" \
     -v /au/Downloads/seq/data:/data \
     -p 5340:80 \
     -p 5341:5341 \
     datalust/seq

seq_input_gelf_up:
    #    docker run \
    #        --name seq-input-gelf \
    #        -p 12201:12201/udp \
    #        -e SEQ_ADDRESS=http://dev1.auctionologies.com:5341 \
    #        datalust/seq-input-gelf
    cd devops/seq && docker-compose up -d

seq_input_gelf_down:
    cd devops/seq && docker-compose down

## REPOMIX

repomix-kt:
    #repomix --include "**/*.kt" -o "repomix.kt.xml"
    repomix src -c repomix.au21-engine.kt.config.json

repomix-build-files:
    repomix \
      --include "build.gradle.kts,settings.gradle.kts,gradle.properties,src/main/resources/application.properties" \
      -o "repomix.au21-engine.gradle.txt" \
      --style "plain" \
      --no-file-summary

repomix-domain:
    repomix \
      --include "src/main/kotlin/au21/engine/domain/**/*.kt,\
      src/main/kotlin/au21/engine/framework/client/client-command-types.kt,\
      src/main/kotlin/au21/engine/framework/client/LiveClientStore.kt\
      " \
      -o "repomix.au21-engine.domain.txt" \
      --style "plain" \
      --no-file-summary \
      --remove-comments \
      --remove-empty-lines \
      --compress


repomix-docker:
    repomix \
      --include "src/main/docker/*" \
      -o "repomix.au21-engine.docker.txt" \
      --style "plain" \
      --no-file-summary

