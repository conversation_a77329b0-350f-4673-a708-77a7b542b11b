{"output": {"filePath": "repomix.au21-engine.kotlin.txt", "style": "plain", "parsableStyle": false, "fileSummary": false, "directoryStructure": true, "removeComments": true, "removeEmptyLines": false, "compress": true, "topFilesLength": 5, "showLineNumbers": false, "copyToClipboard": false, "git": {"sortByChanges": true, "sortByChangesMaxCommits": 100}}, "include": ["**/*.kt"], "ignore": {"useGitignore": true, "useDefaultPatterns": true, "customPatterns": ["**/test/**"]}, "security": {"enableSecurityCheck": true}, "tokenCount": {"encoding": "o200k_base"}}