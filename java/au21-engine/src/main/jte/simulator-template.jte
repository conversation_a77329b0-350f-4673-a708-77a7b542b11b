@import au21.engine.domain.de.model.DeAuction
@import simulator.RoundAttributes;
@import java.util.List;
@import simulator.TemplateParams
@import java.util.Map
@import java.util.Map.Entry
@param TemplateParams templateParams

<!DOCTYPE html>
<html lang="en-US">
<head>
    <title>Data Simulator</title>
    <style>
        * {
            box-sizing: border-box;
        }

        .row {
            margin-left: -5px;
            margin-right: -5px;
        }

        .column {
            float: left;
            width: fit-content;
            padding: 20px;
        }

        /* Clearfix (clear floats) */
        .row::after {
            content: "";
            clear: both;
            display: table;
        }

        table {
            border-collapse: collapse;
            border-spacing: 0;
            border: 1px solid #ddd;
        }

        th, td {
            text-align: left;
            width: fit-content;
            padding: 16px;
            border: 1px solid black;
        }

        #red {
            background-color: lightcoral;
        }

        #green {
            background-color: lightgreen;
        }
    </style>
</head>
<body>
<div class="row">
    <div class="column">
        <h3>Counter party credits</h3>
        <div class="row">
            <div class="column">
                <table>

                    <tbody>
                    <tr>
                        <th>Buyer</th>
                        <th>Seller</th>
                        <th>Million $</th>
                    </tr>
                    @for(var outerEntry : templateParams.getCredits().getBuyerSellerCredits().entrySet())
                        <tr>
                            <td rowspan="4">${outerEntry.getKey()}</td>
                        </tr>
                        @for(var entry : outerEntry.getValue().entrySet())
                            <tr>
                                <td>${entry.getKey()}</td>
                                <td>${entry.getValue()}</td>
                            </tr>
                        @endfor
                    @endfor
                    </tbody>
                </table>
            </div>
            <div class="column">
                <table>
                    <tbody>
                    <tr>
                        <th>Seller</th>
                        <th>Buyer</th>
                        <th>Million $</th>
                    </tr>
                    @for(var outerEntry : templateParams.getCredits().getSellerBuyerCredits().entrySet())
                        <tr>
                            <td rowspan="4">${outerEntry.getKey()}</td>
                        </tr>
                        @for(var entry : outerEntry.getValue().entrySet())
                            <tr>
                                <td>${entry.getKey()}</td>
                                <td>${entry.getValue()}</td>
                            </tr>
                        @endfor
                    @endfor
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="column">
        <h3>Scenarios</h3>
        @for(Entry<String,List<RoundAttributes>> scenario : templateParams.getScenarioToRoundAttributes().entrySet())
            <div class="row">
                <h4>${scenario.getKey()}</h4>
                <div class="column">
                    <table>
                        <tbody>
                        <tr>
                            <th>Trader</th>
                            @for(var company : scenario.getValue().get(0).getBids().keySet())
                                <th>${company}</th>
                            @endfor
                            <th>max flow</th>
                            <th>total supply</th>
                            <th>total demand</th>
                            <th>demand - supply</th>
                            <th>next round price direction</th>
                            <th>next round constraints</th>
                        </tr>

                        @for(int i = 0; i < scenario.getValue().size(); i++)
                            <tr>
                                <th>Round:${scenario.getValue().get(i).getRoundNumber()}</th>
                                @for(var value : scenario.getValue().get(i).getBids().values())
                                    @if(value.getOrderType().equals("BUY"))
                                        <td id="green">${value.getQuantity()}</td>
                                    @else
                                        <td id="red">${value.getQuantity()}</td>
                                    @endif
                                @endfor
                                <td>${scenario.getValue().get(i).getMaxFlow()}</td>
                                <td>${scenario.getValue().get(i).getSupply()}</td>
                                <td>${scenario.getValue().get(i).getDemand()}</td>
                                @if(Double.parseDouble(scenario.getValue().get(i).getAbsoluteSupplyDemand()) >= 0)
                                    <td id="green">${scenario.getValue().get(i).getAbsoluteSupplyDemand()}</td>
                                @else
                                    <td id="red">${scenario.getValue().get(i).getAbsoluteSupplyDemand()}</td>
                                @endif
                                <td>${scenario.getValue().get(i).getPriceDirection()}</td>
                                <td>
                                    @for(Entry<String,List<DeAuction.DeBidConstraints>> constraints : templateParams.getConstraintsMap().get(scenario.getKey()).entrySet())
                                        ${String.format("%s -> %s, %s, %s, %s",constraints.getKey(),constraints.getValue().get(i).getMax_buy_quantity(), constraints.getValue().get(i).getMin_buy_quantity(),constraints.getValue().get(i).getMin_sell_quantity(),constraints.getValue().get(i).getMax_sell_quantity())}
                                        <br>
                                    @endfor
                                </td>
                            </tr>
                        @endfor
                        </tbody>
                    </table>
                </div>
            </div>
        @endfor
    </div>
</div>
</body>
</html>
