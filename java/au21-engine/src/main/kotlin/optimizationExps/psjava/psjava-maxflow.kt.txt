import org.psjava.algo.graph.flownetwork.FordFulkersonAlgorithm
import org.psjava.algo.graph.flownetwork.MaximumFlowAlgorithmResult
import org.psjava.algo.graph.pathfinder.BFSPathFinder
import org.psjava.algo.graph.pathfinder.DFSPathFinder
import org.psjava.ds.graph.CapacityEdge
import org.psjava.ds.graph.MutableCapacityGraph
import org.psjava.ds.numbersystrem.IntegerNumberSystem

fun exp() {


    val capacityGraph = MutableCapacityGraph.create<String, Int>().apply {
        insertVertex("A")
        insertVertex("B")
        insertVertex("C")
        insertVertex("D")
        addEdge("A", "B", 4)
        addEdge("A", "C", 2)
        addEdge("B", "C", 1)
        addEdge("B", "D", 4)
        addEdge("C", "D", 1)
    }

    /*
    * NOTE:   EdmondsKarpAlgorithm is just the FordFulkersonAlgorithm with BFS instead of DFS
    *  ie: EdmondsKarpAlgorithm.getInstance()
    *   calls: FordFulkersonAlgorithm.getInstance(BFSPathFinder.getInstance());
     */

    val isDFS = true

    val start = System.nanoTime()

    val result: MaximumFlowAlgorithmResult<Int, CapacityEdge<String, Int>> =
        FordFulkersonAlgorithm
            .getInstance(if (isDFS) DFSPathFinder.getInstance() else BFSPathFinder.getInstance())
            .calc(capacityGraph, "A", "D", IntegerNumberSystem.getInstance())

// Maximum flow is 5.
    val flow = result.calcTotalFlow()

    val duration = (System.nanoTime() - start) / 1_000_000

    Logger.info("flow: $flow, took: $duration ms")


// Also, you can obtain the flows in each edges by retrieved flow function.
    val flowFunction = result.calcFlowFunction()
    for (e in capacityGraph.getEdges("A")) flowFunction[e]
}

fun main(args: Array<String>) {
    exp()
    exp()
}
