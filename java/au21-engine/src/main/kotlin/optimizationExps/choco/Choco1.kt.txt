package optimizationExps.choco

import org.chocosolver.solver.Model
import org.chocosolver.solver.Solution
import org.chocosolver.solver.variables.IntVar

class Choco1 {


    val n: Int = 8;

    val model: Model = Model("$n-queens problem")

    var vars: Array<IntVar?> = arrayOfNulls(n)

    init {
        0.until(n).forEach { q ->
            vars[q] = model.intVar("Q_$q", 1, n)
        }
        0.until(n - 1).forEach { i ->
            (i + 1).until(n).forEach { j ->
                model.arithm(vars[i], "!=", vars[j]).post()
                model.arithm(vars[i], "!=", vars[j], "-", j - i).post()
                model.arithm(vars[i], "!=", vars[j], "+", j - i).post()
            }
        }
        val solver = model.solver
        solver.showShortStatistics()
        solver.showSolutions()

        val solution: Solution = solver.findSolution()
        println(solution)
    }

}

fun main(args: Array<String>) {
    Choco1()
}
