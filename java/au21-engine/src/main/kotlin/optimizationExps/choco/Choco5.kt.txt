package optimizationExps.choco

import org.chocosolver.solver.Model
import org.chocosolver.solver.Solution
import org.chocosolver.solver.Solver
import org.chocosolver.solver.search.strategy.Search
import org.chocosolver.solver.variables.IntVar

/*
 * working: from: https://github.com/chocoteam/choco-solver
 */
class Choco5 {

    val start = System.nanoTime()

    //  val model: Model = Model("3 trader flow")

    // 1. Create a Model
    val model: Model = Model("my first problem");

    // 2. Create variables
    val source: IntVar = model.intVar("source", 0, 5);
    val sink: IntVar = model.intVar("sink", 0, 5);

    val source_A: IntVar = model.intVar("source_A", 0, 10)
    val A_sink:IntVar = model.intVar("A_sink",0, 20)

    // 3. Create and post constraints thanks to the model
    init {



        model.setObjective(Model.MAXIMIZE, sink)
        //    model.element(x, intArrayOf(5, 0, 4, 1, 3, 2), y).post()

// 3b. Or directly through variables
        // x.add(y).lt(5).post();
// 4. Get the solver
        val solver: Solver = model.solver
        // solver.showDashboard()

// 5. Define the search strategy
        solver.setSearch(Search.inputOrderUBSearch(source, sink));

// 6. Launch the resolution process
//        while (solver.solve()) {
//
        solver.solve()

        if (solver.solutionCount == 0L) {
            println("no solutions")
        } else {
            // 7. Print search statistics
          //  solver.showShortStatistics()
            solver.showSolutions()

            println("solution count = ${solver.solutionCount}")

            repeat(solver.solutionCount.toInt()) {
                val solution: Solution = solver.findSolution()
                println(solution)
            }
        }

        println()
        //           }
//        }
    }
}
// Edges:
//    val source_to_A: IntVar = model.intVar("source->A", 0, 1000)
//    val source_to_B: IntVar = model.intVar("source->B", 0, 1000)
//    val A_to_sink:IntVar = model.intVar("A->sink", 0, 1000)
//    val B_to_sink:IntVar = model.intVar("B->sink", 0, 1000)
//    val A_to_B:IntVar = model.intVar("A->B", 1)
//    val sink_to_source:IntVar = model.intVar("sink_to_source",0, 5000)
//
//    // Nodes:
//    val source: IntVar = sink_to_source.sub(source_to_A).sub(source_to_B).intVar()
//    val A: IntVar = source_to_A.sub(A_to_sink).sub(A_to_B).intVar()
//    val B: IntVar = source_to_B.sub(B_to_sink).add(A_to_B).intVar()
//    val sink: IntVar = A_to_sink.add(B_to_sink).intVar()
//
//    val source_to_sink:IntVar = model.intVar("source_to_sink", 0, 1000)
//
//    val sink:IntVar = source_to_sink.add(0).intVar()
//

//    init {
//
////        model.arithm(A, "=", 0).post()
////        model.arithm(B, "=", 0).post()
////        model.arithm(sink, "=", 0).post()
////        model.arithm(source, "=", 0).post()
//    //    model.arithm(source, "=", sink.neg()).post()
//
//        model.setObjective(Model.MAXIMIZE, sink)
//
//        val solver = model.solver
//        solver.showShortStatistics()
//        solver.showSolutions()
//
//        if (solver.solutionCount == 0L) {
//            println("no solutions")
//        } else {
//
//            val solution: Solution = solver.findSolution()
//            println(solution)
//            println("sink: $sink")
//
//            val duration = (System.nanoTime() - start) / 1_000_000
//            println("duration: $duration ms")
////        model.arithm(B_flow, "=", 0)
//        model.arithm(C_flow, "=", 0)
//     model.arithm(sink_flow, "=", source_flow.neg().intVar())

//        source_A_b.xor(A_sink_b).post()
//        source_B_b.xor(B_sink_b).post()
//        source_C_b.xor(C_sink_b).post()

//        model.ifThen(
//            model.arithm(source_A, ">", 0),
//            model.arithm(A_sink, "=", 0)
//        )
//

fun main(args: Array<String>) {
    Choco5()
}
