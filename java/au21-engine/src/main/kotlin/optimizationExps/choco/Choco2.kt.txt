package optimizationExps.choco

import org.chocosolver.solver.Model
import org.chocosolver.solver.Solution
import org.chocosolver.solver.variables.BoolVar
import org.chocosolver.solver.variables.IntVar

class Choco2 {

    val start = System.nanoTime()

    val model: Model = Model("3 trader flow")

    // MESS !!

    // look at in from inputs!

    val source: IntVar = model.intVar("SOURCE", 0)

    val source_A:IntVar = model.intVar("source->A", 0, 50)
    val source_B:IntVar = model.intVar("source->B", 0, 50)
    val source_C:IntVar = model.intVar("source->C", 0, 50)


    val A_sink:IntVar = model.intVar("A->sink", 0, 50)
    val B_sink:IntVar = model.intVar("B->sink", 0, 50)
    val C_sink:IntVar = model.intVar("C->sink", 0, 50)

    val sink: IntVar = A_sink.add(B_sink).add(C_sink).intVar()



    val NodeA:IntVar = source_A


    val AB:IntVar = model.intVar("A->B", 0, 50)
    val AC:IntVar = model.intVar("A->C", 0, 50)
    val BA:IntVar = model.intVar("B->A", 0, 50)
    val BC:IntVar = model.intVar("B->C", 0, 50)
    val CA:IntVar = model.intVar("C->A", 0, 50)
    val CB:IntVar = model.intVar("C->B", 0, 50)


    val sink_flow:IntVar = A_sink.add(B_sink).add(C_sink).intVar()





    val A_flow:IntVar = source_A.sub(AB).sub(AC).sub(A_sink).intVar()
    val B_flow:IntVar = source_B.sub(BA).sub(BC).sub(B_sink).intVar()
    val C_flow:IntVar = source_C.sub(CA).sub(CB).sub(C_sink).intVar()

    val source_flow:IntVar = source_A.add(source_B).add(source_C).intVar()

//    val source_A_b:BoolVar = source_A.ge(0).boolVar()
//    val source_B_b:BoolVar = source_B.ge(0).boolVar()
//    val source_C_b:BoolVar = source_C.ge(0).boolVar()
//
//    val A_sink_b:BoolVar = A_sink.ge(0).boolVar()
//    val B_sink_b:BoolVar = B_sink.ge(0).boolVar()
//    val C_sink_b:BoolVar = C_sink.ge(0).boolVar()


    init {

        model.arithm(A_flow, "=", 0)
        model.arithm(B_flow, "=", 0)
        model.arithm(C_flow, "=", 0)
   //     model.arithm(sink_flow, "=", source_flow.neg().intVar())

//        source_A_b.xor(A_sink_b).post()
//        source_B_b.xor(B_sink_b).post()
//        source_C_b.xor(C_sink_b).post()

        model.ifThen(
            model.arithm(source_A, ">", 0),
            model.arithm(A_sink, "=", 0)
        )
        model.ifThen(
            model.arithm(A_sink, ">", 0),
            model.arithm(source_A, "=", 0)
        )

        model.ifThen(
            model.arithm(source_C, ">", 0),
            model.arithm(C_sink, "=", 0)
        )
        model.ifThen(
            model.arithm(B_sink, ">", 0),
            model.arithm(source_B, "=", 0)
        )

        model.ifThen(
            model.arithm(source_B, ">", 0),
            model.arithm(B_sink, "=", 0)
        )
        model.ifThen(
            model.arithm(B_sink, ">", 0),
            model.arithm(source_B, "=", 0)
        )

        model.setObjective(Model.MAXIMIZE, sink_flow)

        val solver = model.solver
        solver.showShortStatistics()
        solver.showSolutions()

        val solution: Solution = solver.findSolution()
        println(solution)

        val duration = (System.nanoTime() - start) / 1_000_000
        println("duration: $duration ms")
    }

}

fun main(args: Array<String>) {
    Choco2()
    //Choco2()
}
