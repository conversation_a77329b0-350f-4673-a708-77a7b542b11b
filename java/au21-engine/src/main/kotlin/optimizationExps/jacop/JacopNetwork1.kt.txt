package optimizationExps.jacop

import org.jacop.constraints.netflow.NetworkBuilder
import org.jacop.constraints.netflow.NetworkFlow
import org.jacop.constraints.netflow.simplex.Node
import org.jacop.core.IntVar
import org.jacop.core.Store

class JacopNetwork1(
    val size: Int = 4
) {
    val start = System.nanoTime()
    val store = Store()

    val net: NetworkBuilder = NetworkBuilder()
    val A: Node = net.addNode("A", 0)
    val source: Node = net.addNode("source", 5)
    val sink: Node = net.addNode("sink", -5)



    init {
        net.addArc(source, A, 1, 5)
        val cost = IntVar(store, "cost", 0, 1000)
        net.setCostVariable(cost)
        store.impose(NetworkFlow(net))

    }
}

fun main(args: Array<String>) {
    JacopNetwork1()
}
//    val v: Array<IntVar> = (1..size).map { i ->
//        IntVar(store, "v${i - 1}", 1, size);
//    }.toTypedArray()
//
//    init {
//        XneqY(v[0], v[1]).impose(store)
//        XneqY(v[0], v[2]).impose(store)
//        XneqY(v[1], v[2]).impose(store)
//        XneqY(v[1], v[3]).impose(store)
//        XneqY(v[2], v[3]).impose(store)
//
//        if (!store.consistency()) {
//            throw Error("store inconsistent")
//        }
//
//        val result: Boolean = DepthFirstSearch<IntVar>()
//            .labeling(
//                store,
//                SimpleSelect(v, SmallestDomain(), IndomainMin())
//            )
//
//        val duration = (System.nanoTime() - start) / 1_000
//        println("result: $result, duration: $duration us")
