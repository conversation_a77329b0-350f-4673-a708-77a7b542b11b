package optimizationExps.jacop

import org.jacop.constraints.XneqY
import org.jacop.core.IntVar
import org.jacop.core.Store
import org.jacop.search.*


class Jacop1(
    val size: Int = 4
) {
    val start = System.nanoTime()
    val store = Store()

    val v: Array<IntVar> = (1..size).map { i ->
        IntVar(store, "v${i - 1}", 1, size);
    }.toTypedArray()

    init {
        XneqY(v[0], v[1]).impose(store)
        XneqY(v[0], v[2]).impose(store)
        XneqY(v[1], v[2]).impose(store)
        XneqY(v[1], v[3]).impose(store)
        XneqY(v[2], v[3]).impose(store)

        if (!store.consistency()) {
            throw Error("store inconsistent")
        }

        val result: Boolean = DepthFirstSearch<IntVar>()
            .labeling(
                store,
                SimpleSelect(v, SmallestDomain(), IndomainMin())
            )

        val duration = (System.nanoTime() - start) / 1_000
//        println("result: $result, duration: $duration us")
    }
}

fun main(args: Array<String>) {
    repeat(2) {
        Jacop1()
    }
}
