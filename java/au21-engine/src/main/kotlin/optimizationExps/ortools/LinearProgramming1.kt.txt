// Copyright 2010-2018 Google LLC
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
package optimizationExps.ortools

import com.google.ortools.Loader
import com.google.ortools.linearsolver.MPConstraint
import com.google.ortools.linearsolver.MPObjective
import com.google.ortools.linearsolver.MPSolver
import com.google.ortools.linearsolver.MPVariable
import optimizationExps.ortools.LinearProgramming1.runLinearProgrammingExample
import java.lang.Exception

/**
 * Linear programming example that shows how to use the API.
 */
object LinearProgramming1 {
    fun runLinearProgrammingExample(solverType: String, printModel: Boolean) {
        val solver: MPSolver = MPSolver.createSolver(solverType)
        val infinity = Double.POSITIVE_INFINITY
        // x1, x2 and x3 are continuous non-negative variables.
        val x1: MPVariable = solver.makeNumVar(0.0, infinity, "x1")
        val x2: MPVariable = solver.makeNumVar(0.0, infinity, "x2")
        val x3: MPVariable = solver.makeNumVar(0.0, infinity, "x3")

        // Maximize 10 * x1 + 6 * x2 + 4 * x3.
        val objective: MPObjective = solver.objective()
        objective.setCoefficient(x1, 10.0)
        objective.setCoefficient(x2, 6.0)
        objective.setCoefficient(x3, 4.0)
        objective.setMaximization()

        // x1 + x2 + x3 <= 100.
        val c0: MPConstraint = solver.makeConstraint(-infinity, 100.0)
        c0.setCoefficient(x1, 1.0)
        c0.setCoefficient(x2, 1.0)
        c0.setCoefficient(x3, 1.0)

        // 10 * x1 + 4 * x2 + 5 * x3 <= 600.
        val c1: MPConstraint = solver.makeConstraint(-infinity, 600.0)
        c1.setCoefficient(x1, 10.0)
        c1.setCoefficient(x2, 4.0)
        c1.setCoefficient(x3, 5.0)

        // 2 * x1 + 2 * x2 + 6 * x3 <= 300.
        val c2: MPConstraint = solver.makeConstraint(-infinity, 300.0)
        c2.setCoefficient(x1, 2.0)
        c2.setCoefficient(x2, 2.0)
        c2.setCoefficient(x3, 6.0)
        println("Number of variables = " + solver.numVariables())
        println("Number of constraints = " + solver.numConstraints())
        if (printModel) {
            val model: String = solver.exportModelAsLpFormat()
            println(model)
        }
        val resultStatus: MPSolver.ResultStatus = solver.solve()

        // Check that the problem has an optimal solution.
        if (resultStatus !== MPSolver.ResultStatus.OPTIMAL) {
            System.err.println("The problem does not have an optimal solution!")
            return
        }

        // Verify that the solution satisfies all constraints (when using solvers
        // others than GLOP_LINEAR_PROGRAMMING, this is highly recommended!).
        if (!solver.verifySolution( /*tolerance=*/1e-7,  /* log_errors= */true)) {
            System.err.println(
                "The solution returned by the solver violated the"
                        + " problem constraints by at least 1e-7"
            )
            return
        }
        println("Problem solved in " + solver.wallTime().toString() + " milliseconds")

        // The objective value of the solution.
        println("Optimal objective value = " + solver.objective().value())

        // The value of each variable in the solution.
        println("x1 = " + x1.solutionValue())
        println("x2 = " + x2.solutionValue())
        println("x3 = " + x3.solutionValue())
        val activities: DoubleArray = solver.computeConstraintActivities()
        println("Advanced usage:")
        println("Problem solved in " + solver.iterations().toString() + " iterations")
        println("x1: reduced cost = " + x1.reducedCost())
        println("x2: reduced cost = " + x2.reducedCost())
        println("x3: reduced cost = " + x3.reducedCost())
        println("c0: dual value = " + c0.dualValue())
        println("    activity = " + activities[c0.index()])
        println("c1: dual value = " + c1.dualValue())
        println("    activity = " + activities[c1.index()])
        println("c2: dual value = " + c2.dualValue())
        println("    activity = " + activities[c2.index()])
    }

//    @Throws(Exception::class)
//    @JvmStatic
//    fun main(args: Array<String>) {
//        Loader.loadNativeLibraries()
//        println("---- Linear programming example with GLOP (recommended) ----")
//        runLinearProgrammingExample("GLOP", true)
//        println("---- Linear programming example with CLP ----")
//        runLinearProgrammingExample("CLP", false)
//    }
}

fun main(args: Array<String>) {
    repeat(2) { count ->
        Loader.loadNativeLibraries()
        println("---- Linear programming example with GLOP (recommended) ----")
        runLinearProgrammingExample("GLOP", true)
        println("---- Linear programming example with CLP ----")
        runLinearProgrammingExample("CLP", true)
    }
}
