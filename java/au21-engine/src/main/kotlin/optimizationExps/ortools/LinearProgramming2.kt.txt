// Copyright 2010-2018 Google LLC
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
package optimizationExps.ortools

import com.google.ortools.Loader
import com.google.ortools.linearsolver.MPConstraint
import com.google.ortools.linearsolver.MPObjective
import com.google.ortools.linearsolver.MPSolver
import com.google.ortools.linearsolver.MPVariable
import optimizationExps.ortools.LinearProgramming2.runLinearProgrammingExample

// https://developers.google.com/optimization/introduction/java

/**
 * Linear programming example that shows how to use the API.
 */
object LinearProgramming2 {

    //  val infinity = Double.POSITIVE_INFINITY

    fun runLinearProgrammingExample(solverType: String, printModel: Boolean) {

        // 1) Declare the solver:

        val solver: MPSolver = MPSolver.createSolver(solverType)

        // 2) Create the variables:

        val x: MPVariable = solver.makeNumVar(0.0, 1.0, "x");
        val y: MPVariable = solver.makeNumVar(0.0, 2.0, "y")

        // 3) Define the constraints:

        // Create a linear constraint, 0 <= x + y <= 2.
        val ct: MPConstraint = solver.makeConstraint(0.0, 2.0, "ct").apply {
            setCoefficient(x, 1.0);
            setCoefficient(y, 1.0);
        }

        // 4) Define the objective function:

        // Create the objective function, 3 * x + y.
        val objective: MPObjective = solver.objective().apply {
            setCoefficient(x, 3.0)
            setCoefficient(y, 1.0)
            setMaximization()
        }

        println("Number of variables = " + solver.numVariables())
        println("Number of constraints = " + solver.numConstraints())

        // 5) Invoke the solver and display the results:

        solver.solve();
        println("Solution:");
        println("Objective value = " + objective.value());
        println("x = " + x.solutionValue());
        println("y = " + y.solutionValue());

        // -------------------------------------------------
        // MISC FROM PRIOR EXAMPLE:

        if (printModel) {
            val model: String = solver.exportModelAsLpFormat()
            println(model)
        }
        val resultStatus: MPSolver.ResultStatus = solver.solve()

        // Check that the problem has an optimal solution.
        if (resultStatus !== MPSolver.ResultStatus.OPTIMAL) {
            System.err.println("The problem does not have an optimal solution!")
            return
        }

        // Verify that the solution satisfies all constraints (when using solvers
        // others than GLOP_LINEAR_PROGRAMMING, this is highly recommended!).
        if (!solver.verifySolution( /*tolerance=*/1e-7,  /* log_errors= */true)) {
            System.err.println(
                "The solution returned by the solver violated the"
                        + " problem constraints by at least 1e-7"
            )
            return
        }
        println("Problem solved in " + solver.wallTime().toString() + " milliseconds")


    }

//    @Throws(Exception::class)
//    @JvmStatic
//    fun main(args: Array<String>) {
//        Loader.loadNativeLibraries()
//        println("---- Linear programming example with GLOP (recommended) ----")
//        runLinearProgrammingExample("GLOP", true)
//        println("---- Linear programming example with CLP ----")
//        runLinearProgrammingExample("CLP", false)
//    }
}

fun main(args: Array<String>) {
    repeat(2) { count ->
        Loader.loadNativeLibraries()
        println("---- Linear programming example with GLOP (recommended) ----")
        runLinearProgrammingExample("GLOP", true)
        println("---- Linear programming example with CLP ----")
        runLinearProgrammingExample("CLP", true)
    }
}
