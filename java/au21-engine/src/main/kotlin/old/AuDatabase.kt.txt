package database


// import com.jakewharton.rxrelay2.BehaviorRelay
import com.au21.infrastructure.db.AuEntity
import java.util.*
import javax.persistence.*

/*
* PERSISTENCE FRAMEWORK:
* 1) AuEntity:              all persintent objects inherit from AuEntity
* 2) EntityChangeListener:  listens for Persist, Update, and Remove of AuEntities
* 3) Database:              executed blocks of code as a transaction, then publishes the changes
*
*/


class AuDatabase(
    // odb_path: String =  "commands-01.tmp;drop",
    val odb_path: String,
    val is_resetable: Boolean
) {


    //  private val monitors: AuPrometheusMonitors by inject()

//    companion object {
//        // DB Filenames (now set in config file)?
//        val test_drop = "commands-07.tmp;drop"
//        val test_non_drop = "commands-08.odb"
//        val test_mem = "test.mem"
//    }

    init {
        //Logger.info("${this::class.java.simpleName} using connection path: ${this.odb_path}")
        println("${this::class.java.simpleName} using connection path: ${this.odb_path}")
    }

    /*
        private val connection_string: String =
            if (is_server)
                "objectdb://localhost/$odb_path;user=admin;password=admin" // 007007"
            else
                "objectdb:db/$odb_path"
     */

    private val emf: EntityManagerFactory =
        Persistence.createEntityManagerFactory(this.odb_path) //.apply { Logger.info(connection_url) }

    val em: EntityManager =
        emf.createEntityManager()?.also {
            it.flushMode = FlushModeType.AUTO
        } ?: throw IllegalStateException("Entity Manager is null!")

    @Synchronized
    fun <T> transact(logging: Boolean = true, block: () -> T): T {

        val tx: EntityTransaction = em.transaction ?: throw IllegalStateException("UNABLE TO CREATE TRANSACTION")

        fun pad(msg: String): String = "\n\n*********** <${msg}> ***********\n"

        val start = Date().time
        // val timer = monitors.database_transaction.startTimer()

        try {

            if (logging) {
                //Logger.info(pad("TRANSACTION STARTED"))
                println(pad("TRANSACTION STARTED"))
            }
            tx.begin()
            return block().apply {
                tx.commit()
                if (logging) {
                    //Logger.info(pad("TRANSACTION COMMITED"))
                    println(pad("TRANSACTION COMMITED"))
                }
            }
        } catch (e: Throwable) {
            //Logger.error(pad("TRANSACTION FAILED: ${Date().time - start} msec"), "message: ${e.message}")
            //println(pad("TRANSACTION FAILED: ${Date().time - start} msec"), "message: ${e.message}")
            println(pad("TRANSACTION FAILED: ${Date().time - start} msec, message: ${e.message}"))
            throw e
        } finally {
            if (tx.isActive) {
                //Logger.error(pad("TRANSACTION ROLLING BACK"))
                println(pad("TRANSACTION ROLLING BACK"))
                tx.rollback()
                //Logger.error(pad("TRANSACTION ROLLED BACK"))
                println(pad("TRANSACTION ROLLED BACK"))
            }
            if (logging) {
                //Logger.info(pad("TRANSACTION ENDED: ${Date().time - start} msec"))
                println(pad("TRANSACTION ENDED: ${Date().time - start} msec"))
            }

            //   monitors.database_transaction_duration.set(timer.observeDuration())
        }

    }

    fun <T : AuEntity> save(t: T): T {
        // if (em.transaction.isActive)
        //     throw Exception("db.save: transaction not active, cannot save $t")
        // if (t != null) {
        em.persist(t)
        // }
        return t
    }

    fun <T : AuEntity> delete(t: T) =
        em.remove(t)

    fun <T : AuEntity> refresh(entity: T) =
        em.refresh(entity)

    fun close() {
        //Logger.info("closing connection")
        println("closing connection")
        if (em.isOpen) {
            em.close()
        }
        if (emf.isOpen) {
            emf.close()
        }
        //Logger.info(Thread.currentThread().name)
        println(Thread.currentThread().name)
    }
}


//    // old way:
//    fun <T : AuEntity> find_all(c: Class<T>, include_deleted: Boolean = false): List<T> =
//            try {
//                val cq = em.criteriaBuilder.createQuery(c)
//                cq.select(cq.from(c))
//                em.createQuery(cq)
//                        .resultList
//                        .asSequence()
//                        .filterNotNull()
//                        .filter { include_deleted || !it.deleted }
//                        .toList()
//
//            } catch (e: Exception) {
//                //e.printStackTrace()
//                listOf()
//            }

//    fun commit_and_begin() {
//        em.transaction.commit()
//        em.transaction.begin()
//    }
//
//    fun rollback() {
//        em.transaction.rollback()
//    }

//    fun delete_all_entities() =
//            try {
//                // NOTE: deliberately so that you CAN'T do this from inside a transaction,
//                // ie: if you have to do this, it's outside of the regular transaction system.
//                db.transact {
//                    Logger.info("deleting all AuEntities")
//                    val cq = em.criteriaBuilder.createQuery(AuEntity::class.java)
//                    cq.select(cq.from(AuEntity::class.java))
//                    em.createQuery(cq).resultList.forEach {
//                        em.remove(it)
//                    }
//                }
//            } catch (e: Exception) {
//                e.printStackTrace()
//            }n

//    fun <T> transact(block: () -> T): T {
//        Logger.info("\n--- TRANSACTION START ---------------\n")
//        var tx: EntityTransaction? = null
//        var exception: Throwable? = null
//
//        val result: T? = try {
//            EntityChangeListener.onStartTransaction()
//            tx = em.transaction ?: throw IllegalStateException("UNABLE TO CREATE TRANSACTION")
//            tx.begin()
//            val result: T = block()
//            Logger.info("\n------  TRANSACTION COMMITTING ------\n")
//            tx.commit()
//            EntityChangeListener.onEndTransaction()
//            return result
//        } catch (e: Throwable) {
//            Logger.info("\n----------------- TRANSACTION FAILED ---\n")
//            Logger.info("\nAuDatabase.transact(): ERROR: ${e.message}")
//            exception = e
//            null
//        } finally {
//            if (tx?.isActive == true) {
//                Logger.info("AuDatabase.transact(): TRANSACTION ROLLING BACK\n")
//                tx.rollback()
//                Logger.info("\n-------------------- TRANSACTION ROLLED BACK\n")
//            }
//            Logger.info("\n----------------- TRANSACTION END ---\n")
//        }
//        if (exception != null)
//            throw(exception)
//        else if (result == null)
//            throw Exception("result is null")
//        else
//            return result
//    }

