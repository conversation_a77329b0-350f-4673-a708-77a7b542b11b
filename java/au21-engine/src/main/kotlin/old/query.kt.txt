package au2019.engine.api.database

import com.au21.exp.AuDatabase

fun AuDatabase.delete_all_entities(vararg except: AuEntity) =
        if (is_resetable) // which is fixed at module creation, but could be per-call
            find_all<AuEntity>(include_deleted = true).forEach {
                if(!except.contains(it)) {
                    delete(it)
                } else {
                    println("not deleting: ${it::class.simpleName} with id: ${it.id}")
                }
            }
        else
            throw Exception("database is not resetable")


inline fun <reified T : AuEntity> AuDatabase.find_all(include_deleted: Boolean = false): List<T> =
        try {
            val query: String = "SELECT t FROM ${T::class.java.name} t" + (
                    if (!include_deleted) " WHERE t.deleted=false"
                    else "")
            em.createQuery(query, T::class.java).resultList
        } catch (e: Exception) {
           println(e.message)
            // Logger.error(e.message)
            // e.printStackTrace()
            listOf()
        }

// https://www.objectdb.com/java/jpa/query/jpql/comparison#IS_NOT_NULL
inline fun <reified T : AuEntity> AuDatabase.query(query: String): List<T> =
        try {
            em.createQuery(query, T::class.java).resultList
        } catch (e: Exception) {
            println(e.message)
            //Logger.error(e.message)
            //e.printStackTrace()
            listOf()
        }


// TODO: note: this predicate is working on fetched objects, would be better as an object index or property !
inline fun <reified T : AuEntity> AuDatabase.filter(include_deleted: Boolean = false, pred: (T) -> Boolean): List<T> =
        find_all<T>(include_deleted).filter { pred(it) }

inline fun <reified T : AuEntity> AuDatabase.find_first(include_deleted: Boolean = false, pred: (T) -> Boolean): T? =
        filter(include_deleted, pred).firstOrNull()


inline fun <reified T : AuEntity> AuDatabase.by_id(oid: String?, include_deleted: Boolean = false): T? =
        by_id(oid?.toLongOrNull(), include_deleted)

inline fun <reified T : AuEntity> AuDatabase.by_id(oid: Long?, include_deleted: Boolean = false): T? =
        oid?.let { em.find(T::class.java, oid) }?.takeIf { include_deleted || !it.deleted }
