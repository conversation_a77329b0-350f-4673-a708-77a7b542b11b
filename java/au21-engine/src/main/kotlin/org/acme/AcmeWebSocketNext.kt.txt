package org.acme

import io.quarkus.websockets.next.*
import jakarta.inject.Inject


/*
this is the old AuctionSocket
- which was implemented with websocket-next
- this seems much more concise than the websocket (non-next) library
 */

@WebSocket(path = "/auction-socket/{clientId}")
class AcmeWebsocketNext {

    enum class Type { JOIN, LEAVE, MESSAGE }
    data class Event(val type: Type, val from: String, val message: String? = null)

    @Inject
    lateinit var connection: WebSocketConnection

    @OnOpen(broadcast = true)
    fun onOpen(): Event =
        Event(Type.JOIN, connection.pathParam("clientId"))

    @OnTextMessage(broadcast = true)
    fun onMessage(event: Event): Event = event

    @OnClose
    fun onClose() {
        val leave = Event(Type.LEAVE, connection.pathParam("clientId"))
        connection.broadcast().sendTextAndAwait(leave)
    }

    @OnError
    fun onError(t: Throwable) {
        connection.sendTextAndAwait(Event(Type.MESSAGE, "server", "ERROR: ${t.message}"))
    }
}
