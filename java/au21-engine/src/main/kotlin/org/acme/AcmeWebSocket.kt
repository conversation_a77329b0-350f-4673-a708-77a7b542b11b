package org.acme

import com.fasterxml.jackson.databind.ObjectMapper // Import Jackson ObjectMapper
import io.quarkus.logging.Log
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.websocket.*
import jakarta.websocket.server.PathParam
import jakarta.websocket.server.ServerEndpoint
import java.util.concurrent.ConcurrentHashMap
@ServerEndpoint("/acme-socket/{clientId}") // Standard annotation
@ApplicationScoped // Make it a CDI bean
class AcmeWebSocket {

    // Keep the data classes
    enum class Type { JOIN, LEAVE, MESSAGE }
    data class Event(val type: Type, val from: String, val message: String? = null)

    // Inject ObjectMapper for JSON handling
    @Inject
    lateinit var objectMapper: ObjectMapper

    // Manually manage sessions for broadcasting
    // Use ConcurrentHashMap for thread safety
    private val sessions = ConcurrentHashMap<String, Session>() // Map session ID to Session object
    private val clientIds = ConcurrentHashMap<String, String>() // Map session ID to clientId

    @OnOpen
    fun onOpen(session: Session, @PathParam("clientId") clientId: String) {
        sessions[session.id] = session
        clientIds[session.id] = clientId
        Log.info("WebSocket opened for client: $clientId (Session ID: ${session.id})")
        val event = Event(Type.JOIN, clientId)
        broadcast(event) // Broadcast the JOIN event
    }

    @OnMessage
    fun onMessage(session: Session, message: String) {
        val clientId = clientIds[session.id] ?: "unknown" // Get client ID for this session
        try {
            // The original code expected the client to send a valid Event JSON
            // Deserialize the incoming message string into an Event object
            val incomingEvent = objectMapper.readValue(message, Event::class.java)

            // The original logic broadcasted the received event directly
            // Ensure the 'from' field matches the sender, or handle as needed
            // For simplicity, we'll trust the client or overwrite 'from'
            val eventToBroadcast = incomingEvent.copy(from = clientId) // Optionally enforce 'from'

            Log.info("Received message from $clientId: $eventToBroadcast")
            broadcast(eventToBroadcast) // Broadcast the received/processed event

        } catch (e: Exception) {
            Log.error("Failed to parse message from $clientId: $message", e)
            // Optionally send an error back to the sender
            val errorEvent = Event(Type.MESSAGE, "server", "ERROR: Could not process message - ${e.message}")
            sendToSession(session, errorEvent)
        }
    }

    @OnClose
    fun onClose(session: Session, closeReason: CloseReason) {
        val clientId = clientIds.remove(session.id) // Remove and get clientId
        sessions.remove(session.id) // Remove session
        if (clientId != null) {
            Log.info("WebSocket closed for client: $clientId (Reason: ${closeReason.reasonPhrase})")
            val event = Event(Type.LEAVE, clientId)
            broadcast(event) // Broadcast the LEAVE event
        } else {
            Log.warn("WebSocket closed for unknown client (Session ID: ${session.id})")
        }
    }

    @OnError
    fun onError(session: Session, throwable: Throwable) {
        val clientId = clientIds[session.id] ?: "unknown"
        Log.error("WebSocket error for client $clientId (Session ID: ${session.id})", throwable)

        // Send error message back to the specific client
        val errorEvent = Event(Type.MESSAGE, "server", "ERROR: ${throwable.message}")
        sendToSession(session, errorEvent)

        // Optionally close the session and clean up if the error is fatal
        // It might trigger onClose automatically depending on the error
        clientIds.remove(session.id)
        sessions.remove(session.id)
        // Consider broadcasting a LEAVE message if appropriate for the error type
    }

    // Helper method for broadcasting
    private fun broadcast(event: Event) {
        val message = objectMapper.writeValueAsString(event)
        sessions.values.forEach { session ->
            session.asyncRemote.sendText(message) { result ->
                if (!result.isOK) {
                    Log.error("Failed to send message to session ${session.id}", result.exception)
                    // Handle failed send, maybe remove session?
                }
            }
        }
    }

    // Helper method to send to a specific session
    private fun sendToSession(session: Session, event: Event) {
        try {
            val message = objectMapper.writeValueAsString(event)
            session.asyncRemote.sendText(message) { result ->
                if (!result.isOK) {
                    Log.error( "Failed to send error message to session ${session.id}", result.exception)
                }
            }
        } catch (e: Exception) {
             Log.error("Failed to serialize or send error message to session ${session.id}", e)
        }
    }
}
