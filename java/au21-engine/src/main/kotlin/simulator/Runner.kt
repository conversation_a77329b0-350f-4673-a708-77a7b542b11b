package simulator

import au21.engine.domain.common.model.OrderType
import au21.engine.domain.common.model.PriceDirection
import au21.engine.domain.de.model.DeBidConstraints
import au21.engine.domain.de.services.constraints.calculate_subsequent_round_constraints
import au21.engine.domain.de.validations.validate_de_order_against_constraints_and_throw_if_fails
import com.opencsv.CSVParser
import com.opencsv.CSVParserBuilder
import com.opencsv.CSVReader
import com.opencsv.CSVReaderBuilder
import gg.jte.ContentType
import gg.jte.TemplateEngine
import gg.jte.output.StringOutput
import gg.jte.resolve.DirectoryCodeResolver
import java.io.File
import java.nio.charset.Charset
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.StandardOpenOption

typealias Buyer = String
typealias Sellers = Map<String, String>

const val MAX_BUY_QUANTITY = 50
const val MAX_SELL_QUANTITY = 50

fun main() {
    val path = Path.of("src", "main", "jte")
    val codeResolver = DirectoryCodeResolver(path)
    val templateEngine = TemplateEngine.create(codeResolver, ContentType.Html)

    val (buyerSellerMap, sellerBuyerMap) = readCreditsCsv()
    val credits = Credits(buyerSellerMap, sellerBuyerMap)
    val edges = edgesFromCounterPartyLimits(sellerBuyerMap)

    val scenarios = readOrdersCsv()
    val roundInfoMap = scenarios.map {
        it.key to it.value.first
    }.toMap()

    val constraintsMap = scenarios.map {
        it.key to it.value.second
    }.toMap()

    val scenarioToRoundAttributes = scenarioRoundAttributes(roundInfoMap, edges)
    val templateParams = TemplateParams(
        credits,
        scenarioToRoundAttributes,
        constraintsMap
    )
    outputToFile(templateEngine, templateParams)
}

private fun scenarioRoundAttributes(
    scenarioToRoundInfo: Map<String, List<RoundInfo>>,
    edges: MutableList<Edge>
): Map<String, List<RoundAttributes>> {
    return scenarioToRoundInfo.map { m ->
        m.key to m.value.map { r ->
            val bids = r.bids
            val orderEdges = bids.flatMap {
                val edgeList = mutableListOf<Edge>()
                when (it.order) {
                    "BUY" -> {
                        edgeList.add(Edge(Edge.SELL_SOURCE, it.company, 0))
                        edgeList.add(Edge(it.company, Edge.BUY_SINK, it.quantity.toInt()))
                    }
                    "SELL" -> {
                        edgeList.add(Edge(Edge.SELL_SOURCE, it.company, it.quantity.toInt()))
                        edgeList.add(Edge(it.company, Edge.BUY_SINK, 0))
                    }
                    else -> throw RuntimeException("Invalid Order Type")
                }
                edgeList
            }.toList()
            val flow = calculateMaxFlow(edges + orderEdges)
//            println("Flow : ${flow.first}")
//            println(format_table(edges, listOf("from","to","capacity"), true))
//            println(format_table(orderEdges, listOf("from","to","capacity"), true))
//            println(format_table(flow.second, listOf("from_seller","to_buyer","flow"), true))
            val totalDemand = bids.calculateTotalDemand()
            val totalSupply = bids.calculateTotalSupply()
            val absoluteDemandSupply = totalDemand.minus(totalSupply)

            val bidsMap = linkedMapOf<String, OrderInfo>()
            bids.forEach { bidsMap[it.company] = OrderInfo(it.quantity, it.order) }

            RoundAttributes(
                r.roundNumber, bidsMap, flow.first.toString(), totalDemand.toString(),
                totalSupply.toString(),
                absoluteDemandSupply.toString(),
                r.priceDirection.toString()
            )
        }.toList()
    }.toMap()
}

private fun edgesFromCounterPartyLimits(sellerBuyerMap: MutableMap<String, MutableMap<String, String>>): MutableList<Edge> {
    val edges = sellerBuyerMap.flatMap { company ->
        company.value.map {
            Edge(company.key, it.key, it.value.toInt())
        }.toMutableList()
    }.toMutableList()
    return edges
}

private fun outputToFile(templateEngine: TemplateEngine, templateParams: TemplateParams) {
    val output = StringOutput()
    templateEngine.render("simulator-template.jte", templateParams, output)
    val newBufferedWriter = Files.newBufferedWriter(
        Path.of("src", "main", "jte", "rendered.html"),
        Charset.defaultCharset(),
        StandardOpenOption.WRITE,
        StandardOpenOption.TRUNCATE_EXISTING,
        StandardOpenOption.CREATE
    )
    newBufferedWriter.write(output.toString())
    newBufferedWriter.close()
}

private fun readOrdersCsv(): Map<String, Pair<List<RoundInfo>, Map<String, List<DeBidConstraints>>>> {
    return File("src/main/jte/csv/scenarios/").walk().filter { it.isDirectory.not() }.sorted().map { file ->
        val reader =
            Files.newBufferedReader(Path.of(file.path))
        val parser: CSVParser = CSVParserBuilder().withSeparator(',').withIgnoreQuotations(true).build()
        val csvReader: CSVReader = CSVReaderBuilder(reader).withSkipLines(1).withCSVParser(parser).build()

        val roundOrders = csvReader.groupBy {
            it.first()
        }.map {
            Pair(it.key, it.value.map { arr -> Bid(arr[1], arr[2], arr[3]) }.toList())
        }.map {
            RoundInfo(
                it.first,
                it.second,
                calculatePriceDirection(it.second.calculateTotalDemand(), it.second.calculateTotalSupply())
            )
        }.toList()
        val scenarioName = file.name.removeSuffix(".csv")
        val validatedOrders = validateOrders(scenarioName, roundOrders)
        scenarioName to Pair(roundOrders,validatedOrders)
    }.toMap()
}

private fun calculatePriceDirection(totalDemand: Double, totalSupply: Double): PriceDirection =
    if (totalDemand > totalSupply) PriceDirection.UP else PriceDirection.DOWN

private fun validateOrders(scenarioName: String, roundOrders: List<RoundInfo>): Map<String, List<DeBidConstraints>> {
    val ordersPerCompany = ordersPerCompany(roundOrders)
    return ordersPerCompany.map { eachCompany ->
        // Default starting values, doesn't matter
        var previousOrderQuantity = 0
        var previousOrderConstraints = DeBidConstraints(MAX_BUY_QUANTITY, 0, 0, MAX_SELL_QUANTITY)
        var previousOrderType = OrderType.NONE

        val constraints = eachCompany.value.mapIndexed { index, orderDetails ->
            val logConstraints: DeBidConstraints
            if (index == 0) {
                // First round
                previousOrderQuantity = orderDetails.second
                previousOrderType = OrderType.valueOf(orderDetails.first)
                previousOrderConstraints = calculate_subsequent_round_constraints(
                    previousOrderConstraints,
                    previousOrderType,
                    previousOrderQuantity,
                    orderDetails.third
                )
                logConstraints = previousOrderConstraints
            } else {
                // Rest of the rounds
                val orderQuantity = orderDetails.second
                val orderType = OrderType.valueOf(orderDetails.first)

                validateOrderConstraints(
                    scenarioName,
                    previousOrderConstraints,
                    orderType,
                    orderQuantity,
                    index,
                    eachCompany
                )
                val bidConstraints = calculate_subsequent_round_constraints(
                    previousOrderConstraints,
                    previousOrderType,
                    previousOrderQuantity,
                    orderDetails.third
                )
                previousOrderConstraints = bidConstraints
                previousOrderType = orderType
                previousOrderQuantity = orderQuantity
                logConstraints = bidConstraints
            }
            logConstraints
        }.toList()
        eachCompany.key to constraints
    }.toMap()
}

private fun ordersPerCompany(roundOrders: List<RoundInfo>): MutableMap<String, MutableList<Triple<String, Int, PriceDirection>>> {
    val transformedMap = mutableMapOf<String, MutableList<Triple<String, Int, PriceDirection>>>()
    roundOrders.forEach {
        val bids = it.bids
        bids.forEach { bid ->
            if (transformedMap.containsKey(bid.company)) {
                transformedMap[bid.company] = transformedMap[bid.company]?.apply {
                    this.add(Triple(bid.order, bid.quantity.toInt(), it.priceDirection))
                } ?: throw RuntimeException("Unexpected error")
            } else {
                transformedMap[bid.company] = mutableListOf(Triple(bid.order, bid.quantity.toInt(), it.priceDirection))
            }
        }
    }
    return transformedMap
}

private fun validateOrderConstraints(
    scenarioName: String,
    bidConstraints: DeBidConstraints,
    orderType: OrderType,
    orderQuantity: Int,
    index: Int,
    eachCompany: Map.Entry<String, MutableList<Triple<String, Int, PriceDirection>>>,
) {
    try {
        validate_de_order_against_constraints_and_throw_if_fails(
            bidConstraints,
            orderType,
            orderQuantity,
            "MMLB"
        )

    } catch (ex: Error) {
        throw RuntimeException("For Scenario $scenarioName, In Round ${index + 1}, For company ${eachCompany.key} with order ${eachCompany.value} got validation error : ${ex.message}")
    }
}


private fun readCreditsCsv(): Pair<MutableMap<String, MutableMap<String, String>>, MutableMap<String, MutableMap<String, String>>> {
    val reader = Files.newBufferedReader(Path.of("src", "main", "jte", "csv", "credits.csv"))

    val parser: CSVParser = CSVParserBuilder().withSeparator(',').withIgnoreQuotations(true).build()

    val csvReader: CSVReader = CSVReaderBuilder(reader).withSkipLines(1).withCSVParser(parser).build()

    val buyerSellerMap = mutableMapOf<String, MutableMap<String, String>>()
    val sellerBuyerMap = mutableMapOf<String, MutableMap<String, String>>()
    csvReader.onEach { arr ->
        generateCreditsFromCsv(arr, buyerSellerMap, sellerBuyerMap)
    }
    return Pair(buyerSellerMap, sellerBuyerMap)
}

private fun generateCreditsFromCsv(
    arr: Array<out String>,
    buyerSellerMap: MutableMap<String, MutableMap<String, String>>,
    sellerBuyerMap: MutableMap<String, MutableMap<String, String>>,
) {
    if (arr.size != 3) throw RuntimeException("invalid csv")
    if (buyerSellerMap.containsKey(arr.first())) {
        buyerSellerMap[arr.first()] = buyerSellerMap[arr.first()]?.apply {
            this[arr[1]] = arr[2]
        } ?: throw RuntimeException("invalid csv")
    } else {
        buyerSellerMap[arr.first()] = mutableMapOf(arr[1] to arr[2])
    }
    if (sellerBuyerMap.containsKey(arr[1])) {
        sellerBuyerMap[arr[1]] = sellerBuyerMap[arr[1]]?.apply {
            this[arr.first()] = arr[2]
        } ?: throw RuntimeException("invalid csv")
    } else {
        sellerBuyerMap[arr[1]] = mutableMapOf(arr.first() to arr[2])
    }
}

fun List<Bid>.calculateTotalDemand() =
    this.filter { it.order == "BUY" }.map { it.quantity.toInt() }.fold(0.0) { t, y -> (t + y) }

fun List<Bid>.calculateTotalSupply() =
    this.filter { it.order == "SELL" }.map { it.quantity.toInt() }.fold(0.0) { t, y -> t + y }

data class Credits(val buyerSellerCredits: Map<Buyer, Sellers>, val sellerBuyerCredits: Map<Buyer, Sellers>)

data class RoundInfo(val roundNumber: String, val bids: List<Bid>, val priceDirection: PriceDirection)

data class Bid(val company: String, val order: String, val quantity: String)

data class TemplateParams(
    val credits: Credits,
    val scenarioToRoundAttributes: Map<String, List<RoundAttributes>>,
    val constraintsMap: Map<String, Map<String, List<DeBidConstraints>>>
)

data class RoundAttributes(
    val roundNumber: String,
    val bids: LinkedHashMap<String, OrderInfo>,
    val maxFlow: String,
    val demand: String,
    val supply: String,
    val absoluteSupplyDemand: String,
    val priceDirection: String
)

data class OrderInfo(
    val quantity: String,
    val orderType: String
)
