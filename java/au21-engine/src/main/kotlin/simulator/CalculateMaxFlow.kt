package simulator

import org.psjava.algo.graph.flownetwork.FordFulkersonAlgorithm
import org.psjava.algo.graph.flownetwork.MaximumFlowAlgorithmResult
import org.psjava.algo.graph.pathfinder.BFSPathFinder
import org.psjava.algo.graph.pathfinder.DFSPathFinder
import org.psjava.ds.graph.CapacityEdge
import org.psjava.ds.graph.MutableCapacityGraph
import org.psjava.ds.numbersystrem.IntegerNumberSystem

fun calculateMaxFlow(edges: List<Edge>, isDFS: Boolean = true): Pair<Int, List<FlowResult>> {
    val nodes: Set<String> = mutableSetOf<String>().apply {
        // adding these two in case of Zero edges! (set will remove them)
        add(Edge.SELL_SOURCE)
        add(Edge.BUY_SINK)
        edges.forEach { c: Edge ->
            add(c.from)
            add(c.to)
        }
    }.toSet()

    when {
        !nodes.contains(Edge.SELL_SOURCE) -> throw Error("SOURCE node not found.")
        !nodes.contains(Edge.BUY_SINK) -> throw Error("SINK node not found.")
    }

    val capacityGraph = MutableCapacityGraph.create<String, Int>().apply {
        nodes.forEach { n -> insertVertex(n) }
        edges.forEach { e -> addEdge(e.from, e.to, e.capacity) }
    }

    val result: MaximumFlowAlgorithmResult<Int, CapacityEdge<String, Int>> = FordFulkersonAlgorithm
        .getInstance(if (isDFS) DFSPathFinder.getInstance() else BFSPathFinder.getInstance())
        .calc(
            capacityGraph,
            Edge.SELL_SOURCE,
            Edge.BUY_SINK,
            IntegerNumberSystem.getInstance()
        )

    val maxFlow: Int = result.calcTotalFlow()
    val maxFlowFunction = result.calcFlowFunction()

    val flowResults: List<FlowResult> = nodes
        .filter { it != Edge.SELL_SOURCE && it != Edge.BUY_SINK }
        .flatMap { trader ->
            capacityGraph.getEdges(trader)
                .filter { it.to() != Edge.BUY_SINK }
                .map { e: CapacityEdge<String, Int> ->
                    FlowResult(
                        from_seller = e.from(),
                        to_buyer = e.to(),
                        flow = maxFlowFunction.get(e)
                    )
                }
        }

    return Pair(maxFlow,flowResults)
}

data class Edge(
    val from: String,
    val to: String,
    val capacity: Int
) {
    companion object {
        const val SELL_SOURCE = "SELL_SOURCE"
        const val BUY_SINK = "BUY_SINK"
    }
}

data class FlowResult(
    val from_seller: String,
    val to_buyer: String,
    val flow: Int
)