const fs = require('fs');
const path = require('path');

function processKotlinFilesRecursively(directory) {
    let markdownContent = '';

    function processDirectory(dir) {
        const items = fs.readdirSync(dir, { withFileTypes: true });

        for (const item of items) {
            const fullPath = path.join(dir, item.name);

            if (item.isDirectory()) {
                processDirectory(fullPath);
            } else if (item.isFile() && item.name.endsWith('.kt')) {
                const processedContent = processFile(fullPath);
                if (processedContent) {
                    const relativePath = path.relative(directory, fullPath);
                    markdownContent += `## ${relativePath}\n\n\`\`\`kotlin\n${processedContent}\n\`\`\`\n\n`;
                }
            }
        }
    }

    processDirectory(directory);

    if (markdownContent) {
        const finalDirName = path.basename(directory);
        const markdownPath = path.join(directory, `${finalDirName}.md`);
        fs.writeFileSync(markdownPath, markdownContent);
        console.log(`All processed Kotlin files have been written to ${markdownPath}`);
    } else {
        console.log('No Kotlin files were processed.');
    }
}

function processFile(filePath) {
    let content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const fileName = path.basename(filePath);

    console.log(`Processing file: ${filePath}`);

    let changes = [];

    // Check if the first line is correct, if not, add it
    if (!lines[0].startsWith(`// File: ${fileName}`)) {
        lines.unshift(`// File: ${fileName}`);
        changes.push("Added '// File: ...' as the first line");
    }

    // Remove any other lines starting with "// File"
    const originalLineCount = lines.length;
    const filteredLines = lines.filter((line, index) => {
        return index === 0 || !line.startsWith('// File');
    });
    if (filteredLines.length < originalLineCount) {
        changes.push(`Removed ${originalLineCount - filteredLines.length} additional 'File' line(s)`);
    }

    // Check for package declaration
    const hasPackage = filteredLines.some(line => line.trim().startsWith('package'));
    if (!hasPackage) {
        console.error(`Error: No package declaration found in ${filePath}`);
        return null;
    }

    // Log changes
    if (changes.length > 0) {
        console.log("Changes made:");
        changes.forEach(change => console.log(`- ${change}`));
    } else {
        console.log("No changes were necessary.");
    }

    // Join the lines back together
    const newContent = filteredLines.join('\n');

    // Write the processed content back to the file
    fs.writeFileSync(filePath, newContent);
    console.log(`Completed processing: ${filePath}\n`);

    return newContent;
}

// Check if a directory path is provided as a command-line argument
const directoryPath = process.argv[2];
if (!directoryPath) {
    console.error('Please provide a directory path as an argument.');
    process.exit(1);
}

// Process the Kotlin files recursively in the specified directory
console.log(`\nProcessing directory recursively: ${directoryPath}\n`);
processKotlinFilesRecursively(directoryPath);
