// File: ts-generator-jte.kt
package au21.engine.generators.typescript.exp


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.viewmodel.DateTimeValue
import au21.engine.framework.client.ClientCommand
import au21.engine.framework.client.LiveClientStore
import au21.engine.framework.client.StaleClientStore
import au21.engine.framework.client.StoreValue
import au21.engine.framework.commands.EngineCommand
import au21.engine.generators.get_sub_classes
import au21.engine.generators.resolve_directory
import au21.engine.generators.typescript.StoreClassHelper
import gg.jte.CodeResolver
import gg.jte.ContentType
import gg.jte.TemplateEngine
import gg.jte.output.StringOutput
import io.github.classgraph.ClassGraph
import io.github.classgraph.ClassInfo
import io.quarkus.logging.Log
import me.ntrrgc.tsGenerator.ClassTransformer
import me.ntrrgc.tsGenerator.TypeScriptGenerator
import me.ntrrgc.tsGenerator.onlyOnSubclassesOf
import net.pearx.kasechange.toSnakeCase
import java.io.File
import java.lang.reflect.Field
import java.lang.reflect.ParameterizedType
import kotlin.reflect.KClass
import kotlin.reflect.KProperty
import kotlin.reflect.KVisibility
import kotlin.reflect.full.isSubclassOf
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.javaField

/*
 * TODO: basically we need to create a model of the info
 *  required by the template
 *  eg: for enums it's a list of names
 */

private fun outputToFile(templateEngine: TemplateEngine, templateParams: TsGeneratorParams, to_file: Boolean = false) {
    val output = StringOutput()
    templateEngine.render("ts-generator.jte", templateParams, output)
    println(output)

    if (to_file) {
        File("generated", "generated-jte.ts").apply {
            if (exists()) {
                Log.info("deleting:$canonicalPath")
                delete()
            }
            Log.info("creating: $canonicalPath")
            createNewFile()
            writeText(output.toString())
        }

//        val newBufferedWriter = Files.newBufferedWriter(
//            Path.of("src", "generated", "ts-generator-jte.ts"),
//            Charset.defaultCharset(),
//            StandardOpenOption.WRITE,
//            StandardOpenOption.TRUNCATE_EXISTING,
//            StandardOpenOption.CREATE
//        )
//        newBufferedWriter.write(output.toString())
//        newBufferedWriter.close()
    }
}


class TsGeneratorParams(
    val version: String
) {
    val pkg = "au21.engine"

    /* ENUMS */

    class EnumFields(val name: String, val fields: List<String>) {
        companion object {
            fun create(): List<EnumFields> =
                ClassGraph()
                    .verbose()
                    .enableAllInfo()
                    .acceptPackages(TsGeneratorJte.pkg)
                    .scan()
                    .allEnums.map { info: ClassInfo ->
                        EnumFields(
                            name = info.simpleName,
                            fields = info.fieldInfo.filter { it.name != "\$VALUES" }.map { it.name }
                        )
                    }
        }
    }

    val enums: List<EnumFields> = EnumFields.create()


    val request_classes: List<Class<EngineCommand>> = get_sub_classes()

    // remove empty constructor:
    val commands: List<String> = request_classes.map {
        val name = it.simpleName
        when (it.kotlin.memberProperties.size) {
            0 -> "\nexport const ${name.toSnakeCase()} = () => \n\tcreate_command('${name}', '${it.canonicalName}', {})\n"
            else -> "\nexport const ${name.toSnakeCase()} = (req: $name) => \n\tcreate_command('${name}', '${it.canonicalName}', req)\n"
        }
    }

    val request_ts_generator =
        TypeScriptGenerator(rootClasses = request_classes.map { Class.forName(it.name).kotlin }
            .filter { it.isSubclassOf(EngineCommand::class) }, classTransformers = listOf(
            object : ClassTransformer {
                override fun transformPropertyList(
                    properties: List<KProperty<*>>, klass: KClass<*>
                ): List<KProperty<*>> = properties.filter { property ->
                    // NB: MUST MAKE SURE THAT THE BLACKLISTED PROPERTIES ARE ACTUALLY PRIVATE!
                    !listOf(
                        KVisibility.PRIVATE, KVisibility.PROTECTED
                    ).contains(property.visibility) && !listOf(
                        AuSession::class,
                        DateTimeValue::class
                    ).contains(
                        klass
                    ) && !listOf("session").contains(property.name)
                    // starting_time to avoid duplicate DateTimeValue interface
                }
            }.onlyOnSubclassesOf(EngineCommand::class)
        )
        )

    val request_classes_str: String = request_ts_generator
        //.sortedBy { it.simpleName })
        .individualDefinitions.map {
            Log.info(it)
            it
        }.filterNot { it.startsWith("type") }.filterNot { it.startsWith("interface Companion") }
        .filterNot { it.contains("Handler") }
        //  .filterNot { it.contains("DateTimeValue") }
        // .filterNot { it.contains("StoreValue") }
        .sorted()
        .joinToString(separator = "\n\nexport ")
       // .let { "export $it" }

    val client_command_classes: String = "export " + TypeScriptGenerator(
        rootClasses = get_sub_classes<List<Class<ClientCommand>>>().map { Class.forName(it.name).kotlin },
        ignoreSuperclasses = setOf(Cloneable::class),
        classTransformers = listOf(object : ClassTransformer {
            override fun transformPropertyList(
                properties: List<KProperty<*>>, klass: KClass<*>
            ): List<KProperty<*>> = properties.filter { property ->
                // NB: MUST MAKE SURE THAT THE BLACKLISTED PROPERTIES ARE ACTUALLY PRIVATE!
                property.visibility != KVisibility.PRIVATE
            }
        })
    )
        //.sortedBy { it.simpleName })
        .individualDefinitions.filter {
            //println(it)
            !it.startsWith("interface LiveClientStore")
        }.sorted().filterNot { it.startsWith("type") }.joinToString(separator = "\n\nexport ")


    //////////////////////////////////////////////////////////////////////////////

    var count = 0

    fun heading(heading: String): String {
        count++
        return """
        /***************************************************************************
         *
         *     $count) $heading
         *
         ***************************************************************************/
        """.trimIndent()
    }

}


/**
 * NB, NB, NB:
 * if you want to add a viewmodel Value or Element,
 * then you MUST add it to the LiveStore
 * ie: Values and Elements aren't generated (even if implementing StoreValue etc),
 * unless they are in the livestore, eg, on DeAuctionStatus
 */


fun main() {

//    val timestamp = LocalDate.now().toString(
//        DateTimeFormat.forPattern("yyyy-MM-dd")
//    )
//
//    val local_dir = File("generated")

    // This is the directory where your .jte files are located.
    val codeResolver: CodeResolver = resolve_directory(TsGeneratorParams::class)
    val templateEngine: TemplateEngine = TemplateEngine
        .create(codeResolver, ContentType.Plain)
        .apply { setTrimControlStructures(true) }
    val params = TsGeneratorParams("0.0.0")

    outputToFile(templateEngine, params, true)



    if (true) return

//    listOf(
//        File("generated", "generated2-v2.ts"),
//        //   File("generated", "generator-$timestamp.ts"),
//        //  File("../au21-frontend/libs/client-connector/src/_generated", "generated2.ts")
//    ).forEach {
//        TsGenerator.generate(it)
//        TsGenerator.validate(it)
//    }

    // getCurrentGitBranch().forEach { Log.info(it) }

}

/**
 * NOTE: THERE IS AN ALTERNATIVE LIBRARY:
 * - https://github.com/vojtechhabarta/typescript-generator/wiki/Type-Mapping
 */

object TsGeneratorJte {

    // TODO: probably need a way to test if this has actually changed,
    //       - and if not, don't create it
    //       - because currently that would require the client to fail for no reason

    val pkg = "au21.engine"

    fun generate(f: File) {

        f.apply {


//            appendText(
//                "export " + request_definitions.joinToString(separator = "\n\nexport ")
//            )

            /************************************************
             *  4) View Model: Values and Elements
             */

            //     heading("CLIENT STORE COMMANDS (aka Results)")

            /**
             *   LIVE CLIENT STORE
             */

            StoreClassHelper.write(f, LiveClientStore::class, "AuStore")

            /**
             *   STALE CLIENT STORE
             */

            StoreClassHelper.write(f, StaleClientStore::class)


        }

        // NB: create the version file:
        // TsVersion.setVersion(TsVersion(version = version))
        Log.info(f.readText())
    }

    fun validate(f: File) {
        var failed = false
        fun duplicates(search: String, err_msg: String) =
            f.readLines().filter { it.startsWith(search) }.groupingBy { it }.eachCount().filter { it.value > 1 }
                .takeIf { it.isNotEmpty() }?.let { duplicates ->
                    Log.error("ERROR: duplicate $err_msg found: $duplicates")
                    failed = true
                }
        duplicates("export interface", "interfaces")
        duplicates("export class", "classes")
        if (failed) {
            throw Error("Duplicates detected")
        }
    }
}


class StoreClassHelperJte(
    val prop: String, val type: String, val default: String?
) {
    companion object {

        fun write(f: File, t: KClass<out Any>, extends: String? = null) {

            f.appendText("\nexport class ${t.simpleName}")
            extends?.let { f.appendText(" extends $extends ") }
            f.appendText("{\n")

            to_store_helpers(t).apply {
                forEach { h: StoreClassHelper ->
                    f.appendText("\t${h.prop}: ${h.type}")
                    h.default?.let {
                        f.appendText(" = $it")
                    }
                    f.appendText("\n")
                }
                f.appendText("}\n")
            }

        }

        fun to_store_helpers(k: KClass<out Any>): List<StoreClassHelper> =

            mutableListOf<StoreClassHelper>().apply {

                k.memberProperties.forEach { p ->
                    val ff: Field = p.javaField!!
                    val type: Class<*> = ff.type
                    val is_nullable = p.returnType.isMarkedNullable

                    type.genericInterfaces.forEach {
                        // (1) Find the Store Values
                        if (it.typeName == StoreValue::class.java.name) {
                            add(
                                StoreClassHelper(
                                    prop = p.name, type = type.simpleName + when {
                                        is_nullable -> " | null"
                                        else -> ""
                                    }, default = when {
                                        is_nullable -> "null"
                                        else -> null
                                    }
                                )
                            )
                        }
                    }
                    // (2) Find the Store Elements:
                    if (type.toString() == List::class.java.toString()) {
                        (ff.genericType as ParameterizedType).actualTypeArguments.forEach {
                            it.typeName.let { elem ->
                                println(elem)
                                val elem_name = elem.substring(elem.lastIndexOf(".") + 1, elem.length)
                                add(
                                    StoreClassHelper(
                                        prop = p.name, type = "$elem_name[]", default = "[]"
                                    )
                                )
                            }
                        }
                    }
                }
            }.toList().also {
                println(it)
            }
    }
}

