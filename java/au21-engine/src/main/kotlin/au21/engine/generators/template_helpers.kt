// File: template_helpers.kt
package au21.engine.generators

import gg.jte.resolve.DirectoryCodeResolver
import java.io.BufferedReader
import java.io.IOException
import java.io.InputStreamReader
import java.nio.file.Path
import kotlin.reflect.KClass

/*
 * Given a class, find the directory from the root of the project to that class:
 * - assumes it's under "main/kotlin"
 */
fun resolve_directory(clazz: KClass<*>, is_test: Boolean = false) =
    DirectoryCodeResolver(
        Path.of(
            "src",
            *listOf(
                listOf(
                    if (is_test) "test" else "main",
                    "kotlin"
                ),
                clazz
                    .toString()
                    .removePrefix("class ")
                    .trim()
                    .split(".")
                    .dropLast(1)
            ).flatten().toTypedArray()
        )
    ) // This is the directory where your .jte files are located.



@Throws(IOException::class, InterruptedException::class)
fun getCurrentGitCommit(): List<String> =
    Runtime.getRuntime()
        .exec("git log -1")
        .let {
            it.waitFor()
            BufferedReader(InputStreamReader(it.inputStream))
                .readLines()
        }

@Throws(IOException::class, InterruptedException::class)
fun getCurrentGitBranch(): String =
    Runtime.getRuntime()
        .exec("git rev-parse --abbrev-ref HEAD")
        .let {
            it.waitFor()
            BufferedReader(InputStreamReader(it.inputStream))
                .readLine()
        }
