@import au21.engine.generators.mermaid.MermaidHelper

@param MermaidHelper page

<head>
    @if(page.getDescription() != null)
        <meta name="description" content="${page.getDescription()}">
    @endif
    <title>${page.getTitle()}</title>
</head>
<body>
<h1>${page.getTitle()}</h1>
<p>Welcome to my example page!</p>
</body>

```mermaid
classDiagram
    class Animal
        Vehicle <|-- Car
@for(var e : page.getEntity_classes())
    class ${e.getSimpleName()}
    @for(var p : e)
        ${p.getName()}
    @endfor
@endfor
```
