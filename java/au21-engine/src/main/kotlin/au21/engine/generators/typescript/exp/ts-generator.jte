@import au21.engine.generators.typescript.exp.TsGeneratorParams
@import simulator.TemplateParams

@param TsGeneratorParams p


/* eslint-disable */
/*
TIMESTAMP: ${p.getTimestamp()}

CURRENT BRANCH: ${p.getBranch()}

PREVIOUS COMMIT:
@for(var c : p.getCurrent_commit())
    ${c}
@endfor
*/

${p.heading("VERSION")}
export const generated_version = "${p.getVersion()}"

${p.heading("ENUMS")}

@for(var e : p.getEnums())
    export enum ${e.getName()} {
    @for(var f : e.getFields())
        ${"\t"+f} = '${f}',
    @endfor
    }

@endfor

${p.heading("ENGINE COMMAND HELPERS")}

export interface EngineCommandEnvelope {
    session_id: string
    readonly simplename: string
    readonly classname: string
    readonly command: EngineCommand
    //  version: string
    // request_channel: string
}

function create_command<T extends EngineCommand>(
    simplename: string,
    classname: string,
    command: T
): EngineCommandEnvelope {
    return {
        session_id: "",
        simplename,
        classname,
        command
    } as EngineCommandEnvelope
}

${p.heading("ENGINE COMMANDS")}

@for(var l : p.getCommands())
    ${l}
@endfor

${p.heading("ENGINE COMMAND CLASSES")}

${p.getRequest_classes_str()}

${p.heading("CLIENT STORE COMMANDS")}

${p.getClient_command_classes()}


/** REDUNDANT INFO IS OK HERE BECAUSE THESE ARE GENERATED CLASSES")
 * - eg: properties are always given default values for reactivity to work")
 */

/**
 *    AuStore
 */

export class AuStore{
    time: TimeValue | null = null
    seconds_since_last_message_received: number = 0
//            appendText("\treset(){\n")
//            appendText("\t\tthis.time = null\n")
//            appendText("\t\tthis.seconds_since_last_message_received = 0\n")
//            appendText("\t}\n")
}
