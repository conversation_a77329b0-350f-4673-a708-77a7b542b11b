// File: GitHelper.kt
package au21.engine.generators

import java.io.BufferedReader
import java.io.InputStreamReader

object GitHelper {
    fun getCurrentGitBranch(): String =
        Runtime.getRuntime()
            .exec("git rev-parse --abbrev-ref HEAD")
            .let {
                it.waitFor()
                BufferedReader(InputStreamReader(it.inputStream))
                    .readLine()
            }

    fun getCurrentGitCommit(): List<String> =
        Runtime.getRuntime()
            .exec("git log -1")
            .let {
                it.waitFor()
                BufferedReader(InputStreamReader(it.inputStream))
                    .readLines()
            }
}

fun main(args: Array<String>) {
    val commit = GitHelper.getCurrentGitCommit()
    for (s in commit) {
        println("s=${s}")
    }
    println(commit)
}
