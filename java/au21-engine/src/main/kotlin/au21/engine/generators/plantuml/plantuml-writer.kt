// File: plantuml-writer.kt
package au21.engine.generators.plantuml

import au21.engine.framework.database.AuEntity
import au21.engine.generators.get_classes_with_annotation
import au21.engine.generators.get_enums_in_package
import au21.engine.generators.get_sub_classes
import ch.ifocusit.plantuml.classdiagram.ClassDiagramBuilder
import java.io.File
import javax.persistence.Embeddable

fun diagram(file: File, vararg classes: Class<*>) {
    val diagram: String = ClassDiagramBuilder()
        .addClasses(*classes)
        .build()
        .lines()
        .mapNotNull {
            when {
                it.trim().startsWith("_") -> null
                else -> it
            }
        }
        .joinToString("\n")

    //println(diagram)
    println(file.absoluteFile)
    file.apply {
        //   createNewFile()
        writeText(diagram)
    }

}

fun main() {
    /*
    diagram(
        File("docs/diagrams/model/de/de-model.puml"),
        DeAuction::class.java,
        DeBidConstraints::class.java,
        DeAuction.Match::class.java,
        DeOrder::class.java,
        DeAuction.Round::class.java,
        DeAuction.RoundTraderInfo::class.java,
        DeAuction.Trader::class.java,
    )

    diagram(
        File("docs/diagrams/model/common/common-model.puml"),
        Activity::class.java,
        Auction::class.java,
        AuctionMessage::class.java,
        AuSession::class.java,
        Company::class.java,
        Person::class.java
    )
     */

    diagram(
        File("docs/diagrams/model/entities.puml"),
        *get_sub_classes<AuEntity>().toTypedArray()
    )

    diagram(
        File("docs/diagrams/model/entities_and_embedded.puml"),
        *listOf(
            get_sub_classes<AuEntity>(),
            get_classes_with_annotation(Embeddable::class.java)
        ).flatten().distinct().toTypedArray()
    )

    diagram(
        File("docs/diagrams/enums/enums.puml"),
        *get_enums_in_package().toTypedArray()
    )
}

