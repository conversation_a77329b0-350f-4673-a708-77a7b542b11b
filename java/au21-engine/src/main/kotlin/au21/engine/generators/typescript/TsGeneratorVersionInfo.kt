// File: TsGeneratorVersionInfo.kt
package au21.engine.generators.typescript

import au21.engine.generators.getCurrentGitBranch
import au21.engine.generators.getCurrentGitCommit

data class TsGeneratorVersionInfo(

    val branch: String = getCurrentGitBranch(),

    val current_commit: List<String> = getCurrentGitCommit(),

    // TODO: change to increment on prev version if tsgnerated changed
//    val version: String = updateVersion("src/main/kotlin/au21/engine/generators/typescript/resources/version.json")
    val version: String = ""
)

//fun updateVersion(filePath: String): String {
//    val mapper = jacksonObjectMapper()
//
//    val jsonFile = File(filePath)
//    val jsonMap: Map<String, String> = mapper.readValue(jsonFile)
//
//    val versionString = jsonMap["version"]
//
//    val parts = versionString!!.split(".")
//    if (parts.size != 3) {
//        throw IllegalArgumentException("Invalid version string: $versionString")
//    }
//
//    val currentPatch = parts[2].toInt()
//    val updatedPatch = currentPatch + 1
//    val updatedVersion = "${parts[0]}.${parts[1]}.$updatedPatch"
//    val updatedJson = """{"version": "$updatedVersion"}"""
//    jsonFile.writeText(updatedJson)
//
//    return updatedVersion
//}
