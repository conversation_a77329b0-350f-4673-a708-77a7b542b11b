// File: mermaid-writer.kt
package au21.engine.generators.mermaid

import au21.engine.framework.database.AuEntity
import au21.engine.generators.get_sub_classes
import au21.engine.generators.resolve_directory
import gg.jte.CodeResolver
import gg.jte.ContentType
import gg.jte.TemplateEngine
import gg.jte.TemplateOutput
import gg.jte.output.StringOutput


class MermaidHelper(
    val description: String?,
    val title: String,
    val entity_classes: List<Class<*>>
)

fun main() {
    // This is the directory where your .jte files are located.
    val codeResolver: CodeResolver = resolve_directory(MermaidHelper::class)
    val templateEngine: TemplateEngine = TemplateEngine.create(codeResolver, ContentType.Plain)
    val out: TemplateOutput = StringOutput()

    templateEngine.render(
        "mermaid-entities.jte",
        MermaidHelper(
            "desc",
            "title",
            get_sub_classes<AuEntity>()
        ),
        out
    )
    println(out)
}
