// File: helpers.kt
package au21.engine.generators

import au21.engine.generators.typescript.TsGenerator
import io.github.classgraph.ClassGraph


@Suppress("UNCHECKED_CAST")
inline fun <reified T> get_sub_interfaces(pkg: String = "au21.engine"): List<Class<T>> =
    // TODO: should be configurable
    ClassGraph()
        .verbose()
        .enableAllInfo()
        .acceptPackages(pkg)
        .scan()
        .allClasses
        .filter {
            it.interfaces.any {
                it.simpleName == T::class.simpleName
            }
        }
        .map {
            Class.forName(it.name)
        } as List<Class<T>>

@Suppress("UNCHECKED_CAST")
inline fun <reified T> get_sub_classes(pkg: String = TsGenerator.pkg): List<Class<T>> {
    return ClassGraph()
        //.whitelistPackages(TsGenerator.pkg)
        .acceptPackages(pkg)
        //.acceptClasses()
        .scan()
        .allClasses
        .filter {
            it.extendsSuperclass(T::class.java.canonicalName)
        }
        .map {
            Class.forName(it.name)
        } as List<Class<T>>
}

fun get_classes_with_annotation(clazz: Class<out Annotation>, pkg: String = TsGenerator.pkg): List<Class<*>> =
    ClassGraph()
        .enableAnnotationInfo()
        //.whitelistPackages(TsGenerator.pkg)
        .acceptPackages(pkg)
        //.acceptClasses()
        .scan()
        .allClasses
        //.getClassesWithAnnotation(clazz.name)
        .filter {
            it.hasAnnotation(clazz)
        }
        .map { Class.forName(it.name) }

fun get_enums_in_package(pkg: String = TsGenerator.pkg): List<Class<*>> =
    ClassGraph()
        //.whitelistPackages(TsGenerator.pkg)
        .acceptPackages(pkg)
        //.acceptClasses()
        .scan()
        .allClasses
        //.getClassesWithAnnotation(clazz.name)
        .filter {
            it.isEnum
        }
        .map { Class.forName(it.name) }



// test:
//fun main() {
//    println(get_classes_with_annotation(ApplicationScoped::class.java))
//    println(get_enums_in_package())
//}
