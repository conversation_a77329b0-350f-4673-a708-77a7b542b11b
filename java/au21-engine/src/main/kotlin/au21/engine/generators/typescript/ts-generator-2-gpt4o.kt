// File: ts-generator-2-gpt4o.kt
package au21.engine.generators.typescript

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.viewmodel.DateTimeValue
import au21.engine.framework.client.ClientCommand
import au21.engine.framework.client.LiveClientStore
import au21.engine.framework.client.StaleClientStore
import au21.engine.framework.client.StoreValue
import au21.engine.framework.commands.EngineCommand
import au21.engine.generators.get_sub_classes
import io.github.classgraph.ClassGraph
import io.github.classgraph.ClassInfo
import io.github.classgraph.ScanResult
import io.quarkus.logging.Log
import me.ntrrgc.tsGenerator.ClassTransformer
import me.ntrrgc.tsGenerator.TypeScriptGenerator
import me.ntrrgc.tsGenerator.onlyOnSubclassesOf
import net.pearx.kasechange.toSnakeCase
import java.io.File
import java.lang.reflect.Field
import java.lang.reflect.ParameterizedType
import kotlin.reflect.KClass
import kotlin.reflect.KProperty
import kotlin.reflect.KVisibility
import kotlin.reflect.full.isSubclassOf
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.javaField

// Constants
const val TARGET_DIR_PATH = "temp"
const val TARGET_FILE_NAME = "generated.ts"
const val PACKAGE_NAME = "au21.engine"

fun main() {
    val targetDir = File(TARGET_DIR_PATH)
    val targetFile = File(targetDir, TARGET_FILE_NAME)

    try {
        createTargetDirectory(targetDir)
        generateAndValidate(targetFile)
    } catch (e: Throwable) {
        Log.error("Error during TypeScript generation", e)
    }
}

fun createTargetDirectory(dir: File) {
    if (!dir.exists()) {
        dir.mkdirs()
    }
}

fun generateAndValidate(file: File) {
    file.also {
        TsGenerator.generate(it)
        TsGenerator.validate(it)
    }
}

object TsGenerator {

    val pkg = "au21.engine"
    fun generate(file: File) {
        resetFile(file)

        file.apply {
            writeText("")
            //appendSectionHeader("IMPORTS")
            appendSectionHeader("ENUMS")
            generateEnums(this)

            appendSectionHeader("ENGINE COMMAND (aka Requests) HELPERS")
            generateCommandHelpers(this)

            appendSectionHeader("ENGINE COMMAND (REQUEST) CLASSES")
            generateCommandClasses(this)

            appendSectionHeader("CLIENT STORE COMMANDS (aka Results)")
            generateClientStoreCommands(this)

            writeStoreClasses(this)
        }

        println(file.readText())
    }

    private fun resetFile(file: File) {
        if (file.exists()) {
            //    println("Deleting: ${file.canonicalPath}")
            file.delete()
        }
        println("Creating: ${file.canonicalPath}")
        file.createNewFile()
    }

    private fun File.appendSectionHeader(heading: String) {
        appendText(
            """
            
            /***************************************************************************
             *
             * $heading
             *
             ***************************************************************************/
            
            """.trimIndent()
        )
    }

    private fun generateEnums(file: File) {
        try {
            val scanResult: ScanResult = ClassGraph()
                .verbose()
                .enableAllInfo()
                .acceptPackages(PACKAGE_NAME)
                .scan()

            scanResult.allEnums.forEach { info: ClassInfo ->
                file.appendText("\nexport enum ${info.simpleName} {\n")
                val fields = info.fieldInfo
                    .filter { it.name != "\$VALUES" }
                    .filter { it.name != "\$ENTRIES" }
                    .map { it.name }
                    .joinToString(separator = ", \n") { value ->
                        "\t$value = '$value'"
                    }
                file.appendText(fields)
                file.appendText("\n}\n")
            }
        } catch (e: Exception) {
            Log.error("Error generating enums", e)
        }
    }

    private fun generateCommandHelpers(file: File) {
        file.appendText(
            """
                export interface EngineCommandEnvelope {
                    session_id: string
                    readonly simplename: string
                    readonly classname: string
                    readonly command: EngineCommand
                }

                function create_command<T extends EngineCommand>(
                    simplename: string,
                    classname: string,
                    command: T
                ): EngineCommandEnvelope {
                    return {
                        session_id: "",
                        simplename,
                        classname,
                        command
                    } as EngineCommandEnvelope
                }
            """.trimIndent()
        )

        val requestClasses = get_sub_classes<EngineCommand>()

        requestClasses.forEach {
            println(it.simpleName)
        }

        requestClasses.forEach {
            val name = it.simpleName
            val definition = when (it.kotlin.memberProperties.size) {
                0 -> "export const ${name.toSnakeCase()} = () => create_command('$name', '${it.canonicalName}', {})"
                else -> "export const ${name.toSnakeCase()} = (req: $name) => create_command('$name', '${it.canonicalName}', req)"
            }
            file.appendText("\n$definition\n")
        }
    }

    private fun generateCommandClasses(file: File) {
        val requestClasses = get_sub_classes<EngineCommand>()

        val requestTsGenerator = TypeScriptGenerator(
            rootClasses = requestClasses.map { Class.forName(it.name).kotlin }
                .filter { it.isSubclassOf(EngineCommand::class) },
            classTransformers = listOf(
                object : ClassTransformer {
                    override fun transformPropertyList(
                        properties: List<KProperty<*>>,
                        klass: KClass<*>
                    ): List<KProperty<*>> =
                        properties.filter { property ->
                            !listOf(KVisibility.PRIVATE, KVisibility.PROTECTED).contains(property.visibility) &&
                                    !listOf(AuSession::class, DateTimeValue::class).contains(klass) &&
                                    !listOf("session").contains(property.name)
                        }
                }.onlyOnSubclassesOf(EngineCommand::class)
            )
        )

        val requestDefinitions = requestTsGenerator.individualDefinitions
            .filterNot { it.startsWith("type") || it.startsWith("interface Companion") || it.contains("Handler") }
            .sorted()

        file.appendText("export ${requestDefinitions.joinToString(separator = "\n\nexport ")}")
    }

    // TODO: the command property shouldn't be string, it should be: 'CommandSucceded' | 'ShowMessage' etc!
    private fun generateClientStoreCommands(file: File) {
        val clientCommands = get_sub_classes<ClientCommand>()

        file.appendText("export ${
            TypeScriptGenerator(
                rootClasses = clientCommands.map { Class.forName(it.name).kotlin },
                ignoreSuperclasses = setOf(Cloneable::class),
                classTransformers = listOf(
                    object : ClassTransformer {
                        override fun transformPropertyList(
                            properties: List<KProperty<*>>,
                            klass: KClass<*>
                        ): List<KProperty<*>> =
                            properties.filter { property ->
                                property.visibility != KVisibility.PRIVATE
                            }
                    }
                )
            ).individualDefinitions
                .filterNot { it.startsWith("type") || it.startsWith("interface LiveClientStore") }
                .sorted()
                .joinToString(separator = "\n\nexport ")
        }")

        file.appendText("\n\n// REDUNDANT INFO IS OK HERE BECAUSE THESE ARE GENERATED CLASSES")
        file.appendText("\n\n// - eg: properties are always given default values for reactivity to work")
    }

    private fun writeStoreClasses(file: File) {
        file.appendText("\n\nexport class AuStore{\n")
        file.appendText("\ttime: TimeValue | null = null\n")
        file.appendText("\tseconds_since_last_message_received: number = 0\n")
        file.appendText("}\n\n")

        StoreClassHelper.write(file, LiveClientStore::class, "AuStore")
        StoreClassHelper.write(file, StaleClientStore::class)
    }

    fun validate(file: File) {
        var failed = false

        fun checkDuplicates(search: String, errMsg: String) {
            file.readLines()
                .filter { it.startsWith(search) }
                .groupingBy { it }
                .eachCount()
                .filter { it.value > 1 }
                .takeIf { it.isNotEmpty() }
                ?.let { duplicates ->
                    Log.error("ERROR: duplicate $errMsg found: $duplicates")
                    failed = true
                }
        }

        checkDuplicates("export interface", "interfaces")
        checkDuplicates("export class", "classes")

        if (failed) {
            throw Error("Duplicates detected")
        }
    }
}

class StoreClassHelper(
    val prop: String,
    val type: String,
    val default: String?
) {
    companion object {
        fun write(file: File, kClass: KClass<out Any>, extends: String? = null) {
            file.appendText("\nexport class ${kClass.simpleName}")
            extends?.let { file.appendText(" extends $extends ") }
            file.appendText("{\n")

            toStoreHelpers(kClass).forEach { helper ->
                file.appendText("\t${helper.prop}: ${helper.type}")
                helper.default?.let { file.appendText(" = $it") }
                file.appendText("\n")
            }
            file.appendText("}\n")
        }

        private fun toStoreHelpers(kClass: KClass<out Any>): List<StoreClassHelper> {
            val helpers = mutableListOf<StoreClassHelper>()

            kClass.memberProperties.forEach { prop ->
                val field: Field = prop.javaField!!
                val type: Class<*> = field.type
                val isNullable = prop.returnType.isMarkedNullable

                if (StoreValue::class.java.isAssignableFrom(type)) {
                    helpers.add(
                        StoreClassHelper(
                            prop.name,
                            type.simpleName + if (isNullable) " | null" else "",
                            if (isNullable) "null" else null
                        )
                    )
                } else if (type == List::class.java) {
                    (field.genericType as ParameterizedType).actualTypeArguments.forEach {
                        val elemName = it.typeName.substringAfterLast(".")
                        helpers.add(StoreClassHelper(prop.name, "$elemName[]", "[]"))
                    }
                }
            }

            return helpers
        }
    }
}
