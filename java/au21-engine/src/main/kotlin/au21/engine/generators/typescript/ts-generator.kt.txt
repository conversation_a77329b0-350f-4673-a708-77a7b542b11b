package au21.engine.generators.typescript


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.viewmodel.DateTimeValue
import au21.engine.framework.client.ClientCommand
import au21.engine.framework.client.LiveClientStore
import au21.engine.framework.client.StaleClientStore
import au21.engine.framework.client.StoreValue
import au21.engine.framework.commands.EngineCommand
import au21.engine.generators.get_sub_classes
import io.github.classgraph.ClassGraph
import io.github.classgraph.ClassInfo
import io.github.classgraph.ScanResult
import me.ntrrgc.tsGenerator.ClassTransformer
import me.ntrrgc.tsGenerator.TypeScriptGenerator
import me.ntrrgc.tsGenerator.onlyOnSubclassesOf
import net.pearx.kasechange.toSnakeCase
import org.tinylog.kotlin.Logger
import java.io.File
import java.lang.Exception
import java.lang.reflect.Field
import java.lang.reflect.ParameterizedType
import java.nio.file.Files
import kotlin.reflect.KClass
import kotlin.reflect.KProperty
import kotlin.reflect.KVisibility
import kotlin.reflect.full.isSubclassOf
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.javaField


/**
 * NB, NB, NB:
 * if you want to add a viewmodel Value or Element,
 * then you MUST add it to the LiveStore
 * ie: Values and Elements aren't generated (even if implementing StoreValue etc),
 * unless they are in the livestore, eg, on DeAuctionStatus
 */

fun main() {

    val TARGET_DIR = File("temp")
    val TARGET_FILE = File(TARGET_DIR, "generated.ts")

    try {

       if (!TARGET_DIR.exists()){
           TARGET_DIR.mkdirs()
        }


        // 2) generate and validate generated.ts
        TARGET_FILE.also {
            TsGenerator.generate(it)
            TsGenerator.validate(it)
        }

    } catch (e: Throwable) {
        e.printStackTrace()
    }
    // getCurrentGitBranch().forEach { Logger.info(it) }

}

/**
 * NOTE: THERE IS AN ALTERNATIVE LIBRARY:
 * - https://github.com/vojtechhabarta/typescript-generator/wiki/Type-Mapping
 */

object TsGenerator {

    // TODO: probably need a way to test if this has actually changed,
    //       - and if not, don't create it
    //       - because currently that would require the client to fail for no reason

    val pkg = "au21.engine"

    fun generate(f: File) {

        if (f.exists()) {
            Logger.info("deleting:" + f.canonicalPath)
            f.delete()
        }

        Logger.info("creating: " + f.canonicalPath)
        f.createNewFile()

        f.apply {

            writeText("") // ie: clear the file

            var count = 0

            fun heading(heading: String) {
                count++
                appendText("\n\n\n")
                appendText(
                    """
                    /***************************************************************************
                     *
                     *     $count) $heading
                     *
                     ***************************************************************************/
                    """.trimIndent()
                )
                appendText("\n\n\n")
            }

            /************************************************
             *  0) IMPORTS
             */

            // appendText("\n\nimport { Singleton } from 'typescript-ioc' \n\n");


            /************************************************
             *  1) ENUMS
             */
            heading("ENUMS")

            try {
                val scanResult: ScanResult = ClassGraph()
                    .verbose()
                    .enableAllInfo()
                    .acceptPackages(pkg)
                    .scan()

                scanResult.allEnums.forEach { info: ClassInfo ->

                    appendText("\nexport enum ${info.simpleName} { \n")
//                        val fields = c.typeParameters.joinToString(separator = ",\n") {
//                            "\t${c.name.toString()} = '${c.name.toString()}'"
//                        }
//                        appendText(fields)
//                        appendText("\n}\n")
                    val fields = info.fieldInfo
                        .filter { it.name != "\$VALUES" }
                        .map { it.name }
                        .joinToString(separator = ", \n") { value ->
                            "\t${value} = '${value}'"
                        }
                    appendText(fields)
                    appendText("\n}\n")
                }

            } catch (e: Exception) {
                e.printStackTrace()
            }
            //.map { Class.forName(it.n ame).kotlin }


            /**
             *  2)  COMMAND HELPERS
             */

            heading("ENGINE COMMAND (aka Requests) HELPERS")

            appendText(
                """
                    export interface EngineCommandEnvelope {
                        session_id: string
                        readonly simplename: string
                        readonly classname: string
                        readonly command: EngineCommand
                       //  version: string
                       // request_channel: string
                    }

                    function create_command<T extends EngineCommand>(
                        simplename: string, 
                        classname: string,
                        command: T
                    ): EngineCommandEnvelope {
                        return {
                            session_id: "",
                            simplename,
                            classname,
                            command
                        } as EngineCommandEnvelope
                    }

                """.trimIndent()
            )

            val request_classes: List<Class<EngineCommand>> = get_sub_classes() // get_sub_interfaces()

            request_classes.forEach {
                Logger.info(it.simpleName)
            }

            // TODO: remove empty constructor:

            request_classes.forEach {
                val name = it.simpleName
                when (it.kotlin.memberProperties.size) {
                    0 ->
                        appendText("\nexport const ${name.toSnakeCase()} = () => \n\tcreate_command('${name}', '${it.canonicalName}', {})\n")

                    else ->
                        appendText("\nexport const ${name.toSnakeCase()} = (req: $name) => \n\tcreate_command('${name}', '${it.canonicalName}', req)\n")
                }

            }

            /**
             *  3) COMMAND CLASSES
             */


            heading("ENGINE COMMAND (REQUEST) CLASSES")

            // Logger.info(request_classes.map {  Class.forName(it.name).kotlin })

            val request_ts_generator = TypeScriptGenerator(
                rootClasses = request_classes.map { Class.forName(it.name).kotlin }
                    .filter { it.isSubclassOf(EngineCommand::class) },
                classTransformers = listOf(
                    object : ClassTransformer {
                        override fun transformPropertyList(
                            properties: List<KProperty<*>>,
                            klass: KClass<*>
                        ): List<KProperty<*>> =
                            properties.filter { property ->
                                // NB: MUST MAKE SURE THAT THE BLACKLISTED PROPERTIES ARE ACTUALLY PRIVATE!
                                !listOf(
                                    KVisibility.PRIVATE,
                                    KVisibility.PROTECTED
                                ).contains(property.visibility) &&
                                        !listOf(AuSession::class, DateTimeValue::class).contains(klass) &&
                                        !listOf("session").contains(property.name)
                                // starting_time to avoid duplicate DateTimeValue interface
                            }
                    }.onlyOnSubclassesOf(EngineCommand::class)
                )
            )

            val request_definitions: List<String> = request_ts_generator
                //.sortedBy { it.simpleName })
                .individualDefinitions
                .map {
                    Logger.info(it)
                    it
                }
                .filterNot { it.startsWith("type") }
                .filterNot { it.startsWith("interface Companion") }
                .filterNot { it.contains("Handler") }
                //  .filterNot { it.contains("DateTimeValue") }
                // .filterNot { it.contains("StoreValue") }
                .sorted()

            appendText(
                "export " + request_definitions.joinToString(separator = "\n\nexport ")
            )

            /************************************************
             *  4) View Model: Values and Elements
             */

            heading("CLIENT STORE COMMANDS (aka Results)")


            val client_commands: List<Class<ClientCommand>> = get_sub_classes()

            appendText("export " + TypeScriptGenerator(
                rootClasses = client_commands.map { Class.forName(it.name).kotlin },
                ignoreSuperclasses = setOf(Cloneable::class),
                classTransformers = listOf(
                    object : ClassTransformer {
                        override fun transformPropertyList(
                            properties: List<KProperty<*>>,
                            klass: KClass<*>
                        ): List<KProperty<*>> =
                            properties.filter { property ->
                                // NB: MUST MAKE SURE THAT THE BLACKLISTED PROPERTIES ARE ACTUALLY PRIVATE!
                                property.visibility != KVisibility.PRIVATE
                            }
                    }
                ))
                //.sortedBy { it.simpleName })
                .individualDefinitions
                .filter {
                    //println(it)
                    !it.startsWith("interface LiveClientStore")
                }
                .sorted()
                .filterNot { it.startsWith("type") }
                .joinToString(separator = "\n\nexport "))


            appendText("\n\n// REDUNDANT INFO IS OK HERE BECAUSE THESE ARE GENERATED CLASSES")
            appendText("\n\n// - eg: properties are always given default values for reactivity to work")

            /**
             *    AuStore
             */

            appendText("\n\nexport class AuStore{\n")
            appendText("\ttime: TimeValue | null = null\n")
            appendText("\tseconds_since_last_message_received: number = 0\n")
//            appendText("\treset(){\n")
//            appendText("\t\tthis.time = null\n")
//            appendText("\t\tthis.seconds_since_last_message_received = 0\n")
//            appendText("\t}\n")
            appendText("}\n\n")


            /**
             *   LIVE CLIENT STORE
             */

            StoreClassHelper.write(f, LiveClientStore::class, "AuStore")

            /**
             *   STALE CLIENT STORE
             */

            StoreClassHelper.write(f, StaleClientStore::class)


        }

        // NB: create the version file:
        // TsVersion.setVersion(TsVersion(version = version))
        Logger.info(f.readText())
    }

    fun validate(f: File) {
        var failed = false
        fun duplicates(search: String, err_msg: String) = f.readLines()
            .filter { it.startsWith(search) }
            .groupingBy { it }
            .eachCount()
            .filter { it.value > 1 }
            .takeIf { it.isNotEmpty() }
            ?.let { duplicates ->
                Logger.error("ERROR: duplicate $err_msg found: $duplicates")
                failed = true
            }
        duplicates("export interface", "interfaces")
        duplicates("export class", "classes")
        if (failed) {
            throw Error("Duplicates detected")
        }
    }
}


class StoreClassHelper(
    val prop: String,
    val type: String,
    val default: String?
) {
    companion object {

        fun write(f: File, t: KClass<out Any>, extends: String? = null) {

            f.appendText("\nexport class ${t.simpleName}")
            extends?.let { f.appendText(" extends $extends ") }
            f.appendText("{\n")

            to_store_helpers(t).apply {
                forEach { h: StoreClassHelper ->
                    f.appendText("\t${h.prop}: ${h.type}")
                    h.default?.let {
                        f.appendText(" = $it")
                    }
                    f.appendText("\n")
                }
                f.appendText("}\n")
            }

        }

        fun to_store_helpers(k: KClass<out Any>): List<StoreClassHelper> =

            mutableListOf<StoreClassHelper>().apply {

                k.memberProperties.forEach { p ->
                    val ff: Field = p.javaField!!
                    val type: Class<*> = ff.type
                    val is_nullable = p.returnType.isMarkedNullable

                    type.genericInterfaces.forEach {
                        // (1) Find the Store Values
                        if (it.typeName == StoreValue::class.java.name) {
                            add(
                                StoreClassHelper(
                                    prop = p.name,
                                    type = type.simpleName + when {
                                        is_nullable -> " | null"
                                        else -> ""
                                    },
                                    default = when {
                                        is_nullable -> "null"
                                        else -> null
                                    }
                                )
                            )
                        }
                    }
                    // (2) Find the Store Elements:
                    if (type.toString() == List::class.java.toString()) {
                        (ff.genericType as ParameterizedType).actualTypeArguments.forEach {
                            it.typeName.let { elem ->
                                println(elem)
                                val elem_name = elem.substring(elem.lastIndexOf(".") + 1, elem.length)
                                add(
                                    StoreClassHelper(
                                        prop = p.name,
                                        type = "$elem_name[]",
                                        default = "[]"
                                    )
                                )
                            }
                        }
                    }
                }
            }.toList().also {
                println(it)
            }
    }
}


// ---------------------------------------------------------------------------------

//            // THIS CREATES TYPES
//            appendText(
//
//                    "export " + TypeScriptGenerator(
//                            rootClasses = ClassGraph()
//                                    //.verbose()                   // Log to stderr
//                                    //.enableAllInfo()             // Scan classes, methods, fields, annotations
//                                    .enableClassInfo()
//                                    .whitelistPackages(pkg)        // Scan com.xyz and subpackages (omit to scan all packages)
//                                    .scan()
//                                    .allClasses
//                                    .filter { it.isEnum }
//                                    .map { Class.forName(it.name).kotlin })
//                            //.sortedBy { it.simpleName })
//                            .individualDefinitions
//                            .sorted()
//                            .joinToString(separator = "\n\nexport "))

// THIS CREATES STRING LITERALS:
//            ClassGraph()
//                .enableClassInfo()
//                .whitelistPackages(pkg)
//                .scan()
//                .allClasses
//                .filter { it.isEnum }
//                .forEach {
//                    Class.forName(it.name).let { c ->
//                        appendText("\nexport enum ${c.simpleName} { \n")
//                        val fields = c.typeParameters.joinToString(separator = ",\n") {
//                            "\t${c.name.toString()} = '${c.name.toString()}'"
//                        }
//                        appendText(fields)
//                        appendText("\n}\n")
//                    }
//                }

// ---------------------------------------------------------------------------------


//            val result_value_classes: List<Class<out StoreValue>> = get_sub_interfaces()
//
//            val result_element_classes: List<Class<out StoreElement>> = get_sub_interfaces()
////                    get_classes_with_annotation(ResultList::class) +
////                            get_classes_with_annotation(StoreValue::class)
//
//            val result_classes = (
//                    result_value_classes.map { it.kotlin } +
//                            result_element_classes.map { it.kotlin })
//
//            appendText(
//
//                "export " + TypeScriptGenerator(
//                    rootClasses = result_classes,
//                    ignoreSuperclasses = setOf(Cloneable::class),
//                    classTransformers = listOf(
//                        object : ClassTransformer {
//                            override fun transformPropertyList(
//                                properties: List<KProperty<*>>,
//                                klass: KClass<*>
//                            ): List<KProperty<*>> =
//                                properties.filter { property ->
//                                    // NB: MUST MAKE SURE THAT THE BLACKLISTED PROPERTIES ARE ACTUALLY PRIVATE!
//                                    property.visibility != KVisibility.PRIVATE
//                                }
//                        }
//                    ))
//                    //.sortedBy { it.simpleName })
//                    .individualDefinitions
//                    .sorted()
//                    .filterNot { it.startsWith("type") }
//                    .joinToString(separator = "\n\nexport "))
//
//            // TODO: write the store
//
//
//            /************************************************
//             *  5) STORE
//             */
//            heading("STORE")
//
//            /**
//             *  Store a): problem with automatically,
//             *  ie:
//             *    val request_ts_generator = TypeScriptGenerator(rootClasses =  listOf(RemoteStore::class))
//             *    f.appendText(request_ts_generator.definitionsText)
//             *
//             *  is that it generates this:
//             *    interface RemoteStore {
//             *       a: A | null
//             *       b: B[]
//             *    }
//             *
//             *  What we really want is:
//             *    export class RemoteStore {
//             *       a = null as A
//             *       b = [] as B[]
//             *    }
//             */
//
//            /** Store b)
//             *   this works great if is no backing server-side RemoteStore:
//
//            appendText("export class RemoteStore {")
//            appendText(
//            listOf(
//            result_value_classes.map { value_class ->
//
//            val value: String = value_class.simpleName
//            val key: String = value_class.value_path() //value.removeSuffix("Value").toSnakeCase()
//
//            "\n\t$key = null as $value"
//
//            },
//            result_element_classes.map { element_class ->
//            val value: String = element_class.simpleName
//            val key: String = element_class.element_path()
//
//            "\n\t$key = [] as $value[]"
//            })
//            .flatten()
//            .sorted()
//            .joinToString(separator = "\n")
//            )
//            appendText("\n}")
//             */
//
//            /** Store c: Generate from a backing server-side RemoteStore:
//             */
//
