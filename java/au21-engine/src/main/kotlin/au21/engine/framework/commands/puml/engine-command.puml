@startuml
allowmixing


package "Engine Command Handler" {


        map Queue {
            TICK => JSO<PERSON>
        }

        node "Engine Commond Handler Thread"{
            artifact SessionCommands {

            }

            circle "Exception"

            node "Try Exception" {

                <> Handle

                package Transaction{

                    database db
                    map EngineAction{
                        transact => SessionCommands
                    }
                    EngineAction <|..  "Ticker Action"
                }
                Handle --> "Ticker Action" : TICK

                node "Try Alert Exception" {

                    Handle --> "Engine Command Deserializer" : json
                    Queue --> Handle

                    "Engine Command Deserializer" --> EngineCommand

                }

                circle "Alert Exception"

        '            Engine Command Deserializer" : json | TICK
                    EngineCommand --> EngineAction
                    EngineAction --> SessionCommands
                    EngineCommand --> "Alert Exception"
                    "Alert Exception" --> SessionCommands
                    "Exception" --> SessionCommands
                map EngineCommand{
                    validate => EngineAction
                }
            }
        }

        "Try Exception" --> Exception

        SessionCommands --> RedisClient::ENGINE_OUTPUT_CHANNEL


      node "RedisClient Thread"{


            object ENGINE_INPUT_CHANNEL

'            map RedisClient{
'                ENGINE_INPUT_CHANNEL => Engine Commands
'                ENGINE_OUTPUT_CHANNEL => Client Commands
'            }

            ENGINE_INPUT_CHANNEL --> Queue : json
    }

   node "Heartbeat Thread"{
        TICK --> Queue
    }

    node "Test Thread"{
        TEST --> EngineAction
    }


}
'object JSON{
'    TICK | JSON
'}
'object EngineCommand
'object EngineAction
'
'map RedisClient {
' subscribe (ENGINE_INPUT_CHANNEL) *-> JSON
' JSON *--> EngineCommand
' EngineCommand *---> EngineAction
'}
'
'NewYork --> CapitalCity::USA
@enduml

'
'package "Engine Commands" {
'
'    [redis_queue] --> [JsonToEngineCommand] : json
'    Heartbeat --> [redis_queue] : TICK
'
'    node "RedisClient Thread" {
'        REDIS --> [Redis Client]
'        [Redis Client] --> [redis_queue] :json
'    }
'
'    node "Heartbeat Thread" {
'        node Heartbeat{
'
'        }
'    }
'
'    database "AuTransaction"{
'
'        [JsonToEngineCommand] --> [EngineCommand] : command
'        EngineCommand --> [EngineAction]
'        EngineAction -> [EngineClientCommandEnvelope]
'        [EngineClientCommandEnvelope]-->REDIS
'    }
'}
''node "Other Groups" {
'  FTP - [Second Component]
'  [First Component] --> FTP
'}
'
'cloud {
'  [Example 1]
'}

'
'database "MySql" {
'  folder "This is my folder" {
'    [Folder 3]
'  }
'  frame "Foo" {
'    [Frame 4]
'  }
'}
'
'
'[Another Component] --> [Example 1]
'[Example 1] --> [Folder 3]
'[Folder 3] --> [Frame 4]
'
'@enduml
