// File: AuEntityManager.kt
package au21.engine.framework.database

// import org.eclipse.microprofile.opentracing.Traced
import io.quarkus.logging.Log
import jakarta.annotation.PostConstruct
import jakarta.annotation.PreDestroy
import jakarta.enterprise.context.RequestScoped
import jakarta.inject.Inject
import javax.persistence.EntityManager
import javax.persistence.EntityManagerFactory
import javax.persistence.EntityTransaction

@RequestScoped
class AuEntityManager @Inject constructor(val emf: EntityManagerFactory) {

    companion object {
        var class_count = 0
    }

    var instance_count = 0

    init {
        instance_count = ++class_count
    }

    val em: EntityManager = emf.createEntityManager()

    // used for testing,
    // - see: https://www.objectdb.com/forum/1016
    fun getId(o:AuEntity?): String =
        o.let {
            // println(it)
            emf.persistenceUnitUtil.getIdentifier(o)?.toString() ?: ""
        }
    @PostConstruct
    fun postConstruct() {
       // Log.info("POST_CONSTRUCT: " + this::class.java.simpleName + " #${instance_count} created")
    }

    @PreDestroy
    fun preDestroy() {
       // Log.info("PRE_DESTROY: " + this::class.java.simpleName + " #${instance_count} destroyed")
        if (em.isOpen) {
            // Log.info("closing em: $instance_count")
            em.close()
        }
    }

   // @Traced
    @Synchronized
    fun <T> transact(logging: Boolean = true, block: () -> T): T {
//        if (!em.isOpen) {
//            throw java.lang.Exception("Entity manager is not open!")
//            // probably this is thrown on the next line anyway
//        }
        val tx: EntityTransaction = em.transaction
        try {
            tx.begin()
            return block().apply {
                tx.commit()
            }
        } catch (e: Throwable) {
            e.printStackTrace()
            throw e
        } finally {
            if (tx.isActive) {
                Log.error("TRANSACTION ROLLING BACK")
                tx.rollback()
                Log.error("TRANSACTION ROLLED BACK")
            }
        }
    }

    //@Traced
    fun <E : AuEntity> save(e: E): E =
        e.also {
//            if(!em.isJoinedToTransaction)
//                throw Error("cannot save, not in a transaction")
            em.persist(it)
        }

    //@Traced
    fun <E : AuEntity> refresh(e: E) =
        e.also { em.refresh(it) }

    //@Traced
    fun <E : AuEntity> delete(e: E) =
        e.also { em.remove(it) }

    //@Traced

    @Suppress("JpaQlInspection")
    inline fun <reified E> deleteAllIncDeleted() =
        // TODO: this cannot be allowed to run in production!!
        try {
            // println(E::class.simpleName)
            // https://www.objectdb.com/java/jpa/query/jpql/delete
            em.createQuery("DELETE FROM ${E::class.simpleName}").executeUpdate()
        } catch (e:Exception){
            e.printStackTrace()
        }

    //@Traced
    fun commit_and_begin(){
        em.transaction.commit()
        em.transaction.begin()
    }

    //@Traced
    fun <E : AuEntity> deleteAllExcept(vararg except: AuEntity) =
////        if (!emf.resettable) {
////            throw Exception("database is not resetable")
////        } else  // which is fixed at module creation, but could be per-call
        findAll<AuEntity>(include_deleted = true).forEach {
            if (!except.contains(it)) {
                delete(it)
            } else {
                Log.info("not deleting: ${it::class.simpleName} with id: ${it.id_str()}")
            }
        }

    //@Traced
    inline fun <reified T : AuEntity> findAll(include_deleted: Boolean = false): List<T> =
        try {
            val query: String = "SELECT t FROM ${T::class.java.name} t" + (
                    if (!include_deleted) " WHERE t.deleted=false"
                    else "")
            em.createQuery(query, T::class.java).resultList
        } catch (e: Exception) {
            Log.info(e.message)
            // Log.error(e.message)
            // e.printStackTrace()
            listOf()
        }


    // https://www.objectdb.com/java/jpa/query/jpql/comparison#IS_NOT_NULL
    //@Traced
    inline fun <reified T : AuEntity> query(query: String): List<T> =
        try {
            em.createQuery(query, T::class.java).resultList
        } catch (e: Exception) {
            Log.info(e.message)
            emptyList<T>()
        }


    // TODO: note: this predicate is working on fetched objects, would be better as an object index or property !
    //@Traced
    inline fun <reified T : AuEntity> filter(include_deleted: Boolean = false, pred: (T) -> Boolean): List<T> =
        findAll<T>(include_deleted).filter { pred(it) }

    //@Traced
    inline fun <reified T : AuEntity> findFirst(include_deleted: Boolean = false, pred: (T) -> Boolean): T? =
        filter(include_deleted, pred).firstOrNull()


    //@Traced
    inline fun <reified T : AuEntity> byId(oid: String?, include_deleted: Boolean = false): T? =
        byId(oid?.toLongOrNull(), include_deleted)

    //@Traced
    inline fun <reified T : AuEntity> byId(oid: Long?, include_deleted: Boolean = false): T? =
        oid?.let { em.find(T::class.java, oid) }?.takeIf { include_deleted || !it.deleted }


}
