// File: ISessionsTerminated.kt
package au21.engine.framework.commands.interfaces

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuSession.SessionTerminationReason
import au21.engine.framework.commands.SOCKET_CLOSED_TIMEOUT_SECONDS
import au21.engine.framework.database.AuEntityManager

class SessionTermination(
    s: AuSession,
    val reason:String
){
    val sid:String = s.session_id
}

interface ISessionsTerminated{

    val sessions_terminated:MutableList<SessionTermination>

    fun get_reason_string(tr: SessionTerminationReason):String =
        when(tr){
            SessionTerminationReason.BROWSER_UNLOADED -> TODO()
            SessionTerminationReason.COMPANY_DELETED -> "Company deleted" // also a bit harsh ?
            SessionTerminationReason.COMPANY_NAME_EDITED -> "Company name edited"
            SessionTerminationReason.FORCED_OFF -> TODO()
            SessionTerminationReason.LOGIN_FROM_ANOTHER_BROWSER -> "You have logged in from another browser or device."
            SessionTerminationReason.SERVER_REBOOT -> TODO()
            SessionTerminationReason.SERVER_SWEPT_STALE_SESSION -> "Session swept due to > $SOCKET_CLOSED_TIMEOUT_SECONDS seconds of inactivity"
            SessionTerminationReason.SIGNED_OFF -> ""
            SessionTerminationReason.USER_EDITED -> "User edited"
            SessionTerminationReason.USER_DELETED -> "User deleted" // a bit harsh maybe ?
        }


    fun terminate_session(db: AuEntityManager, s:AuSession, tr: SessionTerminationReason){
        s.terminate(tr)
        db.save(s)
        sessions_terminated.add(SessionTermination(s, get_reason_string(tr)))
    }
}
