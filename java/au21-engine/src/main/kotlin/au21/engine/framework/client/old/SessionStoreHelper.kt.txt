package au21.engine.framework.commands.client

import au21.engine.domain.common.commands.client.*
import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Company
import au21.engine.domain.de.commands.client.DeAuctionValue
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.services.sessions_logged_in
import au21.engine.framework.commands.engine.EngineAction
import au21.engine.framework.database.AuEntityManager
import org.joda.time.DateTime

class SessionStoreHelper(
    val db: AuEntityManager,
    val action: EngineAction
) {

    fun session_store(s: AuSession): SessionStore? {

        val de: DeAuction? = when (val de = s.auction) {
            is DeAuction -> de
            else -> null
        }

        return null
//
//        return SessionStore(
//            auction_rows = AuctionRowElement.all_rows_for(db.findAll(), db.byId(s.person_id)),
//            companies = when (s.role) {
//                AuUserRole.AUCTIONEER -> companies
//                else -> emptyList()
//            },
//            counterparty_credits = when (s.role) {
//                AuUserRole.AUCTIONEER -> counterparty_credits_for_auctioneer
//                AuUserRole.TRADER -> counterparty_credits_for_trader(db.byId(s.company_id))
//                else -> emptyList()
//            },
//            de_auction = when {
//                de == null -> null_de_auction
//                s.role == AuUserRole.AUCTIONEER ->
//                    de_auctioneer_auctions[de] ?: run {
//                        DeAuctionValue.for_auctioneer(de, de.lastround()).also {
//                            de_auctioneer_auctions[de] = it
//                        }
//                    }
//                s.role == AuUserRole.TRADER ->
//                    de.get_trader(s.company_id)?.let { t: DeAuction.Trader ->
//                        de_trader_auctions[t] ?: run {
//                            DeAuctionValue.for_trader(de, t).also {
//                                de_trader_auctions[t] = it
//                            }
//                        }
//                    } ?: null_de_auction
//                else -> null_de_auction
//            },
////            de_award_value = when (s.role) {
////                AuUserRole.AUCTIONEER -> {
////                    when (de) {
////                        is DeAuction -> DeAwardValue(de)
////                        else -> null
////                    }
////                }
////                else -> null
////            },
//            session_user = SessionUserValue(s),
//            users = when (s.role) {
//                AuUserRole.AUCTIONEER -> users
//                else -> emptyList()
//            },
//            time = time
//        )
    }


// DE AUCTION VALUE:

    private val de_auctioneer_auctions: MutableMap<DeAuction, DeAuctionValue> = mutableMapOf()
    private val de_trader_auctions: MutableMap<DeAuction.Trader, DeAuctionValue> = mutableMapOf()
    private val null_de_auction: DeAuctionValue by lazy { DeAuctionValue.for_null_auction() }

// COUNTERPARTY CREDITS:

    val counterparty_credits_for_auctioneer: List<CounterpartyCreditElement> by lazy {
        CounterpartyCreditElement.for_auctioneer(db)
    }

    private val counterparty_credits_by_trader: MutableMap<Company, List<CounterpartyCreditElement>> = mutableMapOf()

    fun counterparty_credits_for_trader(c: Company?): List<CounterpartyCreditElement> =
        when (c) {
            null -> emptyList()
            else -> counterparty_credits_by_trader[c] ?: run {
                CounterpartyCreditElement.for_creditor(db, c).also {
                    counterparty_credits_by_trader[c] = it
                }
            }
        }

// USERS:

    val users: List<UserElement> by lazy {
        UserElement.all(db, db.sessions_logged_in())
    }

// COMPANIES:

    val companies: List<CompanyElement> by lazy {
        CompanyElement.all(db)
    }

    // TIME:
    val time = TimeValue("Houston", DateTimeValue(DateTime()))

// Event helpers:

    fun terminate_session(message: String, vararg sids: String) {
        //  send(ClientCommand.TerminateSession(message), *sids)
    }


    fun notify(
        content: String,
        vararg sids: String
    ) {
        //  send(ClientCommand.ShowMessage(BrowserMessageKind.NOTIFICATION, listOf(content)), *sids)
    }

    fun notify(
        content: List<String>,
        vararg sids: String
    ) {
        //   send(ClientCommand.ShowMessage(BrowserMessageKind.NOTIFICATION, content), *sids)
    }

    fun close_modal(vararg sids: String) {
        //   send(ClientCommand.CommandSucceeded(), *sids)
    }
}
