package com.au21.infrastructure.pubsub

import io.quarkus.redis.client.reactive.ReactiveRedisClient
import javax.annotation.PostConstruct
import javax.enterprise.context.ApplicationScoped
import javax.inject.Inject

@ApplicationScoped
class AuRedisConnector2 {
    @Inject
    lateinit var redisClient: ReactiveRedisClient // note: using the Mutiny RedisClient for now

    @PostConstruct
    fun postConstruct() {
        println(redisClient)

        redisClient
            .subscribe(listOf("AP"))
            .onSubscribe().invoke { ->
                println("Subscribed")
            }
            .onItem().invoke { ->
                println("Item")
            }
            .onFailure().invoke { ->
                println("failure")
            }
            .toMulti()
//            .onCompletion().invoke{ -> println("complete")}
//            .toMulti()
            .subscribe()
//        .onItem().invoke(i -> System.out.println("⬇️ Received item: " + i))
//        .onFailure().invoke(f -> System.out.println("⬇️ Failed with " + f))
//        .onCompletion().invoke(() -> System.out.println("⬇️ Completed"))
//        .onCancellation().invoke(() -> System.out.println("⬆️ Cancelled"))
//        .onRequest().invoke(l -> System.out.println("⬆️ Requested: " + l));
////            .onItem()
//            .invoke{i -> println("Received item: $i")}
//            .subscribe()
        //        RedisAPI.api(redisClient)
//            .subscribe(listOf("INPUT"))
    }
}
