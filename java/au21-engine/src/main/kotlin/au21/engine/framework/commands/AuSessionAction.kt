// File: AuSessionAction.kt
package au21.engine.framework.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.session_by_sid
import au21.engine.framework.database.AuEntityManager
import jakarta.inject.Inject

abstract class AuSessionAction {

    @Inject
    lateinit var db: AuEntityManager

    val errors: MutableList<String> = mutableListOf()

    abstract fun mutate()

    // must be provided, and doesn't exist on heartbeats !!
    // TODO: needs some refactoring possibly
    abstract val session_id: String

    open val session: AuSession by lazy {
        db.session_by_sid(session_id)
            ?: throw AlertException("session not found: $session_id.")
    }
}
