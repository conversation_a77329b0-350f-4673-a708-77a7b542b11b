// File: config.kt
package au21.engine.framework.utils

import io.quarkus.logging.Log
import jakarta.annotation.PostConstruct
import jakarta.enterprise.context.ApplicationScoped
import org.eclipse.microprofile.config.inject.ConfigProperty

@ApplicationScoped
class HeartbeatConfig {

    @ConfigProperty(name = "HEARTBEAT", defaultValue = "ON")
    private lateinit var heartbeatStatus: String

    val heartbeat_enabled: Boolean
        get() = heartbeatStatus != "OFF"

    @PostConstruct
    fun init() {
        Log.info("HeartbeatConfig initialized with:$heartbeatStatus")
    }

}
