// File: AuEntity.kt
package au21.engine.framework.database

import java.io.Serializable
import javax.jdo.annotations.Index
import javax.persistence.Entity
import javax.persistence.GeneratedValue
import javax.persistence.Id

/*
 *  1) Entities
 *
 */
@Entity
// @EntityListeners(AuEntity.EntityChangeListener::class)
open class AuEntity : Serializable {
    @Id
    @GeneratedValue
    var id: Long = 0

    // NOTE: THIS SHOULD ONLY BE USED IN VIEWMODEL or COMMANDS
    // - ie: to/from client
    fun id_str() =
        id.toString()

    @Index
    var deleted: Boolean = false

    // val creation_timestamp = DateTime().toDate()

    fun checkConstraints() {
        // for aggregates
        // opportunity for aggregate to reject changes
        // throws exception
    }
}

// Trying to see if we can test without the database, ie: without ids:
fun is_same_entity(a: AuEntity?, b: AuEntity?): Boolean =
    when {
        a == null -> false
        b == null -> false
        a.id == 0L && b.id == 0L -> a.hashCode() == b.hashCode() // NB: need this because id always 0 before save
        else -> a.id == b.id
    }
