// File: command-validators.kt
package au21.engine.framework.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.viewmodel.DateTimeValue
import au21.engine.framework.utils.is_blank
import java.util.*
import kotlin.reflect.KProperty0

fun humanize(s: String): String = s.replace("_", " ").replaceFirstChar { it.uppercase() }

fun EngineCommand.err_if(pred: <PERSON><PERSON><PERSON>, err: String) {
    if (pred) this.errors().add(err)
}

fun EngineCommand.err_unless(pred: <PERSON><PERSON><PERSON>, err: String) {
    if (!pred) errors().add(err)
}


fun EngineCommand.err_if_blank(prop: KProperty0<String?>, name: String? = null): String? =
    prop.get().also {
        if (it.is_blank())
            errors().add("${humanize(name ?: prop.name)} cannot be blank.")
    }?.trim()

fun EngineCommand.err_if_null(o: Any?, err: String) {
    if (o == null) errors().add(err)
}


fun EngineCommand.fail_if_errors() {
    if (this.errors().isNotEmpty()) {
        // changed when AlertException changed:
        // throw AlertException(*errors().toTypedArray())
        throw AlertException(errors().joinToString(separator = "\n"))
    }
}

fun fail(err: String): Nothing =
    throw AlertException(err)

fun fail_if(check: Boolean, err: String) {
    if (check) fail(err)
}

fun fail_if_not(check: Boolean, err: String) {
    if (!check) fail(err)
}

fun fail_unless(check: Boolean, err: String) =
    fail_if_not(check, err)

fun fail_if_null(o: Any?, err: String) {
    if (o == null) fail(err)
}

inline fun <reified T : Enum<T>> fail_if_not_contains(item: T, vararg items: T, message: () -> String) {
    if (items.none { it == item })
        throw AlertException(message())
}

fun fail_if_not_auctioneer(session: AuSession) {
    if (!session.inRole(AuUserRole.AUCTIONEER))
        fail("Only auctioneers can do this.")
}

/*************
 * numbers:
 * - concept is to be able to take a propert, an error suffix and an optional commands
 * -> add an error if need be
 * -> return the number or null
 */

fun String.trim_commas_and_underscores(): String = trim()
    .replace(",", "")
    .replace("_", "")

fun to_int_or_err(
    s: String?,
    name: String,
    pred: ((Int) -> Boolean) = { true },
    err_fn: ((String?) -> String)
): Int =
    s?.trim_commas_and_underscores()
        ?.toIntOrNull()
        ?.takeIf(pred)
        ?: throw AlertException(err_fn(name))

fun to_int_gt0_or_err(s: String?, name: String): Int =
    to_int_or_err(s, name, ::is_GT_zero, ::add_GT_zero_suffix)

fun to_int_ge0_or_err(s: String?, name: String): Int =
    to_int_or_err(s, name, ::is_GE_zero, ::add_GE_zero_suffix)

fun to_double_or_err(
    s: String?,
    name: String,
    pred: ((Double) -> Boolean) = { true },
    err_fn: ((String?) -> String)
): Double =
    s?.trim_commas_and_underscores()
        ?.toDoubleOrNull()
        ?.takeIf(pred)
        ?: throw AlertException(err_fn(name))


fun EngineCommand.toIntOrError(
    prop: KProperty0<String?>,
    pred: ((Int?) -> Boolean)? = null,
    err_fn: (String) -> String
): Int? =

    prop.get()?.trim_commas_and_underscores()?.toIntOrNull()?.takeIf {
        if (pred != null) pred(it)
        else true
    } ?: // TODO: humanize the property name
    run {
        errors().add(humanize(err_fn(prop.name))) // TODO: humanize the property name
        // TODO: humanize the property name
        null
    }

fun EngineCommand.toLongOrError(
    prop: KProperty0<String?>,
    pred: ((Long?) -> Boolean)? = null,
    err_fn: (String) -> String
): Long? =

    prop.get()?.trim_commas_and_underscores()?.toLongOrNull()?.takeIf {
        if (pred != null) pred(it)
        else true
    } ?: // TODO: humanize the property name
    run {
        errors().add(humanize(err_fn(prop.name))) // TODO: humanize the property name
        // TODO: humanize the property name
        null
    }

fun EngineCommand.toDoubleOrError(
    prop: KProperty0<String?>,
    pred: ((Double?) -> Boolean)? = null,
    err_fn: (String) -> String
): Double? =

    prop.get()?.trim_commas_and_underscores()?.toDoubleOrNull()?.takeIf {
        if (pred != null) pred(it)
        else true
    } ?: // TODO: humanize the property name
    run {
        errors().add(err_fn(humanize(prop.name))) // TODO: humanize the property name
        // TODO: humanize the property name
        null
    }

fun add_GT_zero_suffix(s: String?): String = (s ?: "").plus(" must be a number greater than zero.")
fun add_GE_zero_suffix(s: String?): String = (s ?: "").plus(" must be a number greater than or equal to zero.")

fun is_GT_zero(i: Int?): Boolean = i?.let { it > 0 } ?: false
fun is_GT_zero(i: Long?): Boolean = i?.let { it > 0 } ?: false
fun is_GT_zero(i: Double?): Boolean = i?.let { it > 0 } ?: false

fun is_GE_zero(i: Int?): Boolean = i?.let { it >= 0 } ?: false
fun is_GE_zero(i: Long?): Boolean = i?.let { it >= 0 } ?: false
fun is_GE_zero(i: Double?): Boolean = i?.let { it >= 0 } ?: false


fun EngineCommand.err_unless_Int_GT_zero(prop: KProperty0<String?>): Int? =
    toIntOrError(prop, ::is_GT_zero, ::add_GT_zero_suffix)

fun EngineCommand.err_unless_Long_GT_zero(prop: KProperty0<String?>): Long? =
    toLongOrError(prop, ::is_GT_zero, ::add_GT_zero_suffix)

fun EngineCommand.err_unless_Double_GT_zero(prop: KProperty0<String?>): Double? =
    toDoubleOrError(prop, ::is_GT_zero, ::add_GT_zero_suffix)

fun EngineCommand.err_unless_Int_GE_zero(prop: KProperty0<String?>): Int? =
    toIntOrError(prop, ::is_GE_zero, ::add_GE_zero_suffix)

fun EngineCommand.err_unless_Long_GE_zero(prop: KProperty0<String?>): Long? =
    toLongOrError(prop, ::is_GE_zero, ::add_GE_zero_suffix)

fun EngineCommand.err_unless_Double_GE_zero(prop: KProperty0<String?>): Double? =
    toDoubleOrError(prop, ::is_GE_zero, ::add_GE_zero_suffix)

//inline fun <reified T : Enum<T>?> EngineCommand.enumValueOrErr(prop: KProperty0<String>): T? =
//    toEnumOrNull<T>(prop.get()) ?: run {
//        errors().add(humanize(prop.name) + " not understood.")
//        null
//    }

// newer validation concept:

fun EngineCommand.require_int_GT_zero(s: String, err: String, default: Int = 0): Int =
    s.trim_commas_and_underscores().toIntOrNull()?.takeIf { it > 0 } ?: run {
        errors().add(err)
        default
    }

fun EngineCommand.require_int_GE_zero(s: String, err: String, default: Int = 0): Int =
    s.trim_commas_and_underscores().toIntOrNull()?.takeIf { it >= 0 } ?: run {
        errors().add(err)
        default
    }

fun EngineCommand.require_double_GT_zero(s: String, err: String, default: Double = 0.0): Double =
    s.trim_commas_and_underscores().toDoubleOrNull()?.takeIf { it > 0.0 } ?: run {
        errors().add(err)
        default
    }

fun EngineCommand.require_double_GE_zero(s: String, err: String, default: Double = 0.0): Double =
    s.trim_commas_and_underscores().toDoubleOrNull()?.takeIf { it >= 0.0 } ?: run {
        errors().add(err)
        default
    }


//fun EngineCommand.to_date_or_err(prop: KProperty0<DateTimeValue>): Date? =
fun EngineCommand.to_date_or_err(dt: DateTimeValue?): Date? =
    try {
        dt?.toDate()
    } catch (e: Throwable) {
        e.printStackTrace()
        null
    }
