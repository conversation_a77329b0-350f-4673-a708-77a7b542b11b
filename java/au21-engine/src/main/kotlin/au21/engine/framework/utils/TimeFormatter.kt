// File: TimeFormatter.kt
package au21.engine.framework.utils

import org.joda.time.DateTime
import java.util.*

/**
 * from:
 * - https://ao.ms/human-readable-duration-format-in-java/
 */
object TimeFormatter {

    fun formatDuration(toDate: Date): String? {
        val secs: Int = (toDate.time - DateTime().toDate().time).toInt() / 1_000
        return if (secs < 0)
            null
        else formatDuration(secs)
    }

    fun formatDuration(seconds: Int): String {
        var _secs = seconds
        var res = ""
        val units = intArrayOf(31536000, 86400, 3600, 60, 1)
        val labels = arrayOf("year", "day", "hour", "minute", "second")
        if (_secs == 0) return "now"
        for (i in 0..4) {
            if (_secs >= units[i]) {
                val q = _secs / units[i]
                _secs %= units[i]
                res += ((if (res == "") "" else if (_secs == 0) " and " else ", ")
                        + q + " " + labels[i] + if (q > 1) "s" else "")
            }
        }
        return res
    }

}
