// File: AuFeatures.kt
package au21.engine.framework.features

import jakarta.enterprise.context.ApplicationScoped


@ApplicationScoped
class AuFeatures {
    // NOTE: there is a HeartbeatConfig class which will set the heartbeat too
    // ie: if that is set, then this has no effect
    // TODO: probably merge these (though you have to start the heartbeat
    //  thread if not started)
    var heartbeat = true
}
