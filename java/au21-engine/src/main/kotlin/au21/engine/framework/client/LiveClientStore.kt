// File: LiveClientStore.kt
package au21.engine.framework.client

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.viewmodel.*
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.get_trader
import au21.engine.domain.de.viewmodel.DeAuctionValue
import au21.engine.domain.de.viewmodel.DeMatrixRoundElement
import au21.engine.framework.PageName
import au21.engine.framework.utils.jsonToPrettyFormat
import au21.engine.framework.utils.objToPrettyFormat
import au21.engine.framework.utils.prettyYaml
import au21.engine.framework.utils.toYaml
import au21.engine.framework.utils.to_json
import io.quarkus.logging.Log

/**

See google doc for mapping of commands to store elements

https://docs.google.com/spreadsheets/d/1IjfRGCfKx9D614jguQAQyn8p5ZpePkqA4aqSHVrHa8I/edit#gid=0

Basically, for all requests, we resend the entire store to everyone, except:
- a) time -> just timevalue
- b) message -> just add the message
- c) matrix prior round -> add the elements
- d) auction status -> send auction status

Note: when we set the store:

- Values replace existing values.
- Values replace existing ones with the exception of the matrix.
- the matrix nodes and elements upsert
(so as not to blow away prior rounds)
- we only really need to upsert for current round, so we could just filter out current round and then add the incoming

 */

// TODO: Odd that it doesn't extend AuStore - that seems to be a client-side only class ??
data class LiveClientStore(
    val auction_rows: List<AuctionRowElement>,
    val companies: List<CompanyElement>,
    val counterparty_credits: List<CounterpartyCreditElement>,
    val de_auction: DeAuctionValue?,
    val session_user: SessionUserValue?,
    val time: TimeValue?,
    val users: List<UserElement>,
) {
    companion object {
        val debug = true // TODO make this a quarkus dev feature flag

        fun create(s: AuSession, cache: DbSessionCache): LiveClientStore {
            val role: AuUserRole? = s.user?.role

            return LiveClientStore(
                auction_rows = when (s.page) {
                    PageName.HOME_PAGE ->
                        when (role) {
                            AuUserRole.AUCTIONEER -> cache.auctioneer_row_elements
                            AuUserRole.TRADER -> AuctionRowElement.elements_for_session(
                                cache.auctions,
                                s
                            ) // TODO: would it be quicker to use the AuctionRows already created?
                            else -> emptyList()
                        }
                    else -> emptyList()
                },
                companies = when (role) {
                    AuUserRole.AUCTIONEER -> cache.company_elements
                    else -> emptyList()
                },
                counterparty_credits = when (role) {
                    AuUserRole.AUCTIONEER -> cache.counterpartyCredits
                    AuUserRole.TRADER -> cache.counterpartyCredits.filter { it.seller_id == s.user?.company?.id_str() }
                    else -> emptyList()
                },
                de_auction = when (val de: Auction? = s.auction) {
                    is DeAuction ->
                        when (s.page) {
                            PageName.DE_AUCTIONEER_PAGE -> DeAuctionValue.value_for_auctioneer(
                                de,
                                s,
                                cache.counterpartyCredits.filter { c ->
                                    de.de_trading_companies
                                        .map { it.shortname_at_auction_time }
                                        .contains(c.seller_shortname)
                                }
                            )
                            PageName.DE_TRADER_PAGE ->
                                when (val t = de.get_trader(s)) {
                                    null -> DeAuctionValue.value_for_null_auction
                                    else -> DeAuctionValue.value_for_trader(de, s, t)
                                }
                            else -> DeAuctionValue.value_for_null_auction
                        }
                    else -> DeAuctionValue.value_for_null_auction
                },
                session_user = SessionUserValue.create(s),
                time = cache.time,
                users = when (role) {
                    AuUserRole.AUCTIONEER -> cache.user_elements
                    else -> emptyList()
                }
            ).also {
                if (debug) {
                    Log.info(it.toYaml())
             }
            }
        }

    }
}

class StaleClientStore {
    val stale_de_matrix_rounds: MutableList<DeMatrixRoundElement> = mutableListOf()
}
