// File: misc-utils.kt
package au21.engine.framework.utils

import io.quarkus.logging.Log
import org.joda.time.format.DateTimeFormatter
import org.joda.time.format.ISODateTimeFormat
import java.util.*
import kotlin.math.floor
import kotlin.math.pow

fun String?.is_blank(): Boolean =
    this?.trim()?.isEmpty() ?: true


// safe enums:

inline fun <reified T : Enum<T>> printAllValues() {
    print(enumValues<T>().joinToString { it.name })
}

//inline fun <reified T : Enum<T>?> enumValueOrNull(type: String): T? =
//    try {
//        java.lang.Enum.valueOf(T::class.java, type)
//    } catch (e: Exception) {
//        Log.info(e.message)
//        null
//    }

/*
fun Number.roundToPlaces(places: Int): String {
    require(places >= 0) {"places is ${places} but cannot be less than zero."}
    // TODO: cache of memoize this:
    val format_str = "#." + (0 until places).fold("") { acc: String, next: Int -> acc.plus("#") }
    Log.info(format_str)
    val df = DecimalFormat(format_str)
    return df.format(this).let { if(it.endsWith(".")) it.dropLast(1) else it }
}
*/

// copied and pasted from DefaultGroovyMethods.java !!
fun Double.round(precision: Int): Double =
    floor(this * 10.0.pow(precision.toDouble()) + 0.5) /
            10.0.pow(precision.toDouble())

fun main() {
    (0..10).forEach {
        Log.info(Math.PI.round(it))
    }

}

val iso_date_time_formatter: DateTimeFormatter =
    ISODateTimeFormat.dateTime()

fun String.iso_to_date(): Date? =
    try {
        iso_date_time_formatter.parseDateTime(this).toDate()
    } catch (e: Exception) {
        Log.info(e.message)
        null
    }

fun Date.date_to_iso(): String =
    iso_date_time_formatter.print(this.time)

// Idable:

interface Ideable {
    val id: String
}

// Result, like in JS:

sealed class CBResult<out T> {
    data class CBSuccess<out T>(val data: T) : CBResult<T>()
    data class CBError<out T>(val throwable: Throwable) : CBResult<T>()
}

fun <T> T.asResult() =
    CBResult.CBSuccess(data = this)

fun <T> Throwable.asErrorResult() =
    CBResult.CBError<T>(throwable = this)

// fun duration_ms(start: Long): Int = ((System.nanoTime() - start) / 1_000_000).toInt()

//inline fun <reified T> duration_ms(msg:String? = null, fn: () -> T): T {
//    val start = System.nanoTime()
//    return fn().also {
//        when(msg){
//            null -> println("${duration_ms(start)} ms")
//            else -> println("$msg took ${duration_ms(start)} ms")
//        }
//    }
//}

// example use:
//userProvider.getUsers().subscribe {
//    when (it) {
//        is Result.Success -> handleSuccess(result.data)
//        is Result.Error -> handleError(result.throwable)
//    }
//}

//data class Callback_Result<out T>(
//        val data: T?,
//        val error: Throwable?
//)
//
//fun <T> T.asResult(): Callback_Result<T> {
//    return Callback_Result(data = this, error = null)
//}
//
//fun <T> Throwable.asErrorResult(): Callback_Result<T> {
//    return Callback_Result(data = null, error = this)
//}

///////////////////////
