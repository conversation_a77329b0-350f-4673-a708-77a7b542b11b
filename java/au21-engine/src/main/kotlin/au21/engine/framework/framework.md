## client/ClientsManager.kt

```kotlin
// File: ClientsManager.kt
package au21.engine.framework.client

//import org.eclipse.microprofile.opentracing.Traced
//import io.opentelemetry.instrumentation.annotations.WithSpan
import au21.engine.domain.common.commands.PageSetAction
import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.services.sessions_logged_in
import au21.engine.domain.common.viewmodel.MessageElement
import au21.engine.domain.de.commands.DeRoundControllerAction
import au21.engine.domain.de.viewmodel.DeMatrixRoundElement
import au21.engine.framework.PageName
import au21.engine.framework.client.ClientCommand.*
import au21.engine.framework.client.ClientCommand.StoreCommand.AddElements
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.HeartbeatAction
import au21.engine.framework.commands.interfaces.IAuctionMessage
import au21.engine.framework.commands.interfaces.ISessionsTerminated
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.gzip
import au21.engine.framework.utils.gzipClientCommand
import au21.engine.framework.utils.to_json
import io.quarkus.logging.Log
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.nio.ByteBuffer

/**
 * THIS IS THE SECOND VERSION
 * - for each session, for each property: to-json, gzip, send)
 * ie: none of these optimizations:
 * - 1) skip zipping if json unchanged
 * - 2) cache and share buffers for auctioneers
 * - 3) cache buffers between commands
 */

@ApplicationScoped
class ClientsManager {

    @Inject
    lateinit var socket_handler: SocketHandler

    // for testing:
    var last_action: EngineAction? = null
    var last_stores: List<StoreCommand.SetLiveStore> = emptyList()

    //   var last_update: Long = 0L

    // @Traced
    //@WithSpan
    fun handle(db: AuEntityManager, action: EngineAction) {

        last_action = action

        val is_not_heartbeat: Boolean = action !is HeartbeatAction

        val should_update = true

        if (should_update) {

            // WE'RE NOT GOING TO UPDATE MORE FREQUENTLY THAN 500 ms

            //  val start = System.nanoTime()
            last_stores = session_live_stores(db)
            //  val duration = duration_ms(start)

            if (is_not_heartbeat) {
                Log.info(action.command::class.java.simpleName + ":" + action.command.to_json())
                // Log.info("CLIENT_STORE_CREATION: took = $duration ms, count = ${stores.size}")
            }

            // TODO: might want a thread pool for this:
            last_stores.forEach {
                it.store.session_user?.session_id?.let { sid -> socket_handler.publish(sid, gzip(it.to_json())) }
            }
        }


        // EVENTS:

        fun auction_sessions(a: Auction): List<AuSession> =
            db.sessions_logged_in().filter { a == it.auction }

        when (action) {

            is DeRoundControllerAction -> {
                // TODO: not sure who is calling this?
                val add_matrix: AddElements =
                    DeMatrixRoundElement.add_rounds(action.de, listOf(action.deRound))
                socket_handler.publish(
                    action.session.session_id,
                    gzip(add_matrix.to_json())
                )
            }
            is PageSetAction -> {
                // TODO: this is for the historical rounds??
                if (action.page == PageName.DE_AUCTIONEER_PAGE) {
                    socket_handler.publish(
                        action.session.session_id,
                        gzip(DeMatrixRoundElement.clear_rounds().to_json())
                    )
                }
            }
            else -> {
                // send companies of changed

            }

        }

        // have to do this separately as there can be implementation of more than one interface
        if (action is ISessionsTerminated) {
            action.sessions_terminated.forEach {
                socket_handler.publish(it.sid, gzipClientCommand(TerminateSession(it.reason)))
            }
        }

        if (action is IAuctionMessage) {
            notify(
                socket_handler,
                MessageElement.recipient_sids_for_message(
                    auction_sessions(action.auction),
                    action.auction,
                    action.message
                ),
                MessageElement.create(action.message)
            )
        }

        // if we have a session id then send CommandSuceeded:
        action.session?.let {
            succeeded(socket_handler, listOf(it.session_id))
        }

    }

// START_COMMANDS
// ADD MESSAGE
// TERMINATE
// NOTIFY
// SUCCEEDED

    fun succeeded(socket: SocketHandler, sids: List<String>) {
        gzipClientCommand(CommandSucceeded()).also { buf ->
            sids.forEach { socket.publish(it, buf) }
        }
    }

    fun notify(
        socket: SocketHandler,
        recipient_sids: List<String>,
        m: MessageElement

    ) {
        if (recipient_sids.isEmpty())
            return
        gzipClientCommand(ShowMessage(BrowserMessageKind.NOTIFICATION, listOf(m.message))).also { buf: ByteBuffer ->
            recipient_sids.forEach { socket.publish(it, buf) }
        }

    }

}

//    fun create_store_buffers(
//        db: AuEntityManager,
//        action: EngineAction
//    ): Map<String, ByteBuffer> {
//
//        return duration_ms("SESSION_MANAGER") {
//            val h = SessionStoreHelper(db, action)
//
//            db.sessions_non_terminated().associate { s: AuSession ->
//                val session_store: LiveClientStore = h.session_store(s)
//                val cmd = ClientCommand.StoreCommand.SetLiveStore(session_store)
//                s.session_id to gzip(cmd.to_json())
//            }.toMap()
//        }
//    }

//    ): Map<String, ByteBuffer> {
//
//        val store_map: Map<String, ByteBuffer> =
//            duration_ms("SESSION_MANAGER.HANDLE") {
//                val h = SessionStoreHelper(db, action)
//                db.sessions_non_terminated().associate { s: AuSession ->
//                    val session_store: LiveClientStore = h.session_store(s)
//                    val cmd = ClientCommand.StoreCommand.SetLiveStore(session_store)
//                    s.session_id to gzip(cmd.to_json())
//                }
//            }
//
//        return store_map

//        val sessions: MutableList<Pair<TargetList, ByteBuffer>> = mutableListOf()
//        when (action) {
//            is HeartbeatAction -> {
//
//            }
//            is MessageSendAction -> {
//
//            }
//            is DeFlowControlAction -> {
//
//            }
//            is DeRoundControllerAction -> {
//
//            }
//            else -> {
//            }
//
//
//    }
//
//}
//

```

## client/CommandBuffer.kt

```kotlin
// File: CommandBuffer.kt
package au21.engine.framework.client

import au21.engine.framework.utils.gzip
import au21.engine.framework.utils.to_json
import java.nio.ByteBuffer

open class CommandBuffer<T : ClientCommand> {

    var json: String? = null
        private set

    var buffer: ByteBuffer? = null
        private set

    fun set_buffer(cmd: ClientCommand?): Boolean = // HAS_CHANGED
        when (val new_json = cmd.to_json()) {
            json -> false
            else -> {
                json = new_json
                buffer = gzip(new_json)
                true
            }
        }

    /**
     * Initializes the session, optionally first updating it
     * - error if no update function and no existing buffer
     */
    fun init_session(
        socket: SocketHandler,
        session_id: String?,
        cmd_provider: (() -> T)? = null,
    ): Boolean =
    // if no json_provider given then we use the last buffer
        // - and it's an error for there not to be a last buffer!!
        session_id?.let { sid ->
            when (cmd_provider) {
                null -> {
                    buffer?.let { buf -> socket.publish(sid, buf) }
                        ?: throw Error("ERROR: Init expected buffer not to be null")
                    false
                }
                else -> {
                    set_buffer(cmd_provider()).also { changed ->
                        if (changed) {
                            buffer?.let { socket.publish(sid, it) }
                        }
                    }
                }
            }
        } ?: false


    /**
     * Only runs when the command actually changes the underlying buffer
     * - so: CANNOT USE FOR INIT
     */
    open fun update_sessions(
        socket: SocketHandler,
        sids: Array<String>,
        force: Boolean,
        cmd_provider: () -> T,
    ) {
        fun publish(){
            sids.forEach { buffer?.let { buff -> socket.publish(it, buff) } }
        }
        // if set_buffer_if -> publish
        // else if force -> publish
        when {
            sids.isEmpty() -> return
            set_buffer(cmd_provider()) -> publish()
            force -> publish()
        }
    }
}

```

## client/CommandsHelper.kt

```kotlin
// File: CommandsHelper.kt
package au21.engine.framework.client

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.model.Company
import au21.engine.domain.common.services.sessions_non_terminated
import au21.engine.domain.common.viewmodel.*
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeRound
import au21.engine.domain.de.viewmodel.DeMatrixRoundElement
import au21.engine.framework.client.ClientCommand.StoreCommand.SetLiveStore
import au21.engine.framework.database.AuEntityManager

class DbSessionCache(db: AuEntityManager) {
    val sessions_non_terminated: List<AuSession> by lazy {
        db.sessions_non_terminated()
    }

    val sessions_logged_in: List<AuSession> by lazy {
        sessions_non_terminated.filter { !it.isTerminated() }
    }

    val time: TimeValue = TimeValue.now("Houston")

    // AUCTION ROWS:

    val auctions: List<Auction> by lazy {
        db.findAll()
    }

    // TODO: these don't need to be created separately for auctioneer:
    val auctioneer_row_elements: List<AuctionRowElement> by lazy {
        auctions.map { AuctionRowElement.create(it) } // , true) }
    }

    val companies: List<Company> by lazy { db.findAll() }

    val company_elements: List<CompanyElement> by lazy {
        companies.map { CompanyElement.create(it) }
    }

    val counterpartyCredits: List<CounterpartyCreditElement> by lazy {
        CounterpartyCreditElement.all(companies)
    }

    val user_elements: List<UserElement> by lazy {
        UserElement.user_elements(db, sessions_logged_in)
    }

    // MATRIX ROUNDS

    val de_matrix_rounds: MutableMap<DeAuction, MutableList<DeMatrixRoundElement>> = mutableMapOf()

    // TODO: cache this
    fun get_matrix_round(de: DeAuction, round_number: Int): DeMatrixRoundElement =
        de.rounds.find { it.number == round_number }?.let { r: DeRound ->
            de_matrix_rounds.getOrPut(de) {
                mutableListOf(DeMatrixRoundElement.create(de.lastround(), de.settings.cost_multiplier))
            }.let { matrix_rounds ->
                matrix_rounds.find { it.round_number == round_number }
                    ?: run {
                        DeMatrixRoundElement.create(r, de.settings.cost_multiplier).also {
                            matrix_rounds.add(it)
                        }
                    }
            }
        } ?: throw Error("get_matrix_round(): No round found with number = $round_number")
}

fun session_live_stores(db: AuEntityManager): List<SetLiveStore> {

    val cache = DbSessionCache(db)

    return cache.sessions_non_terminated.map { s: AuSession ->
        SetLiveStore(LiveClientStore.create(s, cache))
    }

}


```

## client/LiveClientStore.kt

```kotlin
// File: LiveClientStore.kt
package au21.engine.framework.client

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.viewmodel.*
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.get_trader
import au21.engine.domain.de.viewmodel.DeAuctionValue
import au21.engine.domain.de.viewmodel.DeMatrixRoundElement
import au21.engine.framework.PageName

/**

See google doc for mapping of commands to store elements

https://docs.google.com/spreadsheets/d/1IjfRGCfKx9D614jguQAQyn8p5ZpePkqA4aqSHVrHa8I/edit#gid=0

Basically, for all requests, we resend the entire store to everyone, except:
- a) time -> just timevalue
- b) message -> just add the message
- c) matrix prior round -> add the elements
- d) auction status -> send auction status

Note: when we set the store:

- Values replace existing values.
- Values replace existing ones with the exception of the matrix.
- the matrix nodes and elements upsert
(so as not to blow away prior rounds)
- we only really need to upsert for current round, so we could just filter out current round and then add the incoming

 */

// TODO: Odd that it doesn't extend AuStore - that seems to be a client-side only class ??
data class LiveClientStore(
    val auction_rows: List<AuctionRowElement>,
    val companies: List<CompanyElement>,
    val counterparty_credits: List<CounterpartyCreditElement>,
    val de_auction: DeAuctionValue?,
    val session_user: SessionUserValue?,
    val time: TimeValue?,
    val users: List<UserElement>,
) {
    companion object {
        val debug = false

        fun create(s: AuSession, cache: DbSessionCache): LiveClientStore {
            val role: AuUserRole? = s.user?.role

            return LiveClientStore(
                auction_rows = when (s.page) {
                    PageName.HOME_PAGE ->
                        when (role) {
                            AuUserRole.AUCTIONEER -> cache.auctioneer_row_elements
                            AuUserRole.TRADER -> AuctionRowElement.elements_for_session(
                                cache.auctions,
                                s
                            ) // TODO: would it be quicker to use the AuctionRows already created?
                            else -> emptyList()
                        }
                    else -> emptyList()
                },
                companies = when (role) {
                    AuUserRole.AUCTIONEER -> cache.company_elements
                    else -> emptyList()
                },
                counterparty_credits = when (role) {
                    AuUserRole.AUCTIONEER -> cache.counterpartyCredits
                    AuUserRole.TRADER -> cache.counterpartyCredits.filter { it.seller_id == s.user?.company?.id_str() }
                    else -> emptyList()
                },
                de_auction = when (val de: Auction? = s.auction) {
                    is DeAuction ->
                        when (s.page) {
                            PageName.DE_AUCTIONEER_PAGE -> DeAuctionValue.value_for_auctioneer(
                                de,
                                s,
                                cache.counterpartyCredits.filter { c ->
                                    de.de_trading_companies
                                        .map { it.shortname_at_auction_time }
                                        .contains(c.seller_shortname)
                                }
                            )
                            PageName.DE_TRADER_PAGE ->
                                when (val t = de.get_trader(s)) {
                                    null -> DeAuctionValue.value_for_null_auction
                                    else -> DeAuctionValue.value_for_trader(de, s, t)
                                }
                            else -> DeAuctionValue.value_for_null_auction
                        }
                    else -> DeAuctionValue.value_for_null_auction
                },
                session_user = SessionUserValue.create(s),
                time = cache.time,
                users = when (role) {
                    AuUserRole.AUCTIONEER -> cache.user_elements
                    else -> emptyList()
                }
            ).also {
                if (debug) println()
            }
        }

    }
}

class StaleClientStore {
    val stale_de_matrix_rounds: MutableList<DeMatrixRoundElement> = mutableListOf()
}

```

## client/SocketHandler.kt

```kotlin
// File: SocketHandler.kt
package au21.engine.framework.client

import au21.engine.domain.common.commands.ClientSocketCommand
import au21.engine.domain.common.model.AuSession
import au21.engine.framework.commands.EngineCommandEnvelope
import au21.engine.framework.utils.to_json
import io.quarkus.logging.Log
import io.vertx.core.eventbus.EventBus
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.websocket.*
import jakarta.websocket.server.PathParam
import jakarta.websocket.server.ServerEndpoint
import java.nio.ByteBuffer

// from:
// - https://quarkus.io/guides/websockets


@ServerEndpoint("/socket/{session_id}")
@ApplicationScoped
class SocketHandler {

    companion object {
        const val TOPIC_SOCKET_COMMAND = "TOPIC_SOCKET_COMMAND"
    }

//    @Inject
//    lateinit var tracer: Tracer

    @Inject
    lateinit var bus: EventBus

    val sessions: MutableMap<String, Session> = mutableMapOf()

    fun publish(session_id: String?, zipped: ByteBuffer?) { // byteBuffer: ByteBuffer){
        if (session_id == null || zipped == null)
            return
        try {
            sessions[session_id]
                ?.asyncRemote
                ?.sendBinary(zipped)
        } catch (e: Throwable) {
            e.printStackTrace()
        }
    }

    @OnOpen
    fun onOpen(s: Session, @PathParam("session_id") session_id: String) {
        Log.info("Socket opened, session_id = $session_id")
        sessions[session_id] = s
        val params: Map<String, List<String>> = s.requestParameterMap

        val browser_name: String = params["browser_name"]?.get(0) ?: ""
        val browser_version: String = params["browser_version"]?.get(0) ?: ""
        val browser_os: String = params["browser_os"]?.get(0) ?: ""

        bus.send(
            TOPIC_SOCKET_COMMAND,
            EngineCommandEnvelope(
                session_id = session_id,
                command = ClientSocketCommand(
                    sid = session_id,
                    state = AuSession.ClientSocketState.OPENED,
                    browser_name = browser_name,
                    browser_version = browser_version,
                    browser_os = browser_os
                )
            ).to_json()
        )
    }

    @OnClose
    fun onClose(session: Session?, @PathParam("session_id") session_id: String) {
        Log.info("Socket closed, session_id = $session_id")
        sessions.remove(session_id)
        bus.send(
            TOPIC_SOCKET_COMMAND,
            EngineCommandEnvelope(
                session_id = session_id,
                command = ClientSocketCommand(
                    sid = session_id,
                    state = AuSession.ClientSocketState.CLOSED)
            ).to_json()
        )
        //Log.trace("SocketHandler.onClose(session_id=$session_id)")
    }

    @OnError
    fun onError(session: Session?, @PathParam("session_id") session_id: String, throwable: Throwable) {
        Log.error("Socket error, session_id = $session_id, error = ${throwable.message}")
        // do we need something like on client here? eg:
        // - session?.close()
        //Log.trace("SocketHandler.onError: session_id=$session_id, error=${throwable.message}")
        println("error: ${throwable.message}")
    }

    @OnMessage
    fun onMessage(message: String, @PathParam("session_id") session_id: String) {
        // tinylog: Log.info("Socket message, session_id = $session_id, message = $message")
        Log.info("Socket message, message =$message")
        bus.send(TOPIC_SOCKET_COMMAND, message)
    }

}

//@ServerEndpoint("/socket/{session_id}")
//@ApplicationScoped
//class SocketHandler {
//    @Inject
//    var bus: EventBus? = null
//
//        companion object {
//        const val TOPIC_SOCKET_COMMAND = "TOPIC_SOCKET_COMMAND"
//    }
//
//    private val sessions: MutableMap<String, Session> = ConcurrentHashMap()
//
//    @OnOpen
//    fun onOpen(session: Session, @PathParam("session_id") sessionId: String) {
//        Log.info("Socket opened, session_id = $sessionId")
//        sessions[sessionId] = session
//    }
//
//    @OnClose
//    fun onClose(session: Session?, @PathParam("session_id") sessionId: String) {
//        Log.info("Socket closed, session_id = $sessionId")
//        sessions.remove(sessionId)
//    }
//
//    @OnError
//    fun onError(session: Session?, @PathParam("session_id") sessionId: String, throwable: Throwable) {
//        Log.error("Socket error, session_id = " + sessionId + ", error = " + throwable.message)
//    }
//
//    @OnMessage
//    fun onMessage(message: String, @PathParam("session_id") sessionId: String) {
//        Log.info("Socket message, session_id = $sessionId, message = $message")
//        bus!!.send("TOPIC_SOCKET_COMMAND", message)
//    }
//
//    fun publish(sessionId: String, gzip: ByteBuffer) {
//        TODO("Not yet implemented")
//    }
//}

```

## client/client-command-types.kt

```kotlin
// File: client-command-types.kt
package au21.engine.framework.client

/**
 *  THIS FILE NEEDS TO BE IN SYNC WITH:
 *  au12-engine/src/main/kotlin/au21/engine/framework/commands/client/client-command-types.kt
 *  au21-frontend/libs/client-connector/src/lib/client-command-types.ts
 */


import net.pearx.kasechange.toSnakeCase


class EngineTransaction(
    val client_command_maps: MutableList<ClientCommandSessionsMap>,
    val engine_command_json: String?, // null in case of heartbeat
    val engine_command_name: String,
    val isHeartbeat: Boolean,
    val session_id: String?,
    var duration_ms: Int,
    var has_alert: Boolean,
    var has_session_differ_error: Boolean,
    var requests_since_last_restart: Int
)

class ClientCommandSessionsMap(
    val command: ClientCommand,
    val sessionIds: List<String>
)

class SessionClientCommandsEnvelope(
    val session_id: String,
    val envelope_count_since_last_restart: Int, // used by FayeServer
    val commands: MutableList<ClientCommand>
)

sealed class ClientCommand {

    /**
     * These were the old ones:
     *    a) Browser Commands (3):
     *      - ShowMessage
     *      - CommandSucceeded
     *      - TerminateSession
     *    b) Store Commands (6):
     *      - SetValue
     *      - DeleteValue // added June 16, 2021
     *      - SetElement
     *      - SetElements
     *      - RemoveElement
     *      - RemoveElements
     *      - ClearElements
     *
     *  But for now I think we just need:
     *  a) SetLiveStore (needs to be changed on client)
     *  b) SetElements (if null, then clear array)
     *    - currently only SetElements I can think of is:
     *      - SetElements("de-prev-rounds", Array<Element>")
     *    - so we need and Element (id) interface
     *    - and probably (like messages), the action can implement SetsElement
     *
     */

    val command: String = this::class.java.simpleName

    // class StartStoreCommands : ClientCommand()
    class CommandSucceeded : ClientCommand()

    class ShowMessage(
        val browser_message_kind: BrowserMessageKind,
        val message: List<String>
    ) : ClientCommand()

    class TerminateSession(val message: String?) : ClientCommand()

    class NetworkDown : ClientCommand()
    class NetworkUp : ClientCommand()

    // class ClearStore : ClientCommand()

    sealed class StoreCommand : ClientCommand() {

        class SetLiveStore(val store: LiveClientStore) : StoreCommand()

        class AddElements(
            element_class: Class<out StoreElement>,
            val elements: List<StoreElement>? // NULL means clear the array, [], means add nothing
        ) : StoreCommand(){
            val path:String = element_class.simpleName
        }

       // class DeMatrixRoundAdd(val de_matrix_round: DeMatrixRoundElement) : StoreCommand()

        //       class AddMessage(val message: MessageElement) : StoreCommand()

//        class SetAuctionRows(val auction_rows: List<AuctionRowElement>) : StoreCommand()
//
//        class SetCompanies(val companies: List<CompanyElement>) : StoreCommand()
//
//        class SetCounterpartyCredits(val credits: List<CounterpartyCreditElement>) : StoreCommand()
//
//        class SetDeAuction(val de_auction: DeAuctionValue) : StoreCommand()
//
//        class SetDeStatus(val de_status: DeCommonStatusValue?) : StoreCommand() {
//            constructor(de: DeAuction) : this(DeCommonStatusValue(de))
//        }
//
//        class SetSessionUser(val session_user: SessionUserValue) : StoreCommand() {
//            constructor(s: AuSession) : this(SessionUserValue(s))
//        }
//
//        class SetTime(val time: TimeValue) : StoreCommand()
//
//        class SetUsers(val users: List<UserElement>) : StoreCommand()

    }

}

/**
 * AuSession Store Elements and Values
 * - client stores have either Values (objects), or arrays of Elements:
 */


interface StoreValue // TODO: not sure we still use this?

interface StoreElement {
    val id: String
}

// TODO: used by generator (I think)?
fun Class<out StoreElement>?.element_path(): String =
    this?.simpleName?.removeSuffix("Element")?.toSnakeCase()?.let {
        if (it.endsWith("y"))
            it.removeSuffix("y") + "ies"
        else
            it + "s"
    } ?: ""

fun Class<out StoreValue>?.value_path(): String =
    this?.simpleName?.removeSuffix("Value")?.toSnakeCase() ?: ""

enum class BrowserMessageKind {
    ALERT, NOTIFICATION
}

enum class BrowserMessageIcon {
    SUCCESS,
    INFO,
    WARNING,
    AUCTIONEER_MESSAGE,
    TRADER_MESSAGE,
    SYSTEM_MESSAGE,
    ORDER_CONFIRMATION
}

```

## commands/AuSessionAction.kt

```kotlin
// File: AuSessionAction.kt
package au21.engine.framework.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.session_by_sid
import au21.engine.framework.database.AuEntityManager
import jakarta.inject.Inject

abstract class AuSessionAction {

    @Inject
    lateinit var db: AuEntityManager

    val errors: MutableList<String> = mutableListOf()

    abstract fun mutate()

    // must be provided, and doesn't exist on heartbeats !!
    // TODO: needs some refactoring possibly
    abstract val session_id: String

    open val session: AuSession by lazy {
        db.session_by_sid(session_id)
            ?: throw AlertException("session not found: $session_id.")
    }
}

```

## commands/EngineAction.kt

```kotlin
// File: EngineAction.kt
package au21.engine.framework.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.framework.database.AuEntityManager

//@Dependent
interface EngineAction {
    val command: EngineCommand
    val db: AuEntityManager
    val session: AuSession?

   // @Traced
    fun mutate()
}

```

## commands/EngineCommand.kt

```kotlin
// File: EngineCommand.kt
package au21.engine.framework.commands

import au21.engine.framework.database.AuEntity
import au21.engine.framework.database.AuEntityManager
import com.fasterxml.jackson.annotation.JsonIgnore
import org.joda.time.DateTime
import java.util.*
import javax.persistence.Entity

abstract class EngineCommand {

    // mar 26, 2021:
    // have to do it this way because of GSON deserialization

    @Transient
    @JsonIgnore
    private var _errors: MutableList<String>? = null // for serialization

    fun errors(): MutableList<String> =
        _errors ?: run {
            mutableListOf<String>().also {
                _errors = it
            }
        }

    //@Traced
    abstract fun validate(db: AuEntityManager, session_id: String? = null): EngineAction

}


/**
 * Creating a separate entity to save the command because if we extend EngineCommand with AuEntity then:
 * - a) client gets the various AuENtity props (id, deleteed) - though we could probably remove from generator
 * - b) we can't use vals with ObjectDB
 *
 */
@Entity
class CommandJson(
    var command_json: String,
    var timestamp: Date = DateTime().toDate()
) : AuEntity()

```

## commands/EngineCommandEnvelope.kt

```kotlin
// File: EngineCommandEnvelope.kt
package au21.engine.framework.commands

/**
 * Note: marking members as private will stop ts-generator from outputing them!
 * but: then we need to have public accessor methods because 'protected'
 * doesn't work with Kotlin extension methods,
 * see: https://stackoverflow.com/questions/33852770/protected-members-not-accessible-in-extension-functions
 */

class EngineCommandEnvelope(
    val session_id: String,
    val command: EngineCommand
) {
    val simplename:String = command.javaClass.simpleName
    val classname:String = command.javaClass.canonicalName
}

```

## commands/HeartbeatAction.kt

```kotlin
// File: HeartbeatAction.kt
package au21.engine.framework.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuSession.SessionTerminationReason.SERVER_SWEPT_STALE_SESSION
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.services.open_auctions
import au21.engine.domain.common.services.sessions_non_terminated
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeFlowControlType
import au21.engine.domain.de.services.state.DeControlValidator
import au21.engine.domain.de.services.state.DeControlValidator.MUTATE
import au21.engine.domain.de.services.state.DeMutator
import au21.engine.framework.commands.interfaces.ISessionsTerminated
import au21.engine.framework.commands.interfaces.SessionTermination
import au21.engine.framework.database.AuEntityManager
import org.joda.time.DateTime

const val SOCKET_CLOSED_TIMEOUT_SECONDS = 15

//@ApplicationScoped
//class OpenAuctionHolder{
//    @Inject
//    lateinit var db:AuEntityManager
//
//    val open_auctions = mutableListOf<Auction>()
//
//    @PostConstruct
//    fun postConstruct(){
//        open_auctions.addAll(db.findAll())
//    }
//}

interface IOpenAuctions {
    val open_auctions: List<Auction>
}

class HeartbeatCommand : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?) =
        //HeartbeatAction(this, db, null, db.open_auctions())
        HeartbeatAction(this, db, null, db.open_auctions())

    companion object {
        val instance = HeartbeatCommand()
    }
}

class HeartbeatAction(
    override val command: HeartbeatCommand,
    override val db: AuEntityManager,
    override val session: AuSession? = null,
    override val open_auctions: List<Auction>
) : EngineAction, ISessionsTerminated, IOpenAuctions {

    override val sessions_terminated = mutableListOf<SessionTermination>()

    val de_auctions_ticked: MutableList<DeAuction> = mutableListOf()
    val de_auctions_round_closed: MutableList<DeAuction> = mutableListOf()

    override fun mutate() {
        //    open_de_auctions.forEach { de -> handle_de_auction(de, DateTime()) }

        val threshold = DateTime().toDate().time - (SOCKET_CLOSED_TIMEOUT_SECONDS * 1_000)

        db.sessions_non_terminated()
            .filter {
                when (val t = it.socket_last_closed) {
                    null -> false
                    else -> t.time < threshold  // ie: Was last closed earlier than the threshold
                }
            }
            .onEach { s: AuSession ->
                terminate_session(db, s, SERVER_SWEPT_STALE_SESSION)
            }

        open_auctions.forEach { a: Auction ->
            when(a){
                is DeAuction ->
                    if (DeControlValidator.validate(a, DeFlowControlType.HEARTBEAT) == MUTATE) {
                        DeMutator.mutate(a, DeFlowControlType.HEARTBEAT, null)
                        db.save(a)
                    }
            }
        }

    }

}

```

## commands/command-validators.kt

```kotlin
// File: command-validators.kt
package au21.engine.framework.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.viewmodel.DateTimeValue
import au21.engine.framework.utils.is_blank
import java.util.*
import kotlin.reflect.KProperty0

fun humanize(s: String): String = s.replace("_", " ").replaceFirstChar { it.uppercase() }

fun EngineCommand.err_if(pred: Boolean, err: String) {
    if (pred) this.errors().add(err)
}

fun EngineCommand.err_unless(pred: Boolean, err: String) {
    if (!pred) errors().add(err)
}


fun EngineCommand.err_if_blank(prop: KProperty0<String?>, name: String? = null): String? =
    prop.get().also {
        if (it.is_blank())
            errors().add("${humanize(name ?: prop.name)} cannot be blank.")
    }?.trim()

fun EngineCommand.err_if_null(o: Any?, err: String) {
    if (o == null) errors().add(err)
}


fun EngineCommand.fail_if_errors() {
    if (this.errors().isNotEmpty()) {
        // changed when AlertException changed:
        // throw AlertException(*errors().toTypedArray())
        throw AlertException(errors().joinToString(separator = "\n"))
    }
}

fun fail(err: String): Nothing =
    throw AlertException(err)

fun fail_if(check: Boolean, err: String) {
    if (check) fail(err)
}

fun fail_if_not(check: Boolean, err: String) {
    if (!check) fail(err)
}

fun fail_unless(check: Boolean, err: String) =
    fail_if_not(check, err)

fun fail_if_null(o: Any?, err: String) {
    if (o == null) fail(err)
}

inline fun <reified T : Enum<T>> fail_if_not_contains(item: T, vararg items: T, message: () -> String) {
    if (items.none { it == item })
        throw AlertException(message())
}

fun fail_if_not_auctioneer(session: AuSession) {
    if (!session.inRole(AuUserRole.AUCTIONEER))
        fail("Only auctioneers can do this.")
}

/*************
 * numbers:
 * - concept is to be able to take a propert, an error suffix and an optional commands
 * -> add an error if need be
 * -> return the number or null
 */

fun String.trim_commas_and_underscores(): String = trim()
    .replace(",", "")
    .replace("_", "")

fun to_int_or_err(
    s: String?,
    name: String,
    pred: ((Int) -> Boolean) = { true },
    err_fn: ((String?) -> String)
): Int =
    s?.trim_commas_and_underscores()
        ?.toIntOrNull()
        ?.takeIf(pred)
        ?: throw AlertException(err_fn(name))

fun to_int_gt0_or_err(s: String?, name: String): Int =
    to_int_or_err(s, name, ::is_GT_zero, ::add_GT_zero_suffix)

fun to_int_ge0_or_err(s: String?, name: String): Int =
    to_int_or_err(s, name, ::is_GE_zero, ::add_GE_zero_suffix)

fun to_double_or_err(
    s: String?,
    name: String,
    pred: ((Double) -> Boolean) = { true },
    err_fn: ((String?) -> String)
): Double =
    s?.trim_commas_and_underscores()
        ?.toDoubleOrNull()
        ?.takeIf(pred)
        ?: throw AlertException(err_fn(name))


fun EngineCommand.toIntOrError(
    prop: KProperty0<String?>,
    pred: ((Int?) -> Boolean)? = null,
    err_fn: (String) -> String
): Int? =

    prop.get()?.trim_commas_and_underscores()?.toIntOrNull()?.takeIf {
        if (pred != null) pred(it)
        else true
    } ?: // TODO: humanize the property name
    run {
        errors().add(humanize(err_fn(prop.name))) // TODO: humanize the property name
        // TODO: humanize the property name
        null
    }

fun EngineCommand.toLongOrError(
    prop: KProperty0<String?>,
    pred: ((Long?) -> Boolean)? = null,
    err_fn: (String) -> String
): Long? =

    prop.get()?.trim_commas_and_underscores()?.toLongOrNull()?.takeIf {
        if (pred != null) pred(it)
        else true
    } ?: // TODO: humanize the property name
    run {
        errors().add(humanize(err_fn(prop.name))) // TODO: humanize the property name
        // TODO: humanize the property name
        null
    }

fun EngineCommand.toDoubleOrError(
    prop: KProperty0<String?>,
    pred: ((Double?) -> Boolean)? = null,
    err_fn: (String) -> String
): Double? =

    prop.get()?.trim_commas_and_underscores()?.toDoubleOrNull()?.takeIf {
        if (pred != null) pred(it)
        else true
    } ?: // TODO: humanize the property name
    run {
        errors().add(err_fn(humanize(prop.name))) // TODO: humanize the property name
        // TODO: humanize the property name
        null
    }

fun add_GT_zero_suffix(s: String?): String = (s ?: "").plus(" must be a number greater than zero.")
fun add_GE_zero_suffix(s: String?): String = (s ?: "").plus(" must be a number greater than or equal to zero.")

fun is_GT_zero(i: Int?): Boolean = i?.let { it > 0 } ?: false
fun is_GT_zero(i: Long?): Boolean = i?.let { it > 0 } ?: false
fun is_GT_zero(i: Double?): Boolean = i?.let { it > 0 } ?: false

fun is_GE_zero(i: Int?): Boolean = i?.let { it >= 0 } ?: false
fun is_GE_zero(i: Long?): Boolean = i?.let { it >= 0 } ?: false
fun is_GE_zero(i: Double?): Boolean = i?.let { it >= 0 } ?: false


fun EngineCommand.err_unless_Int_GT_zero(prop: KProperty0<String?>): Int? =
    toIntOrError(prop, ::is_GT_zero, ::add_GT_zero_suffix)

fun EngineCommand.err_unless_Long_GT_zero(prop: KProperty0<String?>): Long? =
    toLongOrError(prop, ::is_GT_zero, ::add_GT_zero_suffix)

fun EngineCommand.err_unless_Double_GT_zero(prop: KProperty0<String?>): Double? =
    toDoubleOrError(prop, ::is_GT_zero, ::add_GT_zero_suffix)

fun EngineCommand.err_unless_Int_GE_zero(prop: KProperty0<String?>): Int? =
    toIntOrError(prop, ::is_GE_zero, ::add_GE_zero_suffix)

fun EngineCommand.err_unless_Long_GE_zero(prop: KProperty0<String?>): Long? =
    toLongOrError(prop, ::is_GE_zero, ::add_GE_zero_suffix)

fun EngineCommand.err_unless_Double_GE_zero(prop: KProperty0<String?>): Double? =
    toDoubleOrError(prop, ::is_GE_zero, ::add_GE_zero_suffix)

//inline fun <reified T : Enum<T>?> EngineCommand.enumValueOrErr(prop: KProperty0<String>): T? =
//    toEnumOrNull<T>(prop.get()) ?: run {
//        errors().add(humanize(prop.name) + " not understood.")
//        null
//    }

// newer validation concept:

fun EngineCommand.require_int_GT_zero(s: String, err: String, default: Int = 0): Int =
    s.trim_commas_and_underscores().toIntOrNull()?.takeIf { it > 0 } ?: run {
        errors().add(err)
        default
    }

fun EngineCommand.require_int_GE_zero(s: String, err: String, default: Int = 0): Int =
    s.trim_commas_and_underscores().toIntOrNull()?.takeIf { it >= 0 } ?: run {
        errors().add(err)
        default
    }

fun EngineCommand.require_double_GT_zero(s: String, err: String, default: Double = 0.0): Double =
    s.trim_commas_and_underscores().toDoubleOrNull()?.takeIf { it > 0.0 } ?: run {
        errors().add(err)
        default
    }

fun EngineCommand.require_double_GE_zero(s: String, err: String, default: Double = 0.0): Double =
    s.trim_commas_and_underscores().toDoubleOrNull()?.takeIf { it >= 0.0 } ?: run {
        errors().add(err)
        default
    }


//fun EngineCommand.to_date_or_err(prop: KProperty0<DateTimeValue>): Date? =
fun EngineCommand.to_date_or_err(dt: DateTimeValue?): Date? =
    try {
        dt?.toDate()
    } catch (e: Throwable) {
        e.printStackTrace()
        null
    }

```

## commands/controller.kt

```kotlin
// File: controller.kt
package au21.engine.framework.commands

//import io.opentracing.Span
//import io.opentracing.Tracer
//import org.eclipse.microprofile.opentracing.Traced
//import org.eclipse.microprofile.opentracing.Traced
import au21.engine.framework.database.AuEntityManager
import io.quarkus.logging.Log
import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.QueryParam


/**
 * TODO: None of these work outside of Quarkus requests:
 * - @RequestScoped
 * - @PostConstruct
 * - @PreDestroy
 * We could go: RedisClient -> HTTP possibly
 *
 * POSSIBLE SOLUTIONS:
 * a) figure out how to make request-scoped work with Redis - unlikely
 * b) change to a factory: question about how to get lifecycle working
 * c) send messages via beans
 * d) have the backend actually be an http app
 *  - how to push to faye? sse ??
 *  see: https://www.one-tab.com/page/c5oJoidvTNGUiH9jHpGwUA
 */


// TODO: do we need to add @RequestScoped here, or is it assumed? We should test the AuEntityManager
//@Traced(false)
@Path("/ENGINE_INPUT_CHANNEL")
class EngineCommandController {
//
//    @Inject
//    lateinit var bus: EventBus

   // private val LOG: org.jboss.logging.Logger = Logger.getLogger (EngineCommandController::class.java)

    @Inject
    lateinit var handler: EngineCommandHandler

    /**
     * This db instance is create per request:
     * TODO: what happens if there are multiple requests, each with their own db instance??
     */
    @Inject
    lateinit var db: AuEntityManager

  //  @Inject
   // lateinit var tracer: Tracer


    // @Traced
    @GET
    @Synchronized
    fun handle(@QueryParam("message") message: String): String {
        //TracerConfig.addHostTag(tracer.activeSpan())
        try {

            return when (message) {
                "TICK" -> handler.handle(db, message)
                else -> {
                   // duration_ms("ENGINE_COMMAND_HANDLER") {
                        handler.handle(db, message)
                   // }
                }
            }

        } catch (t: Throwable) {
            t.printStackTrace()
            t.message?.let {
                Log.error(it)
               // LOG.error(it)
            }
            t.cause?.message?.let {
                Log.error("caused by: $it")
               // LOG.error(it)
            }
            return t.cause?.message ?: t.message ?: "Error"
        }
    }

}


//    var registry: MeterRegistry? = null
//    var highestPrime = LongAccumulator({ a: Long, b: Long ->
//        java.lang.Long.max(
//            a,
//            b
//        )
//    }, 0)
//
//    fun highestObservedPrimeNumber(): Long {
//        return highestPrime.get()
//    }

//    fun EngineCommandController(registry: MeterRegistry) {
//      //  this.registry = registry
//
//        // Create a gauge that uses the highestPrimeNumberSoFar method
//        // to obtain the highest observed prime number
////        registry.gauge(
////            "prime.number.max",
////             highestObservedPrimeNumber()
////        )
//    }


```

## commands/deserializer.kt

```kotlin
// File: deserializer.kt
package au21.engine.framework.commands

import com.google.gson.Gson
import jakarta.enterprise.context.ApplicationScoped

@ApplicationScoped
class EngineCommandDeserializer {

    //val mapper: ObjectMapper = jacksonObjectMapper()
    //  .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)

    val gson = Gson()

//    val engine_command_map: Map<String, Class<out EngineCommand>> =
//        get_sub_classes<EngineCommand>().let { commands ->
//            var error = false
//            commands.map { it.simpleName }
//                .groupingBy { it }
//                .eachCount()
//                .forEach { (key: String, count: Int) ->
//                    if (count > 1) {
//                        error = true
//                        Log.error(
//                            "Cannot have more than one EngineCommand with the same simplename: $key"
//                        )
//                    }
//                }
//            if (error)
//                throw Error("more than one EngineCommand with the same simplename")
//
//            if(commands.isEmpty())
//                throw Error("no EngineCommand subclasses found")
//
//            commands.map {
//                @Suppress("UNCHECKED_CAST")
//                it.simpleName to it as Class<out EngineCommand>
//            }.toMap()
//        }

    //@Traced
    fun to_command(command_json: Any, classname: String): EngineCommand {
        //Logger.trace("looking for class: $classname")
        //Logger.trace("Class.forname($classname) = ${Class.forName(classname)}")
        return gson.fromJson(command_json.toString(), Class.forName(classname)) as EngineCommand
    }

//        if (!has_inited_jackson) {
//            has_inited_jackson = true
        // This works, but doesn't seem that db's cdi annotations are called:
//    mapper.setInjectableValues(InjectableValues.Std().apply {
//        addValue(AuEntityManager::class.java, db)
//    })
//    mapper.configure(
//        JsonGenerator.Feature.AUTO_CLOSE_JSON_CONTENT, true
//    )
//        }


//            val version: String = toString("version")
//                ?: throw Exception("request has no version")
//                    if(version !=expected_version.version)
//                        throw Exception("request version should be: ${expected_version.version} but is: $version")

        // Log.info(engine_command_map)
//
//        val engine_command_map: Map<String, Class<out EngineCommand>> =
//            mutableMapOf<String, Class<out EngineCommand>>().apply {
//                get_sub_classes<EngineCommand>().forEach {
//                    put(it.simpleName, it)
//                }
//            }
//
//        val command_class: Class<out EngineCommand> = engine_command_map[simplename]
//            ?: throw Exception("command class not found: $simplename " + engine_command_map.entries.map {
//                it.key + "=" + it.value
//            }.joinToString { "," })

        /** (3) command instance
         *
         */

        // jackson
//        val command: EngineCommand = mapper.readValue(
//            // It seems that for classes that have no properties we need to create an empty JSON object:
//            command_json.toString().let {
//                if (it.is_blank()) "{}"
//                else it
//            },
//            command_class
//        )

//        // gson seems 10x faster (7ms vs 0.7 ms)
//        // TODO: tweak gson settings above (and in utils)
//        val command: EngineCommand = gson.fromJson(command_json.toString(), command_class)
//
//        return command
//    }

}

```

## commands/dispatcher.kt

```kotlin
// File: dispatcher.kt
package au21.engine.framework.commands

import au21.engine.framework.client.SocketHandler.Companion.TOPIC_SOCKET_COMMAND
import au21.engine.framework.features.AuFeatures
import au21.engine.framework.utils.HeartbeatConfig
import io.quarkus.logging.Log
import io.quarkus.vertx.ConsumeEvent
import io.vertx.core.eventbus.EventBus
import jakarta.annotation.PostConstruct
import jakarta.annotation.PreDestroy
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import kong.unirest.Unirest
import org.eclipse.microprofile.config.ConfigProvider
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.concurrent.thread


/*
 * This class queues commands and dispatches them.
 *  - via http://localhost:4040/ENGINE_INPUT_CHANNEL
 * It also adds a TICK msg to the queue every second
 *  - unless it already has a TICK message
 * (in case the handler is slow)
 */


@ApplicationScoped
class Dispatcher {

    @Inject
    lateinit var hearbeatConfig: HeartbeatConfig

    @Inject
    lateinit var handler: EngineCommandHandler

    @Inject
    lateinit var bus: EventBus

    @Inject
    lateinit var features: AuFeatures

    val HEARTBEAT_SECS: Long = 1
    val TICK = "TICK"

    private val destroyed = AtomicBoolean(false)
    private val request_queue = LinkedBlockingQueue<String>()

    fun send_message(message:String){
        // currently only used by handler to send the ErrorsMessageCommand, ie: back to auctioneers
        request_queue.add(message)
    }

    final val port: String = ConfigProvider.getConfig().getValue("quarkus.http.port", String::class.java)
    // final val host_address:String = InetAddress.getLocalHost().hostAddress

    @Suppress("HttpUrlsUsage")
    val internal_url: String = "http://127.0.0.1:$port/ENGINE_INPUT_CHANNEL"

    init {
        Log.info { "Internal url = $internal_url" }
    }

    @ConsumeEvent(TOPIC_SOCKET_COMMAND)
    fun addCommand(msg: String) {
        // println(this.hashCode().toString() + " addCommand: " + msg)
        this.request_queue.add(msg)
    }

    @PostConstruct
    fun postConstruct() {
        try {
            handler_thread.start()
            if(hearbeatConfig.heartbeat_enabled) {
                heartbeat_thread.start()
            }
        } catch (e: Throwable) {
            e.printStackTrace()
        }
    }

    @PreDestroy
    fun preDestroy() {
        Log.info("Stopping threads")
        destroyed.set(true)
    }

    val heartbeat_thread: Thread = thread(
        name = "Heartbeat thread",
        start = false
    ) {
        Log.info("${this}: Heartbeat started.")
        while (!destroyed.get()) {
            try {
                if (features.heartbeat) {
                    // 1) send the time signal:
                    // bus.publish("TIME_SIGNAL", DateTime())

                    // 2) add "TICK" to the queue if it doesn't already have one:
                    request_queue.find { it == TICK } ?: run {
                        request_queue.add(TICK)
                    }
                }
                Thread.sleep(HEARTBEAT_SECS * 1_000)
            } catch (e: Throwable) {
                e.printStackTrace()
            }
        }
        Log.info("destroying:${this}")
    }

    val handler_thread: Thread = thread(
        name = "Handler thread",
        start = false
    ) {
        Log.info("$this Handler started.")
        while (!destroyed.get()) {
            try {
                val msg = request_queue.take()
//                if (msg != "TICK") {
//                    println("Took from request queue: $msg")
//                }
                when {
                    request_queue.size > 1 -> Log.info("tasks remaining: ${request_queue.size}")
                }
                // Log.info("request_queue size: ${request_queue.size}")
                if (!destroyed.get()) { // could have been destroyed before message arrived:
                    Unirest.get(internal_url)
                        .queryString(mapOf(Pair("message", msg)))
                        .asJson()
                }

            } catch (t: Throwable) {
                t.printStackTrace()
            }
        }
        Log.info("destroying:${this}")
    }

}


```

## commands/exceptions.kt.kt

```kotlin
// File: exceptions.kt.kt
package au21.engine.framework.commands

class AlertException(error: String) :
// Exception(error.joinToString(separator = "\n")) {
    Error(error) {

//    fun to_envelope(
//        session_id: String,
//        command_name: String,
//        command_json: String,
//        start: Long,
//        requests_since_last_start: Int,
//    ) = EngineTransaction(
//        client_command_maps = mutableListOf(
//            ClientCommandSessionsMap(
//                ShowMessage(
//                    BrowserMessageKind.ALERT, listOf(message ?: "")
//                ),
//                listOf(session_id)
//            )
//        ),
//        engine_command_name = command_name,
//        engine_command_json = command_json,
//        isHeartbeat = command_json == "TICK",
//        session_id = session_id,
//        duration_ms = duration_ms(start),
//        has_alert = true,
//        has_session_differ_error = false,
//        requests_since_last_restart = requests_since_last_start
//    )
//

}

// TODO: implement a way to catch errors created after validation
// - possibly we want to send something to the traders
// - definitely we want to send something to the auctioneers
// - definitely we want to be able to monitor, search, and trigger LogEvent alerts in the logs.
class LogException(error: String) : Error(error)

```

## commands/handler.kt

```kotlin
// File: handler.kt
package au21.engine.framework.commands

//import org.eclipse.microprofile.opentracing.Traced
//import io.opentracing.Tracer
//import org.eclipse.microprofile.opentracing.Traced
//import sun.util.logging.PlatformLogger.ConfigurableBridge.LoggerConfiguration
import au21.engine.domain.common.commands.ErrorsSendCommand
import au21.engine.domain.common.model.AuSession.SessionTerminationReason.SERVER_REBOOT
import au21.engine.domain.common.services.sessions_non_terminated
import au21.engine.domain.de.commands.DeOrderSubmitCommand
import au21.engine.framework.client.BrowserMessageKind
import au21.engine.framework.client.ClientCommand
import au21.engine.framework.client.ClientsManager
import au21.engine.framework.client.SocketHandler
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.metrics.AuMetrics
import au21.engine.framework.utils.gzipClientCommand
import au21.engine.framework.utils.jsonToPrettyFormat
import au21.engine.framework.utils.objToPrettyFormat
import au21.engine.framework.utils.to_json
import com.jsoniter.JsonIterator
import io.quarkus.logging.Log
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import java.util.concurrent.atomic.AtomicBoolean
import java.util.logging.Logger


/*
 * =====================================================================================================
 *                                              MISC
 *   TODO: add version properties to Results and Messages
 *    version will just be the timestamp that this file was created.
 * =====================================================================================================
 */

// NEED TO FIND A PLACE FOR THESE I GUESS:

//typealias String = String

class CommandParams(
    val session_id: String?,
    val command_json: Any?,
    val simplename: String,
    val classname: String,
)

//@Traced
@ApplicationScoped
class EngineCommandHandler {

    var trace_heartbeat = false

    @Inject
    lateinit var metrics: AuMetrics

//    @Inject
//    lateinit var tracer: Tracer

    // not sure this is needed, first run of YourKit had 500k of these objects!
    val heartbeat_simplename: String = HeartbeatCommand::class.java.simpleName
    val heartbeat_classname: String = HeartbeatCommand::class.java.canonicalName

    @Inject
    lateinit var session_manager: ClientsManager

    @Inject
    lateinit var session_socket: SocketHandler

    @Inject
    lateinit var command_deserializer: EngineCommandDeserializer

    @Inject // used to send the ErrorsMessageCommand below
    lateinit var dispatcher: Dispatcher

    private val has_run = AtomicBoolean()

//    @Inject
//    lateinit var configuredTracer: Tracer
//

//    @Inject
//    lateinit var auTracer: AuTracer

    var error: Throwable? = null // used for testing

    // @WithSpan
    @Synchronized
    @Transactional // https://www.objectdb.com/tutorial/jpa/netbeans/spring/dao
    fun handle(
        db: AuEntityManager, // will be created per request
        //  @SpanAttribute("event")
        json: String,
        // store_state: (session_id: String, zipped_array: ByteBuffer) -> Unit,
        // showMessage: (session_id: String, showMessage: ClientCommand.ShowMessage) -> Unit
    ): String {

        error = null // clear last error if any

        val start_command = System.nanoTime()

        val is_heartbeat = json == "TICK"

        if (!is_heartbeat) {
            Log.info(jsonToPrettyFormat(json))
        }

        val commandParams: CommandParams = commandParameters(is_heartbeat, json)

        val command: EngineCommand =
            deserializeToCommand(is_heartbeat, commandParams)

        if (!is_heartbeat) {
            Log.info(command.to_json())
        }

        val cmd_json = saveCommandJson(command, is_heartbeat, commandParams, db)

        //   val action: EngineAction = try {
        try {
//            Log.setLogger(
//                LoggerConfiguration()
//                    .writeTo(coloredConsole())
//                    .writeTo(
//                        rollingFile("test-{Date}.log"),
//                        LogEventLevel.Information
//                    )
//                    .writeTo(seq("http://localhost:5341/"))
//                    .setMinimumLevel(LogEventLevel.Verbose)
//                    .createLogger()
//            )
            val action = validateCommand(command, db, commandParams.session_id)

            //LoggingUtil.logObjectMessage(LOG, action)

            mutateAction(db, action)

            val start_stores = System.nanoTime()

            // events:
            updateStore(is_heartbeat, db, action, commandParams)
            metrics.add_request_latency(cmd_json, start_command, start_stores)

            return "Succeeded"
        } catch (e: Throwable) {

            error = e

            // only makes sense to send the error back if we have a sid:

            if (e is AlertException) {
                Log.error("AlertException: ${e.message}")
                handle_alert(e, commandParams, command)
                return "AlertException handled: ${e.message}"
            } else {
                Log.error("Error: ${e.message}", e)
            }

            e.cause?.let { ee: Throwable ->
                if (ee is AlertException) {
                    Log.error("AlertException cause: ${ee.message}")
                    handle_alert(ee, commandParams, command)
                    return "AlertException cause handler: ${ee.message}"
                } else {
                    Log.error("Error cause: ${e.message}", ee)
                    throw ee
                }
            }

            throw e // ie: neither e nor e.cause is an AlertException

        }
    }

    // @WithSpan
    fun updateStore(
        is_heartbeat: Boolean,
        db: AuEntityManager,
        action: EngineAction,
        commandParams: CommandParams,
    ) {
        when {
            is_heartbeat -> {
                session_manager.handle(db, action)
            }

            else -> {
                Log.info(">>>")
                Log.info("session_id = " + commandParams.session_id)
                session_manager.handle(db, action)
                Log.info("<<<")

            }
        }
    }

    // @WithSpan
    fun mutateAction(
        db: AuEntityManager,
        action: EngineAction,
    ) {
        db.transact {
            action.mutate()
        }
    }

    // @WithSpan
    fun validateCommand(
        command: EngineCommand,
        db: AuEntityManager,
        session_id: String?,
    ): EngineAction {
        val action = command.validate(db, session_id)

        if (!has_run.get()) {
            has_run.set(true)
            db.transact {
                db.sessions_non_terminated().forEach {
                    it.terminate(SERVER_REBOOT)
                    db.save(it)
                }
            }
        }
        return action
    }

    //@WithSpan
    fun saveCommandJson(
        command: EngineCommand,
        is_heartbeat: Boolean,
        commandParams: CommandParams,
        db: AuEntityManager,
    ): String {
        val cmd_json = command::class.java.simpleName +
                if (is_heartbeat) ""
                else ": " + commandParams.command_json!!.toString()

        if (!is_heartbeat) {
            try {
                db.transact {
                    db.save(CommandJson(cmd_json))
                }
            } catch (t: Throwable) {
                t.printStackTrace()
            }
        }
        return cmd_json
    }

    // @WithSpan
    fun deserializeToCommand(
        is_heartbeat: Boolean,
        commandParams: CommandParams,
    ): EngineCommand {
        val command: EngineCommand = when {
            is_heartbeat -> HeartbeatCommand.instance
            else -> try {
                command_deserializer.to_command(
                    commandParams.command_json!!,
                    commandParams.classname
                )
            } catch (t: Throwable) {
                t.printStackTrace()
                throw t
            }
        }
        return command
    }

    // @WithSpan
    fun commandParameters(
        is_heartbeat: Boolean,
        json: String,
    ): CommandParams {
        val commandParams: CommandParams =
            when {
                is_heartbeat -> CommandParams(
                    session_id = null,
                    command_json = null,
                    simplename = heartbeat_simplename,
                    classname = heartbeat_classname
                )

                else -> {
                    val any = JsonIterator.deserialize(json)
                    CommandParams(
                        session_id = any.toString("session_id"),
                        command_json = any.get("command"),
                        simplename = any.toString("simplename"),
                        classname = any.toString("classname")
                    )
                }
            }
        return commandParams
    }

    fun handle_alert(
        e: AlertException,
        commandParams: CommandParams,
        command: EngineCommand
    ) {
        when (val sid = commandParams.session_id) {
            null -> {
                e.printStackTrace()
                throw e
            } // can't send back to user, so this is thrown
            else -> {
                session_socket.publish(
                    sid, gzipClientCommand(
                        ClientCommand.ShowMessage(
                            BrowserMessageKind.ALERT,
                            listOf(e.message!!)
                        )
                    )
                )

                e.message.let {
                    if (it?.trim() != "" && command is DeOrderSubmitCommand) {
                        dispatcher.send_message(
                            mapOf(
                                "session_id" to null,
                                "simplename" to ErrorsSendCommand::class.simpleName,
                                "classname" to ErrorsSendCommand::class.qualifiedName,
                                "command" to mapOf(
                                    "auction_id" to command.auction_id,
                                    "error" to e.message,
                                    "trader_session_id" to sid
                                )
                            ).to_json()
                        )
                    }
                }
            }
        }
    }

}



```

## commands/interfaces/IAuctionMessage.kt

```kotlin
// File: IAuctionMessage.kt
package au21.engine.framework.commands.interfaces

import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.model.AuctionMessage

interface IAuctionMessage {
    val auction:Auction
    val message:AuctionMessage
}

```

## commands/interfaces/ISessionsTerminated.kt

```kotlin
// File: ISessionsTerminated.kt
package au21.engine.framework.commands.interfaces

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuSession.SessionTerminationReason
import au21.engine.framework.commands.SOCKET_CLOSED_TIMEOUT_SECONDS
import au21.engine.framework.database.AuEntityManager

class SessionTermination(
    s: AuSession,
    val reason:String
){
    val sid:String = s.session_id
}

interface ISessionsTerminated{

    val sessions_terminated:MutableList<SessionTermination>

    fun get_reason_string(tr: SessionTerminationReason):String =
        when(tr){
            SessionTerminationReason.BROWSER_UNLOADED -> TODO()
            SessionTerminationReason.COMPANY_DELETED -> "Company deleted" // also a bit harsh ?
            SessionTerminationReason.COMPANY_NAME_EDITED -> "Company name edited"
            SessionTerminationReason.FORCED_OFF -> TODO()
            SessionTerminationReason.LOGIN_FROM_ANOTHER_BROWSER -> "You have logged in from another browser or device."
            SessionTerminationReason.SERVER_REBOOT -> TODO()
            SessionTerminationReason.SERVER_SWEPT_STALE_SESSION -> "Session swept due to > $SOCKET_CLOSED_TIMEOUT_SECONDS seconds of inactivity"
            SessionTerminationReason.SIGNED_OFF -> ""
            SessionTerminationReason.USER_EDITED -> "User edited"
            SessionTerminationReason.USER_DELETED -> "User deleted" // a bit harsh maybe ?
        }


    fun terminate_session(db: AuEntityManager, s:AuSession, tr: SessionTerminationReason){
        s.terminate(tr)
        db.save(s)
        sessions_terminated.add(SessionTermination(s, get_reason_string(tr)))
    }
}

```

## database/AuEntity.kt

```kotlin
// File: AuEntity.kt
package au21.engine.framework.database

import java.io.Serializable
import javax.jdo.annotations.Index
import javax.persistence.Entity
import javax.persistence.GeneratedValue
import javax.persistence.Id

/*
 *  1) Entities
 *
 */
@Entity
// @EntityListeners(AuEntity.EntityChangeListener::class)
open class AuEntity : Serializable {
    @Id
    @GeneratedValue
    var id: Long = 0

    // NOTE: THIS SHOULD ONLY BE USED IN VIEWMODEL or COMMANDS
    // - ie: to/from client
    fun id_str() =
        id.toString()

    @Index
    var deleted: Boolean = false

    // val creation_timestamp = DateTime().toDate()

    fun checkConstraints() {
        // for aggregates
        // opportunity for aggregate to reject changes
        // throws exception
    }
}

// Trying to see if we can test without the database, ie: without ids:
fun is_same_entity(a: AuEntity?, b: AuEntity?): Boolean =
    when {
        a == null -> false
        b == null -> false
        a.id == 0L && b.id == 0L -> a.hashCode() == b.hashCode() // NB: need this because id always 0 before save
        else -> a.id == b.id
    }

```

## database/AuEntityManager.kt

```kotlin
// File: AuEntityManager.kt
package au21.engine.framework.database

// import org.eclipse.microprofile.opentracing.Traced
import io.quarkus.logging.Log
import jakarta.annotation.PostConstruct
import jakarta.annotation.PreDestroy
import jakarta.enterprise.context.RequestScoped
import jakarta.inject.Inject
import javax.persistence.EntityManager
import javax.persistence.EntityManagerFactory
import javax.persistence.EntityTransaction

@RequestScoped
class AuEntityManager @Inject constructor(val emf: EntityManagerFactory) {

    companion object {
        var class_count = 0
    }

    var instance_count = 0

    init {
        instance_count = ++class_count
    }

    val em: EntityManager = emf.createEntityManager()

    // used for testing,
    // - see: https://www.objectdb.com/forum/1016
    fun getId(o:AuEntity?): String =
        o.let {
            // println(it)
            emf.persistenceUnitUtil.getIdentifier(o)?.toString() ?: ""
        }
    @PostConstruct
    fun postConstruct() {
       // Log.info("POST_CONSTRUCT: " + this::class.java.simpleName + " #${instance_count} created")
    }

    @PreDestroy
    fun preDestroy() {
       // Log.info("PRE_DESTROY: " + this::class.java.simpleName + " #${instance_count} destroyed")
        if (em.isOpen) {
            // Log.info("closing em: $instance_count")
            em.close()
        }
    }

   // @Traced
    @Synchronized
    fun <T> transact(logging: Boolean = true, block: () -> T): T {
//        if (!em.isOpen) {
//            throw java.lang.Exception("Entity manager is not open!")
//            // probably this is thrown on the next line anyway
//        }
        val tx: EntityTransaction = em.transaction
        try {
            tx.begin()
            return block().apply {
                tx.commit()
            }
        } catch (e: Throwable) {
            e.printStackTrace()
            throw e
        } finally {
            if (tx.isActive) {
                Log.error("TRANSACTION ROLLING BACK")
                tx.rollback()
                Log.error("TRANSACTION ROLLED BACK")
            }
        }
    }

    //@Traced
    fun <E : AuEntity> save(e: E): E =
        e.also {
//            if(!em.isJoinedToTransaction)
//                throw Error("cannot save, not in a transaction")
            em.persist(it)
        }

    //@Traced
    fun <E : AuEntity> refresh(e: E) =
        e.also { em.refresh(it) }

    //@Traced
    fun <E : AuEntity> delete(e: E) =
        e.also { em.remove(it) }

    //@Traced

    @Suppress("JpaQlInspection")
    inline fun <reified E> deleteAllIncDeleted() =
        // TODO: this cannot be allowed to run in production!!
        try {
            // println(E::class.simpleName)
            // https://www.objectdb.com/java/jpa/query/jpql/delete
            em.createQuery("DELETE FROM ${E::class.simpleName}").executeUpdate()
        } catch (e:Exception){
            e.printStackTrace()
        }

    //@Traced
    fun commit_and_begin(){
        em.transaction.commit()
        em.transaction.begin()
    }

    //@Traced
    fun <E : AuEntity> deleteAllExcept(vararg except: AuEntity) =
////        if (!emf.resettable) {
////            throw Exception("database is not resetable")
////        } else  // which is fixed at module creation, but could be per-call
        findAll<AuEntity>(include_deleted = true).forEach {
            if (!except.contains(it)) {
                delete(it)
            } else {
                Log.info("not deleting: ${it::class.simpleName} with id: ${it.id_str()}")
            }
        }

    //@Traced
    inline fun <reified T : AuEntity> findAll(include_deleted: Boolean = false): List<T> =
        try {
            val query: String = "SELECT t FROM ${T::class.java.name} t" + (
                    if (!include_deleted) " WHERE t.deleted=false"
                    else "")
            em.createQuery(query, T::class.java).resultList
        } catch (e: Exception) {
            Log.info(e.message)
            // Log.error(e.message)
            // e.printStackTrace()
            listOf()
        }


    // https://www.objectdb.com/java/jpa/query/jpql/comparison#IS_NOT_NULL
    //@Traced
    inline fun <reified T : AuEntity> query(query: String): List<T> =
        try {
            em.createQuery(query, T::class.java).resultList
        } catch (e: Exception) {
            Log.info(e.message)
            emptyList<T>()
        }


    // TODO: note: this predicate is working on fetched objects, would be better as an object index or property !
    //@Traced
    inline fun <reified T : AuEntity> filter(include_deleted: Boolean = false, pred: (T) -> Boolean): List<T> =
        findAll<T>(include_deleted).filter { pred(it) }

    //@Traced
    inline fun <reified T : AuEntity> findFirst(include_deleted: Boolean = false, pred: (T) -> Boolean): T? =
        filter(include_deleted, pred).firstOrNull()


    //@Traced
    inline fun <reified T : AuEntity> byId(oid: String?, include_deleted: Boolean = false): T? =
        byId(oid?.toLongOrNull(), include_deleted)

    //@Traced
    inline fun <reified T : AuEntity> byId(oid: Long?, include_deleted: Boolean = false): T? =
        oid?.let { em.find(T::class.java, oid) }?.takeIf { include_deleted || !it.deleted }


}

```

## database/AuEntityManagerFactory.kt

```kotlin
// File: AuEntityManagerFactory.kt
package au21.engine.framework.database

import io.quarkus.logging.Log
import jakarta.annotation.PostConstruct
import jakarta.enterprise.context.ApplicationScoped
import jakarta.ws.rs.Produces
import org.eclipse.microprofile.config.inject.ConfigProperty
import java.io.File
import javax.persistence.EntityManagerFactory
import javax.persistence.Persistence


@ApplicationScoped
class AuEntityManagerFactory {

    @ConfigProperty(name = "OBJECTDB_ACTIVATION_CODE")
    lateinit var OBJECTDB_ACTIVATION_CODE: String

    @ConfigProperty(name = "OBJECTDB_ADMIN_USER", defaultValue = "admin")
    lateinit var OBJECTDB_ADMIN_USER: String

    @ConfigProperty(name = "OBJECTDB_ADMIN_PASSWORD", defaultValue = "admin")
    lateinit var OBJECTDB_ADMIN_PASSWORD: String

    @ConfigProperty(name = "OBJECTDB_CONFIG_TEMPLATE_PATH")
    lateinit var OBJECTDB_CONFIG_TEMPLATE_PATH: String

    @ConfigProperty(name = "OBJECTDB_CONFIG_GENERATED_PATH")
    lateinit var OBJECTDB_CONFIG_GENERATED_PATH: String

    @ConfigProperty(name = "OBJECTDB_DB_HOME")
    lateinit var OBJECTDB_DB_HOME: String

    @ConfigProperty(name = "OBJECTDB_URL")
    lateinit var OBJECTDB_URL: String

    @PostConstruct
    fun init(){
        Log.info("OBJECTDB_ACTIVATION_CODE: $OBJECTDB_ACTIVATION_CODE")
        Log.info("OBJECTDB_ADMIN_USER: $OBJECTDB_ADMIN_USER")
        Log.info("OBJECTDB_ADMIN_PASSWORD: $OBJECTDB_ADMIN_PASSWORD")
        Log.info("OBJECTDB_CONFIG_TEMPLATE_PATH: $OBJECTDB_CONFIG_TEMPLATE_PATH")
        Log.info("OBJECTDB_CONFIG_GENERATED_PATH: $OBJECTDB_CONFIG_GENERATED_PATH")
        Log.info("OBJECTDB_DB_HOME: $OBJECTDB_DB_HOME")
        Log.info("OBJECTDB_URL: $OBJECTDB_URL")
    }

    val BASE_DIR: String = System.getProperty("user.dir").let {
        println("user.dir: $it")
        val index = it.indexOf("/build/classes/kotlin/main")
        if (index > -1)
            it.substring(0, index)
        else
            it
    }

    fun to_absolute_path(relative_path: String): String =
        "$BASE_DIR/$relative_path".let { s: String ->
            s.replace("\\\\", "\\")
        }

    val config_logging = false

//    fun log(s: String) {
//        if (config_logging) {
//            Log.info(s)
//        }
//    }

    private fun getTemplateFileFromClasspath(): File {
       // val resource = javaClass.classLoader.getResource("config/objectdb/au21-engine.objectdb.template.xml")
        val resource = javaClass.classLoader.getResource("au21-engine.objectdb.template.xml")
            ?: throw IllegalStateException("ObjectDB template file not found in classpath")
        return File(resource.toURI())
    }


    @ApplicationScoped
    @Produces
    fun createEntityManagerFactory(): EntityManagerFactory {

        // 1) load config template:
        Log.info("\nOBJECTDB_CONFIG_TEMPLATE_PATH: $OBJECTDB_CONFIG_TEMPLATE_PATH")
//        val template: String = to_absolute_path(OBJECTDB_CONFIG_TEMPLATE_PATH).let { path: String ->
//            val f = File(path)
//            if (!f.exists())
//                throw Exception("no objectdb template file found at:$path")
//            log("fully qualified objectdb config template path: ${f.canonicalPath}")
//            f.readText()
//        }

        val template: String = getTemplateFileFromClasspath().readText()

        Log.info("NB: Objectdb config template: \n$template")

        // 2) create config file from template, and set objectdb.conf location
        File(to_absolute_path(OBJECTDB_CONFIG_GENERATED_PATH)).also { f ->
            val path = f.canonicalPath
            Log.info("Location to write Objectdb config file: $path")
            if (f.exists()) {
                Log.info("Deleting existing objectdb config file at: $path")
                f.delete()
            } else {
                Log.info("Creating directories")
                f.parentFile.mkdirs()
            }
            f.createNewFile()
            Log.info("Created empty objectdb config file: $path")

            val generated = template
                .replace("OBJECTDB_ACTIVATION_CODE", OBJECTDB_ACTIVATION_CODE)
                .replace("OBJECTDB_ADMIN_PASSWORD", OBJECTDB_ADMIN_PASSWORD)
                .replace("OBJECTDB_ADMIN_USER", OBJECTDB_ADMIN_USER)
                .replace("OBJECTDB_URL", OBJECTDB_URL)

            f.writeText(generated)
            Log.info("\nConfig file written at: $path")
            Log.info(f.readText())

            // set objectdb.conf location:
            Log.info("Setting objectdb.conf to: $path")
            System.setProperty("objectdb.conf", path)
        }

        // 3) Set objectdb.home:
        val objectdHomeDir: File = File(to_absolute_path(OBJECTDB_DB_HOME)).also {
            Log.info("Setting objectdb.home to: ${it.canonicalPath}")
            System.setProperty("objectdb.home", it.canonicalPath)
            it.mkdirs()
        }

        // 4) Create EMF based on OBJECTDB_URL:

        Log.info("Objectdb url: \n$OBJECTDB_URL")

        //.createEntityManagerFactory("exp11") // used with persistence.xml
        // "objectdb://localhost:6136/myDbFile.odb"
        // note: could do this instead of properties:
        // 'objectdb://localhost/test.odb;user=admin;password=admin'

        // SHOULD WORK BOTH IN SERVER MODE AND EMBEDDED MODE:

        return if (OBJECTDB_URL.endsWith(".mem")) {
            Persistence.createEntityManagerFactory(OBJECTDB_URL)

        } else if (OBJECTDB_URL.startsWith("objectdb://")) {
            Persistence.createEntityManagerFactory(OBJECTDB_URL,
                HashMap<String, String>().apply {
                    put("javax.persistence.jdbc.user", OBJECTDB_ADMIN_USER)
                    put("javax.persistence.jdbc.password", OBJECTDB_ADMIN_PASSWORD)
                })
        } else {
            // otherwise put it in the OBJECTDB_DB_HOME/db
            File(objectdHomeDir, OBJECTDB_URL).let { odb_file ->
                val path = odb_file.canonicalPath
                Log.info("DB Location: $path")
                Persistence.createEntityManagerFactory(path)
            }
        }
    }
}


```

## database/IAuctionState.kt

```kotlin
// File: IAuctionState.kt
package au21.engine.framework.database

interface IAuctionState {
    fun oneOf(vararg states: IAuctionState): Boolean
}

```

## enums.kt

```kotlin
// File: enums.kt
package au21.engine.framework

//class AuctionTickValue(
//    val a: DeAuction,
//    val messages: List<AuctionMessage>,
//    val auction_closed_during_tick: Boolean,
//    val prior_snap: AuctionStateSnapshot,
//    val post_snap: AuctionStateSnapshot,
//    val state_changed: Boolean = prior_snap.state_label != post_snap.state_label
//)
//
//class AuctionStateSnapshot(a: DeAuction) {
//    val state_label: String = a.auction_state_label
//    val closed: Boolean = a.closed
//
////    inline fun <reified T> getState(): T
////            where
////            T : Enum<T>,
////            T : IAuctionState = enumValueOf(state_string)
//}


/*
 * =====================================================================================================
 *                             FRAMEWORK ENUMS
 * =====================================================================================================
 */

enum class PageName {

    CREDITOR_AUCTIONEER_PAGE,
    CREDITOR_TRADER_PAGE,
    HOME_PAGE,
    LOGIN_PAGE, // used on client side only
    SESSION_PAGE,
    USER_PAGE,

    BH_AUCTIONEER_PAGE,
    BH_SETUP_PAGE,
    BH_TRADER_PAGE,

    DE_AUCTIONEER_PAGE,
    DE_SETUP_PAGE,
    DE_TRADER_PAGE,

    MR_AUCTIONEER_PAGE,
    MR_SETUP_PAGE,
    MR_TRADER_PAGE,

    TE_AUCTIONEER_PAGE,
    TE_SETUP_PAGE,
    TE_TRADER_PAGE,

    TO_AUCTIONEER_PAGE,
    TO_SETUP_PAGE,
    TO_TRADER_PAGE
}

```

## features/AuFeatures.kt

```kotlin
// File: AuFeatures.kt
package au21.engine.framework.features

import jakarta.enterprise.context.ApplicationScoped


@ApplicationScoped
class AuFeatures {
    // NOTE: there is a HeartbeatConfig class which will set the heartbeat too
    // ie: if that is set, then this has no effect
    // TODO: probably merge these (though you have to start the heartbeat
    //  thread if not started)
    var heartbeat = true
}

```

## graphql/GraphqlApi.kt

```kotlin
// File: GraphqlApi.kt
package au21.engine.framework.graphql

import au21.engine.domain.common.commands.*
import au21.engine.domain.common.model.*
import au21.engine.domain.common.services.sessions_logged_in
import au21.engine.domain.common.services.sessions_non_terminated
import au21.engine.domain.de.commands.*
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeFlowControlType
import au21.engine.framework.commands.AlertException
import au21.engine.framework.commands.EngineCommandEnvelope
import au21.engine.framework.commands.EngineCommandHandler
import au21.engine.framework.database.AuEntity
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.features.AuFeatures
import au21.engine.framework.utils.to_json
import com.google.common.collect.ImmutableList
import jakarta.inject.Inject
import org.eclipse.microprofile.graphql.GraphQLApi
import org.eclipse.microprofile.graphql.Mutation
import org.eclipse.microprofile.graphql.Query
import org.joda.time.DateTime
import java.util.*


/**
 * Schema available here:
 * - http://localhost:4040/graphql/schema.graphql
 *
 * Queries available here:
 * - http://localhost:4040/graphql-ui/
 *
 */

@GraphQLApi

class MutationApi1 {
    @Mutation
    fun mutation1(msg: String): String {
        println(msg)
        return msg
    }
}

//@Traced
@GraphQLApi
class GraphqlApi {

    //    @Inject
//    lateinit var prometheus: PrometheusMeterRegistryProvider
    //  val tracer:Tracer = GlobalOpenTelemetry.getTracer("my-service");

//    @Inject
//    lateinit var registry: MeterRegistry


    @Inject
    lateinit var handler: EngineCommandHandler

    @Inject
    lateinit var db: AuEntityManager

    @Inject
    lateinit var features: AuFeatures

    @Query
    fun sessions(include_terminated: Boolean, logged_in_only: Boolean): List<AuSession> =
        when {
            include_terminated && logged_in_only -> throw AlertException("Cannot search for terminated sessions that are logged in.")
            include_terminated -> db.findAll()
            // these are all non-terminated
            logged_in_only -> db.sessions_logged_in()
            else -> db.sessions_non_terminated()
        }

    @Query
    fun people(): List<Person> = db.findAll<Person>()

    @Query
    fun companies(): List<Company> = db.findAll()

    @Query
    fun users(): List<Person> = db.findAll()

    @Query
    fun auctions(): List<Auction> = db.findAll()

    @Query
    fun de_auctions(): List<DeAuction> = db.findAll()

    @Query
    fun entities(): List<AuEntity> = db.findAll()

    // TODO: must check that this is a tmp db !!
    @Mutation
    fun initDb(): String {
        handler.handle(
            db,
            EngineCommandEnvelope(
                session_id = "",
                command = DbInitCommand()
            ).to_json()
        )
        return "send DbInitCommand to handler."
    }

    @Mutation
    fun deleteAuctions(id: String): String {
        return handler.handle(
            db,
            EngineCommandEnvelope(
                session_id = "",
                command = DbDeleteAuctionsCommand()
            ).to_json()
        )
    }

    /**
     * Microprofile API:
     * https://download.eclipse.org/microprofile/microprofile-graphql-1.0/microprofile-graphql.html#mutations
     */
    @Mutation
    fun reCreateDummyDb(
        auction_count: Int = 1,
        auctioneer_count: Int = 2,
        trader_count: Int = 4,
        round_count: Int = 1,
        close_last_round: Boolean = false,
        use_counterparty_credits: Boolean = false,
    ): String {
//        Unirest.get("http://localhost:4040/ENGINE_INPUT_CHANNEL")
//            .queryString(mapOf(Pair("message", cmd)))
        handler.handle(
            db, EngineCommandEnvelope(
                session_id = "",
                command = DeCreateSampleDbCommand(
                    auction_count = auction_count,
                    auctioneer_count = auctioneer_count,
                    round_count = round_count,
                    trader_count = trader_count,
                    close_last_round = close_last_round,
                    use_counterparty_credits = use_counterparty_credits
                )
            ).to_json()
        )
        return "command sent to engine command handler via REST api" // TODO: is this true?
    }


    @Mutation
    fun createAuction(name: String): String {
        val d = DateTime()
        val sessionId = create_session_and_login("a1", "1")
        return handler.handle(
            db,
            EngineCommandEnvelope(
                session_id = sessionId,
                command = DeAuctionSaveCommand(
                    auction_id = "", // If exists then update else create new
                    auction_name = name,
                    use_counterparty_credits = "false", // This is going to change with probably MODE
                    quantity_label = "MMlb", // This is the label with quantity of the traded resource
                    quantity_minimum = "1", // Minimum bid quantity for the commodity in trade
                    quantity_step = "1", // The step is the increase/decrease quantity units for the commodity in trade
                    price_change_initial = "0.5", // This is the price change factor, meaning if it increased in a round then it increases by this number.
                    price_change_post_reversal = "0.125", // This is the price change factor after reversal, meaning if it increased in a round then it increases by this number.
                    price_label = "cpp", // The label of the price...
                    price_decimal_places = "3", // The decimal places in which the price needs to be shown to the user.
                    cost_multiplier = "10000", // This number is how to get the dollar price. 1 cent per pound, 50 Million Barrels , 10,000 ==  500K Dollars
                    /*
                    ===============The below once are just to save and give back to auction, no real logic==============
                     */
                    excess_level_0_label = "+",
                    excess_level_1_label = "++",
                    excess_level_2_label = "+++",
                    excess_level_3_label = "++++",
                    excess_level_4_label = "+++++",
                    excess_level_1_quantity = "10",
                    excess_level_2_quantity = "20",
                    excess_level_3_quantity = "30",
                    excess_level_4_quantity = "40",
                    /*
                    ==================The above once are just to save and give back to auction, no real logic==============
                    */
                    starting_price_announcement_mins = "5", // The time after which the price is going to announce, So this is not used now. No real logic.
                    month_is_1_based = true, // In FE, we use some JS library that starts January with 0 instead of 1. Maybe this can replaced with UTC in future.
                    starting_year = d.year.toString(),
                    starting_month = d.monthOfYear.toString(), // this is 1 based!
                    starting_day = d.dayOfMonth.toString(),
                    starting_hour = d.hourOfDay.toString(),
                    starting_mins = d.minuteOfHour.toString(),
                    /*
                    ===============The below are for automation of round creation, updated==========================
                    */
                    round_red_secs = "15",
                    round_orange_secs = "30",
                    round_open_min_seconds = "15",
                    round_closed_min_secs = "5"
                )
            ).to_json()
        )
    }

    @Mutation
    fun heartbeat(on: Boolean): String {
        features.heartbeat = on
        return "Heartbeat " + if (on) "on" else "off"
    }

    @Mutation
    fun trace_heartbeat(on: Boolean): String {
        handler.trace_heartbeat = on
        return "Heartbeat Trace: " + if (on) "on" else "off"
    }

    @Mutation
    fun broker_mode(on: Boolean): String {
        handler.trace_heartbeat = on
        return "Heartbeat Trace: " + if (on) "on" else "off"
    }

    @Mutation
    fun create_company(shortName: String): String {
        val sessionId = create_session_and_login("a1", "1")
        return handler.handle(
            db,
            EngineCommandEnvelope(
                session_id = sessionId,
                command = CompanySaveCommand("", shortName, "${shortName}_long")
            ).to_json()
        )
    }

    @Mutation
    fun flowControlMutation(auction_id: String, flowControlType: DeFlowControlType): String {
        val sessionId = create_session_and_login("a1", "1")
        return handler.handle(
            db,
            EngineCommandEnvelope(
                session_id = sessionId,
                command = DeFlowControlCommand(
                    auction_id,
                    flowControlType,
                    "10"
                )
            ).to_json()
        )
    }

    @Mutation
    fun addTrader(auction_id: String, traderId: String): String {
        val sessionId = create_session_and_login("a1", "1")
        return handler.handle(
            db,
            EngineCommandEnvelope(
                session_id = sessionId,
                command = DeTradersAddCommand(
                    auction_id,
                    ImmutableList.of(traderId)
                )
            ).to_json()
        )
    }

    @Mutation
    fun orderSubmit(
        auction_id: String,
        traderId: String,
        orderType: OrderType,
        round: String,
        quantity: String,
    ): String {
        val sessionId = create_session_and_login("a1", "1")

        return handler.handle(
            db,
            EngineCommandEnvelope(
                session_id = sessionId,
                command = DeOrderSubmitCommand(
                    auction_id,
                    traderId,
                    orderType,
                    round,
                    quantity
                )
            ).to_json()
        )
    }

    private fun create_session_and_login(userName: String, password: String): String {
        val sessionId = UUID.randomUUID().toString()
        handler.handle(
            db,
            EngineCommandEnvelope(
                session_id = "",
                ClientSocketCommand(sessionId, AuSession.ClientSocketState.OPENED)
            ).to_json()
        )

        handler.handle(
            db,
            EngineCommandEnvelope(
                session_id = sessionId,
                LoginCommand(
                    "a1",
                    "1"
                )
            ).to_json()
        )
        return sessionId
    }
}


```

## observability/GelfLoggingResource.kt

```kotlin
// File: GelfLoggingResource.kt
package au21.engine.framework.observability

import jakarta.enterprise.context.ApplicationScoped
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import org.jboss.logging.Logger

@Path("/gelf-logging")
@ApplicationScoped
class GelfLoggingResource {
    @GET
    fun log():String {
        LOG.info("Some useful log message")
        return "logged"
    }
    companion object {
        private val LOG = Logger.getLogger(GelfLoggingResource::class.java)
    }
}

```

## observability/metrics.kt

```kotlin
// File: metrics.kt
package au21.engine.framework.metrics

import au21.engine.framework.utils.thousands
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path

@ApplicationScoped
class AuMetrics {

    class RequestLatency(
        val cmd:String,
        val command_handling_full: String,
        val stores_json_zip_send: String
    )

    var request_latencies: MutableList<RequestLatency> = mutableListOf()

    fun add_request_latency(cmd:String, start_command: Long, start_stores: Long) {
        val now = System.nanoTime()
        this.request_latencies.add(
            RequestLatency(
                cmd,
                ((now - start_command) / 1_000).thousands() + " us",
                ((now - start_stores) / 1_000).thousands() + " us"
            )
        )
        while (this.request_latencies.size > 30) {
            this.request_latencies.removeFirst()
        }
    }
}


@Path("/au-metrics")
class MetricsController {

    @Inject
    lateinit var metrics: AuMetrics

    @GET
    fun toggle_session_differ(): String {
        var r = "<meta http-equiv='refresh' content='1'>"
        r += "<table border='1'>"
        r += "<thead><tr><th>command</th><th>full</th><th>stores</th></tr></thead>"
        metrics.request_latencies.forEach {
            r += "<tr>"
            r += "<td><div style='width:400px; overflow:auto'>${it.cmd}</div></td>"
            r += "<td>${it.command_handling_full}</td>"
            r += "<td>${it.stores_json_zip_send}</td>"
            r += "</tr>"
        }
        r += "</table>"
        return r
    }
}

```

## observability/tracing.kt

```kotlin
// File: tracing.kt
package au21.engine.framework.observability

//import io.opentelemetry.api.trace.Tracer
import jakarta.enterprise.context.ApplicationScoped

/**
 *  note, using: devops/resources/jaeger/working/run-jaeger-for-opentelemetry.sh
 */
@ApplicationScoped
class AuTracer {

    /*
    final val jaegerChannel: ManagedChannel = ManagedChannelBuilder
        .forAddress("localhost", 14250)
        .usePlaintext()
        .build()

    // Export traces to Jaeger
    final val jaegerExporter: JaegerGrpcSpanExporter = JaegerGrpcSpanExporter.builder()
        .setChannel(jaegerChannel)
        .setTimeout(30, TimeUnit.SECONDS)
        .build()

    final val serviceNameResource: Resource = Resource.create(
        Attributes.of(ResourceAttributes.SERVICE_NAME, "au21-engine")
    )

    // Set to process the spans by the Jaeger Exporter

    // Set to process the spans by the Jaeger Exporter
    final val tracerProvider: SdkTracerProvider = SdkTracerProvider.builder()
        .addSpanProcessor(SimpleSpanProcessor.create(jaegerExporter))
        .setResource(Resource.getDefault().merge(serviceNameResource))
        .build()

    final val openTelemetry: OpenTelemetrySdk = OpenTelemetrySdk.builder()
        .setTracerProvider(tracerProvider)
        .build()

     */

//    val tracer: Tracer? = null // openTelemetry.getTracer("Daves
// instrumentation")

//    init {
//        // it's always a good idea to shut down the SDK cleanly at JVM exit.
//
//        Runtime.getRuntime()
//            .addShutdownHook(
//                Thread { tracerProvider.close() }
//            )
//
//    }


}



//    val sdkTracerProvider = SdkTracerProvider.builder()
//        .addSpanProcessor(BatchSpanProcessor.builder(OtlpGrpcSpanExporter.builder().build()).build())
//        .build()
//
//    val openTelemetry: OpenTelemetry = OpenTelemetrySdk.builder()
//        .setTracerProvider(sdkTracerProvider)
//        .setPropagators(ContextPropagators.create(W3CTraceContextPropagator.getInstance()))
//        .buildAndRegisterGlobal()

//
//    val  openTelemetry: OpenTelemetry =
//        ExampleConfiguration.initOpenTelemetry("http://localhost", 14250);
//
//    Quarkus.run(Au21Engine::class.java, *args)
//}
//}
//
///**
// * All SDK management takes place here, away from the instrumentation code, which should only access
// * the OpenTelemetry APIs.
// */
//internal object ExampleConfiguration {
//    /**
//     * Initialize an OpenTelemetry SDK with a Jaeger exporter and a SimpleSpanProcessor.
//     *
//     * @param jaegerHost The host of your Jaeger instance.
//     * @param jaegerPort the port of your Jaeger instance.
//     * @return A ready-to-use [OpenTelemetry] instance.
//     */
//    fun initOpenTelemetry(jaegerHost: String?, jaegerPort: Int): OpenTelemetry {
//        // Create a channel towards Jaeger end point
//        val jaegerChannel = ManagedChannelBuilder.forAddress(jaegerHost, jaegerPort).usePlaintext().build()
//        // Export traces to Jaeger
//        val jaegerExporter: JaegerGrpcSpanExporter = JaegerGrpcSpanExporter.builder()
//            .setChannel(jaegerChannel)
//            .setTimeout(30, TimeUnit.SECONDS)
//            .build()
//        val serviceNameResource =
//            Resource.create(Attributes.of(ResourceAttributes.SERVICE_NAME, "dm-otel-jaeger-example"))
//
//        // Set to process the spans by the Jaeger Exporter
//        val tracerProvider = SdkTracerProvider.builder()
//            .addSpanProcessor(SimpleSpanProcessor.create(jaegerExporter))
//            .setResource(Resource.getDefault().merge(serviceNameResource))
//            .build()
//        val openTelemetry = OpenTelemetrySdk.builder().setTracerProvider(tracerProvider).build()
//
//        // it's always a good idea to shut down the SDK cleanly at JVM exit.
//        Runtime.getRuntime().addShutdownHook(Thread { tracerProvider.close() })
//        return openTelemetry
//    }
//
//    private fun myWonderfulUseCase() {
//        // Generate a span
//        val span: Span = this.tracer.spanBuilder("Start my wonderful use case").startSpan()
//        span.addEvent("Event 0")
//        // execute my use case - here we simulate a wait
//        doWork()
//        span.addEvent("Event 1")
//        span.end()
//    }
//
//    private fun doWork() {
//        try {
//            Thread.sleep(1000)
//        } catch (e: InterruptedException) {
//            // do the right thing here
//        }
//    }
//
//}

//            System.setProperty(
//                "quarkus.opentelemetry.tracer.exporter.otlp.endpoint",
//                "http://localhost:55680"
//            )
//            System.setProperty(
//                "quarkus.opentelemetry.tracer.exporter.otlp.endpoint.headers",
//                "x-honeycomb-team=********************************"
//            )

//"x-honeycomb-dataset" to "ryzen")

//            val honeyClient: HoneyClient = create(
//                options()
//                    .setWriteKey("********************************")
//                    .setDataset("ryzen")
//                    .setSampleRate(2)
//                    .build()
//            );
//
//            honeyClient.createEvent()
//                .addField("userName", "Bob")
//                .addField("userId", UUID.randomUUID().toString())
//                .setTimestamp(System.currentTimeMillis())
//                .send();
//
//            val tracer: Tracer = GlobalOpenTelemetry.getTracer("my-service")
//            val span: Span = tracer.spanBuilder("expensive-query").startSpan()
//
//// ... do cool stuff
//
//
//// ... do cool stuff
//            span.end()

```

## utils/TimeFormatter.kt

```kotlin
// File: TimeFormatter.kt
package au21.engine.framework.utils

import org.joda.time.DateTime
import java.util.*

/**
 * from:
 * - https://ao.ms/human-readable-duration-format-in-java/
 */
object TimeFormatter {

    fun formatDuration(toDate: Date): String? {
        val secs: Int = (toDate.time - DateTime().toDate().time).toInt() / 1_000
        return if (secs < 0)
            null
        else formatDuration(secs)
    }

    fun formatDuration(seconds: Int): String {
        var _secs = seconds
        var res = ""
        val units = intArrayOf(31536000, 86400, 3600, 60, 1)
        val labels = arrayOf("year", "day", "hour", "minute", "second")
        if (_secs == 0) return "now"
        for (i in 0..4) {
            if (_secs >= units[i]) {
                val q = _secs / units[i]
                _secs %= units[i]
                res += ((if (res == "") "" else if (_secs == 0) " and " else ", ")
                        + q + " " + labels[i] + if (q > 1) "s" else "")
            }
        }
        return res
    }

}

```

## utils/au-format-utils.kt

```kotlin
// File: au-format-utils.kt
package au21.engine.framework.utils

import io.quarkus.logging.Log
import org.joda.time.DateTime
import org.joda.time.DateTimeZone
import org.joda.time.Interval
import org.joda.time.Period
import org.joda.time.format.DateTimeFormat
import org.joda.time.format.DateTimeFormatter
import org.joda.time.format.ISODateTimeFormat
import java.text.DecimalFormat
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.max

// THOUSANDS:

private val thousands_formatter = DecimalFormat("#,###")
fun Int?.thousands(): String = this?.toLong().thousands()
fun Long?.thousands(): String =
    if (this == null) ""
    else thousands_formatter.format(this)
// val mem_format_thousands = Memoize1(::format_thousands)


object AuFormatter {

    ///////////////////////////////////////////////////
    // TODO: add memoization
    // from: https://gist.github.com/sureshg/9aa4bed513f8d4179ad2893ecb7886f4

//    val memIsTextPresent = Memoize2(isTextPresent)
//
//    fun isTextPresent(path: Path, text: String) = path.toFile().readText().contains(text, true)
//
//    class Memoize2<A, B, C>(val func: (A, B) -> C) : (A, B) -> C {
//        val cache = hashMapOf<String, C>()
//        override fun invoke(p1: A, p2: B) = cache.getOrPut(p1.toString(), { func(p1, p2) })
//    }
//
//    class Memoize1<A, C>(val func: (A) -> C) : (A) -> C {
//        val cache = hashMapOf<String, C>()
//        override fun invoke(p1: A) = cache.getOrPut(p1.toString(), { func(p1) })
//    }

    ////////////////////////////////////////////////////

    val fmt: DateTimeFormatter? = ISODateTimeFormat.dateTime()
    val bid_table_formatter: DateTimeFormatter? = DateTimeFormat.forPattern("MMM dd, HH:mm:ss")
    val flatpickr_fmt: DateTimeFormatter? =
        DateTimeFormat.forPattern("yyyy-MM-dd'T'HH:mm:ss'Z'").withZone(DateTimeZone.UTC)
    val place_formatters = LinkedHashMap<Int, DecimalFormat>()
    val simple_date: DateTimeFormatter = DateTimeFormat.forPattern("MMM dd")
    val simple_time: DateTimeFormatter = DateTimeFormat.forPattern("HH:mm:ss")

    val auction_row_date_formatter = SimpleDateFormat("EEE, d MMM yyyy")
    val auction_row_time_formatter = SimpleDateFormat("HH:mm:ss zzz")

    val order_time_format = SimpleDateFormat("HH:mm:ss")
    val months = arrayOf("Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec")

    fun parseIsoDateString(s: String): Date? =
        try {
            fmt!!.parseDateTime(s).toDate()
        } catch (e: Exception) {
            Log.info(e.message)
            null
        }

    fun date_time_format(d: Date): String =
        "${simple_date.print(d.time)}, ${simple_time.print(d.time)}"

    //@Memoized
    fun formatDateTime_for_flatpickr(date: Date): String =
        flatpickr_fmt!!.print(date.time)

    fun bid_datetime_format(date: Date?): String =
        date?.let { bid_table_formatter!!.print(it.time) } ?: ""

    fun formatDateTime_for_te_status(date: Date): String =
        simple_time.print(date.time) + " on " + simple_date.print(date.time)

    // @Memoized
    fun toShortMonth(m: Int): String = months[m]

    //@Memoized
    fun to_duration(start: DateTime, end: DateTime): String {
        val interval = Interval(start, end)
        //val d: Duration = interval.toDuration()
        val p: Period = interval.toPeriod()

        return (if (p.years > 0) p.years.toString() + " years, " else "") +
                (if (p.months > 0) p.months.toString() + " months, " else "") +
                (if (p.days > 0) p.days.toString() + " days" else "") +
                (if (p.hours > 0) p.hours.toString() + " hours, " else "") +
                (if (p.minutes > 0) p.minutes.toString() + " minutes, " else "") +
                p.seconds + " seconds"
    }

//    fun round_to_places(d: Double, places: Int): Double {
//        val factor = Math.pow(10.0, places.toDouble())
//        return Math.round(d * factor) / factor
//    }

    // @Memoized
    fun format_to_places(d: Double, decimal_places: Int): String {
        val places: Int = max(0, decimal_places)

        var format = "#,##0"
        if (places > 0) {
            format += "."
            repeat(places) { format += "0" }
        }

        var df: DecimalFormat? = place_formatters[decimal_places]
        if (df == null) {
            df = DecimalFormat(format)
            place_formatters[decimal_places] = df
        }

        return df.format(d) // added a default
    }

    fun format_currency(d: Double?): String = when(d){
        null -> ""
        else -> "$" + format_to_places(d, 2)
    }

    fun format_order_time(d: Date?): String =  // String fmt_str = "HH:mm:ss.SSS"
        d?.let { order_time_format.format(it) } ?: ""

    fun ltrim(s: String): String =
        s.replace("^\\s+".toRegex(), "")

    fun rtrim(s: String): String =
        s.replace("\\s+\$".toRegex(), "")

    fun seconds_until(d: Date?): Int? = d?.let {
        Period(DateTime(d), DateTime.now()).seconds
    }


}

```

## utils/compression.kt

```kotlin
// File: compression.kt
package au21.engine.framework.utils

import au21.engine.framework.client.ClientCommand
import java.io.ByteArrayOutputStream
import java.nio.ByteBuffer
import java.nio.charset.StandardCharsets.UTF_8
import java.util.zip.GZIPOutputStream


//fun to_json_gzip(o: Any): ByteBuffer =
//    gzip(o.to_json())

fun gzip(content: String): ByteBuffer =
    ByteArrayOutputStream().let { bos ->
        GZIPOutputStream(bos).bufferedWriter(UTF_8).use { it.write(content) }
        ByteBuffer.wrap(bos.toByteArray())
    }

fun gzipClientCommand(c: ClientCommand): ByteBuffer =
    gzip(c.to_json())

```

## utils/config.kt

```kotlin
// File: config.kt
package au21.engine.framework.utils

import io.quarkus.logging.Log
import jakarta.annotation.PostConstruct
import jakarta.enterprise.context.ApplicationScoped
import org.eclipse.microprofile.config.inject.ConfigProperty

@ApplicationScoped
class HeartbeatConfig {

    @ConfigProperty(name = "HEARTBEAT", defaultValue = "ON")
    private lateinit var heartbeatStatus: String

    val heartbeat_enabled: Boolean
        get() = heartbeatStatus != "OFF"

    @PostConstruct
    fun init() {
        Log.info("HeartbeatConfig initialized with:$heartbeatStatus")
    }

}

```

## utils/enum-utils.kt

```kotlin
// File: enum-utils.kt
package au21.engine.framework.utils

/**
Oct 4, 2021
Frome: https://stackoverflow.com/questions/35666815/enum-valueof-in-kotlin
 */
// REPLACED BELOW
//inline fun <reified T : Enum<T>> enumValueOfWithDefault(name: String, default: T): T {
//    return try {
//        java.lang.Enum.valueOf(T::class.java, name)
//    } catch (e: Throwable) {
//        default
//    }
//}


/**
 * Created May 27, 2024 by Opus3
 */

/**
 * Converts the specified string [name] to the corresponding enum value of type [T].
 *
 * @param name The name of the enum value.
 * @return The enum value corresponding to the specified [name], or `null` if no matching enum value is found.
 */
inline fun <reified T : Enum<T>> toEnumOrNull(name: String): T? {
    return T::class.java.enumConstants.firstOrNull { it.name == name }
}

/**
 * Converts the specified string [name] to the corresponding enum value of type [T],
 * or returns the [default] value if no matching enum value is found.
 *
 * @param name The name of the enum value.
 * @param default The default enum value to return if no matching enum value is found.
 * @return The enum value corresponding to the specified [name], or the [default] value if no matching enum value is found.
 */
inline fun <reified T : Enum<T>> toEnumWithDefault(name: String, default: T): T {
    return toEnumOrNull<T>(name) ?: default
}

/**
 * Converts the specified string [name] to the corresponding enum value of type [T].
 *
 * @param name The name of the enum value.
 * @return The enum value corresponding to the specified [name].
 * @throws IllegalArgumentException If no matching enum value is found.
 */
inline fun <reified T : Enum<T>> toEnumOrError(name: String): T {
    return toEnumOrNull<T>(name) ?: throw IllegalArgumentException("Invalid enum value: $name")
}

```

## utils/json-utils.kt

```kotlin
// File: json-utils.kt
package au21.engine.framework.utils


import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.google.gson.GsonBuilder
import com.google.gson.JsonObject
import com.google.gson.JsonParser
import io.micrometer.core.annotation.Timed
//import org.eclipse.microprofile.opentracing.Traced

val mapper = jacksonObjectMapper()

/**
 * 1) JsonIterator:
 *
 * used for to_json()
 * ie: object -> json string
 * because GSON cant handle object{}.to_json()
 */

// NOTE TO USE JAVAASSIST, USE THESE, BUT then object{}.to_json() won't work!
// JsonIterator.setMode(DecodingMode.DYNAMIC_MODE_AND_MATCH_FIELD_WITH_HASH);
// JsonStream.setMode(EncodingMode.DYNAMIC_MODE);

//fun Any?.to_json(): String =
//        this?.let { JsonStream.serialize(this) }
//                ?: throw Exception("to_json: cannot parse: $this")

//fun Any?.to_json(): String =
//        this?.let { gson.toJson(this) }
//                ?: throw Exception("to_json: cannot parse: $this")

//val au_mapper:ObjectMapper = jacksonObjectMapper()

private val gson = GsonBuilder().setPrettyPrinting().create()

fun jsonToPrettyFormat(jsonString: String?): String? {
    val json: JsonObject = JsonParser.parseString(jsonString).asJsonObject
    return gson.toJson(json)
}

fun objToPrettyFormat(o:Any):String {
    return gson.toJson(o)
}

//private val gson = GsonBuilder()
//    //.excludeFieldsWithModifiers(Modifier.PRIVATE)
//    .create()


//@Traced
@Timed("to_json")
fun Any?.to_json(): String =
    try {
        // either of these work:
        // gson.toJson(this) // Gson: 700 ms
        // Json.encode(this) // Java Json: 500 ms
        mapper.writeValueAsString(this) // Jackson: 500 ms
        // JsonStream.serialize(this) // JsonIter: 600 ms
    } catch (e: Throwable) {
        e.printStackTrace()
        throw e
    }


// allows you to add arguments to the object and the json string
// TODO: doesn't catch args that are the same as the object's fields I don't
//  think
fun Any.to_json_with_args(message: String, vararg args: Any?): String {
    try {
        val jsonNode = mapper.readTree(message) as ObjectNode
        require(args.size % 2 == 0) { "Vararg arguments must be provided in key-value pairs" }
        for (i in args.indices step 2) {
            val key = args[i].toString()
            val value = args[i + 1].toString()
            jsonNode.put(key, value)
        }
        val formattedJson = ObjectMapper().writerWithDefaultPrettyPrinter()
        return formattedJson.writeValueAsString(jsonNode)
    } catch (e: Exception) {
        throw e
    }
}


//       this?.let { au_mapper.writeValueAsString(this) }
//   Json.encode(this)
//               ?: throw Exception("to_json: cannot parse: $this")

//
//fun get_jsoniter_any(json: String): com.jsoniter.any.Any =
//        JsonIterator.deserialize(json)

//inline fun <reified T> from_json(json: String): T =
//        from_json(json, T::class.java) as T

// this doesn't work because of constructor issue:
// fun from_json(json: String, ct: Class<*>?): Any? =
//        JsonIterator.parse(json).read(ct)

/**
 * 2) GSON:
 *
 * used for from_json()
 * ie: json string -> object
 * because JsonIterator requires default class parameters!
 */

//val gson by lazy { Gson() }
//
//// fun Any?.to_gson(): String =
////        this?.let { gson.toJson(it) }
////                ?: throw Exception("to_json: cannot parse: $this")
//
////fun Any?.to_gson(): String =
////        this?.let { gson.toJson(it) }
////                ?: throw Exception("to_json: cannot parse: $this")
//
//inline fun <reified T> from_json(json: String): T? =
//        try {
//            gson.fromJson(json, T::class.java)
//        } catch (e: Exception) {
//            ///Log.error("failed to parse ct: $ct:$json")
//            //e.printStackTrace()
//            null
//        }

/**
 * 3) JACKSON:
 */

//class BooleanPropNamingStrategy : PropertyNamingStrategy() {
//    override fun nameForGetterMethod(
//        config: MapperConfig<*>,
//        method: AnnotatedMethod,
//        defaultName: String): String =
//            if (method.let {
//                        it.hasReturnType()
//                                && (it.rawReturnType === Boolean::class.java || it.rawReturnType === Boolean::class.javaPrimitiveType)
//                                && it.name.startsWith("is")
//                    })
//                method.name
//            else
//                super.nameForGetterMethod(config, method, defaultName)
//}

// NB: name 'mapper' is as static object in vertex-common !!
// val au_mapper = jacksonObjectMapper()

//    .setPropertyNamingStrategy(BooleanPropNamingStrategy)
//        .setPropertyNamingStrategy(BooleanPropNamingStrategy())

////// from vertx klutter:
////@Suppress("UNCHECKED_CAST", "PLATFORM_CLASS_MAPPED_TO_KOTLIN")
////fun mapFrom(something: Any): JsonObject {
////    return jsonObjectFromMap<Any?>(Json.mapper.convertValue(something, java.util.Map::class.java) as Map<String, Any?>)
////}
//
//
//fun Any?.json_node(): JsonNode =
//        this?.let { au_mapper.valueToTree<JsonNode>(this) }
//                ?: NullNode.instance
//
//val gson:Gson = GsonBuilder().setPrettyPrinting().create()
//
//fun Any?.to_pretty(): String =
//        when (this) {
//            null           -> "null"
//            is String      -> this
//            is Optional<*> -> when {
//                isPresent() -> get().to_pretty()
//                else        -> "null"
//            }
//            else           ->
//                this::class.java.name + "\n" + gson.toJson(this)
//                   //au_mapper.
//                  //  .writerWithDefaultPrettyPrinter()
//                   //     .writeValueAsString(this)
//        }

//
//fun json_diff(source: Any?, target: Any?): String =
//        JsonDiff.asJson(source.json_node(), target.json_node()).let {
//            au_mapper.writerWithDefaultPrettyPrinter()
//                    .writeValueAsString(it)
//        }


/*
// Vertx:

fun to_json(o: Any?): JsonObject = JsonObject.mapFrom(o)

inline fun <reified T> from_json(json: String): T? =
        try {
            JsonObject(json).mapTo(T::class.java)
            //gson.fromJson(json, T::class.java)
        } catch (e: Exception) {
            Log.error("failed to parse as <${T::class.java}>:" + json)
            e.printStackTrace()
            null
        }
*/


// FROM: https://stackoverflow.com/questions/2253750/testing-two-json-objects-for-equality-ignoring-child-order-in-java
//
//object JSONUtils {
//
//    @Throws(JSONException::class)
//    fun areEqual(ob1: Any, ob2: Any): Boolean =
//            convertJsonElement(ob1) == convertJsonElement(ob2)
//
//
//    @Throws(JSONException::class)
//    private fun convertJsonElement(elem: Any): Any {
//        when (elem) {
//            is JSONObject -> {
//                val obj: JSONObject = elem as JSONObject
//                return listOf(obj.keys())
//                        .map { it.toString() }
//                        .fold(mapOf(), { o: Map<String, Any>,
//                                         k: String ->
//                            o + mapOf(k to convertJsonElement(obj.get(k)))
//                        })
//            }
//            is JSONArray  -> {
//                val arr = elem as JSONArray
//                val jsonSet = HashSet<Any>()
//                return listOf(arr)
//                        .fold(setOf(), { s: Set<Any>,
//                                         e: Any ->
//                            s + convertJsonElement(e)
//                        })
//            }
//            else          ->
//                return elem
//        }
//    }
//
//    fun compare_objects(obj1: Any, obj2: Any, fn: (Any, Any) -> Any) {
//        fn(convertJsonElement(obj1), convertJsonElement(obj2))
//    }
//}

```

## utils/logging.kt

```kotlin
// File: logging.kt
package au21.engine.framework.utils

import io.vertx.core.json.JsonObject
import java.util.logging.ConsoleHandler
import java.util.logging.LogRecord

class CustomJsonLogHandler : ConsoleHandler() {

    override fun publish(record: LogRecord) {
        if (!isLoggable(record)) {
            return
        }

        val json = JsonObject()
        json.put("timestamp", java.time.Instant.ofEpochMilli(record.millis).toString())
        json.put("level", record.level.name)
        json.put("message", record.message)
        json.put("class", record.sourceClassName.substringAfterLast('.'))
        json.put("line", record.sourceMethodName)

        // Print the formatted log to console
        println(json.encodePrettily())
    }
}

```

## utils/misc-utils.kt

```kotlin
// File: misc-utils.kt
package au21.engine.framework.utils

import io.quarkus.logging.Log
import org.joda.time.format.DateTimeFormatter
import org.joda.time.format.ISODateTimeFormat
import java.util.*
import kotlin.math.floor
import kotlin.math.pow

fun String?.is_blank(): Boolean =
    this?.trim()?.isEmpty() ?: true


// safe enums:

inline fun <reified T : Enum<T>> printAllValues() {
    print(enumValues<T>().joinToString { it.name })
}

//inline fun <reified T : Enum<T>?> enumValueOrNull(type: String): T? =
//    try {
//        java.lang.Enum.valueOf(T::class.java, type)
//    } catch (e: Exception) {
//        Log.info(e.message)
//        null
//    }

/*
fun Number.roundToPlaces(places: Int): String {
    require(places >= 0) {"places is ${places} but cannot be less than zero."}
    // TODO: cache of memoize this:
    val format_str = "#." + (0 until places).fold("") { acc: String, next: Int -> acc.plus("#") }
    Log.info(format_str)
    val df = DecimalFormat(format_str)
    return df.format(this).let { if(it.endsWith(".")) it.dropLast(1) else it }
}
*/

// copied and pasted from DefaultGroovyMethods.java !!
fun Double.round(precision: Int): Double =
    floor(this * 10.0.pow(precision.toDouble()) + 0.5) /
            10.0.pow(precision.toDouble())

fun main() {
    (0..10).forEach {
        Log.info(Math.PI.round(it))
    }

}

val iso_date_time_formatter: DateTimeFormatter =
    ISODateTimeFormat.dateTime()

fun String.iso_to_date(): Date? =
    try {
        iso_date_time_formatter.parseDateTime(this).toDate()
    } catch (e: Exception) {
        Log.info(e.message)
        null
    }

fun Date.date_to_iso(): String =
    iso_date_time_formatter.print(this.time)

// Idable:

interface Ideable {
    val id: String
}

// Result, like in JS:

sealed class CBResult<out T> {
    data class CBSuccess<out T>(val data: T) : CBResult<T>()
    data class CBError<out T>(val throwable: Throwable) : CBResult<T>()
}

fun <T> T.asResult() =
    CBResult.CBSuccess(data = this)

fun <T> Throwable.asErrorResult() =
    CBResult.CBError<T>(throwable = this)

// fun duration_ms(start: Long): Int = ((System.nanoTime() - start) / 1_000_000).toInt()

//inline fun <reified T> duration_ms(msg:String? = null, fn: () -> T): T {
//    val start = System.nanoTime()
//    return fn().also {
//        when(msg){
//            null -> println("${duration_ms(start)} ms")
//            else -> println("$msg took ${duration_ms(start)} ms")
//        }
//    }
//}

// example use:
//userProvider.getUsers().subscribe {
//    when (it) {
//        is Result.Success -> handleSuccess(result.data)
//        is Result.Error -> handleError(result.throwable)
//    }
//}

//data class Callback_Result<out T>(
//        val data: T?,
//        val error: Throwable?
//)
//
//fun <T> T.asResult(): Callback_Result<T> {
//    return Callback_Result(data = this, error = null)
//}
//
//fun <T> Throwable.asErrorResult(): Callback_Result<T> {
//    return Callback_Result(data = null, error = this)
//}

///////////////////////

```

## utils/string-utils.kt

```kotlin
// File: string-utils.kt
package au21.engine.framework.utils


private val charPool : List<Char> = ('a'..'z') + ('A'..'Z') + ('0'..'9')


fun randomString(string_length:Int):String = (1..string_length)
    .map { i -> kotlin.random.Random.nextInt(0, charPool.size) }
    .map(charPool::get)
    .joinToString("")


fun main(){
    println(randomString(4))
}

```

## utils/table-formatter.kt

```kotlin
// File: table-formatter.kt
package au21.engine.framework.utils

import de.vandermeer.asciitable.AT_Row
import de.vandermeer.asciitable.AsciiTable
import de.vandermeer.skb.interfaces.transformers.textformat.TextAlignment
import io.quarkus.logging.Log
import kotlin.reflect.KCallable
import kotlin.reflect.full.declaredMembers

inline fun <reified T> format_table(
    collection: Collection<T>,
    field_names: List<String>,
    print_json: Boolean = false
): String {

    // 1) with KCallable members (MUST USE declaredMembers, as members -> Companion members only (?!))

    val members: List<KCallable<*>> =
        field_names.mapNotNull { name ->
            try {
                T::class.declaredMembers.find { m -> m.name == name }
                    ?: run {
                        println("field not found: $name")
                        null
                    }
            } catch (e: Throwable) {
                println("field not found: $name")
                null
            }
        }

    if (members.isEmpty()) {
        println("NO FIELDS FOUND")
        return ""
    }

    try {
        AsciiTable().apply {
            addRule()
            addRow(members.map { it.name })
            addRule()
            collection.forEach { item ->
                addRow(members.map { it.call(item) })
                    .also { row: AT_Row ->
                        members.forEachIndexed { index, m ->
                            when (m.call(item)) {
                                is Int ->
                                    row.cells[index].context.textAlignment = TextAlignment.RIGHT
                            }
                        }
                    }
            }
            addRule()
            return "\n + ${T::class.java.name}:\n" + render()
        }
    } catch (t: Throwable) {
        Log.error(t.localizedMessage)
        return ""
    }

//    if (print_json) {
//        // TODO: does this work with primitives?
//        println(objToPrettyFormat(collection.forEach { println(it.to_json()) }))
//    }

    // 2) with javaclass fields: not working only get companion

//    val fields:List<Field> = field_names.mapNotNull { name ->
//        try {
//            T::class.java.fields.find { f:Field ->
//                f.name == name
//            }
//                ?: run {
//                    println("field not found: $name")
//                    null
//                }
//        } catch (e: Throwable) {
//            println("field not found: $name")
//            null
//        }
//    }
//
//    if (fields.isEmpty()){
//        println("NO FIELDS FOUND")
//        return
//    }
//
//    AsciiTable().apply {
//        addRule()
//        // addRow(members.map { it.name })
//        addRow(fields.map { it.name })
//        addRule()
//        collection.forEach { item ->
//            addRow(fields.map { it.get(item) })
//                .also { row: AT_Row ->
//                    fields.forEachIndexed { index, f ->
//                        val type = f.type
//                        when (type.toString()) {
//                            "Int" ->
//                                row.cells[index].context.textAlignment = TextAlignment.RIGHT
//                        }
//                    }
//                }
//        }
//        addRule()
//        println(render())
//    }


}


//fun print_object_as_table(
//    obj: Any,
//    print_json: Boolean = true
//) {
//
//    val members = obj::class.declaredMembers
//        .filter { m: KCallable<*> ->
//            m.visibility == KVisibility.PUBLIC } //it.visibility == KVisibility.PUBLIC  }
//      //  .filter { m: KCallable<*> -> m.parameters.isEmpty() } // this should take care of collections, as they have a type
//
//    if (members.isEmpty()) {
//        println("NO FIELDS FOUND")
//        return
//    }
//
//    AsciiTable().apply {
//        addRule()
//        addRow("property", "value")
//        addRule()
//        members.forEach { m ->
//            val value = m.call(obj)
//            addRow(m.name, value)
//                .also { row: AT_Row ->
//                    when (value) {
//                        is Int ->
//                            row.cells[1].context.textAlignment = TextAlignment.RIGHT
//                    }
//                }
//        }
//        addRule()
//        println(render())
//    }
//
////  2) JAVA Fields: can't exclude private:
////
////    val fields: List<Field> = obj::class.java.declaredFields
////        .filterNot { f ->
////            println(f.modifiers)
////            Modifier.isPrivate(f.modifiers)
////        }
////
////    if (fields.isEmpty()) {
////        println("NO FIELDS FOUND")
////        return
////    }
////
////    AsciiTable().apply {
////        addRule()
////        addRow("property", "value")
////        addRule()
////        fields.forEach { f ->
////            val value = f.get(obj)
////            addRow(f.name, value)
////                .also { row: AT_Row ->
////                    when (value) {
////                        is Int ->
////                            row.cells[1].context.textAlignment = TextAlignment.RIGHT
////                    }
////                }
////        }
////        addRule()
////        println(render())
////    }
//
//}
//

```

