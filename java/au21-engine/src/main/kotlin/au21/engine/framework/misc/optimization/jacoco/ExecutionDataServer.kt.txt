/*******************************************************************************
 * Copyright (c) 2009, 2021 Mountainminds GmbH & Co. KG and Contributors
 * This program and the accompanying materials are made available under
 * the terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 * Contributors:
 * Marc <PERSON> - initial API and implementation
 *
 */
package au21.engine.framework.optimization.jacoco

import org.jacoco.core.data.ExecutionData

/**
 * This example starts a socket server to collect coverage from agents that run
 * in output mode `tcpclient`. The collected data is dumped to a
 * local file.
 */
object `ExecutionDataServer.txt` {
    private const val DESTFILE = "jacoco-server.exec"
    private const val ADDRESS = "localhost"
    private const val PORT = 6300

    /**
     * Start the server as a standalone program.
     *
     * @param args
     * @throws IOException
     */
    @Throws(IOException::class)
    @JvmStatic
    fun main(args: Array<String>) {
        val fileWriter = ExecutionDataWriter(
            FileOutputStream(DESTFILE)
        )
        val server = ServerSocket(
            PORT, 0,
            InetAddress.getByName(ADDRESS)
        )
        while (true) {
            val handler = Handler(server.accept(), fileWriter)
            Thread(handler).start()
        }
    }

    private class Handler internal constructor(socket: Socket, fileWriter: ExecutionDataWriter) : Runnable,
        ISessionInfoVisitor, IExecutionDataVisitor {
        private val socket: Socket
        private val reader: RemoteControlReader
        private val fileWriter: ExecutionDataWriter
        override fun run() {
            try {
                while (reader.read()) {
                }
                socket.close()
                synchronized(fileWriter) { fileWriter.flush() }
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }

        fun visitSessionInfo(info: SessionInfo) {
            System.out.printf(
                "Retrieving execution Data for session: %s%n",
                info.getId()
            )
            synchronized(fileWriter) { fileWriter.visitSessionInfo(info) }
        }

        fun visitClassExecution(data: ExecutionData?) {
            synchronized(fileWriter) { fileWriter.visitClassExecution(data) }
        }

        init {
            this.socket = socket
            this.fileWriter = fileWriter

            // Just send a valid header:
            RemoteControlWriter(socket.getOutputStream())
            reader = RemoteControlReader(socket.getInputStream())
            reader.setSessionInfoVisitor(this)
            reader.setExecutionDataVisitor(this)
        }
    }
}
