package au21.engine.framework.misc.pubsub

//import au21.engine.framework.utils.to_pretty
import au21.engine.framework.commands.client.ClientCommandsEnvelope
import MonitorSocket.Companion.MONITOR_CHANNEL
import au21.engine.framework.utils.toPrettyFormat
import au21.engine.framework.utils.to_json
import io.lettuce.core.RedisClient
import io.lettuce.core.pubsub.RedisPubSubListener
import io.lettuce.core.pubsub.StatefulRedisPubSubConnection
import io.vertx.core.eventbus.EventBus
import org.eclipse.microprofile.config.inject.ConfigProperty
import java.util.concurrent.LinkedBlockingQueue
import javax.annotation.PostConstruct
import javax.annotation.PreDestroy
import javax.enterprise.context.ApplicationScoped
import javax.inject.Inject

@ApplicationScoped
class AuRedisClient(
    // TODO: Replace these untyped strings with an Inject Config object (probably using dotenv):
    @ConfigProperty(name = "REDIS_URL") val REDIS_URL: String,
    @ConfigProperty(name = "ENGINE_INPUT_CHANNEL") val ENGINE_INPUT_CHANNEL: String,
    @ConfigProperty(name = "ENGINE_OUTPUT_CHANNEL") val ENGINE_OUTPUT_CHANNEL: String
) {

    /**
     * API:
     * - 1) Subscription: messages appear on request_queue
     * - 2) Publishing: publish()
     * NOTE: parking eventbus here for the moment
     */

    @Inject
    lateinit var bus: EventBus

    val request_queue = LinkedBlockingQueue<String>()

    //@Traced
    fun publish(results: ClientCommandsEnvelope) {
        if (results.client_command_maps.isEmpty())
            return
        try {
           // bus.publish(SESSION_CHANNEL, results)
            results.to_json().let { json ->
                Logger.info(toPrettyFormat(json))
                publisher_connection.sync().publish(ENGINE_OUTPUT_CHANNEL, json)
                bus.publish(MONITOR_CHANNEL, json)
            }
        } catch (e: Throwable) {
            e.printStackTrace()
        }
    }

    /**
     * PRIVATE
     */

    private lateinit var client: RedisClient
    private lateinit var subcriber_connection: StatefulRedisPubSubConnection<String, String>
    private lateinit var publisher_connection: StatefulRedisPubSubConnection<String, String>

    @PostConstruct
    fun postConstruct() {
        try {
            client = try {
                RedisClient.create(REDIS_URL)
            } catch (e: Exception) {
                Logger.info("Unable to open redis connection: $REDIS_URL")
                throw e
            }
            subcriber_connection = client.connectPubSub()
            subcriber_connection.addListener(listener)
            subcriber_connection.sync().subscribe(ENGINE_INPUT_CHANNEL)

            publisher_connection = client.connectPubSub()

        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @PreDestroy
    fun preDestroy() {
        try {
            // probably don't need the first 2 lines, and maybe not even the third:
            subcriber_connection.removeListener(listener)
            subcriber_connection.sync().unsubscribe(ENGINE_INPUT_CHANNEL)
            subcriber_connection.close()
            client.shutdown()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private val listener = object : RedisPubSubListener<String, String> {
        override fun punsubscribed(pattern: String?, count: Long) {
            Logger.info("punsubscribed: pattern=$pattern, count=$count")
        }

        override fun unsubscribed(channel: String?, count: Long) {
            Logger.info("unsubscribed: channel=$channel, count=$count")
        }

        override fun subscribed(channel: String?, count: Long) {
            Logger.info("subscribed: channel=$channel, count=$count")
        }

        override fun message(channel: String?, message: String?) {
            try {
                Logger.info("message: channel=$channel, message=${message.to_json()}")
                message?.let {
                    request_queue.add(it)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        override fun message(pattern: String?, channel: String?, message: String?) {
            Logger.info("message: pattern+$pattern, channel=$channel, message=$message")
        }

        override fun psubscribed(pattern: String?, count: Long) {
            Logger.info("psubscribed: pattern=$pattern, count=$count")
        }
    }

}
