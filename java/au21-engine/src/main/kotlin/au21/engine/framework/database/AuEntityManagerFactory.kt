// File: AuEntityManagerFactory.kt
package au21.engine.framework.database

import io.quarkus.logging.Log
import jakarta.annotation.PostConstruct
import jakarta.enterprise.context.ApplicationScoped
import jakarta.ws.rs.Produces
import org.eclipse.microprofile.config.inject.ConfigProperty
import java.io.File
import javax.persistence.EntityManagerFactory
import javax.persistence.Persistence


@ApplicationScoped
class AuEntityManagerFactory {

    @ConfigProperty(name = "OBJECTDB_ACTIVATION_CODE")
    lateinit var OBJECTDB_ACTIVATION_CODE: String

    @ConfigProperty(name = "OBJECTDB_ADMIN_USER", defaultValue = "admin")
    lateinit var OBJECTDB_ADMIN_USER: String

    @ConfigProperty(name = "OBJECTDB_ADMIN_PASSWORD", defaultValue = "admin")
    lateinit var OBJECTDB_ADMIN_PASSWORD: String

    @ConfigProperty(name = "OBJECTDB_CONFIG_TEMPLATE_PATH")
    lateinit var OBJECTDB_CONFIG_TEMPLATE_PATH: String

    @ConfigProperty(name = "OBJECTDB_CONFIG_GENERATED_PATH")
    lateinit var OBJECTDB_CONFIG_GENERATED_PATH: String

    @ConfigProperty(name = "OBJECTDB_DB_HOME")
    lateinit var OBJECTDB_DB_HOME: String

    @ConfigProperty(name = "OBJECTDB_URL")
    lateinit var OBJECTDB_URL: String

    @PostConstruct
    fun init(){
        Log.info("OBJECTDB_ACTIVATION_CODE: $OBJECTDB_ACTIVATION_CODE")
        Log.info("OBJECTDB_ADMIN_USER: $OBJECTDB_ADMIN_USER")
        Log.info("OBJECTDB_ADMIN_PASSWORD: $OBJECTDB_ADMIN_PASSWORD")
        Log.info("OBJECTDB_CONFIG_TEMPLATE_PATH: $OBJECTDB_CONFIG_TEMPLATE_PATH")
        Log.info("OBJECTDB_CONFIG_GENERATED_PATH: $OBJECTDB_CONFIG_GENERATED_PATH")
        Log.info("OBJECTDB_DB_HOME: $OBJECTDB_DB_HOME")
        Log.info("OBJECTDB_URL: $OBJECTDB_URL")
    }

    val BASE_DIR: String = System.getProperty("user.dir").let {
        println("user.dir: $it")
        val index = it.indexOf("/build/classes/kotlin/main")
        if (index > -1)
            it.substring(0, index)
        else
            it
    }

    fun to_absolute_path(relative_path: String): String =
        "$BASE_DIR/$relative_path".let { s: String ->
            s.replace("\\\\", "\\")
        }

    val config_logging = false

//    fun log(s: String) {
//        if (config_logging) {
//            Log.info(s)
//        }
//    }

    private fun getTemplateFileFromClasspath(): File {
       // val resource = javaClass.classLoader.getResource("config/objectdb/au21-engine.objectdb.template.xml")
        val resource = javaClass.classLoader.getResource("au21-engine.objectdb.template.xml")
            ?: throw IllegalStateException("ObjectDB template file not found in classpath")
        return File(resource.toURI())
    }


    @ApplicationScoped
    @Produces
    fun createEntityManagerFactory(): EntityManagerFactory {

        // 1) load config template:
        Log.info("\nOBJECTDB_CONFIG_TEMPLATE_PATH: $OBJECTDB_CONFIG_TEMPLATE_PATH")
//        val template: String = to_absolute_path(OBJECTDB_CONFIG_TEMPLATE_PATH).let { path: String ->
//            val f = File(path)
//            if (!f.exists())
//                throw Exception("no objectdb template file found at:$path")
//            log("fully qualified objectdb config template path: ${f.canonicalPath}")
//            f.readText()
//        }

        val template: String = getTemplateFileFromClasspath().readText()

        Log.info("NB: Objectdb config template: \n$template")

        // 2) create config file from template, and set objectdb.conf location
        File(to_absolute_path(OBJECTDB_CONFIG_GENERATED_PATH)).also { f ->
            val path = f.canonicalPath
            Log.info("Location to write Objectdb config file: $path")
            if (f.exists()) {
                Log.info("Deleting existing objectdb config file at: $path")
                f.delete()
            } else {
                Log.info("Creating directories")
                f.parentFile.mkdirs()
            }
            f.createNewFile()
            Log.info("Created empty objectdb config file: $path")

            val generated = template
                .replace("OBJECTDB_ACTIVATION_CODE", OBJECTDB_ACTIVATION_CODE)
                .replace("OBJECTDB_ADMIN_PASSWORD", OBJECTDB_ADMIN_PASSWORD)
                .replace("OBJECTDB_ADMIN_USER", OBJECTDB_ADMIN_USER)
                .replace("OBJECTDB_URL", OBJECTDB_URL)

            f.writeText(generated)
            Log.info("\nConfig file written at: $path")
            Log.info(f.readText())

            // set objectdb.conf location:
            Log.info("Setting objectdb.conf to: $path")
            System.setProperty("objectdb.conf", path)
        }

        // 3) Set objectdb.home:
        val objectdHomeDir: File = File(to_absolute_path(OBJECTDB_DB_HOME)).also {
            Log.info("Setting objectdb.home to: ${it.canonicalPath}")
            System.setProperty("objectdb.home", it.canonicalPath)
            it.mkdirs()
        }

        // 4) Create EMF based on OBJECTDB_URL:

        Log.info("Objectdb url: \n$OBJECTDB_URL")

        //.createEntityManagerFactory("exp11") // used with persistence.xml
        // "objectdb://localhost:6136/myDbFile.odb"
        // note: could do this instead of properties:
        // 'objectdb://localhost/test.odb;user=admin;password=admin'

        // SHOULD WORK BOTH IN SERVER MODE AND EMBEDDED MODE:

        return if (OBJECTDB_URL.endsWith(".mem")) {
            Persistence.createEntityManagerFactory(OBJECTDB_URL)

        } else if (OBJECTDB_URL.startsWith("objectdb://")) {
            Persistence.createEntityManagerFactory(OBJECTDB_URL,
                HashMap<String, String>().apply {
                    put("javax.persistence.jdbc.user", OBJECTDB_ADMIN_USER)
                    put("javax.persistence.jdbc.password", OBJECTDB_ADMIN_PASSWORD)
                })
        } else {
            // otherwise put it in the OBJECTDB_DB_HOME/db
            File(objectdHomeDir, OBJECTDB_URL).let { odb_file ->
                val path = odb_file.canonicalPath
                Log.info("DB Location: $path")
                Persistence.createEntityManagerFactory(path)
            }
        }
    }
}

