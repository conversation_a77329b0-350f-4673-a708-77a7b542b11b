// File: ClientsManager.kt
package au21.engine.framework.client

//import org.eclipse.microprofile.opentracing.Traced
//import io.opentelemetry.instrumentation.annotations.WithSpan
import au21.engine.domain.common.commands.PageSetAction
import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.services.sessions_logged_in
import au21.engine.domain.common.viewmodel.MessageElement
import au21.engine.domain.de.commands.DeRoundControllerAction
import au21.engine.domain.de.viewmodel.DeMatrixRoundElement
import au21.engine.framework.PageName
import au21.engine.framework.client.ClientCommand.*
import au21.engine.framework.client.ClientCommand.StoreCommand.AddElements
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.HeartbeatAction
import au21.engine.framework.commands.interfaces.IAuctionMessage
import au21.engine.framework.commands.interfaces.ISessionsTerminated
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.gzip
import au21.engine.framework.utils.gzipClientCommand
import au21.engine.framework.utils.to_json
import io.quarkus.logging.Log
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.nio.ByteBuffer

/**
 * THIS IS THE SECOND VERSION
 * - for each session, for each property: to-json, gzip, send)
 * ie: none of these optimizations:
 * - 1) skip zipping if json unchanged
 * - 2) cache and share buffers for auctioneers
 * - 3) cache buffers between commands
 * - 4) MOST IMPORTANTLY: A PATH FOR HEARTBEAT ONLY (ie: No change in heartbeat)
 */

@ApplicationScoped
class ClientsManager {

    @Inject
    lateinit var socket_handler: SocketHandler

    // for testing:
    var last_action: EngineAction? = null
    var last_stores: List<StoreCommand.SetLiveStore> = emptyList()

    //   var last_update: Long = 0L

    // @Traced
    //@WithSpan
    fun handle(db: AuEntityManager, action: EngineAction) {
        last_action = action
        val is_not_heartbeat: Boolean = action !is HeartbeatAction
        val should_update = true
        if (should_update) {
            // WE'RE NOT GOING TO UPDATE MORE FREQUENTLY THAN 500 ms
            //  val start = System.nanoTime()
            last_stores = session_live_stores(db)
            //  val duration = duration_ms(start)
            if (is_not_heartbeat) {
                Log.info(action.command::class.java.simpleName + ":" + action.command.to_json())
                // Log.info("CLIENT_STORE_CREATION: took = $duration ms, count = ${stores.size}")
            }
            // TODO: might want a thread pool for this:
            last_stores.forEach {
                it.store.session_user?.session_id?.let { sid -> socket_handler.publish(sid, gzip(it.to_json())) }
            }
        }

        // EVENTS:

        fun auction_sessions(a: Auction): List<AuSession> =
            db.sessions_logged_in().filter { a == it.auction }

        when (action) {
            is DeRoundControllerAction -> {
                // TODO: not sure who is calling this?
                val add_matrix: AddElements =
                    DeMatrixRoundElement.add_rounds(action.de, listOf(action.deRound))
                socket_handler.publish(
                    action.session.session_id,
                    gzip(add_matrix.to_json())
                )
            }
            is PageSetAction -> {
                // TODO: this is for the historical rounds??
                if (action.page == PageName.DE_AUCTIONEER_PAGE) {
                    socket_handler.publish(
                        action.session.session_id,
                        gzip(DeMatrixRoundElement.clear_rounds().to_json())
                    )
                }
            }
            else -> {
                // send companies of changed

            }

        }

        // have to do this separately as there can be implementation of more than one interface
        if (action is ISessionsTerminated) {
            action.sessions_terminated.forEach {
                socket_handler.publish(it.sid, gzipClientCommand(TerminateSession(it.reason)))
            }
        }

        if (action is IAuctionMessage) {
            notify(
                socket_handler,
                MessageElement.recipient_sids_for_message(
                    auction_sessions(action.auction),
                    action.auction,
                    action.message
                ),
                MessageElement.create(action.message)
            )
        }

        // if we have a session id then send CommandSuceeded:
        action.session?.let {
            succeeded(socket_handler, listOf(it.session_id))
        }

    }

    // START_COMMANDS
    // ADD MESSAGE
    // TERMINATE
    // NOTIFY
    // SUCCEEDED

    fun succeeded(socket: SocketHandler, sids: List<String>) {
        gzipClientCommand(CommandSucceeded()).also { buf ->
            sids.forEach { socket.publish(it, buf) }
        }
    }

    fun notify(
        socket: SocketHandler,
        recipient_sids: List<String>,
        m: MessageElement

    ) {
        if (recipient_sids.isEmpty())
            return
        gzipClientCommand(ShowMessage(BrowserMessageKind.NOTIFICATION, listOf(m.message))).also { buf: ByteBuffer ->
            recipient_sids.forEach { socket.publish(it, buf) }
        }

    }

}

//    fun create_store_buffers(
//        db: AuEntityManager,
//        action: EngineAction
//    ): Map<String, ByteBuffer> {
//
//        return duration_ms("SESSION_MANAGER") {
//            val h = SessionStoreHelper(db, action)
//
//            db.sessions_non_terminated().associate { s: AuSession ->
//                val session_store: LiveClientStore = h.session_store(s)
//                val cmd = ClientCommand.StoreCommand.SetLiveStore(session_store)
//                s.session_id to gzip(cmd.to_json())
//            }.toMap()
//        }
//    }

//    ): Map<String, ByteBuffer> {
//
//        val store_map: Map<String, ByteBuffer> =
//            duration_ms("SESSION_MANAGER.HANDLE") {
//                val h = SessionStoreHelper(db, action)
//                db.sessions_non_terminated().associate { s: AuSession ->
//                    val session_store: LiveClientStore = h.session_store(s)
//                    val cmd = ClientCommand.StoreCommand.SetLiveStore(session_store)
//                    s.session_id to gzip(cmd.to_json())
//                }
//            }
//
//        return store_map

//        val sessions: MutableList<Pair<TargetList, ByteBuffer>> = mutableListOf()
//        when (action) {
//            is HeartbeatAction -> {
//
//            }
//            is MessageSendAction -> {
//
//            }
//            is DeFlowControlAction -> {
//
//            }
//            is DeRoundControllerAction -> {
//
//            }
//            else -> {
//            }
//
//
//    }
//
//}
//
