package au21.engine.domain.de.commands.client

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.lastround
import au21.engine.framework.commands.client.ClientCommand.StoreCommand.*
import au21.engine.framework.commands.client.ClientSocket
import au21.engine.framework.commands.client.CommandBuffer
import au21.engine.framework.commands.client.helper.CommandHelper

class DeAuctioneerBuffer(val de: DeAuction) {

    private val de_auction_value = CommandBuffer<SetDeAuction>().apply {
        set_buffer(DeAuctionValue.command_for_auctioneer(de))
    }

    private val de_status_value = CommandBuffer<SetDeStatus>().apply {
        set_buffer(SetDeStatus(de))
    }

    private val matrix_rounds = sortedMapOf<Int, CommandBuffer<AddDeMatrixRound>>().also {
        it[de.lastround().number] =
            CommandBuffer<AddDeMatrixRound>().apply {
                set_buffer(DeMatrixRoundElement.add_round(de))
            }
    }

    private fun matrix_round_buffer(r: DeAuction.Round = de.lastround()): CommandBuffer<AddDeMatrixRound> =
        matrix_rounds[r.number] ?: run {
            CommandBuffer<AddDeMatrixRound>()
                .apply { set_buffer(DeMatrixRoundElement.add_round(de, r)) }
                .also { matrix_rounds[r.number] = it }
        }


    fun update_auctioneers(socket: ClientSocket, de: DeAuction, h: CommandHelper) {

        if (h.auctioneer_sids.isNotEmpty()) {
            de_status_value.update_sessions(socket, h.auctioneer_sids, force = true) {
                SetDeStatus(de)
            }
            de_auction_value.update_sessions(socket, h.auctioneer_sids, force = true) {
                DeAuctionValue.command_for_auctioneer(de)
            }
            matrix_round_buffer(de.lastround()).update_sessions(socket, h.auctioneer_sids, force = true) {
                AddDeMatrixRound(DeMatrixRoundElement(de, de.lastround()))
            }
        }
    }

    fun init_auctioneer(socket: ClientSocket, s: AuSession?) {
        when {
            s == null -> return
            !s.inRole(AuUserRole.AUCTIONEER) -> throw Error("Expected session to be auctioneer")
            else -> {
                socket.publish(s.session_id, de_auction_value.buffer)
                socket.publish(s.session_id, de_status_value.buffer)
                socket.publish(s.session_id, matrix_round_buffer().buffer)
            }
        }
    }

}
