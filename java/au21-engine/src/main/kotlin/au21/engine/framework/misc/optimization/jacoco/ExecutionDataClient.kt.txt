/*******************************************************************************
 * Copyright (c) 2009, 2021 Mountainminds GmbH & Co. KG and Contributors
 * This program and the accompanying materials are made available under
 * the terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 * Contributors:
 * Marc <PERSON> Hoffmann - initial API and implementation
 *
 */
package au21.engine.framework.optimization.jacoco

import org.jacoco.core.data.ExecutionDataWriter
import java.io.FileOutputStream
import java.io.IOException

/**
 * This example connects to a coverage agent that run in output mode
 * `tcpserver` and requests execution data. The collected data is
 * dumped to a local file.
 */
object ExecutionDataClient {
    private const val DESTFILE = "jacoco-client.exec"
    private const val ADDRESS = "localhost"
    private const val PORT = 6300

    /**
     * Starts the execution data request.
     *
     * @param args
     * @throws IOException
     */
    @Throws(IOException::class)
    @JvmStatic
    fun main(args: Array<String>) {
        val localFile = FileOutputStream(DESTFILE)
        val localWriter = ExecutionDataWriter(
            localFile
        )

        // Open a socket to the coverage agent:
        val socket = Socket(InetAddress.getByName(ADDRESS), PORT)
        val writer = RemoteControlWriter(
            socket.getOutputStream()
        )
        val reader = RemoteControlReader(
            socket.getInputStream()
        )
        reader.setSessionInfoVisitor(localWriter)
        reader.setExecutionDataVisitor(localWriter)

        // Send a dump command and read the response:
        writer.visitDumpCommand(true, false)
        if (!reader.read()) {
            throw IOException("Socket closed unexpectedly.")
        }
        socket.close()
        localFile.close()
    }
}
