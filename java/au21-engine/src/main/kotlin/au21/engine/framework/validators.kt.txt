package au21.engine.framework

import javax.validation.Validation
import kotlin.reflect.KProperty

// usage:


data class CommandErrors(
    val command: String,
    val errors: List<String>
)

class PropertyErrors(
    val property: String,
    val errors: List<String>
)

class AuValidator {

    fun validate(o: Any, validations: List<Any>): CommandErrors {

    }


fun AuValidator.err_if_blank(prop: KProperty<String>, name: String? = null): String =
    prop.getter.call().apply {
        if (is_blank())
            errors.add("${humanize(name ?: prop.name)} cannot be blank.")
    }.trim()

fun AuValidator.err_if_null(o: Any?, err: () -> String) {
    if (o == null) errors.add(err())
}

fun AuValidator.err_if(pred: <PERSON><PERSON>an, err: () -> String) {
    if (!pred) errors.add(err())
}

fun AuValidator.fail_if_errors() {
    if (!this.errors.isEmpty()) throw Alert_Exception(errors)
}

fun AuValidator.alert(err: () -> String): Nothing = throw Alert_Exception(err())

fun AuValidator.fail_if(check: <PERSON><PERSON><PERSON>, err: () -> String) {
    if (check) alert(err)
}

fun AuValidator.fail_if_not(check: Boolean, err: () -> String) {
    if (!check) alert(err)
}

fun AuValidator.fail_if_null(o: Any?, err: () -> String) {
    if (o == null) alert(err)
}

/*************
 * numbers:
 * - concept is to be able to take a propert, an error suffix and an optional test
 * -> add an error if need be
 * -> return the number or null
 */

fun humanize(s: String): String = s.replace("_", " ").capitalize()

fun AuValidator.toIntOrError(
    prop: KProperty<String>,
    pred: ((Int?) -> Boolean)? = null,
    err_fn: (String) -> String
): Int? =

    prop.getter.call().toIntOrNull()?.takeIf {
        if (pred != null) pred(it)
        else true
    } ?: {
        errors.add(humanize(err_fn(prop.name))) // TODO: humanize the property name
        null
    }()

fun AuValidator.toLongOrError(
    prop: KProperty<String>,
    pred: ((Long?) -> Boolean)? = null,
    err_fn: (String) -> String
): Long? =

    prop.getter.call().toLongOrNull()?.takeIf {
        if (pred != null) pred(it)
        else true
    } ?: {
        errors.add(
            humanize(
                err_fn(
                    prop.name
                )
            )
        ) // TODO: humanize the property name
        null

    }()

fun AuValidator.toDoubleOrError(
    prop: KProperty<String>,
    pred: ((Double?) -> Boolean)? = null,
    err_fn: (String) -> String
): Double? =

    prop.getter.call().toDoubleOrNull()?.takeIf {
        if (pred != null) pred(it)
        else true
    } ?: {
        errors.add(err_fn(humanize(prop.name))) // TODO: humanize the property name
        null
    }()

fun add_GT_zero_suffix(s: String): String = s.plus(" must be a number greater than zero.")
fun add_GE_zero_suffix(s: String): String = s.plus(" must be a number greater than or equal to zero.")

fun is_GT_zero(i: Int?): Boolean = i?.let { it > 0 } ?: false
fun is_GT_zero(i: Long?): Boolean = i?.let { it > 0 } ?: false
fun is_GT_zero(i: Double?): Boolean = i?.let { it > 0 } ?: false

fun is_GE_zero(i: Int?): Boolean = i?.let { it >= 0 } ?: false
fun is_GE_zero(i: Long?): Boolean = i?.let { it >= 0 } ?: false
fun is_GE_zero(i: Double?): Boolean = i?.let { it >= 0 } ?: false

fun AuValidator.err_unless_Int_GT_zero(prop: KProperty<String>): Int? =
    toIntOrError(prop, ::is_GT_zero, ::add_GT_zero_suffix)

fun AuValidator.err_unless_Long_GT_zero(prop: KProperty<String>): Long? =
    toLongOrError(prop, ::is_GT_zero, ::add_GT_zero_suffix)

fun AuValidator.err_unless_Double_GT_zero(prop: KProperty<String>): Double? =
    toDoubleOrError(prop, ::is_GT_zero, ::add_GT_zero_suffix)

fun AuValidator.err_unless_Int_GE_zero(prop: KProperty<String>): Int? =
    toIntOrError(prop, ::is_GE_zero, ::add_GE_zero_suffix)

fun AuValidator.err_unless_Long_GE_zero(prop: KProperty<String>): Long? =
    toLongOrError(prop, ::is_GE_zero, ::add_GE_zero_suffix)

fun AuValidator.err_unless_Double_GE_zero(prop: KProperty<String>): Double? =
    toDoubleOrError(prop, ::is_GE_zero, ::add_GE_zero_suffix)

inline fun <reified T : Enum<T>?> AuValidator.enumValueOrErr(prop: KProperty<String>): T? =
    enumValueOrNull<T>(prop.getter.call()) ?: {
        errors.add(humanize(prop.name) + " not understood.")
        null
    }()

/***************/


// String:

fun String?.is_blank(): Boolean =
    when (this) {
        null -> true
        else -> this.trim().isEmpty()
    }

