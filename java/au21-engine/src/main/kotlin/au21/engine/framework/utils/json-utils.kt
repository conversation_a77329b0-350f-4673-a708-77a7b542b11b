// File: json-utils.kt
package au21.engine.framework.utils


import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory
import com.fasterxml.jackson.dataformat.yaml.YAMLGenerator
import com.fasterxml.jackson.dataformat.yaml.YAMLMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.google.gson.GsonBuilder
import com.google.gson.JsonObject
import com.google.gson.JsonParser
import io.micrometer.core.annotation.Timed

val mapper = jacksonObjectMapper()

// Thread-safe ObjectMapper using shared factory
val yamlMapper = ObjectMapper(YAMLFactory().apply {
    configure(YAMLGenerator.Feature.INDENT_ARRAYS, true)
    configure(YAMLGenerator.Feature.MINIMIZE_QUOTES, true)
    configure(YAMLGenerator.Feature.SPLIT_LINES, false)
}).registerKotlinModule()

/**
 * 1) JsonIterator:
 *
 * used for to_json()
 * ie: object -> json string
 * because GSON cant handle object{}.to_json()
 */

// NOTE TO USE JAVAASSIST, USE THESE, BUT then object{}.to_json() won't work!
// JsonIterator.setMode(DecodingMode.DYNAMIC_MODE_AND_MATCH_FIELD_WITH_HASH);
// JsonStream.setMode(EncodingMode.DYNAMIC_MODE);

//fun Any?.to_json(): String =
//        this?.let { JsonStream.serialize(this) }
//                ?: throw Exception("to_json: cannot parse: $this")

//fun Any?.to_json(): String =
//        this?.let { gson.toJson(this) }
//                ?: throw Exception("to_json: cannot parse: $this")

//val au_mapper:ObjectMapper = jacksonObjectMapper()

private val gson = GsonBuilder().setPrettyPrinting().create()

fun jsonToPrettyFormat(jsonString: String?): String? {
    val json: JsonObject = JsonParser.parseString(jsonString).asJsonObject
    return gson.toJson(json)
}

fun objToPrettyFormat(o:Any):String {
    return gson.toJson(o)
}

// Yaml:
fun prettyYaml(obj: Any): String {
    return yamlMapper.writeValueAsString(obj)
}

fun Any.toYaml(): String = yamlMapper.writeValueAsString(this)

fun json_to_yaml(json: String): String =
    ObjectMapper().readTree(json).toYaml()


//private val gson = GsonBuilder()
//    //.excludeFieldsWithModifiers(Modifier.PRIVATE)
//    .create()


//@Traced
@Timed("to_json")
fun Any?.to_json(): String =
    try {
        // either of these work:
        // gson.toJson(this) // Gson: 700 ms
        // Json.encode(this) // Java Json: 500 ms
        mapper.writeValueAsString(this) // Jackson: 500 ms
        // JsonStream.serialize(this) // JsonIter: 600 ms
    } catch (e: Throwable) {
        e.printStackTrace()
        throw e
    }


// allows you to add arguments to the object and the json string
// TODO: doesn't catch args that are the same as the object's fields I don't
//  think
fun Any.to_json_with_args(message: String, vararg args: Any?): String {
    try {
        val jsonNode = mapper.readTree(message) as ObjectNode
        require(args.size % 2 == 0) { "Vararg arguments must be provided in key-value pairs" }
        for (i in args.indices step 2) {
            val key = args[i].toString()
            val value = args[i + 1].toString()
            jsonNode.put(key, value)
        }
        val formattedJson = ObjectMapper().writerWithDefaultPrettyPrinter()
        return formattedJson.writeValueAsString(jsonNode)
    } catch (e: Exception) {
        throw e
    }
}


//       this?.let { au_mapper.writeValueAsString(this) }
//   Json.encode(this)
//               ?: throw Exception("to_json: cannot parse: $this")

//
//fun get_jsoniter_any(json: String): com.jsoniter.any.Any =
//        JsonIterator.deserialize(json)

//inline fun <reified T> from_json(json: String): T =
//        from_json(json, T::class.java) as T

// this doesn't work because of constructor issue:
// fun from_json(json: String, ct: Class<*>?): Any? =
//        JsonIterator.parse(json).read(ct)

/**
 * 2) GSON:
 *
 * used for from_json()
 * ie: json string -> object
 * because JsonIterator requires default class parameters!
 */

//val gson by lazy { Gson() }
//
//// fun Any?.to_gson(): String =
////        this?.let { gson.toJson(it) }
////                ?: throw Exception("to_json: cannot parse: $this")
//
////fun Any?.to_gson(): String =
////        this?.let { gson.toJson(it) }
////                ?: throw Exception("to_json: cannot parse: $this")
//
//inline fun <reified T> from_json(json: String): T? =
//        try {
//            gson.fromJson(json, T::class.java)
//        } catch (e: Exception) {
//            ///Log.error("failed to parse ct: $ct:$json")
//            //e.printStackTrace()
//            null
//        }

/**
 * 3) JACKSON:
 */

//class BooleanPropNamingStrategy : PropertyNamingStrategy() {
//    override fun nameForGetterMethod(
//        config: MapperConfig<*>,
//        method: AnnotatedMethod,
//        defaultName: String): String =
//            if (method.let {
//                        it.hasReturnType()
//                                && (it.rawReturnType === Boolean::class.java || it.rawReturnType === Boolean::class.javaPrimitiveType)
//                                && it.name.startsWith("is")
//                    })
//                method.name
//            else
//                super.nameForGetterMethod(config, method, defaultName)
//}

// NB: name 'mapper' is as static object in vertex-common !!
// val au_mapper = jacksonObjectMapper()

//    .setPropertyNamingStrategy(BooleanPropNamingStrategy)
//        .setPropertyNamingStrategy(BooleanPropNamingStrategy())

////// from vertx klutter:
////@Suppress("UNCHECKED_CAST", "PLATFORM_CLASS_MAPPED_TO_KOTLIN")
////fun mapFrom(something: Any): JsonObject {
////    return jsonObjectFromMap<Any?>(Json.mapper.convertValue(something, java.util.Map::class.java) as Map<String, Any?>)
////}
//
//
//fun Any?.json_node(): JsonNode =
//        this?.let { au_mapper.valueToTree<JsonNode>(this) }
//                ?: NullNode.instance
//
//val gson:Gson = GsonBuilder().setPrettyPrinting().create()
//
//fun Any?.to_pretty(): String =
//        when (this) {
//            null           -> "null"
//            is String      -> this
//            is Optional<*> -> when {
//                isPresent() -> get().to_pretty()
//                else        -> "null"
//            }
//            else           ->
//                this::class.java.name + "\n" + gson.toJson(this)
//                   //au_mapper.
//                  //  .writerWithDefaultPrettyPrinter()
//                   //     .writeValueAsString(this)
//        }

//
//fun json_diff(source: Any?, target: Any?): String =
//        JsonDiff.asJson(source.json_node(), target.json_node()).let {
//            au_mapper.writerWithDefaultPrettyPrinter()
//                    .writeValueAsString(it)
//        }


/*
// Vertx:

fun to_json(o: Any?): JsonObject = JsonObject.mapFrom(o)

inline fun <reified T> from_json(json: String): T? =
        try {
            JsonObject(json).mapTo(T::class.java)
            //gson.fromJson(json, T::class.java)
        } catch (e: Exception) {
            Log.error("failed to parse as <${T::class.java}>:" + json)
            e.printStackTrace()
            null
        }
*/


// FROM: https://stackoverflow.com/questions/2253750/testing-two-json-objects-for-equality-ignoring-child-order-in-java
//
//object JSONUtils {
//
//    @Throws(JSONException::class)
//    fun areEqual(ob1: Any, ob2: Any): Boolean =
//            convertJsonElement(ob1) == convertJsonElement(ob2)
//
//
//    @Throws(JSONException::class)
//    private fun convertJsonElement(elem: Any): Any {
//        when (elem) {
//            is JSONObject -> {
//                val obj: JSONObject = elem as JSONObject
//                return listOf(obj.keys())
//                        .map { it.toString() }
//                        .fold(mapOf(), { o: Map<String, Any>,
//                                         k: String ->
//                            o + mapOf(k to convertJsonElement(obj.get(k)))
//                        })
//            }
//            is JSONArray  -> {
//                val arr = elem as JSONArray
//                val jsonSet = HashSet<Any>()
//                return listOf(arr)
//                        .fold(setOf(), { s: Set<Any>,
//                                         e: Any ->
//                            s + convertJsonElement(e)
//                        })
//            }
//            else          ->
//                return elem
//        }
//    }
//
//    fun compare_objects(obj1: Any, obj2: Any, fn: (Any, Any) -> Any) {
//        fn(convertJsonElement(obj1), convertJsonElement(obj2))
//    }
//}
