// File: HeartbeatAction.kt
package au21.engine.framework.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuSession.SessionTerminationReason.SERVER_SWEPT_STALE_SESSION
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.services.open_auctions
import au21.engine.domain.common.services.sessions_non_terminated
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeFlowControlType
import au21.engine.domain.de.services.state.DeControlValidator
import au21.engine.domain.de.services.state.DeControlValidator.MUTATE
import au21.engine.domain.de.services.state.DeMutator
import au21.engine.framework.commands.interfaces.ISessionsTerminated
import au21.engine.framework.commands.interfaces.SessionTermination
import au21.engine.framework.database.AuEntityManager
import org.joda.time.DateTime

const val SOCKET_CLOSED_TIMEOUT_SECONDS = 15

//@ApplicationScoped
//class OpenAuctionHolder{
//    @Inject
//    lateinit var db:AuEntityManager
//
//    val open_auctions = mutableListOf<Auction>()
//
//    @PostConstruct
//    fun postConstruct(){
//        open_auctions.addAll(db.findAll())
//    }
//}

interface IOpenAuctions {
    val open_auctions: List<Auction>
}

class HeartbeatCommand : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?) =
        //HeartbeatAction(this, db, null, db.open_auctions())
        HeartbeatAction(this, db, null, db.open_auctions())

    companion object {
        val instance = HeartbeatCommand()
    }
}

class HeartbeatAction(
    override val command: HeartbeatCommand,
    override val db: AuEntityManager,
    override val session: AuSession? = null,
    override val open_auctions: List<Auction>
) : EngineAction, ISessionsTerminated, IOpenAuctions {

    override val sessions_terminated = mutableListOf<SessionTermination>()

    val de_auctions_ticked: MutableList<DeAuction> = mutableListOf()
    val de_auctions_round_closed: MutableList<DeAuction> = mutableListOf()

    override fun mutate() {
        //    open_de_auctions.forEach { de -> handle_de_auction(de, DateTime()) }

        val threshold = DateTime().toDate().time - (SOCKET_CLOSED_TIMEOUT_SECONDS * 1_000)

        db.sessions_non_terminated()
            .filter {
                when (val t = it.socket_last_closed) {
                    null -> false
                    else -> t.time < threshold  // ie: Was last closed earlier than the threshold
                }
            }
            .onEach { s: AuSession ->
                terminate_session(db, s, SERVER_SWEPT_STALE_SESSION)
            }

        open_auctions.forEach { a: Auction ->
            when(a){
                is DeAuction ->
                    if (DeControlValidator.validate(a, DeFlowControlType.HEARTBEAT) == MUTATE) {
                        DeMutator.mutate(a, DeFlowControlType.HEARTBEAT, null)
                        db.save(a)
                    }
            }
        }

    }

}
