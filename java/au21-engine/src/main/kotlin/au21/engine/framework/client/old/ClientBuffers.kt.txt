package au21.engine.framework.commands.client.old

import au21.engine.domain.de.commands.client.DeAuctioneerBuffer
import au21.engine.domain.de.model.DeAuction
import au21.engine.framework.commands.client.ClientCommand.StoreCommand.*
import javax.enterprise.context.ApplicationScoped

@ApplicationScoped
class ClientBuffers {

    val auctioneer_auction_rows = CommandBuffer<SetAuctionRows>()
    val auctioneer_counterparties_buffer = CommandBuffer<SetCounterpartyCredits>()
    val companies_buffer = CommandBuffer<SetCompanies>()
    val de_auctions = mutableMapOf<String, DeAuctioneerBuffer>()
    val users_buffer = SetUsersBuffer()

    fun get_de_auction(de: DeAuction): DeAuctioneerBuffer =
        de_auctions[de.id_str()] ?: run {
            DeAuctioneerBuffer(de).also { de_auctions[de.id_str()] = it }
        }
}
