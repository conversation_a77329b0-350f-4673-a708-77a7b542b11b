// File: enum-utils.kt
package au21.engine.framework.utils

/**
Oct 4, 2021
Frome: https://stackoverflow.com/questions/35666815/enum-valueof-in-kotlin
 */
// REPLACED BELOW
//inline fun <reified T : Enum<T>> enumValueOfWithDefault(name: String, default: T): T {
//    return try {
//        java.lang.Enum.valueOf(T::class.java, name)
//    } catch (e: Throwable) {
//        default
//    }
//}


/**
 * Created May 27, 2024 by Opus3
 */

/**
 * Converts the specified string [name] to the corresponding enum value of type [T].
 *
 * @param name The name of the enum value.
 * @return The enum value corresponding to the specified [name], or `null` if no matching enum value is found.
 */
inline fun <reified T : Enum<T>> toEnumOrNull(name: String): T? {
    return T::class.java.enumConstants.firstOrNull { it.name == name }
}

/**
 * Converts the specified string [name] to the corresponding enum value of type [T],
 * or returns the [default] value if no matching enum value is found.
 *
 * @param name The name of the enum value.
 * @param default The default enum value to return if no matching enum value is found.
 * @return The enum value corresponding to the specified [name], or the [default] value if no matching enum value is found.
 */
inline fun <reified T : Enum<T>> toEnumWithDefault(name: String, default: T): T {
    return toEnumOrNull<T>(name) ?: default
}

/**
 * Converts the specified string [name] to the corresponding enum value of type [T].
 *
 * @param name The name of the enum value.
 * @return The enum value corresponding to the specified [name].
 * @throws IllegalArgumentException If no matching enum value is found.
 */
inline fun <reified T : Enum<T>> toEnumOrError(name: String): T {
    return toEnumOrNull<T>(name) ?: throw IllegalArgumentException("Invalid enum value: $name")
}
