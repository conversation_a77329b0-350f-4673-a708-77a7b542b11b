// File: enums.kt
package au21.engine.framework

//class AuctionTickValue(
//    val a: DeAuction,
//    val messages: List<AuctionMessage>,
//    val auction_closed_during_tick: Boolean,
//    val prior_snap: AuctionStateSnapshot,
//    val post_snap: AuctionStateSnapshot,
//    val state_changed: Boolean = prior_snap.state_label != post_snap.state_label
//)
//
//class AuctionStateSnapshot(a: DeAuction) {
//    val state_label: String = a.auction_state_label
//    val closed: Boolean = a.closed
//
////    inline fun <reified T> getState(): T
////            where
////            T : Enum<T>,
////            T : IAuctionState = enumValueOf(state_string)
//}


/*
 * =====================================================================================================
 *                             FRAMEWORK ENUMS
 * =====================================================================================================
 */

enum class PageName {

    CREDITOR_AUCTIONEER_PAGE,
    CREDITOR_TRADER_PAGE,
    HOME_PAGE,
    LOGIN_PAGE, // used on client side only
    SESSION_PAGE,
    USER_PAGE,

    BH_AUCTIONEER_PAGE,
    BH_SETUP_PAGE,
    BH_TRADER_PAGE,

    DE_AUCTIONEER_PAGE,
    DE_SETUP_PAGE,
    DE_TRADER_PAGE,

    MR_AUCTIONEER_PAGE,
    MR_SETUP_PAGE,
    MR_TRADER_PAGE,

    TE_AUCTIONEER_PAGE,
    TE_SETUP_PAGE,
    TE_TRADER_PAGE,

    TO_AUCTIONEER_PAGE,
    TO_SETUP_PAGE,
    TO_TRADER_PAGE
}
