// File: metrics.kt
package au21.engine.framework.metrics

import au21.engine.framework.utils.thousands
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path

@ApplicationScoped
class AuMetrics {

    class RequestLatency(
        val cmd:String,
        val command_handling_full: String,
        val stores_json_zip_send: String
    )

    var request_latencies: MutableList<RequestLatency> = mutableListOf()

    fun add_request_latency(cmd:String, start_command: Long, start_stores: Long) {
        val now = System.nanoTime()
        this.request_latencies.add(
            RequestLatency(
                cmd,
                ((now - start_command) / 1_000).thousands() + " us",
                ((now - start_stores) / 1_000).thousands() + " us"
            )
        )
        while (this.request_latencies.size > 30) {
            this.request_latencies.removeFirst()
        }
    }
}


@Path("/au-metrics")
class MetricsController {

    @Inject
    lateinit var metrics: AuMetrics

    @GET
    fun toggle_session_differ(): String {
        var r = "<meta http-equiv='refresh' content='1'>"
        r += "<table border='1'>"
        r += "<thead><tr><th>command</th><th>full</th><th>stores</th></tr></thead>"
        metrics.request_latencies.forEach {
            r += "<tr>"
            r += "<td><div style='width:400px; overflow:auto'>${it.cmd}</div></td>"
            r += "<td>${it.command_handling_full}</td>"
            r += "<td>${it.stores_json_zip_send}</td>"
            r += "</tr>"
        }
        r += "</table>"
        return r
    }
}
