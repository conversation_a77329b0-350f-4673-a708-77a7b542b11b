package au21.engine.framework.kafka

import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.Producer
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.common.serialization.StringSerializer
import java.net.InetAddress
import java.util.*


private fun createProducer(): Producer<String, String>? {
    try {

        val config = Properties()
        config[StreamsCon.APPLICATION_ID_CONFIG] = "streams-pipe"
        config[StreamsConfig.BOOTSTRAP_SERVERS_CONFIG] = "localhost:9092"
        config["client.id"] = InetAddress.getLocalHost().hostName
        config["key.serializer"] = StringSerializer::class.java
        config["value.serializer"] = StringSerializer::class.java
        config["bootstrap.servers"] = "localhost:9092"
        config["acks"] = "all"
        return KafkaProducer(config)
    }catch (e:Throwable){
        e.printStackTrace()
        return null
    }
}

fun main() {
    val producer = createProducer()
    for (i in 0..4) {
        val future = producer?.send(ProducerRecord("Topic1", i.toString(), Date().toString()))
        println(future?.get())
    }
}
