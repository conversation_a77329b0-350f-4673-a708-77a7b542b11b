package au21.engine.framework.commands.client.manager

import au21.engine.domain.common.commands.client.MessageElement
import au21.engine.domain.common.commands.engine.LoginAction
import au21.engine.domain.common.commands.engine.MessageSendAction
import au21.engine.domain.common.commands.engine.SessionTerminateAction
import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Auction
import au21.engine.domain.de.commands.engine.DeOrderSubmitAction
import au21.engine.domain.de.services.sessions_logged_in
import au21.engine.domain.de.services.sessions_non_terminated
import au21.engine.framework.commands.client.BrowserMessageKind
import au21.engine.framework.commands.client.ClientCommand.*
import au21.engine.framework.commands.client.ClientCommand.StoreCommand.*
import au21.engine.framework.commands.client.ClientSocket
import au21.engine.framework.commands.client.store.builders.clientStore_for_session
import au21.engine.framework.commands.client.store.stores_for_sessions
import au21.engine.framework.commands.engine.EngineAction
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.gzipClientCommand
import org.eclipse.microprofile.opentracing.Traced
import java.nio.ByteBuffer
import javax.inject.Inject

/**
 * THIS IS THE THIRD VERSION
 * Strategy:
 * - 1) build a ClientStores with all Values and Elements
 * - 2) then for each session, in parallel:
 *     - a) to-json (gson, vs java Json, vs kotlix Json (to string and to buffer)
 *     - b) giz and publish
 */

// @ApplicationScoped
class ClientsManager2 : IClientsManager {

    @Inject
    lateinit var socket: ClientSocket

    @Traced
    override fun handle(db: AuEntityManager, action: EngineAction) {

        db.sessions_logged_in().forEach { s ->
            val store = stores_for_sessions(db, s)
            socket.apply {
                publish(s.session_id, gzipClientCommand(StartStoreCommands()))
                publish(s.session_id, gzipClientCommand(SetAuctionRows(store.auction_rows)))
                publish(s.session_id, gzipClientCommand(SetCompanies(store.companies)))
                publish(s.session_id, gzipClientCommand(SetDeAuction(store.de_auction)))
                publish(s.session_id, gzipClientCommand(SetDeStatus(store.de_status)))
                if (store.de_matrix_rounds.isNotEmpty()) {
                    publish(s.session_id, gzipClientCommand(AddDeMatrixRound(store.de_matrix_rounds[0])))
                }
                publish(s.session_id, gzipClientCommand(SetSessionUser(store.session_user)))
                publish(s.session_id, gzipClientCommand(SetTime(store.time)))
                publish(s.session_id, gzipClientCommand(SetUsers(store.users)))
            }

        }


        // EVENTS:

        fun auction_sessions(a: Auction): List<AuSession> =
            db.sessions_logged_in().filter { a.id_str() == it.auction?.id_str() }
        when (action) {
            is DeOrderSubmitAction -> {
                notify(
                    socket,
                    MessageElement.recipient_sids_for_message(auction_sessions(action.de), action.de, action.message),
                    MessageElement(action.message)
                )
            }
            is MessageSendAction -> {
                notify(
                    socket,
                    MessageElement.recipient_sids_for_message(
                        auction_sessions(action.auction),
                        action.auction,
                        action.message
                    ),
                    MessageElement(action.message)
                )
            }
            is LoginAction -> {

                action.existing_session?.let { existing ->
                    terminate_session(
                        socket,
                        existing,
                        "You have logged in from another browser or device.",
                    )
                }
                // send the companies:
            }
            else -> {
                // send companies of changed

            }

        }

        when (action) {
            is SessionTerminateAction -> {
                terminate_session(socket, action.session, "")
            }
        }

        succeeded(socket, db.sessions_non_terminated().map { it.session_id })


    }

// START_COMMANDS
// ADD MESSAGE
// TERMINATE
// NOTIFY
// SUCCEEDED

    fun start_commands(socket: ClientSocket, sids: List<String>) {
        gzipClientCommand(StartStoreCommands()).also { buf ->
            sids.forEach { socket.publish(it, buf) }
        }
    }

    fun succeeded(socket: ClientSocket, sids: List<String>) {
        gzipClientCommand(CommandSucceeded()).also { buf ->
            sids.forEach { socket.publish(it, buf) }
        }
    }

    fun notify(
        socket: ClientSocket,
        recipient_sids: List<String>,
        m: MessageElement
//        sessions: List<AuSession>,
//        a: Auction,
//        m: AuctionMessage
    ) {
        if (recipient_sids.isEmpty())
            return
        gzipClientCommand(ShowMessage(BrowserMessageKind.NOTIFICATION, listOf(m.message))).also { buf: ByteBuffer ->
            recipient_sids.forEach { socket.publish(it, buf) }
        }

//        sessions
//            .filter { s -> MessageElement.is_for(a, m, s) }
//            .map { it.session_id }
//            .filter { it != sender_sid }
//            .forEach { sid ->
//                socket.publish(sid, buffer)
//            }
    }

    fun terminate_session(socket: ClientSocket, s: AuSession?, message: String) {
        when (s) {
            null -> return
            else -> socket.publish(s.session_id, gzipClientCommand(TerminateSession(message)))
        }
    }
}

//    fun create_store_buffers(
//        db: AuEntityManager,
//        action: EngineAction
//    ): Map<String, ByteBuffer> {
//
//        return duration_ms("SESSION_MANAGER") {
//            val h = SessionStoreHelper(db, action)
//
//            db.sessions_non_terminated().associate { s: AuSession ->
//                val session_store: ClientStore = h.session_store(s)
//                val cmd = ClientCommand.StoreCommand.SetStore(session_store)
//                s.session_id to gzip(cmd.to_json())
//            }.toMap()
//        }
//    }

//    ): Map<String, ByteBuffer> {
//
//        val store_map: Map<String, ByteBuffer> =
//            duration_ms("SESSION_MANAGER.HANDLE") {
//                val h = SessionStoreHelper(db, action)
//                db.sessions_non_terminated().associate { s: AuSession ->
//                    val session_store: ClientStore = h.session_store(s)
//                    val cmd = ClientCommand.StoreCommand.SetStore(session_store)
//                    s.session_id to gzip(cmd.to_json())
//                }
//            }
//
//        return store_map

//        val sessions: MutableList<Pair<TargetList, ByteBuffer>> = mutableListOf()
//        when (action) {
//            is HeartbeatAction -> {
//
//            }
//            is MessageSendAction -> {
//
//            }
//            is DeFlowControlAction -> {
//
//            }
//            is DeRoundControllerAction -> {
//
//            }
//            else -> {
//            }
//
//
//    }
//
//}
//
