// File: EngineCommand.kt
package au21.engine.framework.commands

import au21.engine.framework.database.AuEntity
import au21.engine.framework.database.AuEntityManager
import com.fasterxml.jackson.annotation.JsonIgnore
import org.joda.time.DateTime
import java.util.*
import javax.persistence.Entity

abstract class EngineCommand {

    // mar 26, 2021:
    // have to do it this way because of GSON deserialization

    @Transient
    @JsonIgnore
    private var _errors: MutableList<String>? = null // for serialization

    fun errors(): MutableList<String> =
        _errors ?: run {
            mutableListOf<String>().also {
                _errors = it
            }
        }

    //@Traced
    abstract fun validate(db: AuEntityManager, session_id: String? = null): EngineAction

}


/**
 * Creating a separate entity to save the command because if we extend EngineCommand with AuEntity then:
 * - a) client gets the various AuENtity props (id, deleteed) - though we could probably remove from generator
 * - b) we can't use vals with ObjectDB
 *
 */
@Entity
class CommandJson(
    var command_json: String,
    var timestamp: Date = DateTime().toDate()
) : AuEntity()
