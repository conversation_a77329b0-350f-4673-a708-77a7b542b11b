// File: tracing.kt
package au21.engine.framework.observability

//import io.opentelemetry.api.trace.Tracer
import jakarta.enterprise.context.ApplicationScoped

/**
 *  note, using: devops/resources/jaeger/working/run-jaeger-for-opentelemetry.sh
 */
@ApplicationScoped
class AuTracer {

    /*
    final val jaegerChannel: ManagedChannel = ManagedChannelBuilder
        .forAddress("localhost", 14250)
        .usePlaintext()
        .build()

    // Export traces to Jaeger
    final val jaegerExporter: JaegerGrpcSpanExporter = JaegerGrpcSpanExporter.builder()
        .setChannel(jaegerChannel)
        .setTimeout(30, TimeUnit.SECONDS)
        .build()

    final val serviceNameResource: Resource = Resource.create(
        Attributes.of(ResourceAttributes.SERVICE_NAME, "au21-engine")
    )

    // Set to process the spans by the Jaeger Exporter

    // Set to process the spans by the Jaeger Exporter
    final val tracerProvider: SdkTracerProvider = SdkTracerProvider.builder()
        .addSpanProcessor(SimpleSpanProcessor.create(jaegerExporter))
        .setResource(Resource.getDefault().merge(serviceNameResource))
        .build()

    final val openTelemetry: OpenTelemetrySdk = OpenTelemetrySdk.builder()
        .setTracerProvider(tracerProvider)
        .build()

     */

//    val tracer: Tracer? = null // openTelemetry.getTracer("Daves
// instrumentation")

//    init {
//        // it's always a good idea to shut down the SDK cleanly at JVM exit.
//
//        Runtime.getRuntime()
//            .addShutdownHook(
//                Thread { tracerProvider.close() }
//            )
//
//    }


}



//    val sdkTracerProvider = SdkTracerProvider.builder()
//        .addSpanProcessor(BatchSpanProcessor.builder(OtlpGrpcSpanExporter.builder().build()).build())
//        .build()
//
//    val openTelemetry: OpenTelemetry = OpenTelemetrySdk.builder()
//        .setTracerProvider(sdkTracerProvider)
//        .setPropagators(ContextPropagators.create(W3CTraceContextPropagator.getInstance()))
//        .buildAndRegisterGlobal()

//
//    val  openTelemetry: OpenTelemetry =
//        ExampleConfiguration.initOpenTelemetry("http://localhost", 14250);
//
//    Quarkus.run(Au21Engine::class.java, *args)
//}
//}
//
///**
// * All SDK management takes place here, away from the instrumentation code, which should only access
// * the OpenTelemetry APIs.
// */
//internal object ExampleConfiguration {
//    /**
//     * Initialize an OpenTelemetry SDK with a Jaeger exporter and a SimpleSpanProcessor.
//     *
//     * @param jaegerHost The host of your Jaeger instance.
//     * @param jaegerPort the port of your Jaeger instance.
//     * @return A ready-to-use [OpenTelemetry] instance.
//     */
//    fun initOpenTelemetry(jaegerHost: String?, jaegerPort: Int): OpenTelemetry {
//        // Create a channel towards Jaeger end point
//        val jaegerChannel = ManagedChannelBuilder.forAddress(jaegerHost, jaegerPort).usePlaintext().build()
//        // Export traces to Jaeger
//        val jaegerExporter: JaegerGrpcSpanExporter = JaegerGrpcSpanExporter.builder()
//            .setChannel(jaegerChannel)
//            .setTimeout(30, TimeUnit.SECONDS)
//            .build()
//        val serviceNameResource =
//            Resource.create(Attributes.of(ResourceAttributes.SERVICE_NAME, "dm-otel-jaeger-example"))
//
//        // Set to process the spans by the Jaeger Exporter
//        val tracerProvider = SdkTracerProvider.builder()
//            .addSpanProcessor(SimpleSpanProcessor.create(jaegerExporter))
//            .setResource(Resource.getDefault().merge(serviceNameResource))
//            .build()
//        val openTelemetry = OpenTelemetrySdk.builder().setTracerProvider(tracerProvider).build()
//
//        // it's always a good idea to shut down the SDK cleanly at JVM exit.
//        Runtime.getRuntime().addShutdownHook(Thread { tracerProvider.close() })
//        return openTelemetry
//    }
//
//    private fun myWonderfulUseCase() {
//        // Generate a span
//        val span: Span = this.tracer.spanBuilder("Start my wonderful use case").startSpan()
//        span.addEvent("Event 0")
//        // execute my use case - here we simulate a wait
//        doWork()
//        span.addEvent("Event 1")
//        span.end()
//    }
//
//    private fun doWork() {
//        try {
//            Thread.sleep(1000)
//        } catch (e: InterruptedException) {
//            // do the right thing here
//        }
//    }
//
//}

//            System.setProperty(
//                "quarkus.opentelemetry.tracer.exporter.otlp.endpoint",
//                "http://localhost:55680"
//            )
//            System.setProperty(
//                "quarkus.opentelemetry.tracer.exporter.otlp.endpoint.headers",
//                "x-honeycomb-team=********************************"
//            )

//"x-honeycomb-dataset" to "ryzen")

//            val honeyClient: HoneyClient = create(
//                options()
//                    .setWriteKey("********************************")
//                    .setDataset("ryzen")
//                    .setSampleRate(2)
//                    .build()
//            );
//
//            honeyClient.createEvent()
//                .addField("userName", "Bob")
//                .addField("userId", UUID.randomUUID().toString())
//                .setTimestamp(System.currentTimeMillis())
//                .send();
//
//            val tracer: Tracer = GlobalOpenTelemetry.getTracer("my-service")
//            val span: Span = tracer.spanBuilder("expensive-query").startSpan()
//
//// ... do cool stuff
//
//
//// ... do cool stuff
//            span.end()
