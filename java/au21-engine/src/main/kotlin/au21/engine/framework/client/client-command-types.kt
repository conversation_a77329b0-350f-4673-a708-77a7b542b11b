// File: client-command-types.kt
package au21.engine.framework.client

/**
 *  THIS FILE NEEDS TO BE IN SYNC WITH:
 *  au12-engine/src/main/kotlin/au21/engine/framework/commands/client/client-command-types.kt
 *  au21-frontend/libs/client-connector/src/lib/client-command-types.ts
 */


import net.pearx.kasechange.toSnakeCase


class EngineTransaction(
    val client_command_maps: MutableList<ClientCommandSessionsMap>,
    val engine_command_json: String?, // null in case of heartbeat
    val engine_command_name: String,
    val isHeartbeat: Boolean,
    val session_id: String?,
    var duration_ms: Int,
    var has_alert: <PERSON><PERSON>an,
    var has_session_differ_error: <PERSON><PERSON><PERSON>,
    var requests_since_last_restart: Int
)

class ClientCommandSessionsMap(
    val command: ClientCommand,
    val sessionIds: List<String>
)

// Client Commands:

/**
 * These were the old ones:
 *    a) Browser Commands (3):
 *      - ShowMessage
 *      - CommandSucceeded
 *      - TerminateSession
 *    b) Store Commands (6):
 *      - SetValue
 *      - DeleteValue // added June 16, 2021
 *      - SetElement
 *      - SetElements
 *      - RemoveElement
 *      - RemoveElements
 *      - ClearElements
 *
 *  But for now I think we just need:
 *  a) SetLiveStore (needs to be changed on client)
 *  b) SetElements (if null, then clear array)
 *    - currently only SetElements I can think of is:
 *      - SetElements("de-prev-rounds", Array<Element>")
 *    - so we need and Element (id) interface
 *    - and probably (like messages), the action can implement SetsElement
 *
 */

enum class CommandType {
    CommandSucceeded,
    ShowMessage,
    TerminateSession,
    NetworkDown,
    NetworkUp,
    SetLiveStore,
    AddElements
}

sealed class ClientCommand {
    abstract val command: CommandType

    class CommandSucceeded : ClientCommand() {
        override val command = CommandType.CommandSucceeded
    }

    class ShowMessage(
        val browser_message_kind: BrowserMessageKind,
        val message: List<String>
    ) : ClientCommand() {
        override val command = CommandType.ShowMessage
    }

    class TerminateSession(
        val message: String?
    ) : ClientCommand() {
        override val command = CommandType.TerminateSession
    }

    class NetworkDown : ClientCommand() {
        override val command = CommandType.NetworkDown
    }

    class NetworkUp : ClientCommand() {
        override val command = CommandType.NetworkUp
    }

    sealed class StoreCommand : ClientCommand() {
        abstract override val command: CommandType

        class SetLiveStore(
            val store: LiveClientStore
        ) : StoreCommand() {
            override val command = CommandType.SetLiveStore
        }

        class AddElements(
            element_class: Class<out StoreElement>,
            val elements: List<StoreElement>? // NULL means clear the array, [] means add nothing
        ) : StoreCommand() {
            override val command = CommandType.AddElements
            val path: String = element_class.simpleName
        }
    }
}

/**
 * AuSession Store Elements and Values
 * - client stores have either Values (objects), or arrays of Elements:
 */


interface StoreValue // TODO: not sure we still use this?

interface StoreElement {
    val id: String
}

// TODO: used by generator (I think)?
fun Class<out StoreElement>?.element_path(): String =
    this?.simpleName?.removeSuffix("Element")?.toSnakeCase()?.let {
        if (it.endsWith("y"))
            it.removeSuffix("y") + "ies"
        else
            it + "s"
    } ?: ""

fun Class<out StoreValue>?.value_path(): String =
    this?.simpleName?.removeSuffix("Value")?.toSnakeCase() ?: ""

enum class BrowserMessageKind {
    ALERT, NOTIFICATION
}

enum class BrowserMessageIcon {
    SUCCESS,
    INFO,
    WARNING,
    AUCTIONEER_MESSAGE,
    TRADER_MESSAGE,
    SYSTEM_MESSAGE,
    ORDER_CONFIRMATION
}
