// File: logging.kt
package au21.engine.framework.utils

import io.vertx.core.json.JsonObject
import java.util.logging.ConsoleHandler
import java.util.logging.LogRecord

class CustomJsonLogHandler : ConsoleHandler() {

    override fun publish(record: LogRecord) {
        if (!isLoggable(record)) {
            return
        }

        val json = JsonObject()
        json.put("timestamp", java.time.Instant.ofEpochMilli(record.millis).toString())
        json.put("level", record.level.name)
        json.put("message", record.message)
        json.put("class", record.sourceClassName.substringAfterLast('.'))
        json.put("line", record.sourceMethodName)

        // Print the formatted log to console
        println(json.encodePrettily())
    }
}
