// File: dispatcher.kt
package au21.engine.framework.commands

import au21.engine.framework.client.SocketHandler.Companion.TOPIC_SOCKET_COMMAND
import au21.engine.framework.features.AuFeatures
import au21.engine.framework.utils.HeartbeatConfig
import io.quarkus.logging.Log
import io.quarkus.vertx.ConsumeEvent
import io.vertx.core.eventbus.EventBus
import jakarta.annotation.PostConstruct
import jakarta.annotation.PreDestroy
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import kong.unirest.Unirest
import org.eclipse.microprofile.config.ConfigProvider
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.concurrent.thread


/*
 * This class queues commands and dispatches them.
 *  - via http://localhost:4040/ENGINE_INPUT_CHANNEL
 * It also adds a TICK msg to the queue every second
 *  - unless it already has a TICK message
 * (in case the handler is slow)
 */


@ApplicationScoped
class Dispatcher {

    @Inject
    lateinit var hearbeatConfig: HeartbeatConfig

    @Inject
    lateinit var handler: EngineCommandHandler

    @Inject
    lateinit var bus: EventBus

    @Inject
    lateinit var features: AuFeatures

    val HEARTBEAT_SECS: Long = 1
    val TICK = "TICK"

    private val destroyed = AtomicBoolean(false)
    private val request_queue = LinkedBlockingQueue<String>()

    fun send_message(message:String){
        // currently only used by handler to send the ErrorsMessageCommand, ie: back to auctioneers
        request_queue.add(message)
    }

    final val port: String = ConfigProvider.getConfig().getValue("quarkus.http.port", String::class.java)
    // final val host_address:String = InetAddress.getLocalHost().hostAddress

    @Suppress("HttpUrlsUsage")
    val internal_url: String = "http://127.0.0.1:$port/ENGINE_INPUT_CHANNEL"

    init {
        Log.info { "Internal url = $internal_url" }
    }

    @ConsumeEvent(TOPIC_SOCKET_COMMAND)
    fun addCommand(msg: String) {
        // println(this.hashCode().toString() + " addCommand: " + msg)
        this.request_queue.add(msg)
    }

    @PostConstruct
    fun postConstruct() {
        try {
            handler_thread.start()
            if(hearbeatConfig.heartbeat_enabled) {
                heartbeat_thread.start()
            }
        } catch (e: Throwable) {
            e.printStackTrace()
        }
    }

    @PreDestroy
    fun preDestroy() {
        Log.info("Stopping threads")
        destroyed.set(true)
    }

    val heartbeat_thread: Thread = thread(
        name = "Heartbeat thread",
        start = false
    ) {
        Log.info("${this}: Heartbeat started.")
        while (!destroyed.get()) {
            try {
                if (features.heartbeat) {
                    // 1) send the time signal:
                    // bus.publish("TIME_SIGNAL", DateTime())

                    // 2) add "TICK" to the queue if it doesn't already have one:
                    request_queue.find { it == TICK } ?: run {
                        request_queue.add(TICK)
                    }
                }
                Thread.sleep(HEARTBEAT_SECS * 1_000)
            } catch (e: Throwable) {
                e.printStackTrace()
            }
        }
        Log.info("destroying:${this}")
    }

    val handler_thread: Thread = thread(
        name = "Handler thread",
        start = false
    ) {
        Log.info("$this Handler started.")
        while (!destroyed.get()) {
            try {
                val msg = request_queue.take()
//                if (msg != "TICK") {
//                    println("Took from request queue: $msg")
//                }
                when {
                    request_queue.size > 1 -> Log.info("tasks remaining: ${request_queue.size}")
                }
                // Log.info("request_queue size: ${request_queue.size}")
                if (!destroyed.get()) { // could have been destroyed before message arrived:
                    Unirest.get(internal_url)
                        .queryString(mapOf(Pair("message", msg)))
                        .asJson()
                }

            } catch (t: Throwable) {
                t.printStackTrace()
            }
        }
        Log.info("destroying:${this}")
    }

}

