// File: deserializer.kt
package au21.engine.framework.commands

import com.google.gson.Gson
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject

@ApplicationScoped
class EngineCommandDeserializer {

    //val mapper: ObjectMapper = jacksonObjectMapper()
    //  .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)

    val gson = Gson()

//    val engine_command_map: Map<String, Class<out EngineCommand>> =
//        get_sub_classes<EngineCommand>().let { commands ->
//            var error = false
//            commands.map { it.simpleName }
//                .groupingBy { it }
//                .eachCount()
//                .forEach { (key: String, count: Int) ->
//                    if (count > 1) {
//                        error = true
//                        Log.error(
//                            "Cannot have more than one EngineCommand with the same simplename: $key"
//                        )
//                    }
//                }
//            if (error)
//                throw Error("more than one EngineCommand with the same simplename")
//
//            if(commands.isEmpty())
//                throw Error("no EngineCommand subclasses found")
//
//            commands.map {
//                @Suppress("UNCHECKED_CAST")
//                it.simpleName to it as Class<out EngineCommand>
//            }.toMap()
//        }

    //@Traced
    fun to_command(command_json: Any, classname: String): EngineCommand {
        //Logger.trace("looking for class: $classname")
        //Logger.trace("Class.forname($classname) = ${Class.forName(classname)}")
        return gson.fromJson(command_json.toString(), Class.forName(classname)) as EngineCommand
    }

//        if (!has_inited_jackson) {
//            has_inited_jackson = true
        // This works, but doesn't seem that db's cdi annotations are called:
//    mapper.setInjectableValues(InjectableValues.Std().apply {
//        addValue(AuEntityManager::class.java, db)
//    })
//    mapper.configure(
//        JsonGenerator.Feature.AUTO_CLOSE_JSON_CONTENT, true
//    )
//        }


//            val version: String = toString("version")
//                ?: throw Exception("request has no version")
//                    if(version !=expected_version.version)
//                        throw Exception("request version should be: ${expected_version.version} but is: $version")

        // Log.info(engine_command_map)
//
//        val engine_command_map: Map<String, Class<out EngineCommand>> =
//            mutableMapOf<String, Class<out EngineCommand>>().apply {
//                get_sub_classes<EngineCommand>().forEach {
//                    put(it.simpleName, it)
//                }
//            }
//
//        val command_class: Class<out EngineCommand> = engine_command_map[simplename]
//            ?: throw Exception("command class not found: $simplename " + engine_command_map.entries.map {
//                it.key + "=" + it.value
//            }.joinToString { "," })

        /** (3) command instance
         *
         */

        // jackson
//        val command: EngineCommand = mapper.readValue(
//            // It seems that for classes that have no properties we need to create an empty JSON object:
//            command_json.toString().let {
//                if (it.is_blank()) "{}"
//                else it
//            },
//            command_class
//        )

//        // gson seems 10x faster (7ms vs 0.7 ms)
//        // TODO: tweak gson settings above (and in utils)
//        val command: EngineCommand = gson.fromJson(command_json.toString(), command_class)
//
//        return command
//    }

}
