// File: au-format-utils.kt
package au21.engine.framework.utils

import io.quarkus.logging.Log
import org.joda.time.DateTime
import org.joda.time.DateTimeZone
import org.joda.time.Interval
import org.joda.time.Period
import org.joda.time.format.DateTimeFormat
import org.joda.time.format.DateTimeFormatter
import org.joda.time.format.ISODateTimeFormat
import java.text.DecimalFormat
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.max

// THOUSANDS:

private val thousands_formatter = DecimalFormat("#,###")
fun Int?.thousands(): String = this?.toLong().thousands()
fun Long?.thousands(): String =
    if (this == null) ""
    else thousands_formatter.format(this)
// val mem_format_thousands = Memoize1(::format_thousands)


object AuFormatter {

    ///////////////////////////////////////////////////
    // TODO: add memoization
    // from: https://gist.github.com/sureshg/9aa4bed513f8d4179ad2893ecb7886f4

//    val memIsTextPresent = Memoize2(isTextPresent)
//
//    fun isTextPresent(path: Path, text: String) = path.toFile().readText().contains(text, true)
//
//    class Memoize2<A, B, C>(val func: (A, B) -> C) : (A, B) -> C {
//        val cache = hashMapOf<String, C>()
//        override fun invoke(p1: A, p2: B) = cache.getOrPut(p1.toString(), { func(p1, p2) })
//    }
//
//    class Memoize1<A, C>(val func: (A) -> C) : (A) -> C {
//        val cache = hashMapOf<String, C>()
//        override fun invoke(p1: A) = cache.getOrPut(p1.toString(), { func(p1) })
//    }

    ////////////////////////////////////////////////////

    val fmt: DateTimeFormatter? = ISODateTimeFormat.dateTime()
    val bid_table_formatter: DateTimeFormatter? = DateTimeFormat.forPattern("MMM dd, HH:mm:ss")
    val flatpickr_fmt: DateTimeFormatter? =
        DateTimeFormat.forPattern("yyyy-MM-dd'T'HH:mm:ss'Z'").withZone(DateTimeZone.UTC)
    val place_formatters = LinkedHashMap<Int, DecimalFormat>()
    val simple_date: DateTimeFormatter = DateTimeFormat.forPattern("MMM dd")
    val simple_time: DateTimeFormatter = DateTimeFormat.forPattern("HH:mm:ss")

    val auction_row_date_formatter = SimpleDateFormat("EEE, d MMM yyyy")
    val auction_row_time_formatter = SimpleDateFormat("HH:mm:ss zzz")

    val order_time_format = SimpleDateFormat("HH:mm:ss")
    val months = arrayOf("Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec")

    fun parseIsoDateString(s: String): Date? =
        try {
            fmt!!.parseDateTime(s).toDate()
        } catch (e: Exception) {
            Log.info(e.message)
            null
        }

    fun date_time_format(d: Date): String =
        "${simple_date.print(d.time)}, ${simple_time.print(d.time)}"

    //@Memoized
    fun formatDateTime_for_flatpickr(date: Date): String =
        flatpickr_fmt!!.print(date.time)

    fun bid_datetime_format(date: Date?): String =
        date?.let { bid_table_formatter!!.print(it.time) } ?: ""

    fun formatDateTime_for_te_status(date: Date): String =
        simple_time.print(date.time) + " on " + simple_date.print(date.time)

    // @Memoized
    fun toShortMonth(m: Int): String = months[m]

    //@Memoized
    fun to_duration(start: DateTime, end: DateTime): String {
        val interval = Interval(start, end)
        //val d: Duration = interval.toDuration()
        val p: Period = interval.toPeriod()

        return (if (p.years > 0) p.years.toString() + " years, " else "") +
                (if (p.months > 0) p.months.toString() + " months, " else "") +
                (if (p.days > 0) p.days.toString() + " days" else "") +
                (if (p.hours > 0) p.hours.toString() + " hours, " else "") +
                (if (p.minutes > 0) p.minutes.toString() + " minutes, " else "") +
                p.seconds + " seconds"
    }

//    fun round_to_places(d: Double, places: Int): Double {
//        val factor = Math.pow(10.0, places.toDouble())
//        return Math.round(d * factor) / factor
//    }

    // @Memoized
    fun format_to_places(d: Double, decimal_places: Int): String {
        val places: Int = max(0, decimal_places)

        var format = "#,##0"
        if (places > 0) {
            format += "."
            repeat(places) { format += "0" }
        }

        var df: DecimalFormat? = place_formatters[decimal_places]
        if (df == null) {
            df = DecimalFormat(format)
            place_formatters[decimal_places] = df
        }

        return df.format(d) // added a default
    }

    fun format_currency(d: Double?): String = when(d){
        null -> ""
        else -> "$" + format_to_places(d, 2)
    }

    fun format_order_time(d: Date?): String =  // String fmt_str = "HH:mm:ss.SSS"
        d?.let { order_time_format.format(it) } ?: ""

    fun ltrim(s: String): String =
        s.replace("^\\s+".toRegex(), "")

    fun rtrim(s: String): String =
        s.replace("\\s+\$".toRegex(), "")

    fun seconds_until(d: Date?): Int? = d?.let {
        Period(DateTime(d), DateTime.now()).seconds
    }


}
