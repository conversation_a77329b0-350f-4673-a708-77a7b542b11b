package au21.engine.domain.de.commands.client

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.de.model.DeAuction
import au21.engine.framework.commands.client.ClientSocket
import au21.engine.framework.utils.gzipClientCommand

class DeTraderBuffer {

    companion object {

        fun init_trader(socket: ClientSocket, s: AuSession?) {
            if(s == null)
                return
            if (!s.inRole(AuUserRole.TRADER))
                throw Error("Expected trader session")
            when (val de = s.auction) {
                is DeAuction -> {
                    socket.publish(s.session_id, gzipClientCommand(SetDeStatus(de)))
                    de.traders.firstOrNull { it.company_id == s.company_id }?.let { t: DeAuction.Trader ->
                        socket.publish(s.session_id, gzipClientCommand(DeAuctionValue.command_for_trader(de, t)))
                    }
                }
                else -> throw Error("Expected DeAuction")
            }
        }

        fun update_traders() {

        }
    }
}
