// File: CommandsHelper.kt
package au21.engine.framework.client

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.model.Company
import au21.engine.domain.common.services.sessions_non_terminated
import au21.engine.domain.common.viewmodel.*
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeRound
import au21.engine.domain.de.viewmodel.DeMatrixRoundElement
import au21.engine.framework.client.ClientCommand.StoreCommand.SetLiveStore
import au21.engine.framework.database.AuEntityManager

class DbSessionCache(db: AuEntityManager) {
    val sessions_non_terminated: List<AuSession> by lazy {
        db.sessions_non_terminated()
    }

    val sessions_logged_in: List<AuSession> by lazy {
        sessions_non_terminated.filter { !it.isTerminated() }
    }

    val time: TimeValue = TimeValue.now("Houston")

    // AUCTION ROWS:

    val auctions: List<Auction> by lazy {
        db.findAll()
    }

    // TODO: these don't need to be created separately for auctioneer:
    val auctioneer_row_elements: List<AuctionRowElement> by lazy {
        auctions.map { AuctionRowElement.create(it) } // , true) }
    }

    val companies: List<Company> by lazy { db.findAll() }

    val company_elements: List<CompanyElement> by lazy {
        companies.map { CompanyElement.create(it) }
    }

    val counterpartyCredits: List<CounterpartyCreditElement> by lazy {
        CounterpartyCreditElement.all(companies)
    }

    val user_elements: List<UserElement> by lazy {
        UserElement.user_elements(db, sessions_logged_in)
    }

    // MATRIX ROUNDS

    val de_matrix_rounds: MutableMap<DeAuction, MutableList<DeMatrixRoundElement>> = mutableMapOf()

    // TODO: cache this
    fun get_matrix_round(de: DeAuction, round_number: Int): DeMatrixRoundElement =
        de.rounds.find { it.number == round_number }?.let { r: DeRound ->
            de_matrix_rounds.getOrPut(de) {
                mutableListOf(DeMatrixRoundElement.create(de.lastround(), de.settings.cost_multiplier))
            }.let { matrix_rounds ->
                matrix_rounds.find { it.round_number == round_number }
                    ?: run {
                        DeMatrixRoundElement.create(r, de.settings.cost_multiplier).also {
                            matrix_rounds.add(it)
                        }
                    }
            }
        } ?: throw Error("get_matrix_round(): No round found with number = $round_number")
}

fun session_live_stores(db: AuEntityManager): List<SetLiveStore> {

    val cache = DbSessionCache(db)

    return cache.sessions_non_terminated.map { s: AuSession ->
        SetLiveStore(LiveClientStore.create(s, cache))
    }

}

