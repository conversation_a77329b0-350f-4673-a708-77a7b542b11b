package au21.engine.framework.commands.client.old

import au21.engine.domain.common.commands.client.UserElement
import au21.engine.domain.common.model.AuSession
import au21.engine.domain.de.services.sessions_logged_in
import au21.engine.framework.commands.client.ClientCommand.StoreCommand.SetUsers
import au21.engine.framework.commands.client.ClientSocket
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.to_json

class SetUsersBuffer : CommandBuffer<SetUsers>() {

    fun update(socket: ClientSocket, db: AuEntityManager, auctioneer_sids: Array<String>) {

        println("SetUsers: auctioneer_sids: ${auctioneer_sids.to_json()}")
        val logged_in_sessions: List<AuSession> = db.sessions_logged_in()

        super.update_sessions(socket, auctioneer_sids, true) {
            SetUsers(
                UserElement.user_elements(db, logged_in_sessions)
            )
        }
    }


}
