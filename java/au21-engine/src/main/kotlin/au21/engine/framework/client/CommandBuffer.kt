// File: CommandBuffer.kt
package au21.engine.framework.client

import au21.engine.framework.utils.gzip
import au21.engine.framework.utils.to_json
import java.nio.ByteBuffer

open class CommandBuffer<T : ClientCommand> {

    var json: String? = null
        private set

    var buffer: ByteBuffer? = null
        private set

    fun set_buffer(cmd: ClientCommand?): Boolean = // HAS_CHANGED
        when (val new_json = cmd.to_json()) {
            json -> false
            else -> {
                json = new_json
                buffer = gzip(new_json)
                true
            }
        }

    /**
     * Initializes the session, optionally first updating it
     * - error if no update function and no existing buffer
     */
    fun init_session(
        socket: SocketHandler,
        session_id: String?,
        cmd_provider: (() -> T)? = null,
    ): Boolean =
    // if no json_provider given then we use the last buffer
        // - and it's an error for there not to be a last buffer!!
        session_id?.let { sid ->
            when (cmd_provider) {
                null -> {
                    buffer?.let { buf -> socket.publish(sid, buf) }
                        ?: throw Error("ERROR: Init expected buffer not to be null")
                    false
                }
                else -> {
                    set_buffer(cmd_provider()).also { changed ->
                        if (changed) {
                            buffer?.let { socket.publish(sid, it) }
                        }
                    }
                }
            }
        } ?: false


    /**
     * Only runs when the command actually changes the underlying buffer
     * - so: CANNOT USE FOR INIT
     */
    open fun update_sessions(
        socket: SocketHandler,
        sids: Array<String>,
        force: Boolean,
        cmd_provider: () -> T,
    ) {
        fun publish(){
            sids.forEach { buffer?.let { buff -> socket.publish(it, buff) } }
        }
        // if set_buffer_if -> publish
        // else if force -> publish
        when {
            sids.isEmpty() -> return
            set_buffer(cmd_provider()) -> publish()
            force -> publish()
        }
    }
}
