package au21.engine.framework.misc.pubsub

import kong.unirest.Unirest
import org.eclipse.microprofile.config.inject.ConfigProperty
import java.util.concurrent.atomic.AtomicBoolean
import javax.annotation.PostConstruct
import javax.annotation.PreDestroy
import javax.enterprise.context.ApplicationScoped
import javax.inject.Inject
import kotlin.concurrent.thread


// Couldn't get Quarkus Redis to work, this might be useful, but complex++ :
// - https://github.com/Serkan80/quarkus-quickstarts/tree/development/redis-streams-quickstart
// SO: using redis connector from au2019-engine

// @Traced
@ApplicationScoped
class AuRedisManager(

    @ConfigProperty(name = "HEARTBEAT", defaultValue = true.toString()) val HEARTBEAT: Boolean,
    @Inject val client: AuRedisClient
) {

    val HEARTBEAT_SECS: Long = 1
    val TICK = "TICK"

    val destroyed = AtomicBoolean(false)

    @PostConstruct
    fun postConstruct() {
        handler_thread.start()
        if (HEARTBEAT) {
            heartbeat_thread.start()
        }
    }

    @PreDestroy
    fun preDestroy() {
        Logger.info("Stopping threads")
        destroyed.set(true)
    }

    val heartbeat_thread: Thread = thread(
        name = "Heartbeat thread",
        start = true
    ) {
        Logger.info("${this}: Heartbeat started.")
        while (!destroyed.get()) {
            try {
                if (client.request_queue.lastOrNull() == TICK) {
                    Logger.info("Request queue already has a heartbeat!")
                } else {
                    client.request_queue.add(TICK)
                }
                Thread.sleep(HEARTBEAT_SECS * 1_000)
            } catch (e: Throwable) {
                e.printStackTrace()
            }
        }
        Logger.info("destroying:${this}")
    }

    val handler_thread: Thread = thread(
        name = "Handler thread",
        start = false
    ) {
        Logger.info("$this Handler started.")
        while (!destroyed.get()) {
            try {
                val msg = client.request_queue.take()
                if (!destroyed.get()) { // could have been destroyed before message arrived:
                    Unirest.get("http://localhost:4040/ENGINE_INPUT_CHANNEL")
                        .queryString(mapOf(Pair("message", msg)))
                        .asJson()
                }
            } catch (t: Throwable) {
                t.printStackTrace()
            }
        }
        Logger.info("destroying:${this}")
    }

}


// REACTIVE: (not for some reason):
//        client.connectPubSub()
//                .reactive()
//                .apply {
//                    subscribe(config.redis.requestChannel).subscribe()
//                    observeChannels()
//                            .doOnNext {
//                                val results: List<ClientCommandSessions> = command_handler.handle(it.message)
//                                if (results.isNotEmpty()) {
//                                    publish(results.to_json())
//                                }
//                            }
//                            .doOnError {
//                                // TODO: there is a question about whether to send these back to client?
//                                Logger.error(it)
//                            }
//                            .retry()
//                            .subscribe()
//
//                }
