// File: compression.kt
package au21.engine.framework.utils

import au21.engine.framework.client.ClientCommand
import java.io.ByteArrayOutputStream
import java.nio.ByteBuffer
import java.nio.charset.StandardCharsets.UTF_8
import java.util.zip.GZIPOutputStream


//fun to_json_gzip(o: Any): ByteBuffer =
//    gzip(o.to_json())

fun gzip(content: String): ByteBuffer =
    ByteArrayOutputStream().let { bos ->
        GZIPOutputStream(bos).bufferedWriter(UTF_8).use { it.write(content) }
        ByteBuffer.wrap(bos.toByteArray())
    }

fun gzipClientCommand(c: ClientCommand): ByteBuffer =
    gzip(c.to_json())
