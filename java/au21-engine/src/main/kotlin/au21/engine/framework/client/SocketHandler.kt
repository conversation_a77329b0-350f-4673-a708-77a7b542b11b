// File: SocketHandler.kt
package au21.engine.framework.client

import au21.engine.domain.common.commands.ClientSocketCommand
import au21.engine.domain.common.model.AuSession
import au21.engine.framework.commands.EngineCommandEnvelope
import au21.engine.framework.utils.json_to_yaml
import au21.engine.framework.utils.to_json
import io.quarkus.logging.Log
import io.vertx.core.eventbus.EventBus
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.websocket.*
import jakarta.websocket.server.PathParam
import jakarta.websocket.server.ServerEndpoint
import java.nio.ByteBuffer

// from:
// - https://quarkus.io/guides/websockets


@ServerEndpoint("/socket/{session_id}")
@ApplicationScoped
class SocketHandler {

    companion object {
        const val TOPIC_SOCKET_COMMAND = "TOPIC_SOCKET_COMMAND"
    }

//    @Inject
//    lateinit var tracer: Tracer

    @Inject
    lateinit var bus: EventBus

    val sessions: MutableMap<String, Session> = mutableMapOf()

    fun publish(session_id: String?, zipped: ByteBuffer?) { // byteBuffer: ByteBuffer){
        if (session_id == null || zipped == null)
            return
        try {
            sessions[session_id]
                ?.asyncRemote
                ?.sendBinary(zipped)
        } catch (e: Throwable) {
            e.printStackTrace()
        }
    }

    @OnOpen
    fun onOpen(s: Session, @PathParam("session_id") session_id: String) {
        Log.info("Socket opened, session_id = $session_id")
        sessions[session_id] = s
        val params: Map<String, List<String>> = s.requestParameterMap

        val browser_name: String = params["browser_name"]?.get(0) ?: ""
        val browser_version: String = params["browser_version"]?.get(0) ?: ""
        val browser_os: String = params["browser_os"]?.get(0) ?: ""

        bus.send(
            TOPIC_SOCKET_COMMAND,
            EngineCommandEnvelope(
                session_id = session_id,
                command = ClientSocketCommand(
                    sid = session_id,
                    state = AuSession.ClientSocketState.OPENED,
                    browser_name = browser_name,
                    browser_version = browser_version,
                    browser_os = browser_os
                )
            ).to_json()
        )
    }

    @OnClose
    fun onClose(session: Session?, @PathParam("session_id") session_id: String) {
        Log.info("Socket closed, session_id = $session_id")
        sessions.remove(session_id)
        bus.send(
            TOPIC_SOCKET_COMMAND,
            EngineCommandEnvelope(
                session_id = session_id,
                command = ClientSocketCommand(
                    sid = session_id,
                    state = AuSession.ClientSocketState.CLOSED)
            ).to_json()
        )
        //Log.trace("SocketHandler.onClose(session_id=$session_id)")
    }

    @OnError
    fun onError(session: Session?, @PathParam("session_id") session_id: String, throwable: Throwable) {
        Log.error("Socket error, session_id = $session_id, error = ${throwable.message}")
        // do we need something like on client here? eg:
        // - session?.close()
        //Log.trace("SocketHandler.onError: session_id=$session_id, error=${throwable.message}")
        println("error: ${throwable.message}")
    }

    @OnMessage
    fun onMessage(message: String, @PathParam("session_id") session_id: String) {
        // tinylog: Log.info("Socket message, session_id = $session_id, message = $message")
        Log.info("Socket onMessage\n${json_to_yaml(message)}")
        bus.send(TOPIC_SOCKET_COMMAND, message)
    }

}

//@ServerEndpoint("/socket/{session_id}")
//@ApplicationScoped
//class SocketHandler {
//    @Inject
//    var bus: EventBus? = null
//
//        companion object {
//        const val TOPIC_SOCKET_COMMAND = "TOPIC_SOCKET_COMMAND"
//    }
//
//    private val sessions: MutableMap<String, Session> = ConcurrentHashMap()
//
//    @OnOpen
//    fun onOpen(session: Session, @PathParam("session_id") sessionId: String) {
//        Log.info("Socket opened, session_id = $sessionId")
//        sessions[sessionId] = session
//    }
//
//    @OnClose
//    fun onClose(session: Session?, @PathParam("session_id") sessionId: String) {
//        Log.info("Socket closed, session_id = $sessionId")
//        sessions.remove(sessionId)
//    }
//
//    @OnError
//    fun onError(session: Session?, @PathParam("session_id") sessionId: String, throwable: Throwable) {
//        Log.error("Socket error, session_id = " + sessionId + ", error = " + throwable.message)
//    }
//
//    @OnMessage
//    fun onMessage(message: String, @PathParam("session_id") sessionId: String) {
//        Log.info("Socket message, session_id = $sessionId, message = $message")
//        bus!!.send("TOPIC_SOCKET_COMMAND", message)
//    }
//
//    fun publish(sessionId: String, gzip: ByteBuffer) {
//        TODO("Not yet implemented")
//    }
//}
