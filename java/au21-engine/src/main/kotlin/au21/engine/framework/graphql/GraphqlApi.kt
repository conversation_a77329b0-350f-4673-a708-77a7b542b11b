// File: GraphqlApi.kt
package au21.engine.framework.graphql

import au21.engine.domain.common.commands.*
import au21.engine.domain.common.model.*
import au21.engine.domain.common.services.sessions_logged_in
import au21.engine.domain.common.services.sessions_non_terminated
import au21.engine.domain.de.commands.*
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeFlowControlType
import au21.engine.framework.commands.AlertException
import au21.engine.framework.commands.EngineCommandEnvelope
import au21.engine.framework.commands.EngineCommandHandler
import au21.engine.framework.database.AuEntity
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.features.AuFeatures
import au21.engine.framework.utils.to_json
import com.google.common.collect.ImmutableList
import jakarta.inject.Inject
import org.eclipse.microprofile.graphql.GraphQLApi
import org.eclipse.microprofile.graphql.Mutation
import org.eclipse.microprofile.graphql.Query
import org.joda.time.DateTime
import java.util.*


/**
 * Schema available here:
 * - http://localhost:4040/graphql/schema.graphql
 *
 * Queries available here:
 * - http://localhost:4040/graphql-ui/
 *
 */

@GraphQLApi

class MutationApi1 {
    @Mutation
    fun mutation1(msg: String): String {
        println(msg)
        return msg
    }
}

//@Traced
@GraphQLApi
class GraphqlApi {

    //    @Inject
//    lateinit var prometheus: PrometheusMeterRegistryProvider
    //  val tracer:Tracer = GlobalOpenTelemetry.getTracer("my-service");

//    @Inject
//    lateinit var registry: MeterRegistry


    @Inject
    lateinit var handler: EngineCommandHandler

    @Inject
    lateinit var db: AuEntityManager

    @Inject
    lateinit var features: AuFeatures

    @Query
    fun sessions(include_terminated: Boolean, logged_in_only: Boolean): List<AuSession> =
        when {
            include_terminated && logged_in_only -> throw AlertException("Cannot search for terminated sessions that are logged in.")
            include_terminated -> db.findAll()
            // these are all non-terminated
            logged_in_only -> db.sessions_logged_in()
            else -> db.sessions_non_terminated()
        }

    @Query
    fun people(): List<Person> = db.findAll<Person>()

    @Query
    fun companies(): List<Company> = db.findAll()

    @Query
    fun users(): List<Person> = db.findAll()

    @Query
    fun auctions(): List<Auction> = db.findAll()

    @Query
    fun de_auctions(): List<DeAuction> = db.findAll()

    @Query
    fun entities(): List<AuEntity> = db.findAll()

    // TODO: must check that this is a tmp db !!
    @Mutation
    fun initDb(): String {
        handler.handle(
            db,
            EngineCommandEnvelope(
                session_id = "",
                command = DbInitCommand()
            ).to_json()
        )
        return "send DbInitCommand to handler."
    }

    @Mutation
    fun deleteAuctions(id: String): String {
        return handler.handle(
            db,
            EngineCommandEnvelope(
                session_id = "",
                command = DbDeleteAuctionsCommand()
            ).to_json()
        )
    }

    /**
     * Microprofile API:
     * https://download.eclipse.org/microprofile/microprofile-graphql-1.0/microprofile-graphql.html#mutations
     */
    @Mutation
    fun reCreateDummyDb(
        auction_count: Int = 1,
        auctioneer_count: Int = 2,
        trader_count: Int = 4,
        round_count: Int = 1,
        close_last_round: Boolean = false,
        use_counterparty_credits: Boolean = false,
    ): String {
//        Unirest.get("http://localhost:4040/ENGINE_INPUT_CHANNEL")
//            .queryString(mapOf(Pair("message", cmd)))
        handler.handle(
            db, EngineCommandEnvelope(
                session_id = "",
                command = DeCreateSampleDbCommand(
                    auction_count = auction_count,
                    auctioneer_count = auctioneer_count,
                    round_count = round_count,
                    trader_count = trader_count,
                    close_last_round = close_last_round,
                    use_counterparty_credits = use_counterparty_credits
                )
            ).to_json()
        )
        return "command sent to engine command handler via REST api" // TODO: is this true?
    }


    @Mutation
    fun createAuction(name: String): String {
        val d = DateTime()
        val sessionId = create_session_and_login("a1", "1")
        return handler.handle(
            db,
            EngineCommandEnvelope(
                session_id = sessionId,
                command = DeAuctionSaveCommand(
                    auction_id = "", // If exists then update else create new
                    auction_name = name,
                    use_counterparty_credits = "false", // This is going to change with probably MODE
                    quantity_label = "MMlb", // This is the label with quantity of the traded resource
                    quantity_minimum = "1", // Minimum bid quantity for the commodity in trade
                    quantity_step = "1", // The step is the increase/decrease quantity units for the commodity in trade
                    price_change_initial = "0.5", // This is the price change factor, meaning if it increased in a round then it increases by this number.
                    price_change_post_reversal = "0.125", // This is the price change factor after reversal, meaning if it increased in a round then it increases by this number.
                    price_label = "cpp", // The label of the price...
                    price_decimal_places = "3", // The decimal places in which the price needs to be shown to the user.
                    cost_multiplier = "10000", // This number is how to get the dollar price. 1 cent per pound, 50 Million Barrels , 10,000 ==  500K Dollars
                    /*
                    ===============The below once are just to save and give back to auction, no real logic==============
                     */
                    excess_level_0_label = "+",
                    excess_level_1_label = "++",
                    excess_level_2_label = "+++",
                    excess_level_3_label = "++++",
                    excess_level_4_label = "+++++",
                    excess_level_1_quantity = "10",
                    excess_level_2_quantity = "20",
                    excess_level_3_quantity = "30",
                    excess_level_4_quantity = "40",
                    /*
                    ==================The above once are just to save and give back to auction, no real logic==============
                    */
                    starting_price_announcement_mins = "5", // The time after which the price is going to announce, So this is not used now. No real logic.
                    month_is_1_based = true, // In FE, we use some JS library that starts January with 0 instead of 1. Maybe this can replaced with UTC in future.
                    starting_year = d.year.toString(),
                    starting_month = d.monthOfYear.toString(), // this is 1 based!
                    starting_day = d.dayOfMonth.toString(),
                    starting_hour = d.hourOfDay.toString(),
                    starting_mins = d.minuteOfHour.toString(),
                    /*
                    ===============The below are for automation of round creation, updated==========================
                    */
                    round_red_secs = "15",
                    round_orange_secs = "30",
                    round_open_min_seconds = "15",
                    round_closed_min_secs = "5"
                )
            ).to_json()
        )
    }

    @Mutation
    fun heartbeat(on: Boolean): String {
        features.heartbeat = on
        return "Heartbeat " + if (on) "on" else "off"
    }

    @Mutation
    fun trace_heartbeat(on: Boolean): String {
        handler.trace_heartbeat = on
        return "Heartbeat Trace: " + if (on) "on" else "off"
    }

    @Mutation
    fun broker_mode(on: Boolean): String {
        handler.trace_heartbeat = on
        return "Heartbeat Trace: " + if (on) "on" else "off"
    }

    @Mutation
    fun create_company(shortName: String): String {
        val sessionId = create_session_and_login("a1", "1")
        return handler.handle(
            db,
            EngineCommandEnvelope(
                session_id = sessionId,
                command = CompanySaveCommand("", shortName, "${shortName}_long")
            ).to_json()
        )
    }

    @Mutation
    fun flowControlMutation(auction_id: String, flowControlType: DeFlowControlType): String {
        val sessionId = create_session_and_login("a1", "1")
        return handler.handle(
            db,
            EngineCommandEnvelope(
                session_id = sessionId,
                command = DeFlowControlCommand(
                    auction_id,
                    flowControlType,
                    "10"
                )
            ).to_json()
        )
    }

    @Mutation
    fun addTrader(auction_id: String, traderId: String): String {
        val sessionId = create_session_and_login("a1", "1")
        return handler.handle(
            db,
            EngineCommandEnvelope(
                session_id = sessionId,
                command = DeTradersAddCommand(
                    auction_id,
                    ImmutableList.of(traderId)
                )
            ).to_json()
        )
    }

    @Mutation
    fun orderSubmit(
        auction_id: String,
        traderId: String,
        orderType: OrderType,
        round: String,
        quantity: String,
    ): String {
        val sessionId = create_session_and_login("a1", "1")

        return handler.handle(
            db,
            EngineCommandEnvelope(
                session_id = sessionId,
                command = DeOrderSubmitCommand(
                    auction_id,
                    traderId,
                    orderType,
                    round,
                    quantity
                )
            ).to_json()
        )
    }

    private fun create_session_and_login(userName: String, password: String): String {
        val sessionId = UUID.randomUUID().toString()
        handler.handle(
            db,
            EngineCommandEnvelope(
                session_id = "",
                ClientSocketCommand(sessionId, AuSession.ClientSocketState.OPENED)
            ).to_json()
        )

        handler.handle(
            db,
            EngineCommandEnvelope(
                session_id = sessionId,
                LoginCommand(
                    "a1",
                    "1"
                )
            ).to_json()
        )
        return sessionId
    }
}

