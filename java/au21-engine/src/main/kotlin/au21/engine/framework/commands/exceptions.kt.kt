// File: exceptions.kt.kt
package au21.engine.framework.commands

class AlertException(error: String) :
// Exception(error.joinToString(separator = "\n")) {
    Error(error) {

//    fun to_envelope(
//        session_id: String,
//        command_name: String,
//        command_json: String,
//        start: Long,
//        requests_since_last_start: Int,
//    ) = EngineTransaction(
//        client_command_maps = mutableListOf(
//            ClientCommandSessionsMap(
//                ShowMessage(
//                    BrowserMessageKind.ALERT, listOf(message ?: "")
//                ),
//                listOf(session_id)
//            )
//        ),
//        engine_command_name = command_name,
//        engine_command_json = command_json,
//        isHeartbeat = command_json == "TICK",
//        session_id = session_id,
//        duration_ms = duration_ms(start),
//        has_alert = true,
//        has_session_differ_error = false,
//        requests_since_last_restart = requests_since_last_start
//    )
//

}

// TODO: implement a way to catch errors created after validation
// - possibly we want to send something to the traders
// - definitely we want to send something to the auctioneers
// - definitely we want to be able to monitor, search, and trigger LogEvent alerts in the logs.
class LogException(error: String) : Error(error)
