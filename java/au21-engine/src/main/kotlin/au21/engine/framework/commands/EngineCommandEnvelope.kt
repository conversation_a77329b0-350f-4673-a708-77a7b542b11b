// File: EngineCommandEnvelope.kt
package au21.engine.framework.commands

/**
 * Note: marking members as private will stop ts-generator from outputing them!
 * but: then we need to have public accessor methods because 'protected'
 * doesn't work with Kotlin extension methods,
 * see: https://stackoverflow.com/questions/33852770/protected-members-not-accessible-in-extension-functions
 */

class EngineCommandEnvelope(
    val session_id: String,
    val command: EngineCommand
) {
    val simplename:String = command.javaClass.simpleName
    val classname:String = command.javaClass.canonicalName
}
