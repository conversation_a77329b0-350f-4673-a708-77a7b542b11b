// File: table-formatter.kt
package au21.engine.framework.utils

import de.vandermeer.asciitable.AT_Row
import de.vandermeer.asciitable.AsciiTable
import de.vandermeer.skb.interfaces.transformers.textformat.TextAlignment
import io.quarkus.logging.Log
import kotlin.reflect.KCallable
import kotlin.reflect.full.declaredMembers

inline fun <reified T> format_table(
    collection: Collection<T>,
    field_names: List<String>,
    print_json: Boolean = false
): String {

    // 1) with KCallable members (MUST USE declaredMembers, as members -> Companion members only (?!))

    val members: List<KCallable<*>> =
        field_names.mapNotNull { name ->
            try {
                T::class.declaredMembers.find { m -> m.name == name }
                    ?: run {
                        println("field not found: $name")
                        null
                    }
            } catch (e: Throwable) {
                println("field not found: $name")
                null
            }
        }

    if (members.isEmpty()) {
        println("NO FIELDS FOUND")
        return ""
    }

    try {
        AsciiTable().apply {
            addRule()
            addRow(members.map { it.name })
            addRule()
            collection.forEach { item ->
                addRow(members.map { it.call(item) })
                    .also { row: AT_Row ->
                        members.forEachIndexed { index, m ->
                            when (m.call(item)) {
                                is Int ->
                                    row.cells[index].context.textAlignment = TextAlignment.RIGHT
                            }
                        }
                    }
            }
            addRule()
            return "\n + ${T::class.java.name}:\n" + render()
        }
    } catch (t: Throwable) {
        Log.error(t.localizedMessage)
        return ""
    }

//    if (print_json) {
//        // TODO: does this work with primitives?
//        println(objToPrettyFormat(collection.forEach { println(it.to_json()) }))
//    }

    // 2) with javaclass fields: not working only get companion

//    val fields:List<Field> = field_names.mapNotNull { name ->
//        try {
//            T::class.java.fields.find { f:Field ->
//                f.name == name
//            }
//                ?: run {
//                    println("field not found: $name")
//                    null
//                }
//        } catch (e: Throwable) {
//            println("field not found: $name")
//            null
//        }
//    }
//
//    if (fields.isEmpty()){
//        println("NO FIELDS FOUND")
//        return
//    }
//
//    AsciiTable().apply {
//        addRule()
//        // addRow(members.map { it.name })
//        addRow(fields.map { it.name })
//        addRule()
//        collection.forEach { item ->
//            addRow(fields.map { it.get(item) })
//                .also { row: AT_Row ->
//                    fields.forEachIndexed { index, f ->
//                        val type = f.type
//                        when (type.toString()) {
//                            "Int" ->
//                                row.cells[index].context.textAlignment = TextAlignment.RIGHT
//                        }
//                    }
//                }
//        }
//        addRule()
//        println(render())
//    }


}


//fun print_object_as_table(
//    obj: Any,
//    print_json: Boolean = true
//) {
//
//    val members = obj::class.declaredMembers
//        .filter { m: KCallable<*> ->
//            m.visibility == KVisibility.PUBLIC } //it.visibility == KVisibility.PUBLIC  }
//      //  .filter { m: KCallable<*> -> m.parameters.isEmpty() } // this should take care of collections, as they have a type
//
//    if (members.isEmpty()) {
//        println("NO FIELDS FOUND")
//        return
//    }
//
//    AsciiTable().apply {
//        addRule()
//        addRow("property", "value")
//        addRule()
//        members.forEach { m ->
//            val value = m.call(obj)
//            addRow(m.name, value)
//                .also { row: AT_Row ->
//                    when (value) {
//                        is Int ->
//                            row.cells[1].context.textAlignment = TextAlignment.RIGHT
//                    }
//                }
//        }
//        addRule()
//        println(render())
//    }
//
////  2) JAVA Fields: can't exclude private:
////
////    val fields: List<Field> = obj::class.java.declaredFields
////        .filterNot { f ->
////            println(f.modifiers)
////            Modifier.isPrivate(f.modifiers)
////        }
////
////    if (fields.isEmpty()) {
////        println("NO FIELDS FOUND")
////        return
////    }
////
////    AsciiTable().apply {
////        addRule()
////        addRow("property", "value")
////        addRule()
////        fields.forEach { f ->
////            val value = f.get(obj)
////            addRow(f.name, value)
////                .also { row: AT_Row ->
////                    when (value) {
////                        is Int ->
////                            row.cells[1].context.textAlignment = TextAlignment.RIGHT
////                    }
////                }
////        }
////        addRule()
////        println(render())
////    }
//
//}
//
