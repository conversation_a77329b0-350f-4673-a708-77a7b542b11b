// File: controller.kt
package au21.engine.framework.commands

//import io.opentracing.Span
//import io.opentracing.Tracer
//import org.eclipse.microprofile.opentracing.Traced
//import org.eclipse.microprofile.opentracing.Traced
import au21.engine.framework.database.AuEntityManager
import io.quarkus.logging.Log
import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.QueryParam


/**
 * TODO: None of these work outside of Quarkus requests:
 * - @RequestScoped
 * - @PostConstruct
 * - @PreDestroy
 * We could go: RedisClient -> HTTP possibly
 *
 * POSSIBLE SOLUTIONS:
 * a) figure out how to make request-scoped work with Redis - unlikely
 * b) change to a factory: question about how to get lifecycle working
 * c) send messages via beans
 * d) have the backend actually be an http app
 *  - how to push to faye? sse ??
 *  see: https://www.one-tab.com/page/c5oJoidvTNGUiH9jHpGwUA
 */


// TODO: do we need to add @RequestScoped here, or is it assumed? We should test the AuEntityManager
//@Traced(false)
@Path("/ENGINE_INPUT_CHANNEL")
class EngineCommandController {
//
//    @Inject
//    lateinit var bus: EventBus

    @Inject
    lateinit var handler: EngineCommandHandler

    /**
     * This db instance is create per request:
     * TODO: what happens if there are multiple requests, each with their own db instance??
     */
    @Inject
    lateinit var db: AuEntityManager

  //  @Inject
   // lateinit var tracer: Tracer


    // @Traced
    @GET
    @Synchronized
    fun handle(@QueryParam("message") message: String): String {
        //TracerConfig.addHostTag(tracer.activeSpan())
        try {

            return when (message) {
                "TICK" -> handler.handle(db, message)
                else -> {
                   // duration_ms("ENGINE_COMMAND_HANDLER") {
                        handler.handle(db, message)
                   // }
                }
            }

        } catch (t: Throwable) {
            t.printStackTrace()
            t.message?.let {
                Log.error(it)
               // LOG.error(it)
            }
            t.cause?.message?.let {
                Log.error("caused by: $it")
               // LOG.error(it)
            }
            return t.cause?.message ?: t.message ?: "Error"
        }
    }

}


//    var registry: MeterRegistry? = null
//    var highestPrime = LongAccumulator({ a: Long, b: Long ->
//        java.lang.Long.max(
//            a,
//            b
//        )
//    }, 0)
//
//    fun highestObservedPrimeNumber(): Long {
//        return highestPrime.get()
//    }

//    fun EngineCommandController(registry: MeterRegistry) {
//      //  this.registry = registry
//
//        // Create a gauge that uses the highestPrimeNumberSoFar method
//        // to obtain the highest observed prime number
////        registry.gauge(
////            "prime.number.max",
////             highestObservedPrimeNumber()
////        )
//    }

