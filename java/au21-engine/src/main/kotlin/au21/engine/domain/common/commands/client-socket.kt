// File: client-socket.kt
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuSession.ClientSocketState.CLOSED
import au21.engine.domain.common.model.AuSession.ClientSocketState.OPENED
import au21.engine.domain.common.services.session_by_sid
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail
import au21.engine.framework.commands.interfaces.ISessionsTerminated
import au21.engine.framework.commands.interfaces.SessionTermination
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.is_blank
import io.quarkus.logging.Log

/*
 * from EngineSocket.kt when sockets open and close
 * IF OPENED AND NO EXISTING SESSION THEN NEW SESSION CREATED
 * (this should be the only place where sessions are created!)
 */

class ClientSocketCommand(
    val sid: String,
    val state: AuSession.ClientSocketState,
    val browser_name: String? = null,
    val browser_version: String? = null,
    val browser_os: String? = null
) : EngineCommand() {

    // NOTE: this session id is not used, because it will be null in tests
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

//        // note: we don't use session_non_terminated_or_alert, as it could have been terminated alreayd
//        val session = db.session_by_sid(sid)
//            ?: throw Error("session id should not be null")

        if (sid.is_blank())
            fail("No session id.")

        if (sid == "0")
            fail("Session id cannot be zero")

        val existing_session: AuSession? = db.session_by_sid(sid)

        // MOVED BELOW, not an AlertException error:
//        if (state == CLOSED && existing_session == null) {
//            throw Error("existing session not found for socket")
//        }

        return ClientSocketAction(
            this, db, existing_session,
            sid,
            state,
            browser_name,
            browser_version,
            browser_os
        )
    }
}


class ClientSocketAction(
    override val command: ClientSocketCommand,
    override val db: AuEntityManager,
    override val session: AuSession?, // ie: Existing Session nb // won't exist when socket OPENED for first time!
    val sid: String,
    val state: AuSession.ClientSocketState,
    val browser_name: String? = null,
    val browser_version: String? = null,
    val browser_os: String? = null
) : EngineAction, ISessionsTerminated {

    override val sessions_terminated = mutableListOf<SessionTermination>()

    /**
     * TODO: unclear about how new_session works.
     * - BTW, it should always be non-null. bacause:
     * - if Socket OPENED then it's either the new_session or the existing session
     * - if Socket CLOSED then it's the old session or throws excetions
     *
     * TODO: do we need to terminate any sessions (can't see why)
     */

    var new_session: AuSession? = null

    override fun mutate() {

        when (state) {
            OPENED ->
                when (session) {
                    null ->
                        // here we create a new session if need be:
                        // - default state is OPENED:
                        //  TODO: check that this is the only place where sessions are created!
                        new_session = AuSession(
                            session_id = sid,
                            browser_name = browser_name,
                            browser_version = browser_version,
                            browser_os = browser_os
                        ).also {
                            db.save(it)
                            Log.info("session created: $sid")
                            // new_session = it
                        }
                    else -> {
                        session.socket_state = OPENED
                        // new_session = session
                    }
                }
            CLOSED -> {
                when (session) {
                    null -> Log.error("sid $sid closed, but has no AuSession!!")
                    else -> {
                        session.socket_state = CLOSED
                        //new_session = session
                    }
                }
            }
        }


        new_session?.let { db.save(it) }
        session?.let { db.save(it) }

        // TODO: not sure what this is?
// this will actually be fired before the session is created !
//        when {
//            is_terminated -> {
//                new_session.socket_state = state
//                db.save(new_session)
//            }
//        }
    }

}
