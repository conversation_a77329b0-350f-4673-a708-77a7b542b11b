// File: test.kt
package au21.engine.domain.de.services.state

import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeAuctionSettings
import au21.engine.domain.de.model.DeFlowControlType
import au21.engine.domain.de.model.DePriceRule
import au21.engine.domain.de.services.state.DeControlValidator.MUTATE
import org.joda.time.DateTime
import kotlin.concurrent.thread

const val TICK_SECS = 3

fun main() {

    val de = DeAuction(
        auction_name = "auction 1",
        settings = DeAuctionSettings(
            use_counterparty_credits = false,
            round_open_min_secs = 5,
            round_closed_min_secs = 5,
            round_orange_secs = 15,
            round_red_secs = 30,
            starting_price_announcement_mins = 1,
            price_rule = DePriceRule()
        )
    ).apply {
        starting_time = DateTime().plusMinutes(1).toDate()
    }

    fun validate_and_mutate(command: DeFlowControlType) {
        when (val result: String? = DeControlValidator.validate(de, command)) {
            null -> {
            }
            MUTATE -> DeMutator.mutate(de, command, null)
            else -> println(result)
        }
    }

    thread {
        while (true) {
            try {
                validate_and_mutate(DeFlowControlType.HEARTBEAT)
                Thread.sleep(TICK_SECS * 1_000L)
            } catch (e: Throwable) {
                println("error: " + e.message)
            }
        }
    }

    //   validate_and_mutate(DeAuctionCommand.SET_STARTING_PRICE)
    //   validate_and_mutate(DeAuctionCommand.ENGAGE_AUTO_PILOT)

    while (true) {
        readLine()?.let { x ->
            try {
                println("pre: ${de.auctioneer_state}")
                when (x.uppercase()) {
//                    "ON" -> validate_and_mutate(DeFlowControlType.ENGAGE_AUTO_PILOT)
//                    "OFF" -> validate_and_mutate(DeFlowControlType.DISENGAGE_AUTO_PILOT)
                    "BEAT" -> validate_and_mutate(DeFlowControlType.HEARTBEAT)
                    "PRICE" -> validate_and_mutate(DeFlowControlType.SET_STARTING_PRICE)
                    "ANNOUNCE" -> validate_and_mutate(DeFlowControlType.ANNOUNCE_STARTING_PRICE)
                    "START" -> validate_and_mutate(DeFlowControlType.START_AUCTION)
                    "CLOSE" -> validate_and_mutate(DeFlowControlType.CLOSE_ROUND)
                    "RESET" -> validate_and_mutate(DeFlowControlType.REOPEN_ROUND)
                    "NEXT" -> validate_and_mutate(DeFlowControlType.NEXT_ROUND)
                    "AWARD" -> validate_and_mutate(DeFlowControlType.AWARD_AUCTION)
                    else -> println("command not understood: $x")
                }
                println("post: ${de.auctioneer_state}")
                println(de.de_controls())
                println("------------------------------------------------------\n")
            } catch (e: NotImplementedError) {
                println("$x not implemented")
            } catch (e: Throwable) {

            }
        }


    }
}

