// File: DeRound.kt
package au21.engine.domain.de.model

import au21.engine.domain.common.model.OrderType
import au21.engine.domain.common.model.PriceDirection
import au21.engine.framework.commands.AlertException
import org.joda.time.DateTime
import java.util.*
import javax.persistence.Embeddable


@Embeddable
class DeRound(
    var number: Int,
    var price: Double?,
    has_reversed_: <PERSON><PERSON><PERSON>,
    direction: PriceDirection? // will be null on the first round
) {

    init {
        if (number == 0)
            throw AlertException("Rounds must always have a number > 0")
        if (number > 1 && price == null)
            throw AlertException("After round 1, rounds must be created with a non-null price.")
    }

    var has_reversed: Boolean = has_reversed_
        private set

    var price_direction_label: String? = direction?.toString()
        private set
    var price_direction: PriceDirection?
        get() = price_direction_label?.let { PriceDirection.valueOf(it) }
        set(varue) {
            this.price_direction_label = varue.toString()
        }

    var open_time: Date? = null
        private set

    fun open() {
        if (this.open_time != null)
            throw AlertException("open_time should be null")
        open_time = DateTime().toDate()
    }

    var closed_time: Date? = null
        private set

    fun close() {
        if (this.closed_time != null)
            throw AlertException("closed time should be null")
        closed_time = DateTime().toDate()
    }

    var time_started: Date? = null
        private set

    fun started() {
        time_started = DateTime().toDate()
    }

    var trader_infos: MutableList<DeRoundTraderInfo> = mutableListOf()
        private set

    // not sure we need to keep these, seem to be only used as part of flow calc?
//        var limits: MutableList<VolumeLimit> = mutableListOf()
//            private set

    fun buy_orders(): List<DeOrder> =
        trader_infos
            .map { it.order }
            .filter { it.type == OrderType.BUY }

    fun sell_orders(): List<DeOrder> =
        trader_infos
            .map { it.order }
            .filter { it.type == OrderType.SELL }

    fun buyers(): List<DeTradingCompany> = buy_orders().map { it.trading_company }
    fun sellers(): List<DeTradingCompany> = sell_orders().map { it.trading_company }

    var matches: MutableList<DeMatch> = mutableListOf()
        private set

    fun setMatches(m: List<DeMatch>) {
        matches.clear()
        m.forEach { matches.add(it) }
    }

    fun getMatch(t: DeTradingCompany): DeMatch? =
        matches.find {
            it.buyer_shortname == t.shortname_at_auction_time ||
                    it.seller_shortname == t.shortname_at_auction_time
        }

    fun match_vol(): Int = matches.sumOf { it.match }
    fun match_vol(t: DeTradingCompany): Int = matches
        // TODO: t.company_id should be either buy or sell but not both!
        .filter {
            it.buy_order.trading_company.company_id == t.company_id
                    || it.sell_order.trading_company.company_id == t.company_id
        } // it's an alertException if they are equal !!
        .sumOf { it.match }

    fun all_orders_are_non_default(): Boolean =
        trader_infos.all { it.has_non_default_bid() }

    fun round_open_seconds(): Int = 0 // we'll tick this

    var max_potential_flow: Int = 0
        private set

    // TODO: reimplement
//        fun setMaxPotentialFlow(mfr: MaxFlowResult) { // from an optimization engine
//            this.max_potential_flow = mfr.max_flow
//        }

//        var match_ratio_label: String = MatchedVolumeRatio.UP_TO_10.toString()
//            private set
//
//        var matched_sell_ratio: MatchedVolumeRatio
//            get() = enumvalueOfWithDefault(match_ratio_label, MatchedVolumeRatio.UP_TO_10)
//            set(it) {
//                this.match_ratio_label = it.toString()
//            }

}
