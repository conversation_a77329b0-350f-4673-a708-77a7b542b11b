// File: queries.kt
package au21.engine.domain.common.services

import au21.engine.domain.common.model.*
import au21.engine.framework.commands.AlertException
import au21.engine.framework.database.AuEntityManager
import io.quarkus.logging.Log


/*
      Database extensions
 */

fun AuEntityManager.open_auctions(): List<Auction> =
    try {
        val query = "SELECT a FROM ${Auction::class.java.name} a WHERE a.deleted=false AND a.closed=false"
        em.createQuery(query, Auction::class.java).resultList
    } catch (e: Exception) {
        Log.error(e.message)
        listOf()
    }

inline fun <reified A : Auction> AuEntityManager.auction_or_alert(auction_id: String?): A =
    auction_id
        ?.let { this.byId(auction_id) }
        ?: throw  AlertException("no auction found with id: $auction_id")


/**
 * Command related (ie: throw errors)
 */

/** SESSION */

private fun AuEntityManager.non_terminated_sessions(
    logged_in_only: <PERSON><PERSON><PERSON>,
    role: AuUserRole? = null
): List<AuSession> {

    val non_terminated_sessions: List<AuSession> =
        query("select s from AuSession s WHERE s.deleted = false AND s.termination_reason_label == null")

    val first_filter =
        if (logged_in_only)
            non_terminated_sessions.filter { it.user != null }
        else
            non_terminated_sessions

    val second_filter = role?.let {
        first_filter.filter { it.inRole(role) }
    } ?: first_filter

    return second_filter
}

//    try {
//        val cb: CriteriaBuilder = em.criteriaBuilder
//        val cq: CriteriaQuery<AuSession> = cb.createQuery(AuSession::class.java)
//        val root: Root<AuSession> = cq.from(AuSession::class.java)
//
//        val predicates = mutableListOf(
//            cb.isTrue(root.get("deleted")),
//            cb.isNull(root.get<String?>("termination_reason_label"))
//        )
//
//        if (logged_in_only)
//            predicates.add(cb.isNotNull(root.get<String>("user_id")))
//
//        role?.let {
//            predicates.add(cb.equal(root.get<String>("role_label"), it.toString()))
//        }
//        cq.select(root).where(*predicates.toTypedArray())
//        em.createQuery(cq).resultList
//    } catch (t: Throwable) {
//        t.cause?.let { Logger.error(it.message.toString()) }
//        Logger.error(t.message.toString())
//        listOf()
//    }

fun AuEntityManager.sessions_non_terminated(): List<AuSession> =
    non_terminated_sessions(logged_in_only = false)

fun AuEntityManager.sessions_logged_in(): List<AuSession> =
    non_terminated_sessions(logged_in_only = true)

fun AuEntityManager.sessions_logged_in_auctioneers(): List<AuSession> =
    non_terminated_sessions(logged_in_only = true, AuUserRole.AUCTIONEER)

fun AuEntityManager.sessions_logged_in_traders(): List<AuSession> =
    non_terminated_sessions(logged_in_only = true, AuUserRole.TRADER)

fun AuEntityManager.session_by_sid(sid: String?): AuSession? =
    findFirst {
        // Loginfo("looking for: $sid, found: ${it.session_id}")
        it.session_id == sid
    }

fun AuEntityManager.logged_in_session_for_user(u: Person?): AuSession? =
    u?.let {
        this.non_terminated_sessions(logged_in_only = true)
            .find { s: AuSession -> s.user == u }
    }

fun AuEntityManager.is_logged_in(p: Person?): Boolean =
    this.logged_in_session_for_user(p) != null


fun AuEntityManager.session_non_terminated_or_alert(session_id: String?): AuSession =
    when (val s: AuSession? = session_by_sid(session_id)) {
        null -> throw AlertException("no session found with id: $session_id")
        else ->
            if (s.isTerminated())
                throw AlertException("Session already terminated.")
            else
                s
    }

fun AuEntityManager.auctioneer_session_or_alert(session_id: String?): AuSession =
    session_non_terminated_or_alert(session_id)
        .takeIf { it.is_auctioneer() }
        ?: throw AlertException("Only auctioneers can perform this action.")


// TODO: implement JPA Criteria API here instead:
//fun AuEntityManager.sessions_by_sid(sessionIds: List<String>): List<AuSession> =
//    filter { it.logged_in }

fun AuEntityManager.user_by_username(
    username: String?,
    case_insensitive: Boolean = true,
    include_deleted: Boolean = false
): Person? =
    username?.let {
        findFirst(include_deleted) {
            if (case_insensitive)
                it.username.equals(username, ignoreCase = true)
            else
                it.username == username
        }
    }


fun AuEntityManager.traders(): List<Person> =
    findAll<Person>().filter { it.isTrader() }

fun AuEntityManager.company_by_shortname(
    shortname: String?,
    case_insensitive: Boolean = true,
    include_deleted: Boolean = false
): Company? =
    shortname?.let {
        findFirst(include_deleted) {
            if (case_insensitive)
                it.shortname.equals(shortname, ignoreCase = true)
            else it.shortname == shortname
        }
    }

fun AuEntityManager.company_by_longname(
    longname: String?,
    case_insensitive: Boolean = true,
    include_deleted: Boolean = false
): Company? =
    longname?.let {
        findFirst(include_deleted) {
            if (case_insensitive)
                it.longname.equals(longname, ignoreCase = true)
            else it.longname == longname
        }
    }

fun AuEntityManager.company_users(c: Company): List<Person> =
    findAll<Person>().filter { it.company == c }


inline fun <reified T : Auction> AuEntityManager.find_auction_by_name(
    auction_name: String?,
    case_insensitive: Boolean = true,
    include_deleted: Boolean = false
): T? =
    auction_name?.let {
        findFirst(include_deleted) {
            if (case_insensitive)
                it.auction_name.equals(auction_name, ignoreCase = true)
            else
                it.auction_name == auction_name
        }
    }

//
//fun AuDb.counter_parties(lender: Company, borrower: Company): COUNTER_PARTY_CREDIT_LIMIT? =
//    this.filter<COUNTER_PARTY_CREDIT_LIMIT> { it.lender == lender && it.borrower == borrower }
//        .maxBy { it.timestamp } // should get the highest one
//
//fun counterparty_credit_vol_limit(
//    limit: COUNTER_PARTY_CREDIT_LIMIT, // dollars of credit from seller to buyer
//    price: Double, // ie: round price
//    price_volume_to_currency_converter: Double // e.g: price * volume * converter = dollars
//): Int {
// e.g: cents/lb * dollars/cent * MMlb/lb
// = cpp * 0.01 * 1 million
// = cpp * 10,000
// so limit is dollars / (cpp * 10,000)
//
//return (price * price_volume_to_currency_converter).let { denominator: Double ->
//    if (denominator == 0.0)
//        0
//    else
//        (limit.credit_limit / denominator).toInt()
//}
//
//}
//

//// TODO: does not take into account seller eligibility, buyer min and buyer eligibility
//
//
