// File: CompanyElement.kt
package au21.engine.domain.common.viewmodel


import au21.engine.domain.common.model.Company
import au21.engine.framework.client.StoreElement
import au21.engine.framework.database.AuEntityManager

data class CompanyElement(
    override val id: String,
    val company_id: String,
    val company_longname: String,
    val company_shortname: String,
//    val credit_limit: String = none,
//    val default_risk: String = none
) : StoreElement {


    companion object {

        fun create(c: Company) = CompanyElement(
            id = c.id_str(),
            company_id = c.id_str(),
            company_longname = c.longname,
            company_shortname = c.shortname
        )

        fun all(db: AuEntityManager): List<CompanyElement> =
            db.findAll<Company>().map { create(it) }

    }
}

