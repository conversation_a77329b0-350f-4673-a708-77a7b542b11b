// File: control_state_viewmodel.kt
package au21.engine.domain.de.services.state

import au21.engine.domain.de.model.*
import au21.engine.domain.de.model.DeAuctioneerState.*
import au21.engine.domain.de.model.DeFlowControlType.*


fun DeAuction.de_controls(): Map<DeFlowControlType, Boolean> =
    enumValues<DeFlowControlType>()
        .filter { it != HEARTBEAT }
        .associateWith {
            when (it) {
//                ENGAGE_AUTO_PILOT -> when (autopilot) {
//                    ENGAGED -> false
//                    DISENGAGED -> when (auction_state) {
//                        STARTING_PRICE_SET,
//                        STARTING_PRICE_ANNOUNCED,
//                        ROUND_OPEN_WAITING_FOR_BIDS,
//                        ROUND_OPEN_ALL_BIDS_IN,
//                        ROUND_CLOSED_NOT_AWARDABLE,
//                        ROUND_CLOSED_AWARDABLE -> true
//                        else -> false
//                    }
//                }
//                DISENGAGE_AUTO_PILOT -> autopilot == ENGAGED
                SET_STARTING_PRICE -> when (auctioneer_state) {
                    STARTING_PRICE_NOT_SET,
                    STARTING_PRICE_SET,
                    STARTING_PRICE_ANNOUNCED -> true
                    else -> false
                }
                ANNOUNCE_STARTING_PRICE -> auctioneer_state == STARTING_PRICE_SET
                START_AUCTION -> auctioneer_state == STARTING_PRICE_ANNOUNCED
                CLOSE_ROUND -> auctioneer_state.common_state() == DeCommonState.ROUND_OPEN
                REOPEN_ROUND, // TODO: do we need this?
                NEXT_ROUND -> auctioneer_state == DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE
                AWARD_AUCTION -> auctioneer_state == ROUND_CLOSED_AWARDABLE
                HEARTBEAT -> throw Error("HEARTBEAT should have been filtered out.")
            }
        }
