// File: extensions.kt
package au21.engine.domain.de.model

import au21.engine.domain.common.model.*
import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.AuFormatter
import au21.engine.framework.utils.thousands
import org.joda.time.DateTime
import kotlin.math.abs


/********************
 * De AUCTION
 */


// can be used during a round:
fun DeAuction.price_has_overshot(): Boolean =
    lastround().let { n ->
        when (n.price_direction) {
            null -> false // ie: first round
            PriceDirection.UP -> excess_supply(n) < 0
            PriceDirection.DOWN -> excess_demand(n) < 0
        }
    }

fun DeAuction.current_price_direction(): PriceDirection? = lastround().let { n: DeRound ->
    if (n.number == 1)
        return null
    penultimate()?.let { pen: DeRound ->
        // NOTE: assumes that after the first round, round prices cannot be null
        if (n.price!! > pen.price!!) PriceDirection.UP
        else if (pen.price!! > n.price!!) PriceDirection.DOWN
        else throw Error("subsequent rounds can't have the same price")
    }
}

fun DeAuction.time_state(): DeTimeState? = DateTime().toDate().let { now ->
    announce_time()?.let {
        if (now.before(it))
            return DeTimeState.BEFORE_ANNOUNCE_TIME
    }
    starting_time?.let {
        return if (now.before(it))
            DeTimeState.BEFORE_START_TIME
        else
            DeTimeState.AUCTION_HAS_STARTED
    }
    return null
}


fun DeAuction.is_awardable(): Boolean = lastround().let { n ->
    val total_demand = total_buy(n)
    val total_supply = total_sell(n)
    when {

        de_trading_companies.all { it.next_round_order_will_be_mandatory } -> true

        n.price == null -> false

        // n.number == 1 -> false
        total_demand > total_supply ->
            // so the question is: did you get here from an up or a down?
            when (n.has_reversed) {
                false -> false // price will reverse
                true -> {
                    val potential_next_price = n.price!! - settings.price_rule.price_change_post_reversal
                    rounds.any { it.price != null && it.price!! <= potential_next_price }
                }
            }

        total_supply > total_demand ->
            // so the question is: did you get here from an up or a down?
            when (n.has_reversed) {
                (n.price == null) -> false
                false -> false
                true -> {
                    val potential_next_price = n.price!! + settings.price_rule.price_change_post_reversal
                    rounds.any { it.price != null && it.price!! >= potential_next_price }
                }
            }

        else -> true // ie: supply == demand !
    }
}


// 1) if supply equals demand then it's awardable

// 2) if demand != supply and we have not reversed then we keep going

//    // at this point we must have a penultimate
//    val pen: DeAuction.Round = penultimate() ?:
//    throw Error("is_awardable() expected penultimate round")
//
//    if (excess_supply(n) > 0) {
//        // so we need to go down, unless the next price is the same as the previous round
//        return small_decrease(n) == pen.price
//    }
//    if (excess_demand(n) > 0) {
//        return small_increase(n) == pen.price
//    }

//    return false
//}

//fun get_trader(p: Person?): Trader? =
//    traders.find { it.company == p?.company }
//
//fun create_trader(u: Person): Trader =
//    Trader(u.company
//        ?: throw Error("user has no company: $u")).also {
//        traders.add(it)
//    }

////////////////////////////////////


/*
        AUCTION extension methods:
 */


fun DeAuction.getTrader(c: Company?): DeTradingCompany? =
    when (c) {
        null -> null
        else -> de_trading_companies.find { it.company_id == c.id }
    }

fun DeAuction.hasTrader(c: Company?): Boolean =
    when (c) {
        null -> false
        else -> de_trading_companies.any { t -> t.company_id == c.id }
    }

fun DeAuction.hasTrader(u: Person?): Boolean =
    when (u) {
        null -> false
        else -> hasTrader(u.company)
    }

//fun DeAuction.state_snapshot(): AuctionStateSnapshot = AuctionStateSnapshot(this)

// ie: for determining closability: (moved to round )
//fun DeAuction.has_non_default_bid(company: Company?) =
//    when (company) {
//        null -> false
//        else -> rounds.any { r ->
//            r.trader_infos
//                .find { it.company == company }?.let { rti: RoundTraderInfo ->
//                    rti.has_non_default_bid()
//                } ?: false
//        }
//    }

// ie: for determining credit editing, trader remove
fun DeAuction.has_non_zero_bid(company: Company?) =
    when (company) {
        null -> false
        else -> rounds.any { r ->
            r.trader_infos
                .find { it.de_trading_company.company_id == company.id }
                ?.let { rti: DeRoundTraderInfo -> rti.has_non_zero_bid() }
                ?: false
        }
    }


fun DeAuction.is_before_end_of_first_round(): Boolean = rounds.size > 1 || !listOf(
    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE,
    DeAuctioneerState.ROUND_CLOSED_AWARDABLE,
    DeAuctioneerState.AUCTION_CLOSED
).contains(auctioneer_state)


//fun DeAuction.round_closeable(): Boolean =
//    lastround()?.let {
//        when {
//            it.open_time == null -> false
//            it.closed_time != null -> false
//            DateTime(it.open_time)
//                .plusSeconds(settings.round_open_min_secs)
//                .isBefore(DateTime()) ->
//                true
//            else -> false
//        }
//    } ?: false

//fun DeAuction.next_round_openable(): Boolean =
//    lastround()?.let {
//        when {
//            it.closed_time == null -> false
//            DateTime(it.closed_time)
//                .plusSeconds(settings.round_closed_min_secs)
//                .isBefore(DateTime()) -> true
//            else -> false
//        }
//    } ?: false

//fun DeAuction.auction_row_datetime_format(): String =
//    when (this) {
////                is TE_AUCTION ->
////                    "${date_time_format(a.window_one_opens)} to ${date_time_format(a.closing_datetime())}"
//        else -> "not implemented"
//    }
/*
var round_counterparty_limits: MutableList<DE_VOLUME_LIMIT> = mutableListOf()
var matches: MutableList<DeAuction.Match> = mutableListOf()
val rbis: MutableMap<DeAuction.Trader, DeAuction.Trader_ROUND> = mutableMapOf()
 */

// moved to queries
//fun AuEntityManager.open_de_auctions(): List<DeAuction> = findAll<DeAuction>().filter { !it.isClosed() }


//fun Round.get_match_potential(buyer_rti: RoundTraderInfo, seller_rti: RoundTraderInfo): Int {
//    return minOf(
//        limits.find {
//            is_same_entity(
//                it.buyer.company,
//                buyer_rti.trader.company
//            ) && is_same_entity(it.seller.company, seller_rti.trader.company)
//        }?.limit ?: 0,
//        buyer_rti.constraints.max_buy_volume,
//        seller_rti.constraints.max_sell_volume
//    )
//}


fun DeAuction.message_auction_state(): String =
// TODO: this is in the wrong place, it is used by OnMessage and MESSAGE,
// TODO: ie: move to Common_Extensions file
    when (auctioneer_state) {
//        DeState.AUCTION_INIT -> "Auction setting up "
//        DeState.ROUND_READY -> "Round " + this.rounds.size.toString() + " ready to start "
//        DeState.ROUND_RUNNING -> "Round " + this.rounds.size.toString() + " ticking @ " + this.time_remaining + " sec "
//        DeState.ROUND_PAUSED -> "Round " + this.rounds.size.toString() + " PAUSED @ " + this.time_remaining + " sec "
//        DeState.ROUND_CLOSED -> "Round " + this.rounds.size.toString() + " closed "
//        DeState.AUCTION_CLOSED -> "Auction closed "
        // else -> return "state not handled: " + this.state
        else -> throw Exception()
    }

fun DeAuction.excess_side(r: DeRound): OrderType =
    when {
        total_buy(r) > total_sell(r) -> OrderType.BUY
        total_sell(r) > total_buy(r) -> OrderType.SELL
        else -> OrderType.NONE
    }

fun DeAuction.excess_quantity(r: DeRound): Int =
    abs(total_buy(r) - total_sell(r))

fun DeAuction.excess_level(r: DeRound, role: AuUserRole): String =
    settings.price_rule.get_excess_level(excess_quantity(r), role)

//fun DeAuction.excess_direction_str(r: Round): String =
//    if (total_buy(r) > total_sell(r)) "Buy"
//    else if (total_sell(r) > total_buy(r)) "Sell"
//    else "none"

fun DeAuction.show_trader_excess(r: DeRound): Boolean = r != lastround() || listOf(
    DeCommonState.AUCTION_CLOSED
).contains(common_state)

fun DeAuction.format_round_price(role: AuUserRole, r: DeRound): String =
    when (role) {
        // auctioneer view: (auctioneer status, blotter, etc):
        AuUserRole.AUCTIONEER -> r.price?.let {
            AuFormatter.format_to_places(it, this.price_decimal_places)
        } ?: "---"
        // trader view: (ie: common status)
        AuUserRole.TRADER -> when {
            r.price == null || !starting_price_announced() -> "waiting"
            else -> AuFormatter.format_to_places(r.price!!, this.price_decimal_places)
        }
    }


// fun DeAuction.de_counterparty_credit_vol_limit(r: DeAuction_ROUND, seller: DeAuction.Trader, buyer: DeAuction.Trader): Int =
//        Common_Getters.common_counterparty_credit_vol_limit (r.price, a.settings.cost_multiplier, seller.person, buyer.person))

/******************
 * De Trader
 */

fun DeAuction.trader_by_company_id(company_id: Long?): DeTradingCompany? =
    when (company_id) {
        null -> null
        else -> de_trading_companies.find { it.company_id == company_id }
    }

fun DeAuction.trader_by_company_id_str(company_id_str: String?): DeTradingCompany? =
    when (company_id_str) {
        null -> null
        else -> de_trading_companies.find { it.company_id.toString() == company_id_str }
    }

//fun DeAuction.get_trader(u: Person?): DeAuction.Trader? =
//        if (u?.Company != null)
//            traders.first { it.Company == u.Company }
//        else
//            null


fun DeAuction.get_trader(s: AuSession?): DeTradingCompany? =
    when (val c = s?.user?.company) {
        null -> null
        else -> getTrader(c)
    }

fun DeAuction.round_sellers(r: DeRound): List<DeTradingCompany> =
    de_trading_companies.filter {
        r.get_order(it).type == OrderType.SELL
    }

fun DeAuction.round_buyers(r: DeRound): List<DeTradingCompany> =
    de_trading_companies.filter {
        r.get_order(it).type == OrderType.BUY
    }

fun DeAuction.seller_count(r: DeRound): Int = round_sellers(r).size

fun DeAuction.buyer_count(r: DeRound): Int = round_buyers(r).size


/**
 *
 * DeOrder
 *
 */


fun DeAuction.get_orders(t: DeTradingCompany): List<DeOrder> =
    rounds.map { it.trader_infos }
        .flatten()
        .filter { it.de_trading_company.company_id == t.company_id }
        .map { it.order }


// Can't compare traders as they aren't entities, and don't have identity !!
fun DeRound.get_order(t: DeTradingCompany): DeOrder =
    trader_infos
        .find { it.de_trading_company.company_id == t.company_id }
        ?.order
        ?: throw AlertException("We should always have a round info and an order for a trader")


//fun Order?.side_str(): String =
//    when {
//        this == null -> "---"
//        isBuy() -> "Buy"
//        isSell() -> "Sell"
//        else -> throw AlertException("ERROR: order is neither buy, sell, or null.")
//    }

//fun DeOrder?.format_order_volume(): String =
//        this?.let { FormatUtil.format_thousands(this.volume) } ?: ""

fun DeOrder?.format(): String = when (this?.type) {
    null -> "---"
    OrderType.BUY -> "Buy: " + buyVol().thousands()
    OrderType.SELL -> "Sell: " + sellVol().thousands()
    OrderType.NONE -> "Zero"
}


fun DeAuction.order_value_str(o: DeOrder?): String = o?.run {
    // note: o.price will be null before the round price is set!
    // TODO: when round price set: existing orders need to be replaced with new price
    AuFormatter.format_currency(o.price * o.quantity * settings.cost_multiplier)
} ?: "---"


// don't need this:
// fun get_orders(a: DeAuction, r: DeAuction_ROUND): List<DeOrder> = r.get_orders()


/***************
 * De Round
 */

fun DeAuction.penultimate(): DeRound? = rounds.takeIf { it.size > 1 }?.let { it[it.size - 2] }

fun DeAuction.total_sell(r: DeRound? = lastround()): Int = r?.sell_orders()?.sumOf { it.quantity } ?: 0

fun DeAuction.total_buy(r: DeRound? = lastround()): Int = r?.buy_orders()?.sumOf { it.quantity } ?: 0

fun DeAuction.excess_demand(r: DeRound? = lastround()): Int = total_buy(r) - total_sell(r)

fun DeAuction.excess_supply(r: DeRound? = lastround()): Int = total_sell(r) - total_buy(r)


/**************
 * De Trader Round
 */

fun DeRound?.get_rti(t: DeTradingCompany?): DeRoundTraderInfo? =
// NB: THIS SHOULD BE THE ONLY PLACE WHERE WE GET RBIs !!
    if (t == null || this == null) {
        null
    } else {
        this.trader_infos.find { it.de_trading_company.company_id == t.company_id }
    }

//fun Round?.get_rti_by_company(company: Company?): RoundTraderInfo? = when {
//    this == null -> null
//    company == null -> null
//    else -> trader_infos.find { it.company == company }
//}

// TODO: do we need to look up by companp_id?
//fun Round?.get_rti_by_company_id(company_id: String?): RoundTraderInfo? = when {
//    this == null -> null
//    company_id == null -> null
//    else -> trader_infos.find { it.company.id_str() == company_id }
//}

fun DeRound?.get_rti_by_company(company: Company?): DeRoundTraderInfo? =
    when {
    this == null -> null
    company == null -> null
    else -> trader_infos.find { it.de_trading_company.company_id == company.id }
}



fun DeRound.sell_quantity(t: DeTradingCompany): Int =
    get_order(t).takeIf { it.type == OrderType.SELL }?.quantity ?: 0


fun DeRound.buy_quantity(t: DeTradingCompany): Int =
    get_order(t).takeIf { it.type == OrderType.BUY }?.quantity ?: 0


//
//fun set_round_price(ratio:MatchedVolumeRatio) {
//    when (ratio) {
//        MatchedVolumeRatio.UP_TO_10  -> price_level_1
//        MatchedVolumeRatio.UP_TO_20  -> price_level_2
//        MatchedVolumeRatio.UP_TO_30  -> price_level_3
//        MatchedVolumeRatio.UP_TO_40  -> price_level_4
//        MatchedVolumeRatio.UP_TO_50  -> price_level_5
//    }
//}

//fun DeAuction.sell_match(): Int =
//        if (?.is_sell() == true) current_matched_vol
//        else 0
//
//fun DeAuction.Trader_ROUND.buy_match(): Int =
//        if (order?.is_buy() == true) current_matched_vol
//        else 0

/**************
 * De Match
 */

// MOVED TO Order.match_vol(t)

//fun DeAuction.Round.volume_limit(seller: DeAuction.Trader, buyer: DeAuction.Trader): Int =
//    this.limits.find { it.sellerId == seller.company_id && it.buyerId == buyer.company_id }?.limit ?: 0
//
//fun DeOrder?.sell_match_vol(t: DeAuction.Trader): Int =
//    if (this == null || this.isBuy()) 0
//    else this.matches.filter { it.company_id == t.company_id }.sumBy { it.volume }
//


//fun DeAuction.Round.total_matched(): Int = {
// NOT SURE EXACTLY WHAT THIS IS DOING: should it be on all the matches, or just this round??
// this.orders.matches.sumBy { it.match_volume }
//    matches.filter { it.buy_order.round_number == this.number || it.sell_order.round_number == this.number }
//        .sumBy { it.match_volume }
////        if (this == null)
//            0
//        else
//            matches.sumBy { it.match_volume }
// or: matches.map { it.match_volume }.sum()

//fun DeAuction_ROUND?.total_match(t: DeAuction.Trader): Int =
//// NOTE: ASSUMES THAT YOU CAN'T BE A BUYER AND A SELLER IN A ROUND
//        if (this == null)
//            0
//        else
//            matches.filter {
//                it.buy_order.trader == t ||
//                        it.sell_order.trader == t
//            }.sumBy { it.match_volume }
//
//fun DeAuction.Round.buy_matches(o:DeOrder?): List<DeAuction.Match> =
//    when{
//        o == null -> emptyList()
//        o.isSell() -> emptyList()
//        else -> o.matches
//    }
//
//fun DeAuction.Round.sell_matches(o:DeOrder?):List<DeAuction.Match> =
//    when{
//        o == null -> emptyList()
//        o.isBuy() -> emptyList()
//        else -> o.matches
//    }
//
//fun DeAuction.Round.buy_match_vol(o:DeOrder?):Int =
//    buy_matches(o).sumBy { it.volume }
//
//fun DeAuction.Round.sell_match_vol(o:DeOrder?):Int =
//    sell_matches(o).sumBy { it.volume }

//
//fun DeAuction_ROUND.match_potential(seller: DeAuction.Trader, buyer: DeAuction.Trader): DeAuction_ROUND.DeCounterPartyVolume? =
//        this.round_counterparty_limits.find {
//            it.buyer == buyer && it.seller == seller
//        }


/*
fun DeAuction.counter_party_potential_match(
    r: DeAuction.Round,
    limit:
): Int {
    // see similar function in DeMatrixEdgeElement
    val buyer: DeAuction.Trader = get_trader(limit.borrower)
    val seller: DeAuction.Trader = get_trader(limit.lender)

    val buyer_rbi: DeAuction.RoundTraderInfo? = r.get_rbi(buyer)
    val seller_rbi: DeAuction.RoundTraderInfo? = r.get_rbi(seller)

    if (buyer_rbi == null || seller_rbi == null)
        return 0

    if (seller_rbi.max_vol <= 0)
        return 0

    // minimum of seller elig, and counter party volume based on credit:
    return seller_rbi.max_vol.coerceAtMost(
        counterparty_credit_vol_limit(limit, r.price, settings.currency_conversion_rate)
    )

}
*/


//fun DeAuction.calculate_value(r: DeAuction_ROUND?, volume: Int): Double {
//    return if (settings.cost_multiplier && r?.price)
//        volume * settings.cost_multiplier * r!!.price
//    else
//        0.0
//}

//fun DeAuction.is_continuable(): Boolean =
//    lastround()?.let {
//        when (settings.initial_price_direction) {
//            UP -> total_buy(it) > total_sell(it)
//            DOWN -> total_sell(it) > total_buy(it)
//        }
//    } ?: false

//fun DeAuction.actual_activity_percentage(r: Round): Int {
//    val match_vol: Int = r.match_vol()
//    val sell_total: Int = total_sell(r)
//    return if (sell_total == 0)
//        0
//    else
//        (100.0 * match_vol / sell_total).roundToInt()
//}

//fun DeAuction.activity_percentage_formatted_auctioneer(r: Round): String =
//    actual_activity_percentage(r).toString() + "%"
//
//fun DeAuction.activity_percentage_formatted_trader(r: Round): String =
//// TODO: is this implemented already?
//    if (r == lastround() && !this.isClosed())
//        "---"
//    else
//        r.matched_sell_ratio.label

//fun DeAuction.seller_activity(r: Round, for_auctioneer: Boolean): String {
//    val label = settings.volume_units
//    val sell_vol = total_sell(r)
//    return if (r == lastround() && !isClosed())
//        "---"
//    else if (sell_vol > 100)
//        "> 100 $label"
//    else if (sell_vol > 75)
//        "76-100 $label"
//    else if (sell_vol > 50)
//        "51-75 $label"
//    else if (sell_vol > 25)
//        "26-50 $label"
//    else
//        "0-25 $label"
//}


//fun DeAuction.round_feedback(r: Round): String =
//    excess_demand(r).let {
//        when {
//            it > 50 -> "++++"
//            it > 40 -> "++++"
//            it > 30 -> "+++"
//            it > 20 -> "+++"
//            it > 10 -> "++"
//            else -> "+"
//        }
//    }

//fun DeAuction.is_ascending_price(): Boolean = settings.initial_price_direction == UP
//fun DeAuction.is_descending_price(): Boolean = settings.initial_price_direction == DOWN

//fun DeAuction.state_display_label(is_top_line: Boolean): String =
//    when (auctioneer_state) {
////        DeState.AUCTION_INIT ->
////            if (is_top_line) "Waiting to open"
////            else ""
////        DeState.ROUND_READY ->
////            if (is_top_line) "Ready to open round " + this.rounds.size.toString()
////            else ""
////        DeState.ROUND_RUNNING ->
////            if (is_top_line) "Round " + this.rounds.size.toString() + " - ROUND_OPEN"
////            else this.time_remaining_label()
////        DeState.ROUND_PAUSED ->
////            if (is_top_line) "Round " + this.rounds.size.toString() + " - PAUSED"
////            else this.time_remaining_label()
//////         DeState.ROUND_TRIGGERED -> {
//////            throw Exception("ROUND_TRIGGERED NOT IMPLMENTED")
//////            return "Round " + String.valueOf(r!!.round_number) + " open @ " + String.valueOf(app.framework.FormatUtil.invokeMethod("format_round_price", arrayOf<Any>(a, r?.price))) + " " + this.settings.price_label + "\nClock paused at: " + String.valueOf(this.time_remaining) + " seconds" + "\nWAITING FOR ALL ORDERS!"
//////        }
////        DeState.ROUND_CLOSED ->
////            if (is_top_line) "Round " + this.rounds.size.toString() + " AUCTION_CLOSED"
////            else ""
////        DeState.AUCTION_CLOSED ->
////            if (is_top_line) "Auction Closed"
////            else ""
//        else ->
//            throw java.lang.Exception()
//    }

//fun DeAuction.time_remaining_label(): String = "TODO"
////    if (state.oneOf(DeState.ROUND_RUNNING, DeState.ROUND_PAUSED))
////        time_remaining.toString() + " seconds remaining"
////    else ""

