// File: de-price-calculator.kt
package au21.engine.domain.de.services.nextroundprice

import au21.engine.domain.common.model.PriceDirection
import au21.engine.framework.commands.AlertException

/**
 * NEXT ROUND PRICE AND DIRECTION
 */

class DeNextRoundPriceInfo(
    val price: Double,
    val direction: PriceDirection,
    val is_post_price_reversal: Boolean
) {
    companion object {

        // TODO: where is the check than price has not gone below/above a prior price, nor is equal to a prev price

        fun create(
            prev_round_number: Int,
            prev_round_price: Double,
            prev_round_direction: PriceDirection?,
            prev_round_is_post_reversal: Boolean,
            prev_round_total_buy: Int,
            prev_round_total_sell: Int,
            price_change_initial: Double,
            price_change_post_reversal: Double
        ): DeNextRoundPriceInfo {

            val next_round_direction: PriceDirection = when {
                prev_round_total_buy > prev_round_total_sell -> PriceDirection.UP
                prev_round_total_sell > prev_round_total_buy -> PriceDirection.DOWN
                else -> throw AlertException("Cannot create subsequent round if prior round supply and demand are equal!")
            }

            /**
             * first round:
             */

            if (prev_round_number == 1) {
                return DeNextRoundPriceInfo(
                    direction = next_round_direction,
                    is_post_price_reversal = false,
                    price = when (next_round_direction) {
                        PriceDirection.UP ->
                            prev_round_price + price_change_initial
                        PriceDirection.DOWN ->
                            prev_round_price - price_change_initial
                    }
                )
            }

            /**
             * subsequent rounds:
             */

            if(prev_round_direction == null)
                throw AlertException("After first round, previous round direction cannot be null.")

            val price_has_reversed = when {
                    prev_round_is_post_reversal -> true // once you've reversed, then you've always reversed!
                    else -> prev_round_direction != next_round_direction
                }

            val next_round_price: Double = when (next_round_direction) {
                    PriceDirection.UP ->
                        when (price_has_reversed) {
                            false -> prev_round_price + price_change_initial
                            true -> prev_round_price + price_change_post_reversal
                        }
                    PriceDirection.DOWN ->
                        when (price_has_reversed) {
                            false -> prev_round_price - price_change_initial
                            true -> prev_round_price - price_change_post_reversal
                        }
                }

            return DeNextRoundPriceInfo(
                price = next_round_price,
                direction = next_round_direction,
                is_post_price_reversal = price_has_reversed
            )
        }
    }
}
