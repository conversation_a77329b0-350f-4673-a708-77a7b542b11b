// File: errors-send.kt
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.*
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_by_sid
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail_if
import au21.engine.framework.commands.interfaces.IAuctionMessage
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.is_blank

/**
 * Somewhat experimental
 * idea is that for certain commands (eg: DeOrderSubmit),
 * the AlertException.error will be send by the handler back here
 * to be broadcast to auctioneers only.
 */
class ErrorsSendCommand(
    val auction_id: String,
    val trader_session_id: String,
    val error: String
) : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        // TODO: probably we shouldn't fail with these,
        //  but given that there is no session id, it won't be sent to any users anyway!

        val a: Auction = db.auction_or_alert(auction_id)

        fail_if(error.is_blank(), "Error is blank")

        val preamble: String = "Order entry error " + try {
            val trader_session: AuSession? = db.session_by_sid(trader_session_id)
            val trader_user: Person? = trader_session?.user
            val trader_company: Company? = trader_session?.user?.company

            if (trader_user != null && trader_company != null) {
                "from ${trader_user.username} (${trader_company.shortname})"
            } else {
                "from unknown trader"
            }
        } catch (t: Throwable) {
            t.printStackTrace()
            "from unknown trader"
        }

        val m = AuctionMessage(
            message_type_ = AuMessageType.SYSTEM_TO_AUCTIONEER,
            message_ = "$preamble: $error",
            from_user_ = null,
            to_company_ = null
        )
        return ErrorsSendAction(this, db, null, a, m)

    }
}


class ErrorsSendAction(
    override val command: ErrorsSendCommand,
    override val db: AuEntityManager,
    override val session: AuSession?,
    override val auction: Auction,
    override val message: AuctionMessage,
) : EngineAction, IAuctionMessage {
    override fun mutate() {
        auction.messages.add(message)
        db.save(auction)
    }
}
