// File: AuctionMessage.kt
package au21.engine.domain.common.model

import au21.engine.domain.common.model.AuMessageType.*
import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.AuFormatter
import org.joda.time.DateTime
import java.util.*
import javax.persistence.Embeddable

@Embeddable
class AuctionMessage(
    message_type_: AuMessageType,
    message_: String,
    from_user_: PersonProxy? = null,
    from_company_:CompanyProxy? = null,
    to_company_: CompanyProxy? = null
) {

    var message_type: AuMessageType = message_type_
        private set

    var message: String = message_
        private set

    var from_user: PersonProxy? = from_user_
        private set

    var from_company: CompanyProxy? = from_company_
        private set

    var to_company: CompanyProxy? = to_company_
        private set

    init {

        fun check_from_auctioneer() {
            if (from_user == null || from_user!!.role_at_auction_item != AuUserRole.AUCTIONEER)
                throw AlertException("Message type $message_type can only be sent by auctioneers")
        }

        fun check_from_trader() {
            if (from_user == null || from_user!!.role_at_auction_item != AuUserRole.TRADER)
                throw AlertException("Message type $message_type can only be sent by a trader.")
        }

        fun check_to_trader() {
            if (to_company == null)
                throw AlertException("Message type $message_type can only be sent to traders.")
        }

        fun check_fromUser_null() {
            if (from_user != null)
                throw AlertException("Message type $message_type should have blank 'from'.")
        }

        fun check_toCompany_null() {
            if (from_user != null && to_company != null)
                throw AlertException("Message type $message_type should have blank 'to' company.")
        }

        when (message_type) {
            AUCTIONEER_BROADCAST -> {
                check_from_auctioneer()
                check_toCompany_null()
            }
            AUCTIONEER_TO_TRADER -> {
                check_from_auctioneer()
                check_to_trader()
            }
            TRADER_TO_AUCTIONEER -> {
                check_from_trader()
                check_toCompany_null()
            }
            SYSTEM_BROADCAST -> {
                check_fromUser_null()
                check_toCompany_null()
            }
            SYSTEM_TO_TRADER -> {
                check_fromUser_null()
                check_to_trader()
            }
            SYSTEM_TO_AUCTIONEER -> {
                check_fromUser_null()
                check_toCompany_null()
            }
        }
    }

    var body: String = message
        private set

    var message_type_label: String = message_type.toString()
        private set

    // we never persist enums, as the order could change
    fun messageType() = valueOf(message_type_label)

    // LABEL:

    var from_label: String =
        when (messageType()) {
            AUCTIONEER_BROADCAST -> "Auctioneer"
            AUCTIONEER_TO_TRADER -> "Auctioneer"
            // TOOD: check that this is tested above when initialized:
            TRADER_TO_AUCTIONEER -> "${from_user!!.username_at_auction_time} (${from_company!!.shortname_at_auction_time})"
            SYSTEM_BROADCAST -> "System"
            SYSTEM_TO_TRADER -> "System"
            SYSTEM_TO_AUCTIONEER -> "System"
        }
        private set

    var to_label: String =
        when (messageType()) {
            AUCTIONEER_BROADCAST -> "all"
            AUCTIONEER_TO_TRADER -> to_company!!.shortname_at_auction_time
            TRADER_TO_AUCTIONEER -> "auctioneer"
            SYSTEM_BROADCAST -> "all"
            SYSTEM_TO_TRADER -> to_company!!.shortname_at_auction_time
            SYSTEM_TO_AUCTIONEER -> "auctioneer"
        }
        private set

    var timestamp: Date = DateTime().toDate()
        private set
    var timestamp_label: String = AuFormatter.date_time_format(timestamp)
        private set

// var message_type: AuMessageType by lazy { AuMessageType.valueOf(message_type_name) }


}
