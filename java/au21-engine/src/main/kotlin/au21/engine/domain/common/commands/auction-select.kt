// File: auction-select.kt
package au21.engine.domain.common.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.framework.PageName
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail_if
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.database.is_same_entity
import io.quarkus.logging.Log

class AuctionSelectCommand(
    val auction_id: String
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        val a: Auction = db.auction_or_alert(auction_id)

        fail_if(
            !a.show_auction(session),
            "Unable to display auction, because either it was deleted or you are not a trader in this auction."
        )

        return AuctionSelectAction(this, db, session, a)
    }

}


class AuctionSelectAction(
    override val command: AuctionSelectCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val a: Auction
) : EngineAction {

    override fun mutate() {

        if (is_same_entity(session.auction, a)) {
            Log.warn("already on auction, not resending")
            return
        }



        // https://auctionologies4.myjetbrains.com/youtrack/issue/A2019FS-329

//         * To current session:
//
//        (a) SessionUserValue:
//
//        set current auction
//        set current page, ie: changes page

        val page: PageName = when (a) {
            is DeAuction ->
                if (session.is_auctioneer())
                    PageName.DE_AUCTIONEER_PAGE
                else if (session.is_trader())
                    PageName.DE_TRADER_PAGE
                else throw Error("No page for role: " + session.user?.role)
            else -> throw Error("Auction design not handled: " + a::class.java.simpleName)
        }

        if (session.inRole(AuUserRole.TRADER)) {
            a.add_trader_that_has_seen_auction(session)
        }

        session.set_page(page, a)
        db.save(session)
        db.save(a)

        //        (b) MessageElements:
//
//        if auctioneer:
//
//        clear messages for prev auction
//        send all messages

//        (c) AuctionNoticeSave:
//
//        send auction notice for current auction
//        if trader
//
//        all messages sent by auctioneer
//        bid confirmation messages for that trader
//        messages sent by that trader


    }

}
