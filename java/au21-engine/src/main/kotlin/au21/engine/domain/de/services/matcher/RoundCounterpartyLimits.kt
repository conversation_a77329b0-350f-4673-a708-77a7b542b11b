// File: RoundCounterpartyLimits.kt
package au21.engine.domain.de.services.matcher

import au21.engine.domain.de.model.DeRoundTraderInfo
import au21.engine.framework.commands.AlertException

// TODO: should this be persisted??

class RoundCounterpartyLimits(
    val seller_rti: DeRoundTraderInfo,
    val buyer_rti: DeRoundTraderInfo,
    val price_cost_multiplier: Double
) {
    init {
        if (seller_rti.de_trading_company.company_id == buyer_rti.de_trading_company.company_id)
            throw AlertException("Quantity limit between same buyer and seller not allowed.")
    }

    val buy_max: Int = buyer_rti.constraints.max_buy_quantity
    val buy_limit: Int = buyer_rti.order.quantity
    val sell_max: Int = seller_rti.constraints.max_sell_quantity
    val sell_limit: Int = seller_rti.order.quantity

    // null = no limit
    // note: we're getting this from the trader, not the company!!
//    val sell_quantity_cost_limit: Int =
//        seller_rti.de_trading_company.initial_limits.initial_selling_cost_limit get_credit_limit_quantity(price_cost_multiplier)

    val capacity = minOf(buy_limit, sell_limit) // , sell_quantity_cost_limit)

//    val capacity_potential = when (credit_quantity_limit)
//    {
//        null -> minOf(buy_max, sell_max)
//        else -> minOf(buy_max, sell_max, credit_quantity_limit)
//    }

//    val credit_limit_str = seller_rti.tradingCompany.get_credit_limit_quantity_str(price_cost_multiplier)

    companion object {

        // ONLY USED BY THE DeMatcher (other than testing)
        fun calculate_counterparty_capacities(
            round_trader_infos:List<DeRoundTraderInfo>,
            price_cost_multiplier: Double
        ): List<RoundCounterpartyLimits> {

            return round_trader_infos.flatMap { buyer_rti: DeRoundTraderInfo ->
                round_trader_infos.mapNotNull { seller_rti: DeRoundTraderInfo ->
                    when (buyer_rti.de_trading_company.company_id == seller_rti.de_trading_company.company_id) {
                        true -> null
                        false -> RoundCounterpartyLimits(
                            seller_rti = seller_rti,
                            buyer_rti = buyer_rti,
                            price_cost_multiplier = price_cost_multiplier
                        )
                    }
                }
            }
        }

    }
}
