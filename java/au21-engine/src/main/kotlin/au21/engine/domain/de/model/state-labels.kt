// File: state-labels.kt
package au21.engine.domain.de.model


//    override fun toString(): String =
//        "$auction_state_label \n" +
//                "$round_state_label \n" +
//                "$autopilot \n" +
//                "$starting_price \n" +
//                "$starting_price_announcement_mins} \n" +
//                "$starting_time \n" +
//                "${announce_time()} \n" +
//                "${seconds_until(starting_time)} \n" +
//                "-------------------------\n"


//
//
//fun DeAuction._starting_price_label(): String =
//    when (auctioneer_state) {
//        DeAuctioneerState.STARTING_PRICE_NOT_SET -> ""
//        DeAuctioneerState.STARTING_PRICE_SET ->
//            when (settings.starting_price_announcement_mins) {
//                0 -> "Will be announced when the auction starts"
//                1 -> "Will be announced 1 minute before the auction starts"
//                else -> "Will be announced ${settings.starting_price_announcement_mins} before the auction starts"
//                // TODO: probably we could count down to this?
//            }
//        DeAuctioneerState.STARTING_PRICE_ANNOUNCED ->
//            settings.starting_price?.let { " ${AuFormatter.format_currency(it)} ${settings.price_units}" } ?: "---"
//        DeAuctioneerState.ROUND_OPEN_WAITING_FOR_BIDS -> ""
//        DeAuctioneerState.ROUND_OPEN_ALL_BIDS_IN -> ""
//        DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> ""
//        DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> ""
//        DeAuctioneerState.AUCTION_CLOSED -> ""
//    }
//


//fun DeAuction._auction_row_state_display(): String =
//    when (a) {
//        is DeAuction ->
//            a.current_state.let {
//                when (it.auction_state) {
//                    DeAuctioneerState.STARTING_PRICE_NOT_SET ->
//                        auction_starting_time_text(it.starting_time)
//                    DeAuctioneerState.STARTING_PRICE_SET ->
//                        auction_starting_time_text(it.starting_time)
//                    DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> auction_starting_time_text(it.starting_time)
//                    DeAuctioneerState.ROUND_OPEN ->
//                        "Auction round ${a.lastround().number} is open"
//                    DeAuctioneerState.ROUND_CLOSED ->
//                        "Auction round ${a.lastround().number} closed"
//                    DeAuctioneerState.AUCTION_CLOSED ->
//                        "Auction closed"
//                }
//            }
//        else -> "${a::class.java.simpleName} state not implemented"
//    }


