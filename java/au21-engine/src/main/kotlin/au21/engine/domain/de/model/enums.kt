// File: enums.kt
package au21.engine.domain.de.model

import au21.engine.framework.database.IAuctionState

enum class DeRoundOpenState {
    GREEN, ORANGE, RED
}

enum class DeTimeState {
    BEFORE_ANNOUNCE_TIME,
    BEFORE_START_TIME,
    AUCTION_HAS_STARTED
}

enum class DeAuctioneerInfoLevel {
    NORMAL,
    WARNING,
    ERROR
}

enum class DeAuctioneerState : IAuctionState {
    STARTING_PRICE_NOT_SET,
    STARTING_PRICE_SET,
    STARTING_PRICE_ANNOUNCED,
    ROUND_OPEN_ALL_ORDERS_NOT_IN,
    ROUND_OPEN_ALL_ORDERS_IN,
    ROUND_CLOSED_NOT_AWARDABLE,
    ROUND_CLOSED_AWARDABLE,
    AUCTION_CLOSED;
    override fun oneOf(vararg states: IAuctionState): Boolean {
        return states.contains(this)
    }
}

enum class DeCommonState {
    SETUP,
    STARTING_PRICE_ANNOUNCED,
    ROUND_OPEN,
    ROUND_CLOSED,
    AUCTION_CLOSED
}


/**
 * allows us to avoid sending substates to traders
 */
fun DeAuctioneerState.common_state():DeCommonState =
    when (this) {
        DeAuctioneerState.STARTING_PRICE_NOT_SET -> DeCommonState.SETUP
        DeAuctioneerState.STARTING_PRICE_SET -> DeCommonState.SETUP
        DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> DeCommonState.STARTING_PRICE_ANNOUNCED
        DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> DeCommonState.ROUND_OPEN
        DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> DeCommonState.ROUND_OPEN
        DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> DeCommonState.ROUND_CLOSED
        DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> DeCommonState.ROUND_CLOSED
        DeAuctioneerState.AUCTION_CLOSED -> DeCommonState.AUCTION_CLOSED
    }


enum class DeRoundState {
    NOT_OPEN, GREEN, ORANGE, RED
}

enum class DeFlowControlType {
//    ENGAGE_AUTO_PILOT,
//    DISENGAGE_AUTO_PILOT,
    HEARTBEAT,
    SET_STARTING_PRICE,
    ANNOUNCE_STARTING_PRICE,
    START_AUCTION,
    CLOSE_ROUND,
    REOPEN_ROUND, // TODO: do we need this??
    NEXT_ROUND,
    AWARD_AUCTION
}

enum class DeCreditSetMode {
    MANUAL,
    MINIMUM
}

// unclear if we'd use MatchedVolum, or excess Demand
//enum class MatchedVolumeRatio(val label: String) {
//    UP_TO_10("up to 10%"),
//    UP_TO_20("10+ to 20%"),
//    UP_TO_30("20+ to 30%"),
//    UP_TO_40("30+ to 40%"),
//    UP_TO_50("40+ to 50%"),
//    UP_TO_60("50+ to 60%"),
//    UP_TO_70("60+ to 70%"),
//    UP_TO_80("70+ to 80%"),
//    UP_TO_90("80+ to 90%"),
//    UP_TO_100("90+ to 100%"),
//    GE_100("100+%");
//}

//enum class ExcessLevel {
//    NONE,
//    ONE_PLUS,
//    TWO_PLUS,
//    THREE_PLUS,
//    FOUR_PLUS
//}

//enum class ExcessDemand{
//    DEMAND_LEVEL_1,
//    DEMAND_LEVEL_2,
//    DEMAND_LEVEL_3,
//    DEMAND_LEVEL_4;
//  //  DEMAND_LEVEL_5;
//
//    companion object {
//        fun get_excess_level(excess: Int): ExcessDemand {
//            return when {
//         //       excess > 50 -> DEMAND_LEVEL_5   // greater than 50 -> 2.000 cpp
//                excess <= 50 -> DEMAND_LEVEL_4  // 50 or less -> 1.000 cpp
//                excess <= 40 -> DEMAND_LEVEL_3  // 40 or less -> 0.500 cpp
//                excess <= 30 -> DEMAND_LEVEL_2  // 30 or less -> 0.250
//                excess <= 20 -> DEMAND_LEVEL_1  // 20 or less -> 0.125
//                else -> throw Error("excess has no level: $excess")// shouldn't get here
//            }
//        }
//    }
//}
//
