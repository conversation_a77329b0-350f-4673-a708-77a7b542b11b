package com.au21.exp

import org.eclipse.microprofile.graphql.GraphQLApi
import org.eclipse.microprofile.graphql.Mutation
import org.eclipse.microprofile.graphql.Query

@GraphQLApi
class ExampleResource2 {

//    @Inject
//    var context: Context? = null

    @Query
    fun hello(): String {
//        println(context)
        return "hello"
    }

    @Mutation
    fun execute(r: SaveUser): ResultsEnvelope {
        return ResultsEnvelope(
            listOf(
                <PERSON><PERSON><PERSON><PERSON><PERSON>(),
                User<PERSON><PERSON><PERSON>(username = r.username)
            )
        )
    }

    @Mutation
    fun execute(r: SaveCompany): ResultsEnvelope {
        return ResultsEnvelope(
            listOf(
                <PERSON><PERSON><PERSON><PERSON>ult(),
                UserR<PERSON>ult(username = r.shortName)
            )
        )
    }

}
