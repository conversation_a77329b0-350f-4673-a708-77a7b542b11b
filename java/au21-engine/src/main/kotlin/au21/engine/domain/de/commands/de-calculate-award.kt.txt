package au21.engine.domain.de.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.auctioneer_session_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.viewmodel.DeAwardValue
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail_if_not_auctioneer
import au21.engine.framework.database.AuEntityManager

/*
 * TODO: MIGHT NOT NEED THIS IF WE'RE GENERATING SOLUTIONS CLIENT SIDE
 */

class DeCalculateAwardCommand(
    val auction_id:String
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        val de: DeAuction = db.auction_or_alert(auction_id)
        val session = db.auctioneer_session_or_alert(session_id)
        fail_if_not_auctioneer(session)

        return DeCalculateAwardAction(this, db, session, de)
    }
}


class DeCalculateAwardAction(
    override val command: DeCalculateAwardCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de:DeAuction
) : EngineAction {

    val deAwardValue = DeAwardValue.create(de)

    override fun mutate() {

    }

}
