// File: DeControlValidator.kt
package au21.engine.domain.de.services.state

import au21.engine.domain.common.model.AutopilotMode
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeAuctioneerState
import au21.engine.domain.de.model.DeFlowControlType
import au21.engine.domain.de.model.is_awardable
import org.joda.time.DateTime

object DeControlValidator {

    const val MUTATE = "MUTATE"

    /**
     * When the return value is:
     * if null:          no need to continue to mutate
     * else if MUTATE:   can go to mutate (set price needs to validate too, before mutate)
     * else:             return the string as an error (AlertException)
     */

    fun validate(de: DeAuction, command: DeFlowControlType): String? {
        //val n: DeAuction.Round = de.lastround()

        // println("\nvalidate $command")
        //  println("$current")
        val now = DateTime().toDate()

        return when (command) {
//            DeFlowControlType.ENGAGE_AUTO_PILOT ->
//                when (de.autopilot) {
//                    AutopilotMode.ENGAGED -> null
//                    AutopilotMode.DISENGAGED -> when (de.auction_state) {
//                        DeAuctioneerState.STARTING_PRICE_NOT_SET -> "Cannot engage autopilot before starting price set"
//                        DeAuctioneerState.STARTING_PRICE_SET -> MUTATE
//                        DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> MUTATE
//                        DeAuctioneerState.ROUND_OPEN_WAITING_FOR_BIDS -> MUTATE
//                        DeAuctioneerState.ROUND_OPEN_ALL_BIDS_IN -> MUTATE
//                        DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> MUTATE
//                        DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> "Cannot engage autopilot, auction is awardable."
//                        DeAuctioneerState.AUCTION_CLOSED -> "Auction closed"
//                    }
//                }
//            DeFlowControlType.DISENGAGE_AUTO_PILOT ->
//                when (de.autopilot) {
//                    AutopilotMode.ENGAGED -> MUTATE
//                    AutopilotMode.DISENGAGED -> null
//                }
            DeFlowControlType.HEARTBEAT ->
                when (de.autopilot) {
                    AutopilotMode.DISENGAGED -> null
                    AutopilotMode.ENGAGED ->
                        when (de.auctioneer_state) {
                            DeAuctioneerState.STARTING_PRICE_NOT_SET -> null
                            DeAuctioneerState.STARTING_PRICE_SET ->
                                when (now.after(de.announce_time()) && de.firstround().price != null) {
                                    true -> MUTATE
                                    else -> null
                                }
                            DeAuctioneerState.STARTING_PRICE_ANNOUNCED ->
                                when (now.after(de.starting_time) && de.firstround().price != null) {
                                    true -> MUTATE
                                    else -> null
                                }
                            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> null
                            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> null
//                                    when (de.has_all_bids() && de.round_closeable()) {
//                                        true -> MUTATE
//                                        else -> null
//                                    }

                            DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE ->
                                TODO()
//                                    when (de.next_round_openable()) {
//                                        true -> MUTATE
//                                        else -> null
//                                    }

                            DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> null // TODO: check this !

                            // no heartbeat here
                            DeAuctioneerState.AUCTION_CLOSED -> null
                        }
                }
            DeFlowControlType.SET_STARTING_PRICE ->
                when (de.auctioneer_state) {
                    DeAuctioneerState.STARTING_PRICE_NOT_SET -> MUTATE
                    DeAuctioneerState.STARTING_PRICE_SET -> MUTATE
                    DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> MUTATE
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> "Cannot set price after auction started."
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> "Cannot set price after auction started."
                    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> "Cannot set price after auction started."
                    DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> "Cannot set price after auction started."
                    DeAuctioneerState.AUCTION_CLOSED -> "Auction closed."
                }
            DeFlowControlType.ANNOUNCE_STARTING_PRICE ->
                when (de.auctioneer_state) {
                    DeAuctioneerState.STARTING_PRICE_NOT_SET -> "Starting price must be set before you can announce it."
                    DeAuctioneerState.STARTING_PRICE_SET -> MUTATE
                    DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> null
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> "Auction already started."
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> "Auction already started."
                    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> "Auction already started."
                    DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> "Auction already started."
                    DeAuctioneerState.AUCTION_CLOSED -> "Auction closed."
                }
            DeFlowControlType.START_AUCTION ->
                when (de.auctioneer_state) {
                    DeAuctioneerState.STARTING_PRICE_NOT_SET -> "Cannot start auction before starting price announced."
                    DeAuctioneerState.STARTING_PRICE_SET -> "Cannot start auction buferu starting price announced."
                    DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> MUTATE
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> null
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> null
                    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> "Auction already started."
                    DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> "Auction already started."
                    DeAuctioneerState.AUCTION_CLOSED -> "Auction closed."
                }
            DeFlowControlType.CLOSE_ROUND ->
                when (de.auctioneer_state) {
                    DeAuctioneerState.STARTING_PRICE_NOT_SET -> "Auction not started."
                    DeAuctioneerState.STARTING_PRICE_SET -> "Auction not started."
                    DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> "Auction not started."
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> MUTATE
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> MUTATE
                    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> null
                    DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> null
                    DeAuctioneerState.AUCTION_CLOSED -> "Auction closed."
                }
            DeFlowControlType.REOPEN_ROUND -> {
                val msg = "Reset only possible when round is closed."
                return when (de.auctioneer_state) {
                    DeAuctioneerState.STARTING_PRICE_NOT_SET,
                    DeAuctioneerState.STARTING_PRICE_SET,
                    DeAuctioneerState.STARTING_PRICE_ANNOUNCED,
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN,
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> msg
                    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> MUTATE
                    DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> MUTATE
                    DeAuctioneerState.AUCTION_CLOSED -> "Auction closed."
                }
            }
            DeFlowControlType.NEXT_ROUND ->
                if (de.is_awardable())
                    "Auction awardable, cannot continue"
                else when (de.auctioneer_state) {
                    DeAuctioneerState.STARTING_PRICE_NOT_SET -> "Auction not started."
                    DeAuctioneerState.STARTING_PRICE_SET -> "Auction not started."
                    DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> "Auction not started"
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> "Current round still open"
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> "Current round still open"
                    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> MUTATE
                    DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> MUTATE
                    DeAuctioneerState.AUCTION_CLOSED -> "Auction Closed."
                }
            DeFlowControlType.AWARD_AUCTION ->
                when (de.auctioneer_state) {
                    DeAuctioneerState.STARTING_PRICE_NOT_SET -> "Auction not started."
                    DeAuctioneerState.STARTING_PRICE_SET -> "Auction not started."
                    DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> "Auction not started."
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> "Round still open."
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> "Round still open."
                    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> "Auction not awardable" // might want to change this!
                    DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> MUTATE
                    DeAuctioneerState.AUCTION_CLOSED -> "Auction closed."
                }
        }
    }

}
