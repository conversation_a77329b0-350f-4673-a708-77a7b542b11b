// File: Result.kt
package au21.engine.domain.de.exp

interface Result {
    val type: ResultType
}


// results:

enum class ResultType {
    ALERT, OBJECT, ARRAY_ITEM
}

class ResultsEnvelope(
    val results: List<Result> = listOf()
)

class UserResult() : Result {
    override val type = ResultType.OBJECT

    lateinit var username: String

    constructor(
        username: String
    ) : this() {
        this.username = username
    }

}
