package au21.engine.domain.de.viewmodel.commands

import au21.engine.domain.de.model.*
import StoreElement
import au21.engine.framework.utils.thousands

class DeAuctioneerRoundElement(
    a: DeAuction,
    r: DeAuction.Round
) : StoreElement {

    val auction_id: String = a.id_str()

    // TODO: check for bidder blinding !

    // val match_range: DeMatchedSellRatio,
    val round_number: String = r.number.toString()
    val round_price: String = a.format_round_price(r)

    val sell_volume:String = r.sell_orders().sumBy { it.volume }.thousands()
    val buy_volume:String = r.buy_orders().sumBy { it.volume }.thousands()
    val match_volume:String = r.current_matched().thousands()

    val match_range_label: String = a.activity_percentage_formatted_trader(r)
   // val seller_range: String = a.seller_activity(r)
    val round_feedback:String = a.round_feedback(r)

    override val id: String = "ROUND.$round_number"

    companion object {
        fun all(a: DeAuction): List<DeAuctioneerRoundElement> =
            a.rounds.map { r: DeAuction.Round ->
                DeAuctioneerRoundElement(a, r)
            }
    }
}

//        fun draw_de_trader_history_table(
//                to: List<AuSession>,
//                a: DeAuction,
//                t: DeAuction.Trader) {
//
//            // TODO: a) should this be in the result generator, and b) do we need sessions or sids as the parameter
//
//            if (to.isNotEmpty()) { // if no sids, don't need to send messages !
//
//                val sids = to.map { it.session_id }
//
////                ClientCommand.StoreCommand.ClearElements(DeTraderHistoryRowElement::class.java)
//
////                ClientCommand.StoreCommand.SetElements(a.rounds.map { r ->
////                    DeTraderHistoryRowElement(a, r, a.get_order(t, r))
////                })
//            }
//        }
//
//
//        fun update(to: List<AuSession>, a: DeAuction, t: DeAuction.Trader) {
//            // TODO: why do we update pen??
//
//            if (to.isNotEmpty()) {
//                val sids = to.map { it.session_id }
//                val r = a.penultimate() ?: a.lastround()
//
////                ClientCommand.StoreCommand.SetElement(DeTraderHistoryRowElement(a, r, a.get_order(t, r)))
//            }
//        }
//    }
//    }


