// File: DateTimeValue.kt
package au21.engine.domain.common.viewmodel

import au21.engine.framework.client.StoreValue
import org.joda.time.DateTime
import java.util.*

/*
NB: THIS FILE CONVERTS BETWEEN:
- JS: 0-based months
- Java: 1-based months
 */

data class DateTimeValue(
    val year: Int,
    val month: Int,// NB: month is new ZERO based !! (but jvm DateTime is 1 based)
    val day_of_week: Int,
    val day_of_month: Int,
    val hour: Int,
    val minutes: Int,
    val seconds: Int,
) : StoreValue {

    fun toDate(): Date = DateTime(
        year,
        month + 1, // because Joda month is 1-based and DateTimeValue is 0-based
        day_of_month,
        hour,
        minutes,
        seconds
    ).toDate()

    companion object {
        fun create(dt: DateTime): DateTimeValue =
            DateTimeValue(
                year = dt.year,
                month = dt.monthOfYear - 1, // NB: month is new ZERO based !! (but DateTime is 1 based)
                day_of_week = dt.dayOfWeek,
                day_of_month = dt.dayOfMonth,
                hour = dt.hourOfDay,
                minutes = dt.minuteOfHour,
                seconds = dt.secondOfMinute
            )
    }
}
