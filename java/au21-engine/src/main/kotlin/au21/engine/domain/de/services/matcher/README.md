
<PERSON><PERSON><PERSON><PERSON> does the matching in 4 calculations:

### (1) Round Counterparty Capacity:

- The maximim volume that could flow between these two counterparties
- ie: minimum of:
  - a) The counterparty credit volume limit from seller to buyer using current round price.
  - b) The seller volume
  - c) The buyer volume
  
### (2) De Capacity Edge:

- simplified to: [from_seller, to_buyer, capacity]

- NOTE: for sources and sinks, only one of these can be positive

ie:
   
| trader | source -> trader | trader -> sink |
| ------ |------------------|----------------|
| seller | greater than 0   | 0              |
| buyer | 0                | greater than 0 |
| no bid | 0                | 0              |

The number of Capacity Edges should be:   
 
- (number of traders) * (number of traders + 1)

ie:
- number of traders squared,
- minus same buyer/seller page
- plus SOURCE -> trader (sell capacities)
- plus trader -> SINK   (buy capacities)
- = n^2 + n


### (3) De Flow Results:

- max flow results
- all flows (source, sink, traders)
- expressed as (from_seller, to_buyer, flow)


### (4) DeMatches:

- Calculate Matches using above.



---

OLD

--- 


(1) de-maxflow: calculate-max-flow()

This function is side-effect free and domain free.
ie: it just takes in and returns data objects defined in that file.
This is facilitate easier testing.

Parameters:
- list of traders,
- list of constraints,

Returns:
- List of Flow Results (buyer, seller, volume)
  
Assumptions (NB):

a) there is 1 SOURCE node and 1 SINK node
b) that sellers have 0 flow to sink
c) that buyers have 0 flow from source


(2) de-maxflow-actual

- called after every order is submitted
- ASSUMES that volume limits have been set already!  
- takes a round
- creates the nodes and constraints
- calls calculate_max_flow
- returns the FlowResult list

ie: it requires the de domain, 
- but is also side-effect free 
  (expects calling function to do the mutations, ie set the matches)

(3) de-maxflow-potential

- for all combinations, ie: 
  - for ascending price buyers can be sellers (2 ^ buyers)
  - for descending price, sellers can become buyers (2 ^ sellers)
- should be done client or server side?
- should be handled in a separate thread (probably)?
- if handled in another thread, then can't be side-effect btw.
