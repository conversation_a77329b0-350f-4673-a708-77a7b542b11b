found 24 files:
- common/model/Activity.kt
- common/model/AuSession.kt
- common/model/Auction.kt
- common/model/AuctionMessage.kt
- common/model/Company.kt
- common/model/CounterpartyCreditLimit.kt
- common/model/Person.kt
- common/model/enums.kt
- common/model/extensions.kt
- de/model/DeAuction.kt
- de/model/DeAuctionSettings.kt
- de/model/DeAuctionTemplate.kt
- de/model/DeBidConstraints.kt
- de/model/DeInitialLimits.kt
- de/model/DeMatch.kt
- de/model/DeOrder.kt
- de/model/DePriceRule.kt
- de/model/DeRound.kt
- de/model/DeRoundTraderInfo.kt
- de/model/DeTradingCompany.kt
- de/model/enums.kt
- de/model/extensions.kt
- de/model/starting-time-labels.kt
- de/model/state-labels.kt

common/model/Activity.kt
```kt
// File: Activity.kt
package au21.engine.domain.common.model

import au21.engine.framework.database.AuEntity
import org.joda.time.DateTime
import java.util.*
import javax.jdo.annotations.Index
import javax.persistence.Entity

@Entity
class Activity(
    var heading: String,
    var body: MutableMap<String, String> = mutableMapOf(),  // one could be: "json": "<json>"
    var entities: MutableMap<String, AuEntity> = mutableMapOf() // so that we can pass entities
) : AuEntity() {

    @Index
    var timestamp: Date = DateTime().toDate()
        private set
}

```
common/model/AuSession.kt
```kt
// File: AuSession.kt
package au21.engine.domain.common.model

import au21.engine.domain.common.model.AuSession.ClientSocketState.CLOSED
import au21.engine.domain.common.model.AuSession.ClientSocketState.OPENED
import au21.engine.framework.PageName
import au21.engine.framework.database.AuEntity
import au21.engine.framework.utils.toEnumOrError
import org.joda.time.DateTime
import java.util.*
import javax.persistence.Entity

@Entity
class AuSession(
    var session_id: String,
    var created: Date = DateTime().toDate(),
    var browser_name: String? = null,
    var browser_version: String? = null,
    var browser_os: String? = null
) : AuEntity() {

    enum class ClientSocketState {
        OPENED, CLOSED
    }

    enum class SessionTerminationReason {
        BROWSER_UNLOADED,
        COMPANY_DELETED,
        COMPANY_NAME_EDITED,
        FORCED_OFF, // eg if user or company edited
        LOGIN_FROM_ANOTHER_BROWSER,
        SERVER_REBOOT,
        SERVER_SWEPT_STALE_SESSION,
        SIGNED_OFF,
        USER_EDITED,
        USER_DELETED,
    }

//    enum class SessionStatus {
//        CREATED,
//        LOGGED_IN,
//        MANUAL_LOGOUT,
//        USER_DELETED,
//        SOCKET_DISCONNECTED,
//        SWEPT
//        /*
//            ONLINE,
//            AT_RISK,
//            LOGGED_OUT_MANUAL_SIGNOUT,
//            LOGGED_OUT_BROWSER_UNLOADED,
//            LOGGED_OUT_FAYE_DISCONNECT,
//            LOGGED_OUT_BIDDER_WITH_SAME_ID,
//            LOGGED_OUT_SWEPT
//        */
//    }



    var socket_state_label: String = OPENED.toString()
        private set

    var socket_state: ClientSocketState
        get() = socket_state_label.let { ClientSocketState.valueOf(it) }
        set(state) {
            this.socket_state_label = state.toString()
            when (state) {
                OPENED -> this.socket_last_closed = null
                CLOSED -> if (this.socket_last_closed == null)
                    this.socket_last_closed = DateTime().toDate()
            }
        }

    var socket_last_closed: Date? = null
        private set

    fun hasConnectionProblem(): Boolean =
        this.socket_state == CLOSED && !this.isTerminated() // TODO: Wifi Dropout detection via pings?

    var auction: Auction? = null
        private set

    //  @Index
//    var user_id: String? = null
//        private set

    // @Index
//    var company_id: String? = null
//        private set
//    var company_shortname: String? = null
//        private set
//    var company_longname: String? = null
//        private set

//    var role_label: String? = null
//        private set

    var user: Person? = null
        private set

//    var role: AuUserRole? = user?.role
//        get() = role_label?.let { AuUserRole.valueOf(it) }
//        set(r: AuUserRole?) {
//            this.role_label = r?.toString()
//        }


    fun is_trader(): Boolean = user?.isTrader() ?: false
    fun is_auctioneer(): Boolean = user?.isAuctioneer() ?: false

    // DO WE NEED THIS?
    fun inRole(vararg roles: AuUserRole): Boolean = roles.any { it == user?.role }


    var page_label: String = PageName.LOGIN_PAGE.toString()
        private set

    var page: PageName // mustn't be null
        get () = toEnumOrError<PageName>(page_label)
//        get() = enumValueOfWithDefault(page_label, PageName.LOGIN_PAGE)
        set(value) {
            this.page_label = value.toString()
        }

    fun set_page(p: PageName, a: Auction? = null) {
        // if you move off the auction page, then the auction is set to null, this is a change
        page = p
        auction = a
    }

    fun login(p: Person) {
        set_page(PageName.HOME_PAGE)
        user = p
//        if(user?.isTrader() == true && user?.company == null )
//            throw AlertException("all traders must have a company")
    }

// OLD: NOTE if a person changes company, while logged then MUST call this !!
//    fun set_user(p: Person) {
//        user = p
//        role = p.role
//        username = p.username
//        user_id = p.id_str()
//        company_id = p.company?.id_str()
//        company_shortname = p.company?.shortname
//        company_longname = p.company?.longname
//}


    fun is_logged_in(): Boolean = !isTerminated() && user != null

    // NOT NEEDED
    // - when auction deleted, session bounced to homepage
    // - when homepage is set, then auction will be set to null
//    fun remove_auction() {
//        // ie: if auction deleted, or if removed from auction
//        auction = null
//    }

    var last_ping: Date = DateTime().toDate()
        private set

    var termination_time: Date? = null
        private set

    var termination_reason_label: String? = null
        private set

    fun terminate(tr: SessionTerminationReason) {
        auction = null
        termination_time = DateTime().toDate()
        termination_reason_label = tr.toString()
        // TODO: not sure about adding the last two:
        socket_state = CLOSED
        socket_last_closed = DateTime().toDate()
    }

    // for some reason kotlin not happy with nullable enums!
    fun termination_reason(): SessionTerminationReason? =
        termination_reason_label?.let { SessionTerminationReason.valueOf(it) }

    fun isTerminated(): Boolean = termination_reason() != null

    fun ping() {
        last_ping = DateTime().toDate()
    }


}

```
common/model/Auction.kt
```kt
// File: Auction.kt
package au21.engine.domain.common.model

import au21.engine.framework.database.AuEntity
import java.util.*
import javax.jdo.annotations.Index
import javax.persistence.Entity

// no longer abstract, so can find it more easily
@Entity
abstract class Auction(
    var auction_name: String,
) : AuEntity() {

    @Index
    var closed: Boolean = false
        protected set

    @Index
    var hidden: Boolean = false

    var notice: String = ""

    var starting_time: Date? = null

    var auction_has_started: Boolean = false

    var common_state_text: String = ""
    var auctioneer_state_text: String = ""

    abstract fun starting_time_text(): String

    protected var trading_companies: MutableList<CompanyProxy> = mutableListOf()
        private set

    fun has_trader(c: Company?): Boolean =
        when (c) {
            null -> false
            else -> trading_companies.any { it.company_id == c.id }
        }

    fun show_auction(s: AuSession?): Boolean =
        when {
            s == null -> false
            s.user?.role == AuUserRole.AUCTIONEER -> true
            this.hidden -> false
            this.has_trader(s.user?.company) -> true
            else -> false
        }

    var messages: MutableList<AuctionMessage> = mutableListOf()
        private set

    var users_that_have_seen_auction: MutableSet<PersonProxy> = mutableSetOf()
        private set


    var companies_that_have_seen_auction: MutableSet<CompanyProxy> = mutableSetOf()
        private set

    fun add_trader_that_has_seen_auction(s: AuSession) {
        s.user?.let { u: Person ->
            users_that_have_seen_auction.add(PersonProxy(u))
            u.company?.let { c: Company ->
                trading_companies.find { it.company_id == c.id }
                    ?.let {
                        companies_that_have_seen_auction.add(it)
                    }
            }
        }
    }

    // Counterparty credit:
    var counterparty_credit_limits: MutableList<CounterpartyCreditLimit> = mutableListOf()
        private set

    fun get_counterparty_credit_limit(lender: CompanyProxy, borrower: CompanyProxy): CounterpartyCreditLimit? =
        counterparty_credit_limits.find {
            it.lender == lender && it.borrower == borrower
        }

    fun set_counterparty_credit_limit(lender: CompanyProxy, borrower: CompanyProxy, limit: Double?) {
        (get_counterparty_credit_limit(lender, borrower)
            ?: run {
                CounterpartyCreditLimit(lender, borrower).also {
                    counterparty_credit_limits.add(it)
                }
            }).set_credit_limit(limit)
    }

    //     var state : IAuctionState
//        get() = enumValueOf(state_label) // enumValueOrNull<TeAuctionState>(state_string)!!
//        set(value) {
//            state_label = value.toString()
//        }

    // Another way of doing enums in general
//    var state_label: String = DeState.AUCTION_INIT.toString()
//        private set
//
//    var state: DeState
//        get() = DeState.valueOf(state_label)
//        set(it) {
//            this.state_label = it.toString()
//        }


    // 2) state label (ie: state could be open_for_bidding, and label could be "open for 20:00:00" etc
    // abstract var state_label: String


}

```
common/model/AuctionMessage.kt
```kt
// File: AuctionMessage.kt
package au21.engine.domain.common.model

import au21.engine.domain.common.model.AuMessageType.*
import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.AuFormatter
import org.joda.time.DateTime
import java.util.*
import javax.persistence.Embeddable

@Embeddable
class AuctionMessage(
    message_type_: AuMessageType,
    message_: String,
    from_user_: PersonProxy? = null,
    from_company_:CompanyProxy? = null,
    to_company_: CompanyProxy? = null
) {

    var message_type: AuMessageType = message_type_
        private set

    var message: String = message_
        private set

    var from_user: PersonProxy? = from_user_
        private set

    var from_company: CompanyProxy? = from_company_
        private set

    var to_company: CompanyProxy? = to_company_
        private set

    init {

        fun check_from_auctioneer() {
            if (from_user == null || from_user!!.role_at_auction_item != AuUserRole.AUCTIONEER)
                throw AlertException("Message type $message_type can only be sent by auctioneers")
        }

        fun check_from_trader() {
            if (from_user == null || from_user!!.role_at_auction_item != AuUserRole.TRADER)
                throw AlertException("Message type $message_type can only be sent by a trader.")
        }

        fun check_to_trader() {
            if (to_company == null)
                throw AlertException("Message type $message_type can only be sent to traders.")
        }

        fun check_fromUser_null() {
            if (from_user != null)
                throw AlertException("Message type $message_type should have blank 'from'.")
        }

        fun check_toCompany_null() {
            if (from_user != null && to_company != null)
                throw AlertException("Message type $message_type should have blank 'to' company.")
        }

        when (message_type) {
            AUCTIONEER_BROADCAST -> {
                check_from_auctioneer()
                check_toCompany_null()
            }
            AUCTIONEER_TO_TRADER -> {
                check_from_auctioneer()
                check_to_trader()
            }
            TRADER_TO_AUCTIONEER -> {
                check_from_trader()
                check_toCompany_null()
            }
            SYSTEM_BROADCAST -> {
                check_fromUser_null()
                check_toCompany_null()
            }
            SYSTEM_TO_TRADER -> {
                check_fromUser_null()
                check_to_trader()
            }
            SYSTEM_TO_AUCTIONEER -> {
                check_fromUser_null()
                check_toCompany_null()
            }
        }
    }

    var body: String = message
        private set

    var message_type_label: String = message_type.toString()
        private set

    // we never persist enums, as the order could change
    fun messageType() = valueOf(message_type_label)

    // LABEL:

    var from_label: String =
        when (messageType()) {
            AUCTIONEER_BROADCAST -> "Auctioneer"
            AUCTIONEER_TO_TRADER -> "Auctioneer"
            // TOOD: check that this is tested above when initialized:
            TRADER_TO_AUCTIONEER -> "${from_user!!.username_at_auction_time} (${from_company!!.shortname_at_auction_time})"
            SYSTEM_BROADCAST -> "System"
            SYSTEM_TO_TRADER -> "System"
            SYSTEM_TO_AUCTIONEER -> "System"
        }
        private set

    var to_label: String =
        when (messageType()) {
            AUCTIONEER_BROADCAST -> "all"
            AUCTIONEER_TO_TRADER -> to_company!!.shortname_at_auction_time
            TRADER_TO_AUCTIONEER -> "auctioneer"
            SYSTEM_BROADCAST -> "all"
            SYSTEM_TO_TRADER -> to_company!!.shortname_at_auction_time
            SYSTEM_TO_AUCTIONEER -> "auctioneer"
        }
        private set

    var timestamp: Date = DateTime().toDate()
        private set
    var timestamp_label: String = AuFormatter.date_time_format(timestamp)
        private set

// var message_type: AuMessageType by lazy { AuMessageType.valueOf(message_type_name) }


}

```
common/model/Company.kt
```kt
// File: Company.kt
package au21.engine.domain.common.model

import au21.engine.framework.commands.AlertException
import au21.engine.framework.database.AuEntity
import javax.persistence.Embeddable
import javax.persistence.Entity

// rules entites don't reference other entities, are coherent


@Embeddable
abstract class CompanyProxy(c:Company){
    var company_id:Long = c.id
        private set
    var shortname_at_auction_time:String = c.shortname
    var longname_at_auction_time:String = c.longname
}


@Entity
class Company(
    var longname: String,
    var shortname: String
) : AuEntity() {

    init {
        // NB: remember to trim all command inputs !!
        if (shortname.trim() == "")
            throw AlertException("Company short name cannot be blank.")

        if (shortname.trim().length > 10) {
            throw AlertException("Company short name cannot be greater than 10 characters: $shortname")
        }
    }


}

```
common/model/CounterpartyCreditLimit.kt
```kt
// File: CounterpartyCreditLimit.kt
package au21.engine.domain.common.model

import au21.engine.framework.utils.AuFormatter
import javax.persistence.Embeddable

@Embeddable
class CounterpartyCreditLimit(
    lender_: CompanyProxy,
    borrower_: CompanyProxy) {

    companion object {
        const val no_limit = "no limit"
        const val none = "none"
    }

    var lender: CompanyProxy = lender_
        private set

    var borrower: CompanyProxy = borrower_
        private set

    var credit_limit: Double? = null
        private set

    var credit_limit_str: String? = null
        private set

    fun set_credit_limit(limit: Double?) {
        this.credit_limit = limit
        this.credit_limit_str = when (limit) {
            null -> no_limit
            else -> AuFormatter.format_currency(limit)
        }
    }
}

```
common/model/Person.kt
```kt
// File: Person.kt
package au21.engine.domain.common.model


import au21.engine.framework.database.AuEntity
import au21.engine.framework.utils.toEnumOrError
import javax.persistence.Embeddable
import javax.persistence.Entity

@Embeddable
class PersonProxy(p: Person) {
    var person_id: Long = p.id
        private set
    var username_at_auction_time: String = p.username
    var role_at_auction_item:AuUserRole = p.role
}

@Entity
class Person(
    role: AuUserRole,
    var username: String, // if edit this, then must edit the proxy username at auction time for all open auctions
    var password: String,
    var company: Company? = null,
    var email: String = "",
    var isObserver: Boolean = false,
    var isTester: Boolean = false,
    var phone: String = ""
) : AuEntity() {

    var role_label: String = role.toString()
        private set

    var role: AuUserRole // mustn't be null
        get() = toEnumOrError(role_label)
        set(r) {
            role_label = r.toString()
//            if(r == AuUserRole.AUCTIONEER && company == null)
//                throw AlertException("Traders cannot have a null company")
        }

    // TODO: should these be in extensions?:

    fun inRole(vararg roles: AuUserRole): Boolean =
        roles.contains(role)

    fun isTrader(): Boolean = inRole(AuUserRole.TRADER)
    fun isAuctioneer(): Boolean = inRole(AuUserRole.AUCTIONEER)
  //  fun isAdmin(): Boolean = inRole(AuUserRole.ADMIN)

}



```
common/model/enums.kt
```kt
// File: enums.kt
package au21.engine.domain.common.model

//enum class AuctionDesign {
//    BACKHAUL,
//    DOUBLE,
//    MULTIROUND,
//    TRANSPORT_ENGLISH,
//    PIPELINE,
//    TRANSPORT_OPTIMIZATION
//}


//inline fun <reified T> DeAuction.setState(
//        state: T,
//        cb: ((T) -> DeAuction) = { this })
//        where
//        T : Enum<T>,
//        T : IAuctionState {
//    this.state_label = state.toString()
//    cb(state)
//}
//
//inline fun <reified T> DeAuction.getState(): T
//        where
//        T : Enum<T>,
//        T : IAuctionState = enumValueOf(state_label)
//
//inline fun <reified T> DeAuction.inState(vararg states: T): Boolean
//        where
//        T : Enum<T>,
//        T : IAuctionState = states.contains(getState())

inline fun <reified T> String.enumValueOfWithTrace():T
        where
        T : Enum<T> {
    try {
        return enumValueOf(this)
    }
    catch (e:Throwable){
        println("Unable to get ${T::class.simpleName} from $this")
        throw e
    }
}

enum class AutopilotMode {
    DISENGAGED,
    ENGAGED
}

enum class Crud {
    CREATE,
    READ,
    UPDATE,
    DELETE,
    ADD,
    REMOVE,
    CLEAR
}

enum class AuMessageType {
    AUCTIONEER_BROADCAST,  // Auctioneer to all traders
    AUCTIONEER_TO_TRADER,  // Auctioneer to 1 trader (probably should implement as per Zoom, also, should be for 1 Company)
    TRADER_TO_AUCTIONEER,  // 1 Trader to Auctioneer
    SYSTEM_BROADCAST,      // System to all traders and auctioneers
    SYSTEM_TO_TRADER,      // System to 1 trader (or trading Company when implemented)
    SYSTEM_TO_AUCTIONEER   // System to auctioneers
}

enum class AuctionInstruction {
    HIDE, UNHIDE, DELETE
}

enum class AuUserRole {
    //  ADMIN, // NOTE: there are various tests for AUCTIONEER or alert
    AUCTIONEER, // can be observers
    TRADER      // can be testers
}

enum class Operator(var label: String) {
    GT("greater than"),
    GE("greater than or equal to");

    fun check(x: Double, limit: Double): Boolean =
        when (this) {
            GT -> x > limit
            GE -> x >= limit
        }

    fun check(x: Int, limit: Int): Boolean =
        when (this) {
            GT -> x > limit
            GE -> x >= limit
        }
}

enum class OrderType { BUY, SELL, NONE }

enum class OrderSubmissionType { MANUAL, DEFAULT, MANDATORY }

enum class PriceDirection { UP, DOWN }

//    var price_direction: PriceDirection
//        get() = valueOf(price_direction_label)
//        set(value) {
//            price_direction_label = value.name
//        }


enum class ActivityRule { ABSOLUTE, RATIO }


enum class StopMode {
    LT, LE, NONE;

    fun check(x: Int, limit: Int): Boolean =
        when (this) {
            StopMode.LT -> x < limit
            StopMode.LE -> x <= limit
            NONE -> false
        }
}

enum class Visibility { ALL, FIRST_ROUND, ELIGIBILITY }

```
common/model/extensions.kt
```kt
// File: extensions.kt
package au21.engine.domain.common.model

inline fun <reified T : Auction> AuSession.get_auction(): T = this as T

/*
    COMPANY extensions
 */


fun Company?.fullname(): String =
    when (this) {
        null -> ""
        else -> "$longname + ($shortname)"
    }


//fun AuSession?.has_username(username: String) =
//        this?.person?.username.equals(username) ?: false

/*
     USER extensions
 */

fun Person.label(): String =
    this.username + this.company?.let { c -> " (${c.shortname})" }




```
de/model/DeAuction.kt
```kt
// File: DeAuction.kt
package au21.engine.domain.de.model

import au21.engine.domain.common.model.*
import au21.engine.domain.de.services.constraints.calculate_first_round_constraints
import au21.engine.domain.de.services.defaultorders.DeOrderInfo
import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.AuFormatter
import au21.engine.framework.utils.TimeFormatter
import org.joda.time.DateTime
import java.util.*
import javax.persistence.Entity

@Entity
class DeAuction(
    auction_name: String,
    var settings: DeAuctionSettings
) : Auction(auction_name) {

    /**
     *   SEE BOTTOM OF CLASS FOR INITIALIZER (setState)
     */

//    @Index
//    var ticking: Boolean = state.in_state(DeState.ROUND_RUNNING)

    private var auctioneer_state_str: String = DeAuctioneerState.STARTING_PRICE_NOT_SET.toString()
    var auctioneer_state: DeAuctioneerState
        get() = DeAuctioneerState.valueOf(auctioneer_state_str)
        private set(s) {
            auctioneer_state_str = s.toString()
        }

    private var common_state_str: String = DeCommonState.SETUP.toString()
    var common_state: DeCommonState
        get() = common_state_str.enumValueOfWithTrace()
        private set(v) {
            common_state_str = v.toString()
        }

    override fun starting_time_text(): String =
        when (common_state) {
            DeCommonState.SETUP,
            DeCommonState.STARTING_PRICE_ANNOUNCED ->
                starting_time?.let { d: Date ->
                    val starting_time_text = "${AuFormatter.auction_row_date_formatter.format(d)} at " +
                            "${AuFormatter.auction_row_time_formatter.format(d)} "

                    val time_to =
                        TimeFormatter.formatDuration(d).let {
                            // TODO: not sure what this is doing??
                            when (it) {
                                "now" -> "(starting now)"
                                else -> "($it from now)"
                            }
                        }

                    return "$starting_time_text $time_to"
                } ?: ""

            DeCommonState.ROUND_OPEN, DeCommonState.ROUND_CLOSED -> "Auction started"
            DeCommonState.AUCTION_CLOSED -> "Auction closed"
        }


//    var round_state_label: String = DeRoundState.NOT_OPEN.toString()
//        private set
//    var round_state: DeRoundState
//        get() = DeRoundState.valueOf(round_state_label)
//        private set(value) {
//            this.round_state_label = value.toString()
//        }

    fun lastround(): DeRound = rounds.lastOrNull() ?: throw AlertException("Auction must have at least on round.")
    fun firstround(): DeRound = rounds.firstOrNull() ?: throw AlertException("Auction must have at least on round.")

    // TODO: should this be pre-calculated ? Probably some value in leaving it as is so that it can be set by server time
    // TOD: probably want to do the same with starting time, btw
    fun announce_time(): Date? = starting_time?.let { d: Date ->
        DateTime(d)
            .minusMinutes(settings.starting_price_announcement_mins)
            .toDate()
    }

    fun setState(state: DeAuctioneerState) {
        // NB: because last round can't be null, we can't call this before the first round is created!

        val n = lastround()
        closed = state == DeAuctioneerState.AUCTION_CLOSED
        auctioneer_state = state
        auctioneer_state_text = when (state) {
            DeAuctioneerState.STARTING_PRICE_NOT_SET -> "Starting price not set"
            DeAuctioneerState.STARTING_PRICE_SET -> "Starting price set"
            DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> "Starting price announced"
            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN ->
                "Round ${n.number} open, all orders not in." + if (price_has_overshot()) " Price has overshoot" else ""

            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN ->
                "Round ${n.number} open, all orders in." + if (price_has_overshot()) " Price has overshoot" else ""

            DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE ->
                "Round ${n.number} closed, auction not awardable." + if (price_has_overshot()) " Price has overshoot" else ""

            DeAuctioneerState.ROUND_CLOSED_AWARDABLE ->
                "Round ${n.number} closed, auction awardable."

            DeAuctioneerState.AUCTION_CLOSED -> "Auction closed"
        }

        common_state = when (state) {
            DeAuctioneerState.STARTING_PRICE_NOT_SET -> DeCommonState.SETUP
            DeAuctioneerState.STARTING_PRICE_SET -> DeCommonState.SETUP
            DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> DeCommonState.STARTING_PRICE_ANNOUNCED
            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> DeCommonState.ROUND_OPEN
            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> DeCommonState.ROUND_OPEN
            DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> DeCommonState.ROUND_CLOSED
            DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> DeCommonState.ROUND_CLOSED
            DeAuctioneerState.AUCTION_CLOSED -> DeCommonState.AUCTION_CLOSED
        }

        common_state_text = when (common_state) {
            DeCommonState.SETUP -> "Waiting for starting price"
            DeCommonState.STARTING_PRICE_ANNOUNCED -> "Starting price announced"
            DeCommonState.ROUND_OPEN -> "Round ${n.number} open for orders!"
            DeCommonState.ROUND_CLOSED -> "Round ${n.number} closed"
            DeCommonState.AUCTION_CLOSED -> "Auction closed"
        }
    }

    fun starting_price_announced(): Boolean = !auctioneer_state.oneOf(
        DeAuctioneerState.STARTING_PRICE_NOT_SET,
        DeAuctioneerState.STARTING_PRICE_SET
    )

    var autopilot_label: String = AutopilotMode.DISENGAGED.toString()
        private set
    var autopilot: AutopilotMode
        get() = AutopilotMode.valueOf(autopilot_label)
        set(value) {
            this.autopilot_label = value.toString()
        }


    // TODO: set in parent
    fun auction_has_started(): Boolean =
        last_round_has_started() || this.rounds.size > 1


    fun last_round_has_started(): Boolean =
        this.auctioneer_state.oneOf(
            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN,
            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN,
            DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE,
            DeAuctioneerState.ROUND_CLOSED_AWARDABLE,
            DeAuctioneerState.AUCTION_CLOSED
        )

    fun create_trader(c: Company): DeTradingCompany {
        // TODO: make sure not already added!
        // note: for tests make sure you have an id!

        if (!is_before_end_of_first_round())
            throw AlertException("Cannot add traders after the first round is closed.")

        if (trading_companies.any { it.company_id == c.id })
            throw Error("Trader already exists with name: ${c.shortname}")

        val t = DeTradingCompany(c).also {
            trading_companies.add(it)
        }

        create_first_round_rti(t)

        return t
    }

    // ASSUMES THAT TRADER DOESN'T HAVE A NON_ZERO BID:
    fun remove_trader(c: Company) {
        de_trading_companies.removeIf { it.company_id == c.id }
        rounds.forEach { r: DeRound ->
            r.trader_infos.removeIf { it.de_trading_company.company_id == c.id }
        }
        trading_companies.removeIf { it.company_id == c.id }
    }


    /**
     *  use this for:
     *  1) traders added
     *  2) starting price set:
     */

    private fun create_first_round_rti(t: DeTradingCompany) {
        // called:
        // a) when adding a trader to an auction
        // b) when editing the trader's initial constraints

        val n = lastround()

        if (n.number != 1)
            throw AlertException("Cannot create first round infos after the first round.")

        // remove if existing:

        n.apply {
            get_rti(t)?.let {
                trader_infos.remove(it)
            }
        }


        // 3: create rti with constraints, and default order
        val constraints = calculate_first_round_constraints(t,  settings, n)
        val default_order = DeOrderInfo.create_default_from_constraints(constraints)

        val rti = DeRoundTraderInfo(
            trading_company_ = t,
            constraints_ = constraints,
            default_order = DeOrder(
                round = n,
                trading_company_ = t,
                u = null,
                order_info = default_order,
                cost_multiplier = settings.cost_multiplier,
                prev_order = null,
            ),
            current_matched_vol = 0,
            fully_opposed_match_vol = 0
        )

        n.trader_infos.add(rti)
    }

    fun set_starting_price(price: Double) {
        if (lastround().number != 1)
            throw AlertException("Cannot create first round infos after the first round.")

        firstround().price = price

        // need to re-create the first round rti's with the new price!
        de_trading_companies.forEach { create_first_round_rti(it) }
    }

//    var time_remaining: Int = settings.first_round_duration
//        private set

//    fun set_time_remaining() {
//        time_remaining = when (lastround().number) {
//            1 -> settings.first_round_duration
//            else -> settings.following_round_duration
//        }
//    }

//    fun decrement_time_remaining() {
//        time_remaining--
//    }

    var price_decimal_places: Int = 3
    var awarded_round: DeRound? = null

    var revised_orders: MutableList<DeOrder> = mutableListOf()
        private set

    var rounds: MutableList<DeRound> = mutableListOf()
        private set

    // TODO: probably a better way of checking this?
    @Suppress("UNCHECKED_CAST")
    var de_trading_companies: MutableList<DeTradingCompany> = mutableListOf()
        get() = trading_companies as MutableList<DeTradingCompany>
        private set


//    fun reset_round() {
//        // TODO: what if there is no last round?
//        lastround()?.let { n ->
//            n.orders.clear()
//            n.trader_infos.clear()
//            traders.forEach { create_rti(n, it) }
//            //set_time_remaining()
//        }
//    }

    fun set_rank(t: DeTradingCompany) {
        // set rank if none:
        if (t.rank == null) {
            // first will be 1
            t.rank = 1 + de_trading_companies.filter { it.rank != null }.size
        }
    }

    // TODO: MOVE THIS OUT OF HERE
    fun create_manual_order(
        r: DeRound,
        t: DeTradingCompany,
        u: PersonProxy,
        order_type: OrderType,
        order_quantity: Int
    ): DeOrder {
        val rti = r.get_rti(t)
            ?: throw AlertException("expected rti for trader: ${t.shortname_at_auction_time}")

//        rti.has_bid_explicitly = true

        set_rank(t) // TODO: do we still need this?

        return DeOrder(
            round = r,
            trading_company_ = t,
            u = u,
            order_info = DeOrderInfo(
                OrderSubmissionType.MANUAL,
                order_type,
                order_quantity
            ),
            cost_multiplier = settings.cost_multiplier,
            prev_order = rti.order
        ).also {
            rti.order = it
        }
    }

    // Do it from
    fun set_credit() {
        // TODO
//        de_trading_companies.forEach { dCompany ->
//            val borrowerCreditLimits = counterparty_credit_limits
//                .asSequence()
//                .filter { it.borrower.company_id == dCompany.company_id }
//                .toList()
//            val credit_limit = borrowerCreditLimits.minByOrNull { it.credit_limit ?: 0.0 }?.credit_limit ?: 0.0
//            dCompany.initial_buying_cost_limit = credit_limit
//        }
    }

    init {
        rounds.add(DeRound(number = 1, price = null, has_reversed_ = false, direction = null)) // no direction
        setState(DeAuctioneerState.STARTING_PRICE_NOT_SET)
    }

}


/*
    Convention: June 24, 2019:
    - collections are only on the root DeAuction // OLD
    - other components embed var constructor pointers
    - lookup is therefore by the root
    - creation is checked by the root
    - extensions are only on the root
    - separate View reports will be used to create json

    Convention: Jan 28, 2021
    - using pattern more convenient for potential move to RavenDB, Documents (Aggregates) should be:
        - Independent:
          - have a separate identity (ie: an Entity)
        - Isolated:
          - a document is changed in a transaction, independent from other documents!!
        - Coherent:
          - a document should be legible on it's own.
    - therefore trying to make sure that documents don't reference other documents.
    - so, for exampl, Traders, will be referenced by short name, and users by username
    - this means that trader names cannot be changed for open auctions, etc. !!

 */


// 1) State: note: the state is set via the enum, but as a string

//     var state : IAuctionState
//        get() = enumvalueOf(state_label) // enumvarueOrNull<TeAuctionState>(state_string)!!
//        set(varue) {
//            state_label = varue.toString()
//        }
//
//    inline fun <reified T> getState(): T
//            where
//            T : Enum<T>,
//            T : IAuctionState = enumvalueOf(state_string)
//
//    inline fun <reified T> inState(vararg states: T): Boolean
//            where
//            T : Enum<T>,
//            T : IAuctionState = states.contains(getState())
//
//    fun <T> setState(state: T) where
//            T : Enum<T>,
//            T : IAuctionState {
//        this.state_string = state.toString()
//    }
//
//    fun setState(state: DeState) {
//        super.setState(state)
//        closed = state.in_state(DeState.AUCTION_CLOSED)
//        ticking = state.in_state(DeState.ROUND_RUNNING) // I think this is the only one?
//    }

```
de/model/DeAuctionSettings.kt
```kt
// File: DeAuctionSettings.kt
package au21.engine.domain.de.model

import javax.persistence.Embeddable

@Embeddable
class DeAuctionSettings(

    var use_counterparty_credits: Boolean,

    // these must'nt be null:
    // var starting_time: Date,
    var starting_price_announcement_mins: Int = 0,

    var round_open_min_secs: Int,
    var round_closed_min_secs: Int,
    var round_orange_secs: Int,
    var round_red_secs: Int,

//  var show_current_round_in_history: Boolean = false, // so that BWP can have it the way they like it
    //  var first_round_duration: Int,
    //  var following_round_duration: Int,

    var cost_multiplier: Double = 10_000.0, // ie: currency = price_units x volume_units x currency_conversion_rateø

    // VOLUME:
    var quantity_units: String = "MMlb",
    var quantity_minimum: Int = 1,
    var quantity_step: Int = 1,

    // PRICE:
    var price_units: String = "cpp",
    var price_decimal_places: Int = 3,

    // var starting_price: Double, // probably not needed, can set on first round
    var price_rule: DePriceRule
)

```
de/model/DeAuctionTemplate.kt
```kt
// File: DeAuctionTemplate.kt
package au21.engine.domain.de.model

import au21.engine.framework.database.AuEntity
import javax.persistence.Entity

@Entity
class DeAuctionTemplate(
        var template_name: String,
        var settings: DeAuctionSettings
) : AuEntity()

```
de/model/DeBidConstraints.kt
```kt
// File: DeBidConstraints.kt
package au21.engine.domain.de.model

import javax.persistence.Embeddable


@Embeddable
class DeBidConstraints(
    var max_buy_quantity: Int,
    var min_buy_quantity: Int,
    var min_sell_quantity: Int,
    var max_sell_quantity: Int
)

```
de/model/DeInitialLimits.kt
```kt
// File: DeInitialLimits.kt
package au21.engine.domain.de.model

import au21.engine.framework.utils.AuFormatter
import au21.engine.framework.utils.thousands
import javax.persistence.Embeddable

/*
  ie: limits before the auction starts, ie: ignoring round price
  - round constraints will be checked against this.
 */
@Embeddable
class DeInitialLimits {
    // FOR NOW JUST USING BUYING COST LIMIT AND SELLING QUANTITY LIMIT
    var initial_buying_cost_limit = 50_000_000.0
        set(value) {
            field = value
            initial_buying_cost_limit_str = AuFormatter.format_currency(field)
        }

    // removing for now, not strictly needed, might add later
//    var initial_selling_cost_limit = 0.0
//        set(value) {
//            field = value
//            initial_selling_cost_limit_str = AuFormatter.format_currency(field)
//        }
    // removing for now, not strictly needed, might add later
//    var initial_buying_quantity_limit = 0
//        set(value) {
//            field = value
//            initial_buying_quantity_limit_str = field.thousands()
//        }

    var initial_selling_quantity_limit = 50
        set(value) {
            field = value
            initial_selling_quantity_limit_str = field.thousands()
        }


    var initial_buying_cost_limit_str:String = ""
   // var initial_selling_cost_limit_str:String = ""
   // var initial_buying_quantity_limit_str:String = ""
    var initial_selling_quantity_limit_str:String = ""

}

```
de/model/DeMatch.kt
```kt
// File: DeMatch.kt
package au21.engine.domain.de.model

import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.AuFormatter
import javax.persistence.Embeddable


@Embeddable
class DeMatch(
    //round: Round,
    sell_order_: DeOrder,
    buy_order_: DeOrder,
    match_: Int,
    capacity_: Int,
    round_price_multiplier: Double, // ie: cost_multiplier * round_price
) {

    init {
        // TODO: we seem to have this check in so many places!
        if (sell_order_.trading_company.company_id == buy_order_.trading_company.company_id)
            throw AlertException(
                "Buyer and seller cannot be the same: " +
                        sell_order_.trading_company.shortname_at_auction_time
            )
    }

    var sell_order: DeOrder = sell_order_
        private set

    var buy_order: DeOrder = buy_order_
        private set

    var buyer_shortname = buy_order.trading_company.shortname_at_auction_time
        private set

    var seller_shortname = sell_order.trading_company.shortname_at_auction_time
        private set

    // TODO: maybe these 3 point back to the initial limits object?
    var selling_quantity_limit: Int =
        sell_order.trading_company.initial_limits.initial_selling_quantity_limit
        private set

    var credit:Double? = sell_order.trading_company.initial_limits.initial_buying_cost_limit
        private set

    var credit_str:String = sell_order.trading_company.initial_limits.initial_buying_cost_limit_str
        private set

    var buy_limit: Int = buy_order.quantity
        private set

    var sell_limit: Int = sell_order.quantity

    // ie: the min of credit_volume_limit, buyer order vol, seller order vol:
    var capacity: Int = capacity_
        private set

    var match: Int = match_
        private set

    var value: Double = match * round_price_multiplier
        private set

    var value_str: String = AuFormatter.format_currency(value)
        private set
}

```
de/model/DeOrder.kt
```kt
// File: DeOrder.kt
package au21.engine.domain.de.model

import au21.engine.domain.common.model.OrderSubmissionType
import au21.engine.domain.common.model.OrderType
import au21.engine.domain.common.model.PersonProxy
import au21.engine.domain.de.services.defaultorders.DeOrderInfo
import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.AuFormatter
import org.joda.time.DateTime
import java.util.*
import javax.persistence.Embeddable

@Embeddable
class DeOrder(
    round: DeRound,
    trading_company_: DeTradingCompany,
    u: PersonProxy?,
    order_info: DeOrderInfo,
    cost_multiplier: Double,
    prev_order: DeOrder?
) {

    init {
        // TODO: pretty sure this is handled in numerous other places !
        order_info.apply {
            if (order_type != OrderType.NONE && quantity == 0) {
                throw AlertException("Order type cannot be $order_type if quantity is zero")
            }
            if (order_type == OrderType.NONE && quantity != 0) {
                throw AlertException("Order type cannot be NONE if quantity is not zero")
            }
        }
        if (order_info.quantity < 0) {
            throw AlertException("Order quantity cannot be negative")
        }
    }

    var user: PersonProxy? = u
        private set

    var trading_company: DeTradingCompany = trading_company_
        private set

    var timestamp: Date = DateTime().toDate() // used so that can mock
        private set

    var price: Double = round.price ?: 0.0
        private set

    var round_number: Int = round.number
        private set


    // NB: if you have an implied buy, then you are a buyer,
    // and will be allocated in n or pen, even if no incremental bid

    var submission_type_label: String = order_info.submission_type.toString()

    var submission_type: OrderSubmissionType = order_info.submission_type
        get() = OrderSubmissionType.valueOf(submission_type_label)
        private set

    var order_type_label: String = order_info.order_type.toString()
        private set

    var type: OrderType = order_info.order_type
        get() = OrderType.valueOf(order_type_label)
        private set

    var quantity: Int = order_info.quantity
        private set

    fun sellVol() = if (type == OrderType.SELL) quantity else 0
    fun buyVol() = if (type == OrderType.BUY) quantity else 0

    var prev_order: DeOrder? = prev_order
        private set

    var cost:Double = price * cost_multiplier * quantity
        private set

    var cost_str: String = AuFormatter.format_currency(cost)
        private set
}

```
de/model/DePriceRule.kt
```kt
// File: DePriceRule.kt
package au21.engine.domain.de.model

import au21.engine.domain.common.model.AuUserRole
import javax.persistence.Embeddable


@Embeddable
class DePriceRule(
    var price_change_initial: Double = 0.5,
    var price_change_post_reversal: Double = 0.125,
    var excess_level_1_quantity: Int = 10,
    var excess_level_2_quantity: Int = 20,
    var excess_level_3_quantity: Int = 30,
    var excess_level_4_quantity: Int = 40,
    var excess_level_0_label: String = "1+",
    var excess_level_1_label: String = "2+",
    var excess_level_2_label: String = "3+",
    var excess_level_3_label: String = "4+",
    var excess_level_4_label: String = "5+",
) {

    // NOTE: Can be either excess demand (UP), or excess supply (DOWN):

    fun get_excess_level(excess: Int, role: AuUserRole): String =
        when {
            excess > excess_level_4_quantity -> excess_level_4_label
            excess > excess_level_3_quantity -> excess_level_3_label
            excess > excess_level_2_quantity -> excess_level_2_label
            excess > excess_level_1_quantity -> excess_level_1_label
            excess > 0 -> excess_level_0_label
            excess == 0 ->
                if (role == AuUserRole.AUCTIONEER) {
                    "0"
                } else {
                    excess_level_0_label
                }
            else ->
                if (role == AuUserRole.AUCTIONEER) {
                    "-"
                } else {
                    excess_level_0_label
                }
        }

//    fun get_excess_label(excess: Int): String =
//        when {
//            excess <= excess_level_1_volume -> excess_level_0_label
//            excess <= excess_level_2_volume -> excess_level_1_label
//            excess <= excess_level_3_volume -> excess_level_2_label
//            excess <= excess_level_4_volume -> excess_level_3_label
//            else -> excess_level_4_label
//        }

//    fun get_price_change_for_excess_level(level: ExcessLevel): Double =
//        when (level) {
//            ExcessLevel.NONE -> 0.0
//            ExcessLevel.ONE_PLUS -> price_level_1
//            ExcessLevel.TWO_PLUS -> price_level_2
//            ExcessLevel.THREE_PLUS -> price_level_3
//            ExcessLevel.FOUR_PLUS -> price_level_4
//        }
}

```
de/model/DeRound.kt
```kt
// File: DeRound.kt
package au21.engine.domain.de.model

import au21.engine.domain.common.model.OrderType
import au21.engine.domain.common.model.PriceDirection
import au21.engine.framework.commands.AlertException
import org.joda.time.DateTime
import java.util.*
import javax.persistence.Embeddable


@Embeddable
class DeRound(
    var number: Int,
    var price: Double?,
    has_reversed_: Boolean,
    direction: PriceDirection? // will be null on the first round
) {

    init {
        if (number == 0)
            throw AlertException("Rounds must always have a number > 0")
        if (number > 1 && price == null)
            throw AlertException("After round 1, rounds must be created with a non-null price.")
    }

    var has_reversed: Boolean = has_reversed_
        private set

    var price_direction_label: String? = direction?.toString()
        private set
    var price_direction: PriceDirection?
        get() = price_direction_label?.let { PriceDirection.valueOf(it) }
        set(varue) {
            this.price_direction_label = varue.toString()
        }

    var open_time: Date? = null
        private set

    fun open() {
        if (this.open_time != null)
            throw AlertException("open_time should be null")
        open_time = DateTime().toDate()
    }

    var closed_time: Date? = null
        private set

    fun close() {
        if (this.closed_time != null)
            throw AlertException("closed time should be null")
        closed_time = DateTime().toDate()
    }

    var time_started: Date? = null
        private set

    fun started() {
        time_started = DateTime().toDate()
    }

    var trader_infos: MutableList<DeRoundTraderInfo> = mutableListOf()
        private set

    // not sure we need to keep these, seem to be only used as part of flow calc?
//        var limits: MutableList<VolumeLimit> = mutableListOf()
//            private set

    fun buy_orders(): List<DeOrder> =
        trader_infos
            .map { it.order }
            .filter { it.type == OrderType.BUY }

    fun sell_orders(): List<DeOrder> =
        trader_infos
            .map { it.order }
            .filter { it.type == OrderType.SELL }

    fun buyers(): List<DeTradingCompany> = buy_orders().map { it.trading_company }
    fun sellers(): List<DeTradingCompany> = sell_orders().map { it.trading_company }

    var matches: MutableList<DeMatch> = mutableListOf()
        private set

    fun setMatches(m: List<DeMatch>) {
        matches.clear()
        m.forEach { matches.add(it) }
    }

    fun getMatch(t: DeTradingCompany): DeMatch? =
        matches.find {
            it.buyer_shortname == t.shortname_at_auction_time ||
                    it.seller_shortname == t.shortname_at_auction_time
        }

    fun match_vol(): Int = matches.sumOf { it.match }
    fun match_vol(t: DeTradingCompany): Int = matches
        // TODO: t.company_id should be either buy or sell but not both!
        .filter {
            it.buy_order.trading_company.company_id == t.company_id
                    || it.sell_order.trading_company.company_id == t.company_id
        } // it's an alertException if they are equal !!
        .sumOf { it.match }

    fun all_orders_are_non_default(): Boolean =
        trader_infos.all { it.has_non_default_bid() }

    fun round_open_seconds(): Int = 0 // we'll tick this

    var max_potential_flow: Int = 0
        private set

    // TODO: reimplement
//        fun setMaxPotentialFlow(mfr: MaxFlowResult) { // from an optimization engine
//            this.max_potential_flow = mfr.max_flow
//        }

//        var match_ratio_label: String = MatchedVolumeRatio.UP_TO_10.toString()
//            private set
//
//        var matched_sell_ratio: MatchedVolumeRatio
//            get() = enumvalueOfWithDefault(match_ratio_label, MatchedVolumeRatio.UP_TO_10)
//            set(it) {
//                this.match_ratio_label = it.toString()
//            }

}

```
de/model/DeRoundTraderInfo.kt
```kt
// File: DeRoundTraderInfo.kt
package au21.engine.domain.de.model

import au21.engine.domain.common.model.OrderSubmissionType
import au21.engine.domain.common.model.OrderType
import javax.persistence.Embeddable

@Embeddable
class DeRoundTraderInfo(
    constraints_: DeBidConstraints,
    trading_company_: DeTradingCompany,
    var default_order: DeOrder,
    var current_matched_vol: Int,
    var fully_opposed_match_vol: Int
) {

    var de_trading_company: DeTradingCompany = trading_company_
        private set

//        var company_id: String = trader.company_id
//            private set

    var constraints: DeBidConstraints = constraints_
        private set

    var order: DeOrder = default_order
//            set(o: Order) {
//                field = o
////                if (o.submission_type == OrderSubmissionType.MANDATORY)
////                    has_bid_explicitly = true
//            }

    var bid_while_closed: Boolean = false

    fun has_non_default_bid(): Boolean =
        when (order.submission_type) {
            OrderSubmissionType.DEFAULT -> false
            OrderSubmissionType.MANUAL -> true
            OrderSubmissionType.MANDATORY -> true
        }

    fun has_non_zero_bid(): Boolean =
        order.type != OrderType.NONE ||
                order.quantity != 0 // not sure this can happen

}

```
de/model/DeTradingCompany.kt
```kt
// File: DeTradingCompany.kt
package au21.engine.domain.de.model

import au21.engine.domain.common.model.Company
import au21.engine.domain.common.model.CompanyProxy
import au21.engine.domain.common.model.OrderType
import javax.persistence.Embeddable

@Embeddable
class DeTradingCompany(c: Company) : CompanyProxy(c){

    var initial_limits:DeInitialLimits = DeInitialLimits()
        private set

    // Set whenever an order is submitted, and when round is created assuming we alllow that to happen:
    var next_round_order_will_be_mandatory:Boolean = false

    // null means 'no limit'
    var awarded_quantity: Int = 0
        private set

    var awarded_order_type: OrderType = OrderType.NONE
        private set

    fun award(order_type: OrderType, quantity: Int) {
        awarded_order_type = order_type
        awarded_quantity = quantity
    }

    var blinded: Boolean = false

    var rank: Int? = null
}

```
de/model/enums.kt
```kt
// File: enums.kt
package au21.engine.domain.de.model

import au21.engine.framework.database.IAuctionState

enum class DeRoundOpenState {
    GREEN, ORANGE, RED
}

enum class DeTimeState {
    BEFORE_ANNOUNCE_TIME,
    BEFORE_START_TIME,
    AUCTION_HAS_STARTED
}

enum class DeAuctioneerInfoLevel {
    NORMAL,
    WARNING,
    ERROR
}

enum class DeAuctioneerState : IAuctionState {
    STARTING_PRICE_NOT_SET,
    STARTING_PRICE_SET,
    STARTING_PRICE_ANNOUNCED,
    ROUND_OPEN_ALL_ORDERS_NOT_IN,
    ROUND_OPEN_ALL_ORDERS_IN,
    ROUND_CLOSED_NOT_AWARDABLE,
    ROUND_CLOSED_AWARDABLE,
    AUCTION_CLOSED;
    override fun oneOf(vararg states: IAuctionState): Boolean {
        return states.contains(this)
    }
}

enum class DeCommonState {
    SETUP,
    STARTING_PRICE_ANNOUNCED,
    ROUND_OPEN,
    ROUND_CLOSED,
    AUCTION_CLOSED
}


/**
 * allows us to avoid sending substates to traders
 */
fun DeAuctioneerState.common_state():DeCommonState =
    when (this) {
        DeAuctioneerState.STARTING_PRICE_NOT_SET -> DeCommonState.SETUP
        DeAuctioneerState.STARTING_PRICE_SET -> DeCommonState.SETUP
        DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> DeCommonState.STARTING_PRICE_ANNOUNCED
        DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> DeCommonState.ROUND_OPEN
        DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> DeCommonState.ROUND_OPEN
        DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> DeCommonState.ROUND_CLOSED
        DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> DeCommonState.ROUND_CLOSED
        DeAuctioneerState.AUCTION_CLOSED -> DeCommonState.AUCTION_CLOSED
    }


enum class DeRoundState {
    NOT_OPEN, GREEN, ORANGE, RED
}

enum class DeFlowControlType {
//    ENGAGE_AUTO_PILOT,
//    DISENGAGE_AUTO_PILOT,
    HEARTBEAT,
    SET_STARTING_PRICE,
    ANNOUNCE_STARTING_PRICE,
    START_AUCTION,
    CLOSE_ROUND,
    REOPEN_ROUND, // TODO: do we need this??
    NEXT_ROUND,
    AWARD_AUCTION
}

enum class DeCreditSetMode {
    MANUAL,
    MINIMUM
}

// unclear if we'd use MatchedVolum, or excess Demand
//enum class MatchedVolumeRatio(val label: String) {
//    UP_TO_10("up to 10%"),
//    UP_TO_20("10+ to 20%"),
//    UP_TO_30("20+ to 30%"),
//    UP_TO_40("30+ to 40%"),
//    UP_TO_50("40+ to 50%"),
//    UP_TO_60("50+ to 60%"),
//    UP_TO_70("60+ to 70%"),
//    UP_TO_80("70+ to 80%"),
//    UP_TO_90("80+ to 90%"),
//    UP_TO_100("90+ to 100%"),
//    GE_100("100+%");
//}

//enum class ExcessLevel {
//    NONE,
//    ONE_PLUS,
//    TWO_PLUS,
//    THREE_PLUS,
//    FOUR_PLUS
//}

//enum class ExcessDemand{
//    DEMAND_LEVEL_1,
//    DEMAND_LEVEL_2,
//    DEMAND_LEVEL_3,
//    DEMAND_LEVEL_4;
//  //  DEMAND_LEVEL_5;
//
//    companion object {
//        fun get_excess_level(excess: Int): ExcessDemand {
//            return when {
//         //       excess > 50 -> DEMAND_LEVEL_5   // greater than 50 -> 2.000 cpp
//                excess <= 50 -> DEMAND_LEVEL_4  // 50 or less -> 1.000 cpp
//                excess <= 40 -> DEMAND_LEVEL_3  // 40 or less -> 0.500 cpp
//                excess <= 30 -> DEMAND_LEVEL_2  // 30 or less -> 0.250
//                excess <= 20 -> DEMAND_LEVEL_1  // 20 or less -> 0.125
//                else -> throw Error("excess has no level: $excess")// shouldn't get here
//            }
//        }
//    }
//}
//

```
de/model/extensions.kt
```kt
// File: extensions.kt
package au21.engine.domain.de.model

import au21.engine.domain.common.model.*
import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.AuFormatter
import au21.engine.framework.utils.thousands
import org.joda.time.DateTime
import kotlin.math.abs


/********************
 * De AUCTION
 */


// can be used during a round:
fun DeAuction.price_has_overshot(): Boolean =
    lastround().let { n ->
        when (n.price_direction) {
            null -> false // ie: first round
            PriceDirection.UP -> excess_supply(n) < 0
            PriceDirection.DOWN -> excess_demand(n) < 0
        }
    }

fun DeAuction.current_price_direction(): PriceDirection? = lastround().let { n: DeRound ->
    if (n.number == 1)
        return null
    penultimate()?.let { pen: DeRound ->
        // NOTE: assumes that after the first round, round prices cannot be null
        if (n.price!! > pen.price!!) PriceDirection.UP
        else if (pen.price!! > n.price!!) PriceDirection.DOWN
        else throw Error("subsequent rounds can't have the same price")
    }
}

fun DeAuction.time_state(): DeTimeState? = DateTime().toDate().let { now ->
    announce_time()?.let {
        if (now.before(it))
            return DeTimeState.BEFORE_ANNOUNCE_TIME
    }
    starting_time?.let {
        return if (now.before(it))
            DeTimeState.BEFORE_START_TIME
        else
            DeTimeState.AUCTION_HAS_STARTED
    }
    return null
}


fun DeAuction.is_awardable(): Boolean = lastround().let { n ->
    val total_demand = total_buy(n)
    val total_supply = total_sell(n)
    when {

        de_trading_companies.all { it.next_round_order_will_be_mandatory } -> true

        n.price == null -> false

        // n.number == 1 -> false
        total_demand > total_supply ->
            // so the question is: did you get here from an up or a down?
            when (n.has_reversed) {
                false -> false // price will reverse
                true -> {
                    val potential_next_price = n.price!! - settings.price_rule.price_change_post_reversal
                    rounds.any { it.price != null && it.price!! <= potential_next_price }
                }
            }

        total_supply > total_demand ->
            // so the question is: did you get here from an up or a down?
            when (n.has_reversed) {
                (n.price == null) -> false
                false -> false
                true -> {
                    val potential_next_price = n.price!! + settings.price_rule.price_change_post_reversal
                    rounds.any { it.price != null && it.price!! >= potential_next_price }
                }
            }

        else -> true // ie: supply == demand !
    }
}


// 1) if supply equals demand then it's awardable

// 2) if demand != supply and we have not reversed then we keep going

//    // at this point we must have a penultimate
//    val pen: DeAuction.Round = penultimate() ?:
//    throw Error("is_awardable() expected penultimate round")
//
//    if (excess_supply(n) > 0) {
//        // so we need to go down, unless the next price is the same as the previous round
//        return small_decrease(n) == pen.price
//    }
//    if (excess_demand(n) > 0) {
//        return small_increase(n) == pen.price
//    }

//    return false
//}

//fun get_trader(p: Person?): Trader? =
//    traders.find { it.company == p?.company }
//
//fun create_trader(u: Person): Trader =
//    Trader(u.company
//        ?: throw Error("user has no company: $u")).also {
//        traders.add(it)
//    }

////////////////////////////////////


/*
        AUCTION extension methods:
 */


fun DeAuction.getTrader(c: Company?): DeTradingCompany? =
    when (c) {
        null -> null
        else -> de_trading_companies.find { it.company_id == c.id }
    }

fun DeAuction.hasTrader(c: Company?): Boolean =
    when (c) {
        null -> false
        else -> de_trading_companies.any { t -> t.company_id == c.id }
    }

fun DeAuction.hasTrader(u: Person?): Boolean =
    when (u) {
        null -> false
        else -> hasTrader(u.company)
    }

//fun DeAuction.state_snapshot(): AuctionStateSnapshot = AuctionStateSnapshot(this)

// ie: for determining closability: (moved to round )
//fun DeAuction.has_non_default_bid(company: Company?) =
//    when (company) {
//        null -> false
//        else -> rounds.any { r ->
//            r.trader_infos
//                .find { it.company == company }?.let { rti: RoundTraderInfo ->
//                    rti.has_non_default_bid()
//                } ?: false
//        }
//    }

// ie: for determining credit editing, trader remove
fun DeAuction.has_non_zero_bid(company: Company?) =
    when (company) {
        null -> false
        else -> rounds.any { r ->
            r.trader_infos
                .find { it.de_trading_company.company_id == company.id }
                ?.let { rti: DeRoundTraderInfo -> rti.has_non_zero_bid() }
                ?: false
        }
    }


fun DeAuction.is_before_end_of_first_round(): Boolean = rounds.size > 1 || !listOf(
    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE,
    DeAuctioneerState.ROUND_CLOSED_AWARDABLE,
    DeAuctioneerState.AUCTION_CLOSED
).contains(auctioneer_state)


//fun DeAuction.round_closeable(): Boolean =
//    lastround()?.let {
//        when {
//            it.open_time == null -> false
//            it.closed_time != null -> false
//            DateTime(it.open_time)
//                .plusSeconds(settings.round_open_min_secs)
//                .isBefore(DateTime()) ->
//                true
//            else -> false
//        }
//    } ?: false

//fun DeAuction.next_round_openable(): Boolean =
//    lastround()?.let {
//        when {
//            it.closed_time == null -> false
//            DateTime(it.closed_time)
//                .plusSeconds(settings.round_closed_min_secs)
//                .isBefore(DateTime()) -> true
//            else -> false
//        }
//    } ?: false

//fun DeAuction.auction_row_datetime_format(): String =
//    when (this) {
////                is TE_AUCTION ->
////                    "${date_time_format(a.window_one_opens)} to ${date_time_format(a.closing_datetime())}"
//        else -> "not implemented"
//    }
/*
var round_counterparty_limits: MutableList<DE_VOLUME_LIMIT> = mutableListOf()
var matches: MutableList<DeAuction.Match> = mutableListOf()
val rbis: MutableMap<DeAuction.Trader, DeAuction.Trader_ROUND> = mutableMapOf()
 */

// moved to queries
//fun AuEntityManager.open_de_auctions(): List<DeAuction> = findAll<DeAuction>().filter { !it.isClosed() }


//fun Round.get_match_potential(buyer_rti: RoundTraderInfo, seller_rti: RoundTraderInfo): Int {
//    return minOf(
//        limits.find {
//            is_same_entity(
//                it.buyer.company,
//                buyer_rti.trader.company
//            ) && is_same_entity(it.seller.company, seller_rti.trader.company)
//        }?.limit ?: 0,
//        buyer_rti.constraints.max_buy_volume,
//        seller_rti.constraints.max_sell_volume
//    )
//}


fun DeAuction.message_auction_state(): String =
// TODO: this is in the wrong place, it is used by OnMessage and MESSAGE,
// TODO: ie: move to Common_Extensions file
    when (auctioneer_state) {
//        DeState.AUCTION_INIT -> "Auction setting up "
//        DeState.ROUND_READY -> "Round " + this.rounds.size.toString() + " ready to start "
//        DeState.ROUND_RUNNING -> "Round " + this.rounds.size.toString() + " ticking @ " + this.time_remaining + " sec "
//        DeState.ROUND_PAUSED -> "Round " + this.rounds.size.toString() + " PAUSED @ " + this.time_remaining + " sec "
//        DeState.ROUND_CLOSED -> "Round " + this.rounds.size.toString() + " closed "
//        DeState.AUCTION_CLOSED -> "Auction closed "
        // else -> return "state not handled: " + this.state
        else -> throw Exception()
    }

fun DeAuction.excess_side(r: DeRound): OrderType =
    when {
        total_buy(r) > total_sell(r) -> OrderType.BUY
        total_sell(r) > total_buy(r) -> OrderType.SELL
        else -> OrderType.NONE
    }

fun DeAuction.excess_quantity(r: DeRound): Int =
    abs(total_buy(r) - total_sell(r))

fun DeAuction.excess_level(r: DeRound, role: AuUserRole): String =
    settings.price_rule.get_excess_level(excess_quantity(r), role)

//fun DeAuction.excess_direction_str(r: Round): String =
//    if (total_buy(r) > total_sell(r)) "Buy"
//    else if (total_sell(r) > total_buy(r)) "Sell"
//    else "none"

fun DeAuction.show_trader_excess(r: DeRound): Boolean = r != lastround() || listOf(
    DeCommonState.AUCTION_CLOSED
).contains(common_state)

fun DeAuction.format_round_price(role: AuUserRole, r: DeRound): String =
    when (role) {
        // auctioneer view: (auctioneer status, blotter, etc):
        AuUserRole.AUCTIONEER -> r.price?.let {
            AuFormatter.format_to_places(it, this.price_decimal_places)
        } ?: "---"
        // trader view: (ie: common status)
        AuUserRole.TRADER -> when {
            r.price == null || !starting_price_announced() -> "waiting"
            else -> AuFormatter.format_to_places(r.price!!, this.price_decimal_places)
        }
    }


// fun DeAuction.de_counterparty_credit_vol_limit(r: DeAuction_ROUND, seller: DeAuction.Trader, buyer: DeAuction.Trader): Int =
//        Common_Getters.common_counterparty_credit_vol_limit (r.price, a.settings.cost_multiplier, seller.person, buyer.person))

/******************
 * De Trader
 */

fun DeAuction.trader_by_company_id(company_id: Long?): DeTradingCompany? =
    when (company_id) {
        null -> null
        else -> de_trading_companies.find { it.company_id == company_id }
    }

fun DeAuction.trader_by_company_id_str(company_id_str: String?): DeTradingCompany? =
    when (company_id_str) {
        null -> null
        else -> de_trading_companies.find { it.company_id.toString() == company_id_str }
    }

//fun DeAuction.get_trader(u: Person?): DeAuction.Trader? =
//        if (u?.Company != null)
//            traders.first { it.Company == u.Company }
//        else
//            null


fun DeAuction.get_trader(s: AuSession?): DeTradingCompany? =
    when (val c = s?.user?.company) {
        null -> null
        else -> getTrader(c)
    }

fun DeAuction.round_sellers(r: DeRound): List<DeTradingCompany> =
    de_trading_companies.filter {
        r.get_order(it).type == OrderType.SELL
    }

fun DeAuction.round_buyers(r: DeRound): List<DeTradingCompany> =
    de_trading_companies.filter {
        r.get_order(it).type == OrderType.BUY
    }

fun DeAuction.seller_count(r: DeRound): Int = round_sellers(r).size

fun DeAuction.buyer_count(r: DeRound): Int = round_buyers(r).size


/**
 *
 * DeOrder
 *
 */


fun DeAuction.get_orders(t: DeTradingCompany): List<DeOrder> =
    rounds.map { it.trader_infos }
        .flatten()
        .filter { it.de_trading_company.company_id == t.company_id }
        .map { it.order }


// Can't compare traders as they aren't entities, and don't have identity !!
fun DeRound.get_order(t: DeTradingCompany): DeOrder =
    trader_infos
        .find { it.de_trading_company.company_id == t.company_id }
        ?.order
        ?: throw AlertException("We should always have a round info and an order for a trader")


//fun Order?.side_str(): String =
//    when {
//        this == null -> "---"
//        isBuy() -> "Buy"
//        isSell() -> "Sell"
//        else -> throw AlertException("ERROR: order is neither buy, sell, or null.")
//    }

//fun DeOrder?.format_order_volume(): String =
//        this?.let { FormatUtil.format_thousands(this.volume) } ?: ""

fun DeOrder?.format(): String = when (this?.type) {
    null -> "---"
    OrderType.BUY -> "Buy: " + buyVol().thousands()
    OrderType.SELL -> "Sell: " + sellVol().thousands()
    OrderType.NONE -> "Zero"
}


fun DeAuction.order_value_str(o: DeOrder?): String = o?.run {
    // note: o.price will be null before the round price is set!
    // TODO: when round price set: existing orders need to be replaced with new price
    AuFormatter.format_currency(o.price * o.quantity * settings.cost_multiplier)
} ?: "---"


// don't need this:
// fun get_orders(a: DeAuction, r: DeAuction_ROUND): List<DeOrder> = r.get_orders()


/***************
 * De Round
 */

fun DeAuction.penultimate(): DeRound? = rounds.takeIf { it.size > 1 }?.let { it[it.size - 2] }

fun DeAuction.total_sell(r: DeRound? = lastround()): Int = r?.sell_orders()?.sumOf { it.quantity } ?: 0

fun DeAuction.total_buy(r: DeRound? = lastround()): Int = r?.buy_orders()?.sumOf { it.quantity } ?: 0

fun DeAuction.excess_demand(r: DeRound? = lastround()): Int = total_buy(r) - total_sell(r)

fun DeAuction.excess_supply(r: DeRound? = lastround()): Int = total_sell(r) - total_buy(r)


/**************
 * De Trader Round
 */

fun DeRound?.get_rti(t: DeTradingCompany?): DeRoundTraderInfo? =
// NB: THIS SHOULD BE THE ONLY PLACE WHERE WE GET RBIs !!
    if (t == null || this == null) {
        null
    } else {
        this.trader_infos.find { it.de_trading_company.company_id == t.company_id }
    }

//fun Round?.get_rti_by_company(company: Company?): RoundTraderInfo? = when {
//    this == null -> null
//    company == null -> null
//    else -> trader_infos.find { it.company == company }
//}

// TODO: do we need to look up by companp_id?
//fun Round?.get_rti_by_company_id(company_id: String?): RoundTraderInfo? = when {
//    this == null -> null
//    company_id == null -> null
//    else -> trader_infos.find { it.company.id_str() == company_id }
//}

fun DeRound?.get_rti_by_company(company: Company?): DeRoundTraderInfo? =
    when {
    this == null -> null
    company == null -> null
    else -> trader_infos.find { it.de_trading_company.company_id == company.id }
}



fun DeRound.sell_quantity(t: DeTradingCompany): Int =
    get_order(t).takeIf { it.type == OrderType.SELL }?.quantity ?: 0


fun DeRound.buy_quantity(t: DeTradingCompany): Int =
    get_order(t).takeIf { it.type == OrderType.BUY }?.quantity ?: 0


//
//fun set_round_price(ratio:MatchedVolumeRatio) {
//    when (ratio) {
//        MatchedVolumeRatio.UP_TO_10  -> price_level_1
//        MatchedVolumeRatio.UP_TO_20  -> price_level_2
//        MatchedVolumeRatio.UP_TO_30  -> price_level_3
//        MatchedVolumeRatio.UP_TO_40  -> price_level_4
//        MatchedVolumeRatio.UP_TO_50  -> price_level_5
//    }
//}

//fun DeAuction.sell_match(): Int =
//        if (?.is_sell() == true) current_matched_vol
//        else 0
//
//fun DeAuction.Trader_ROUND.buy_match(): Int =
//        if (order?.is_buy() == true) current_matched_vol
//        else 0

/**************
 * De Match
 */

// MOVED TO Order.match_vol(t)

//fun DeAuction.Round.volume_limit(seller: DeAuction.Trader, buyer: DeAuction.Trader): Int =
//    this.limits.find { it.sellerId == seller.company_id && it.buyerId == buyer.company_id }?.limit ?: 0
//
//fun DeOrder?.sell_match_vol(t: DeAuction.Trader): Int =
//    if (this == null || this.isBuy()) 0
//    else this.matches.filter { it.company_id == t.company_id }.sumBy { it.volume }
//


//fun DeAuction.Round.total_matched(): Int = {
// NOT SURE EXACTLY WHAT THIS IS DOING: should it be on all the matches, or just this round??
// this.orders.matches.sumBy { it.match_volume }
//    matches.filter { it.buy_order.round_number == this.number || it.sell_order.round_number == this.number }
//        .sumBy { it.match_volume }
////        if (this == null)
//            0
//        else
//            matches.sumBy { it.match_volume }
// or: matches.map { it.match_volume }.sum()

//fun DeAuction_ROUND?.total_match(t: DeAuction.Trader): Int =
//// NOTE: ASSUMES THAT YOU CAN'T BE A BUYER AND A SELLER IN A ROUND
//        if (this == null)
//            0
//        else
//            matches.filter {
//                it.buy_order.trader == t ||
//                        it.sell_order.trader == t
//            }.sumBy { it.match_volume }
//
//fun DeAuction.Round.buy_matches(o:DeOrder?): List<DeAuction.Match> =
//    when{
//        o == null -> emptyList()
//        o.isSell() -> emptyList()
//        else -> o.matches
//    }
//
//fun DeAuction.Round.sell_matches(o:DeOrder?):List<DeAuction.Match> =
//    when{
//        o == null -> emptyList()
//        o.isBuy() -> emptyList()
//        else -> o.matches
//    }
//
//fun DeAuction.Round.buy_match_vol(o:DeOrder?):Int =
//    buy_matches(o).sumBy { it.volume }
//
//fun DeAuction.Round.sell_match_vol(o:DeOrder?):Int =
//    sell_matches(o).sumBy { it.volume }

//
//fun DeAuction_ROUND.match_potential(seller: DeAuction.Trader, buyer: DeAuction.Trader): DeAuction_ROUND.DeCounterPartyVolume? =
//        this.round_counterparty_limits.find {
//            it.buyer == buyer && it.seller == seller
//        }


/*
fun DeAuction.counter_party_potential_match(
    r: DeAuction.Round,
    limit:
): Int {
    // see similar function in DeMatrixEdgeElement
    val buyer: DeAuction.Trader = get_trader(limit.borrower)
    val seller: DeAuction.Trader = get_trader(limit.lender)

    val buyer_rbi: DeAuction.RoundTraderInfo? = r.get_rbi(buyer)
    val seller_rbi: DeAuction.RoundTraderInfo? = r.get_rbi(seller)

    if (buyer_rbi == null || seller_rbi == null)
        return 0

    if (seller_rbi.max_vol <= 0)
        return 0

    // minimum of seller elig, and counter party volume based on credit:
    return seller_rbi.max_vol.coerceAtMost(
        counterparty_credit_vol_limit(limit, r.price, settings.currency_conversion_rate)
    )

}
*/


//fun DeAuction.calculate_value(r: DeAuction_ROUND?, volume: Int): Double {
//    return if (settings.cost_multiplier && r?.price)
//        volume * settings.cost_multiplier * r!!.price
//    else
//        0.0
//}

//fun DeAuction.is_continuable(): Boolean =
//    lastround()?.let {
//        when (settings.initial_price_direction) {
//            UP -> total_buy(it) > total_sell(it)
//            DOWN -> total_sell(it) > total_buy(it)
//        }
//    } ?: false

//fun DeAuction.actual_activity_percentage(r: Round): Int {
//    val match_vol: Int = r.match_vol()
//    val sell_total: Int = total_sell(r)
//    return if (sell_total == 0)
//        0
//    else
//        (100.0 * match_vol / sell_total).roundToInt()
//}

//fun DeAuction.activity_percentage_formatted_auctioneer(r: Round): String =
//    actual_activity_percentage(r).toString() + "%"
//
//fun DeAuction.activity_percentage_formatted_trader(r: Round): String =
//// TODO: is this implemented already?
//    if (r == lastround() && !this.isClosed())
//        "---"
//    else
//        r.matched_sell_ratio.label

//fun DeAuction.seller_activity(r: Round, for_auctioneer: Boolean): String {
//    val label = settings.volume_units
//    val sell_vol = total_sell(r)
//    return if (r == lastround() && !isClosed())
//        "---"
//    else if (sell_vol > 100)
//        "> 100 $label"
//    else if (sell_vol > 75)
//        "76-100 $label"
//    else if (sell_vol > 50)
//        "51-75 $label"
//    else if (sell_vol > 25)
//        "26-50 $label"
//    else
//        "0-25 $label"
//}


//fun DeAuction.round_feedback(r: Round): String =
//    excess_demand(r).let {
//        when {
//            it > 50 -> "++++"
//            it > 40 -> "++++"
//            it > 30 -> "+++"
//            it > 20 -> "+++"
//            it > 10 -> "++"
//            else -> "+"
//        }
//    }

//fun DeAuction.is_ascending_price(): Boolean = settings.initial_price_direction == UP
//fun DeAuction.is_descending_price(): Boolean = settings.initial_price_direction == DOWN

//fun DeAuction.state_display_label(is_top_line: Boolean): String =
//    when (auctioneer_state) {
////        DeState.AUCTION_INIT ->
////            if (is_top_line) "Waiting to open"
////            else ""
////        DeState.ROUND_READY ->
////            if (is_top_line) "Ready to open round " + this.rounds.size.toString()
////            else ""
////        DeState.ROUND_RUNNING ->
////            if (is_top_line) "Round " + this.rounds.size.toString() + " - ROUND_OPEN"
////            else this.time_remaining_label()
////        DeState.ROUND_PAUSED ->
////            if (is_top_line) "Round " + this.rounds.size.toString() + " - PAUSED"
////            else this.time_remaining_label()
//////         DeState.ROUND_TRIGGERED -> {
//////            throw Exception("ROUND_TRIGGERED NOT IMPLMENTED")
//////            return "Round " + String.valueOf(r!!.round_number) + " open @ " + String.valueOf(app.framework.FormatUtil.invokeMethod("format_round_price", arrayOf<Any>(a, r?.price))) + " " + this.settings.price_label + "\nClock paused at: " + String.valueOf(this.time_remaining) + " seconds" + "\nWAITING FOR ALL ORDERS!"
//////        }
////        DeState.ROUND_CLOSED ->
////            if (is_top_line) "Round " + this.rounds.size.toString() + " AUCTION_CLOSED"
////            else ""
////        DeState.AUCTION_CLOSED ->
////            if (is_top_line) "Auction Closed"
////            else ""
//        else ->
//            throw java.lang.Exception()
//    }

//fun DeAuction.time_remaining_label(): String = "TODO"
////    if (state.oneOf(DeState.ROUND_RUNNING, DeState.ROUND_PAUSED))
////        time_remaining.toString() + " seconds remaining"
////    else ""


```
de/model/starting-time-labels.kt
```kt
// File: starting-time-labels.kt
package au21.engine.domain.de.model


/*

// NOT SURE THIS IS ACTUALLY USED?
fun DeAuction._trader_state_label(): String {
    val state = current_state
    return when (state.auction_state.trader_state()) {
        DeAuctionTraderState.SETUP -> "Waiting for auction to start"
        DeAuctionTraderState.STARTING_PRICE_ANNOUNCED -> "Starting price announced"
        DeAuctionTraderState.ROUND_OPEN -> "Round ${rounds.size} open, waiting for orders."
        DeAuctionTraderState.ROUND_CLOSED -> "Round ${rounds.size} closed."
        DeAuctionTraderState.AUCTION_CLOSED -> "Auction closed"

        DeAuctioneerState.STARTING_PRICE_NOT_SET ->
            when (time_state()) {
                DeTimeState.BEFORE_ANNOUNCE_TIME ->
                    settings.auction_starting_time_text() +
                            " starting price will be annouced ${settings.starting_price_announcement_mins} before the auction starts"
                DeTimeState.BEFORE_START_TIME ->
                    settings.auction_starting_time_text() +
                            " starting price will be announced soon"
                DeTimeState.AFTER_START_TIME ->
                    "Starting price will be announced soon"
            }
        DeAuctioneerState.STARTING_PRICE_SET ->
            when (time_state()) {
                DeTimeState.BEFORE_ANNOUNCE_TIME ->
                    settings.auction_starting_time_text() +
                            " starting price will be annouced ${settings.starting_price_announcement_mins} before the auction starts"
                DeTimeState.BEFORE_START_TIME ->
                    settings.auction_starting_time_text() +
                            " starting price will be annouced before the auction starts"
                DeTimeState.AFTER_START_TIME ->
                    "starting price will be announced soon"
            }

        DeAuctioneerState.STARTING_PRICE_ANNOUNCED ->
            when (time_state()) {
                DeTimeState.BEFORE_ANNOUNCE_TIME ->
                    "starting price is ${_starting_price_label()} ${settings.auction_starting_time_text()}"
                DeTimeState.BEFORE_START_TIME ->
                    "starting price is ${_starting_price_label()} ${settings.auction_starting_time_text()}"
                DeTimeState.AFTER_START_TIME ->
                    "starting price is ${_starting_price_label()} auction will start shortly"
            }
        DeAuctioneerState.ROUND_OPEN_WAITING_FOR_BIDS ->
            lastround()?.let { n: DeAuction.Round ->
                "Round ${n.number} open for bidding." +
                        if (n.round_open_seconds() > settings.round_red_secs)
                            " waiting for slow bidders"
                        else ""
            } ?: ""
        DeAuctioneerState.ROUND_OPEN_ALL_BIDS_IN ->
            lastround()?.let { n: DeAuction.Round ->
                "Round ${n.number} open for bidding." +
                        if (n.round_open_seconds() > settings.round_red_secs)
                            " waiting for slow bidders"
                        else ""
            } ?: ""
        DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> "Round ${lastround()?.number} closed."
        DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> "Round ${lastround()?.number} closed."
        DeAuctioneerState.AUCTION_CLOSED -> "Auction closed"


    }
}

 */

```
de/model/state-labels.kt
```kt
// File: state-labels.kt
package au21.engine.domain.de.model


//    override fun toString(): String =
//        "$auction_state_label \n" +
//                "$round_state_label \n" +
//                "$autopilot \n" +
//                "$starting_price \n" +
//                "$starting_price_announcement_mins} \n" +
//                "$starting_time \n" +
//                "${announce_time()} \n" +
//                "${seconds_until(starting_time)} \n" +
//                "-------------------------\n"


//
//
//fun DeAuction._starting_price_label(): String =
//    when (auctioneer_state) {
//        DeAuctioneerState.STARTING_PRICE_NOT_SET -> ""
//        DeAuctioneerState.STARTING_PRICE_SET ->
//            when (settings.starting_price_announcement_mins) {
//                0 -> "Will be announced when the auction starts"
//                1 -> "Will be announced 1 minute before the auction starts"
//                else -> "Will be announced ${settings.starting_price_announcement_mins} before the auction starts"
//                // TODO: probably we could count down to this?
//            }
//        DeAuctioneerState.STARTING_PRICE_ANNOUNCED ->
//            settings.starting_price?.let { " ${AuFormatter.format_currency(it)} ${settings.price_units}" } ?: "---"
//        DeAuctioneerState.ROUND_OPEN_WAITING_FOR_BIDS -> ""
//        DeAuctioneerState.ROUND_OPEN_ALL_BIDS_IN -> ""
//        DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> ""
//        DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> ""
//        DeAuctioneerState.AUCTION_CLOSED -> ""
//    }
//


//fun DeAuction._auction_row_state_display(): String =
//    when (a) {
//        is DeAuction ->
//            a.current_state.let {
//                when (it.auction_state) {
//                    DeAuctioneerState.STARTING_PRICE_NOT_SET ->
//                        auction_starting_time_text(it.starting_time)
//                    DeAuctioneerState.STARTING_PRICE_SET ->
//                        auction_starting_time_text(it.starting_time)
//                    DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> auction_starting_time_text(it.starting_time)
//                    DeAuctioneerState.ROUND_OPEN ->
//                        "Auction round ${a.lastround().number} is open"
//                    DeAuctioneerState.ROUND_CLOSED ->
//                        "Auction round ${a.lastround().number} closed"
//                    DeAuctioneerState.AUCTION_CLOSED ->
//                        "Auction closed"
//                }
//            }
//        else -> "${a::class.java.simpleName} state not implemented"
//    }



```