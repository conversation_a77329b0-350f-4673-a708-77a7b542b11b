// File: auction-row.kt
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.model.AuctionInstruction
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.common.services.sessions_logged_in
import au21.engine.domain.common.services.sessions_logged_in_traders
import au21.engine.framework.PageName
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail_if
import au21.engine.framework.commands.fail_if_not_auctioneer
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.database.is_same_entity

class AuctionRowCommand(
    val auction_id: String,
    val instruction: AuctionInstruction
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        val session = db.session_non_terminated_or_alert(session_id)
        val a: Auction = db.auction_or_alert(auction_id)

        fail_if_not_auctioneer(session)

        when (instruction) {
            AuctionInstruction.HIDE -> fail_if(a.hidden, "Auction is already hidden")
            AuctionInstruction.UNHIDE -> fail_if(!a.hidden, "Auction is not hidden")
            AuctionInstruction.DELETE -> {
            } // fail_if(!a.hidden, "Auction is not hidden")
        }

        return AuctionRowAction(this, db, session, a)
    }

}

class AuctionRowAction(
    override val command: AuctionRowCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val a: Auction
) : EngineAction {
    // NEED to collect bounced sessions here
    // - because by the time it reaches generator the session.auction will be null
    // - and won't be found eg by auction_sessions_online

    //val bounced_sessions: MutableList<AuSession> = mutableListOf()

    //fun bounced_sids(): Array<String> = bounced_sessions.map { it.session_id }.toTypedArray()

    override fun mutate() {

        fun bounce(s: AuSession) {
            s.set_page(PageName.HOME_PAGE)
            //bounced_sessions.add(s)
            db.save(s)
        }

        when (command.instruction) {
            AuctionInstruction.HIDE -> {
                a.hidden = true
                db.sessions_logged_in_traders()
                    .filter { is_same_entity(it.auction, a) }
                    .forEach { bounce(it) }
            }
            AuctionInstruction.UNHIDE -> {
                a.hidden = false
            }
            AuctionInstruction.DELETE -> {
                a.deleted = true
                db.sessions_logged_in()
                    .filter { is_same_entity(it.auction, a) }
                    .forEach { bounce(it) }
            }
        }
        db.save(a)
    }

}
