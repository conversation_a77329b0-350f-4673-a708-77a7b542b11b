package au21.engine.domain.de.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.getTrader
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.alert
import au21.engine.framework.database.AuEntityManager

enum class DeTraderCommandType {
    HALT,
    WITHDRAW
}

class DeTraderCommand(
    val auction_id: String,
    val company_id: String,
    val kind: DeTraderCommandType

) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        val de: DeAuction = db.auction_or_alert(auction_id)
        de.getTrader(db.byId(company_id))
            ?: alert("you are not a trader in this auction")

        return DeTraderAction(this, db, session, de, kind)
    }
}


class DeTraderAction(
    override val command: DeTrader<PERSON>ommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de: DeAuction,
    val kind: DeTraderCommandType
) : EngineAction {
    override fun mutate() {
        TODO("Not implemented yet")
    }

}
