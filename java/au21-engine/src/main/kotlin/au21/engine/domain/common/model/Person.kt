// File: Person.kt
package au21.engine.domain.common.model


import au21.engine.framework.database.AuEntity
import au21.engine.framework.utils.toEnumOrError
import javax.persistence.Embeddable
import javax.persistence.Entity

@Embeddable
class PersonProxy(p: Person) {
    var person_id: Long = p.id
        private set
    var username_at_auction_time: String = p.username
    var role_at_auction_item:AuUserRole = p.role
}

@Entity
class Person(
    role: AuUserRole,
    var username: String, // if edit this, then must edit the proxy username at auction time for all open auctions
    var password: String,
    var company: Company? = null,
    var email: String = "",
    var isObserver: Boolean = false,
    var isTester: Boolean = false,
    var phone: String = ""
) : AuEntity() {

    var role_label: String = role.toString()
        private set

    var role: AuUserRole // mustn't be null
        get() = toEnumOrError(role_label)
        set(r) {
            role_label = r.toString()
//            if(r == AuUserRole.AUCTIONEER && company == null)
//                throw AlertException("Traders cannot have a null company")
        }

    // TODO: should these be in extensions?:

    fun inRole(vararg roles: AuUserRole): Boolean =
        roles.contains(role)

    fun isTrader(): Boolean = inRole(AuUserRole.TRADER)
    fun isAuctioneer(): Boolean = inRole(AuUserRole.AUCTIONEER)
  //  fun isAdmin(): Boolean = inRole(AuUserRole.ADMIN)

}


