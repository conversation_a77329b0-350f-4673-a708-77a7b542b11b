// File: de_order_validations.kt
package au21.engine.domain.de.validations

import au21.engine.domain.common.model.OrderType
import au21.engine.domain.de.model.DeBidConstraints
import au21.engine.framework.commands.fail_if
import au21.engine.framework.utils.thousands

/**
 * ASSUMPTIONS:
 *
 * assumes that order type is corrected, ie:
 * - if buy or sell volume is 0 then set to OrderType.NONE
 *
 */

fun validate_de_order_against_constraints_and_throw_if_fails(
    constraints: DeBidConstraints,
    order_type: OrderType,
    order_quantity: Int,
    quantity_units: String,
) {

    // TODO: this is a LogException, to auctioneers and log files, not sure what trader sees?
    // for now just sending back to trader (and they won't know what 'OrderType None' is)

    fun with_units(vol: Int): String = "${vol.thousands()} $quantity_units"

    return when (order_type) {
        OrderType.NONE -> {
            fail_if(order_quantity > 0, "Order quantity must be zero if OrderType is None")
            fail_if(
                constraints.min_buy_quantity > 0,
                "You cannot submit a zero quantity bid. It is less than your min buy quantity of ${
                    with_units(constraints.min_buy_quantity)
                }"
            )
            fail_if(
                constraints.min_sell_quantity > 0,
                "You cannot submit a zero quantity bid. It is less than your min sell quantity of ${
                    with_units(constraints.min_sell_quantity)
                }"
            )
        }
        OrderType.BUY -> {
            fail_if(
                constraints.min_sell_quantity > 0,
                "You cannot submit a buy order when you have a minimum sell quantity constraint of (${
                    with_units(constraints.min_sell_quantity)
                })"
            )
            fail_if(
                order_quantity > constraints.max_buy_quantity,
                "You cannot buy more than your maximum buy quantity of ${
                    with_units(constraints.max_buy_quantity)
                }"
            )

            fail_if(
                order_quantity < constraints.min_buy_quantity,
                "You cannot buy less than your minimum buy quantity of ${
                    with_units(constraints.min_buy_quantity)
                }"
            )
        }
        OrderType.SELL -> {
            fail_if(
                constraints.min_buy_quantity > 0,
                "You cannot submit a sell order when you have a minimum buy quantity constraint of (${
                    with_units(constraints.min_buy_quantity)
                })"
            )
            fail_if(
                order_quantity > constraints.max_sell_quantity,
                "You cannot sell more than your maximum sell quantity of ${with_units(constraints.max_sell_quantity)}"
            )

            fail_if(
                order_quantity < constraints.min_sell_quantity,
                "You cannot sell less than your minimum sell quantity of ${with_units(constraints.min_sell_quantity)}"
            )

        }
    }

}
