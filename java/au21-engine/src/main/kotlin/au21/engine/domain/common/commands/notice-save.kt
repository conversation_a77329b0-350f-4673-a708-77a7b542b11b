// File: notice-save.kt
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail_if_not_auctioneer
import au21.engine.framework.database.AuEntityManager

class NoticeSaveCommand(
    val auction_id:String,
    val notice:String
) : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)

        val a: Auction = db.auction_or_alert(auction_id)

        return NoticeSaveAction(this, db, session, a, notice)
    }
}


class NoticeSaveAction(
    override val command: NoticeSaveCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val a:Auction,
    val notice:String
) : EngineAction {
    override fun mutate() {
        a.notice = notice
        db.save(a)
    }

}
