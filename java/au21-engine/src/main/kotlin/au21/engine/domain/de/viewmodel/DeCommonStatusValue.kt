// File: DeCommonStatusValue.kt
package au21.engine.domain.de.viewmodel

import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.PriceDirection
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeCommonState
import au21.engine.domain.de.model.DeRound
import au21.engine.domain.de.model.format_round_price
import au21.engine.framework.client.StoreValue

data class DeCommonStatusValue(
    // auction state moved back here as it's ok to be seed by both
    // - and needed eg in DeRoundPanel.vue
    //    val starting_price: String,
    val common_state: DeCommonState,
    val common_state_text: String,
    val isClosed: <PERSON>olean,
    val price_direction: PriceDirection?,
    val price_has_reversed: Boolean,
    val round_number: Int,
    val round_price: String,
    val round_seconds: Int,
    val starting_price_announced: <PERSON>olean,
    val starting_time_text: String,
) : StoreValue {

    /**
     * Seen by all users an and auction (traders and auctioneers)
     * Auctioneer-only info was move to DeAuctioneerStatusValue
     */

    companion object {
        fun create(de: DeAuction): DeCommonStatusValue {

//    @Transient
//    private val n: DeAuction.Round? = de.lastround()

            val n: DeRound = de.lastround()

            return DeCommonStatusValue(
                isClosed = de.closed,
                price_direction = n.price_direction,
                price_has_reversed = n.has_reversed,
                round_number = n.number,
                round_seconds = n.round_open_seconds(),
                round_price = when (de.starting_price_announced()) {
                    false -> "waiting"
                    true -> de.format_round_price(AuUserRole.TRADER, n)
                },
                starting_price_announced = de.starting_price_announced(),
                starting_time_text = de.starting_time_text(),
                common_state = de.common_state,
                common_state_text = de.common_state_text
            )
        }
    }
}

