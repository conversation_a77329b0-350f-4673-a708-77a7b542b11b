// File: Activity.kt
package au21.engine.domain.common.model

import au21.engine.framework.database.AuEntity
import org.joda.time.DateTime
import java.util.*
import javax.jdo.annotations.Index
import javax.persistence.Entity

@Entity
class Activity(
    var heading: String,
    var body: MutableMap<String, String> = mutableMapOf(),  // one could be: "json": "<json>"
    var entities: MutableMap<String, AuEntity> = mutableMapOf() // so that we can pass entities
) : AuEntity() {

    @Index
    var timestamp: Date = DateTime().toDate()
        private set
}
