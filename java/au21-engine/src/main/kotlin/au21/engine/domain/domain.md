## common/commands/auction-row.kt

```kotlin
// File: auction-row.kt
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.model.AuctionInstruction
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.common.services.sessions_logged_in
import au21.engine.domain.common.services.sessions_logged_in_traders
import au21.engine.framework.PageName
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail_if
import au21.engine.framework.commands.fail_if_not_auctioneer
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.database.is_same_entity

class AuctionRowCommand(
    val auction_id: String,
    val instruction: AuctionInstruction
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        val session = db.session_non_terminated_or_alert(session_id)
        val a: Auction = db.auction_or_alert(auction_id)

        fail_if_not_auctioneer(session)

        when (instruction) {
            AuctionInstruction.HIDE -> fail_if(a.hidden, "Auction is already hidden")
            AuctionInstruction.UNHIDE -> fail_if(!a.hidden, "Auction is not hidden")
            AuctionInstruction.DELETE -> {
            } // fail_if(!a.hidden, "Auction is not hidden")
        }

        return AuctionRowAction(this, db, session, a)
    }

}

class AuctionRowAction(
    override val command: AuctionRowCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val a: Auction
) : EngineAction {
    // NEED to collect bounced sessions here
    // - because by the time it reaches generator the session.auction will be null
    // - and won't be found eg by auction_sessions_online

    //val bounced_sessions: MutableList<AuSession> = mutableListOf()

    //fun bounced_sids(): Array<String> = bounced_sessions.map { it.session_id }.toTypedArray()

    override fun mutate() {

        fun bounce(s: AuSession) {
            s.set_page(PageName.HOME_PAGE)
            //bounced_sessions.add(s)
            db.save(s)
        }

        when (command.instruction) {
            AuctionInstruction.HIDE -> {
                a.hidden = true
                db.sessions_logged_in_traders()
                    .filter { is_same_entity(it.auction, a) }
                    .forEach { bounce(it) }
            }
            AuctionInstruction.UNHIDE -> {
                a.hidden = false
            }
            AuctionInstruction.DELETE -> {
                a.deleted = true
                db.sessions_logged_in()
                    .filter { is_same_entity(it.auction, a) }
                    .forEach { bounce(it) }
            }
        }
        db.save(a)
    }

}

```

## common/commands/auction-select.kt

```kotlin
// File: auction-select.kt
package au21.engine.domain.common.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.framework.PageName
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail_if
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.database.is_same_entity
import io.quarkus.logging.Log

class AuctionSelectCommand(
    val auction_id: String
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        val a: Auction = db.auction_or_alert(auction_id)

        fail_if(
            !a.show_auction(session),
            "Unable to display auction, because either it was deleted or you are not a trader in this auction."
        )

        return AuctionSelectAction(this, db, session, a)
    }

}


class AuctionSelectAction(
    override val command: AuctionSelectCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val a: Auction
) : EngineAction {

    override fun mutate() {

        if (is_same_entity(session.auction, a)) {
            Log.warn("already on auction, not resending")
            return
        }



        // https://auctionologies4.myjetbrains.com/youtrack/issue/A2019FS-329

//         * To current session:
//
//        (a) SessionUserValue:
//
//        set current auction
//        set current page, ie: changes page

        val page: PageName = when (a) {
            is DeAuction ->
                if (session.is_auctioneer())
                    PageName.DE_AUCTIONEER_PAGE
                else if (session.is_trader())
                    PageName.DE_TRADER_PAGE
                else throw Error("No page for role: " + session.user?.role)
            else -> throw Error("Auction design not handled: " + a::class.java.simpleName)
        }

        if (session.inRole(AuUserRole.TRADER)) {
            a.add_trader_that_has_seen_auction(session)
        }

        session.set_page(page, a)
        db.save(session)
        db.save(a)

        //        (b) MessageElements:
//
//        if auctioneer:
//
//        clear messages for prev auction
//        send all messages

//        (c) AuctionNoticeSave:
//
//        send auction notice for current auction
//        if trader
//
//        all messages sent by auctioneer
//        bid confirmation messages for that trader
//        messages sent by that trader


    }

}

```

## common/commands/client-socket.kt

```kotlin
// File: client-socket.kt
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuSession.ClientSocketState.CLOSED
import au21.engine.domain.common.model.AuSession.ClientSocketState.OPENED
import au21.engine.domain.common.services.session_by_sid
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail
import au21.engine.framework.commands.interfaces.ISessionsTerminated
import au21.engine.framework.commands.interfaces.SessionTermination
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.is_blank
import io.quarkus.logging.Log

/*
 * from EngineSocket.kt when sockets open and close
 * IF OPENED AND NO EXISTING SESSION THEN NEW SESSION CREATED
 * (this should be the only place where sessions are created!)
 */

class ClientSocketCommand(
    val sid: String,
    val state: AuSession.ClientSocketState,
    val browser_name: String? = null,
    val browser_version: String? = null,
    val browser_os: String? = null
) : EngineCommand() {

    // NOTE: this session id is not used, because it will be null in tests
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

//        // note: we don't use session_non_terminated_or_alert, as it could have been terminated alreayd
//        val session = db.session_by_sid(sid)
//            ?: throw Error("session id should not be null")

        if (sid.is_blank())
            fail("No session id.")

        if (sid == "0")
            fail("Session id cannot be zero")

        val existing_session: AuSession? = db.session_by_sid(sid)

        // MOVED BELOW, not an AlertException error:
//        if (state == CLOSED && existing_session == null) {
//            throw Error("existing session not found for socket")
//        }

        return ClientSocketAction(
            this, db, existing_session,
            sid,
            state,
            browser_name,
            browser_version,
            browser_os
        )
    }
}


class ClientSocketAction(
    override val command: ClientSocketCommand,
    override val db: AuEntityManager,
    override val session: AuSession?, // ie: Existing Session nb // won't exist when socket OPENED for first time!
    val sid: String,
    val state: AuSession.ClientSocketState,
    val browser_name: String? = null,
    val browser_version: String? = null,
    val browser_os: String? = null
) : EngineAction, ISessionsTerminated {

    override val sessions_terminated = mutableListOf<SessionTermination>()

    /**
     * TODO: unclear about how new_session works.
     * - BTW, it should always be non-null. bacause:
     * - if Socket OPENED then it's either the new_session or the existing session
     * - if Socket CLOSED then it's the old session or throws excetions
     *
     * TODO: do we need to terminate any sessions (can't see why)
     */

    var new_session: AuSession? = null

    override fun mutate() {

        when (state) {
            OPENED ->
                when (session) {
                    null ->
                        // here we create a new session if need be:
                        // - default state is OPENED:
                        //  TODO: check that this is the only place where sessions are created!
                        new_session = AuSession(
                            session_id = sid,
                            browser_name = browser_name,
                            browser_version = browser_version,
                            browser_os = browser_os
                        ).also {
                            db.save(it)
                            Log.info("session created: $sid")
                            // new_session = it
                        }
                    else -> {
                        session.socket_state = OPENED
                        // new_session = session
                    }
                }
            CLOSED -> {
                when (session) {
                    null -> Log.error("sid $sid closed, but has no AuSession!!")
                    else -> {
                        session.socket_state = CLOSED
                        //new_session = session
                    }
                }
            }
        }


        new_session?.let { db.save(it) }
        session?.let { db.save(it) }

        // TODO: not sure what this is?
// this will actually be fired before the session is created !
//        when {
//            is_terminated -> {
//                new_session.socket_state = state
//                db.save(new_session)
//            }
//        }
    }

}

```

## common/commands/company-delete.kt

```kotlin
// File: company-delete.kt
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.model.Company
import au21.engine.domain.common.model.Person
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.framework.commands.*
import au21.engine.framework.commands.interfaces.ISessionsTerminated
import au21.engine.framework.commands.interfaces.SessionTermination
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.database.is_same_entity

class CompanyDeleteCommand(
    val company_id: String
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)

        val company: Company? = db.byId(company_id)
        if (company == null) {
            fail("No company found with id: $company_id")
        } else {

            // shouldn't be able to delete company if it is an open auction

            fail_if(db.findAll<Auction>().any { a: Auction ->
                a.companies_that_have_seen_auction.any { it.company_id == company.id}
            }, "Cannot remove a company that has bid in, or seen, any auction.")

            fail_if(
                db.findAll<Person>().any { is_same_entity(it.company, company) },
                "Cannot delete company: ${company.shortname} because it has traders."
            )

            // we're now bouncing logged in users:
//            if (db.company_users(company).any { db.logged_in_session_for_user(it) != null })
//                alert("cannot delete company because some company users are still logged in!")

            return CompanyDeleteAction(this, db, session, company)
        }
    }
}


class CompanyDeleteAction(
    override val command: CompanyDeleteCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val company: Company
) : EngineAction, ISessionsTerminated {

    override val sessions_terminated = mutableListOf<SessionTermination>()

    override fun mutate() {
        // TODO: check this, shouldn't be able to delete a company if they have any users
        // especially if they are logged in!

//        db.company_users(company).forEach { u: Person ->
//            db.logged_in_session_for_user(u)?.let { s: AuSession ->
//                terminate_session(db, s, AuSession.SessionTerminationReason.COMPANY_DELETED)
//            }
//        }
        db.delete(company)
    }

}

```

## common/commands/company-save.kt

```kotlin
// File: company-save.kt
package au21.engine.domain.common.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.model.Company
import au21.engine.domain.common.model.Person
import au21.engine.domain.common.services.*
import au21.engine.framework.commands.*
import au21.engine.framework.commands.interfaces.ISessionsTerminated
import au21.engine.framework.commands.interfaces.SessionTermination
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.is_blank

class CompanySaveCommand(
    val company_id: String,
    val company_shortname: String,
    val company_longname: String
) : EngineCommand() {


    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        val is_create: Boolean = company_id.is_blank()

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)

        err_if(company_shortname.is_blank(), "Company short name cannot be blank.")
        err_if(company_longname.is_blank(), "Company long name cannot be blank.")

        val shortname_trimmed = company_shortname.trim()
        val longname_trimmed = company_shortname.trim()

        err_if(shortname_trimmed.length > 10, "Company short name cannot be longer than 10 characters.")
        err_if(longname_trimmed.length > 20, "Company long name cannot be longer than 20 characters.")

        err_if(shortname_trimmed.contains("mandory"), "Short name cannot be 'mandatory'")
        err_if(shortname_trimmed.contains("default"), "Short name cannot be 'default'")

        err_if(longname_trimmed.contains("mandory"), "Long name cannot be 'mandatory'")
        err_if(longname_trimmed.contains("default"), "Long name cannot be 'default'")

        fail_if_errors()

        if (is_create) {

            err_if(db.company_by_shortname(shortname_trimmed) != null, "Company short name taken.")
            err_if(db.company_by_longname(longname_trimmed) != null, "Company long name taken.")
            fail_if_errors()

            return CompanySaveAction(
                this, db, session,
                Company(
                    longname = longname_trimmed,
                    shortname = shortname_trimmed
                )
            )

        } else {

            // ie: EDIT

            val company: Company? = db.byId(company_id)

            if (company == null) {

                fail("No company found with id: $company_id")

            } else {

                fail_if(
                    db.open_auctions().any { a: Auction -> a.has_trader(company) },
                    "Company is a trader in an open auction, first remove them from that auction then delete."
                )

                db.company_by_shortname(company_shortname)?.let { c ->
                    // ie: we have a company with that shorname and it's not the existing one:
                    err_if(c != company, "Another company has that short name.")
                }
                db.company_by_longname(company_longname)?.let { c ->
                    // as above we have a company with that longname and it's not the existing one:
                    err_if(c != company, "Another company has that long name.")
                }
                fail_if_errors()

                company.longname = company_longname
                company.shortname = company_shortname

                return CompanySaveAction(this, db, session, company)
            }
        }

    }
}


class CompanySaveAction(
    override val command: CompanySaveCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val company: Company
) : EngineAction, ISessionsTerminated {

    override val sessions_terminated = mutableListOf<SessionTermination>()


    override fun mutate() {
        db.company_users(company).forEach { u: Person ->
            db.logged_in_session_for_user(u)?.let { s: AuSession ->
                terminate_session(db, s, AuSession.SessionTerminationReason.COMPANY_DELETED)
            }
        }

        db.save(company)
    }

}

```

## common/commands/db-delete-auctions.kt

```kotlin
// File: db-delete-auctions.kt
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Auction
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.database.AuEntityManager

/**
 * TODO: must make sure this is not done in production!
 * - deletes all auctions
 */
class DbDeleteAuctionsCommand : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        // TODO: check that it's not production!! and/or backup db first!
        return DbDeleteAuctionsAction(this, db, null)
    }
}

class DbDeleteAuctionsAction(
    override val command: DbDeleteAuctionsCommand,
    override val db: AuEntityManager,
    override val session: AuSession? = null,
) : EngineAction {

    override fun mutate() {
        db.deleteAllIncDeleted<Auction>()
    }
}

```

## common/commands/db-init.kt

```kotlin
// File: db-init.kt
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.Person
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.database.AuEntity
import au21.engine.framework.database.AuEntityManager

/**
 * TODO: must make sure this is not done in production!
 * - deletes all entities and creates a1/1
 */
class DbInitCommand : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        // TODO: check that it's not production!! and/or backup db first!
        return DbInitAction(this, db, null)
    }
}

class DbInitAction(
    override val command: DbInitCommand,
    override val db: AuEntityManager,
    override val session: AuSession? = null,
) : EngineAction {

    override fun mutate() {
        db.deleteAllIncDeleted<AuEntity>()
        db.save(
            Person(
                username = "a1",
                password = "1",
                role = AuUserRole.AUCTIONEER
            )
        )
    }
}

```

## common/commands/errors-send.kt

```kotlin
// File: errors-send.kt
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.*
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_by_sid
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail_if
import au21.engine.framework.commands.interfaces.IAuctionMessage
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.is_blank

/**
 * Somewhat experimental
 * idea is that for certain commands (eg: DeOrderSubmit),
 * the AlertException.error will be send by the handler back here
 * to be broadcast to auctioneers only.
 */
class ErrorsSendCommand(
    val auction_id: String,
    val trader_session_id: String,
    val error: String
) : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        // TODO: probably we shouldn't fail with these,
        //  but given that there is no session id, it won't be sent to any users anyway!

        val a: Auction = db.auction_or_alert(auction_id)

        fail_if(error.is_blank(), "Error is blank")

        val preamble: String = "Order entry error " + try {
            val trader_session: AuSession? = db.session_by_sid(trader_session_id)
            val trader_user: Person? = trader_session?.user
            val trader_company: Company? = trader_session?.user?.company

            if (trader_user != null && trader_company != null) {
                "from ${trader_user.username} (${trader_company.shortname})"
            } else {
                "from unknown trader"
            }
        } catch (t: Throwable) {
            t.printStackTrace()
            "from unknown trader"
        }

        val m = AuctionMessage(
            message_type_ = AuMessageType.SYSTEM_TO_AUCTIONEER,
            message_ = "$preamble: $error",
            from_user_ = null,
            to_company_ = null
        )
        return ErrorsSendAction(this, db, null, a, m)

    }
}


class ErrorsSendAction(
    override val command: ErrorsSendCommand,
    override val db: AuEntityManager,
    override val session: AuSession?,
    override val auction: Auction,
    override val message: AuctionMessage,
) : EngineAction, IAuctionMessage {
    override fun mutate() {
        auction.messages.add(message)
        db.save(auction)
    }
}

```

## common/commands/login.kt

```kotlin
// File: login.kt
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Person
import au21.engine.domain.common.services.logged_in_session_for_user
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.common.services.user_by_username
import au21.engine.framework.commands.*
import au21.engine.framework.commands.interfaces.ISessionsTerminated
import au21.engine.framework.commands.interfaces.SessionTermination
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.jsonToPrettyFormat
import au21.engine.framework.utils.objToPrettyFormat
import au21.engine.framework.utils.to_json
import io.quarkus.logging.Log

class LoginCommand(
    val username: String,
    val password: String,
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): LoginAction {

        val session = db.session_non_terminated_or_alert(session_id)
        err_if_blank(::username)
        err_if_blank(::password)
        fail_if_errors()

        if(username == "admin")
            fail("admin not implemented")

        val u: Person = db.user_by_username(
            username,
            case_insensitive = true,
            include_deleted = false
        ) ?: fail("No active user found with username: $username")

        fail_if(u.deleted, "Account not active.")
        fail_if(u.password.uppercase() != u.password.uppercase(), "Password incorrect")

        return LoginAction(this, db, session, u)
    }

}

class LoginAction(
    override val command: LoginCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val user: Person
) : EngineAction, ISessionsTerminated {

    override val sessions_terminated = mutableListOf<SessionTermination>()

    override fun mutate() {

        // create a few default users

        // if this user is already logged in then force off the session:
        db.logged_in_session_for_user(user)?.let { existing ->
            terminate_session(db, existing, AuSession.SessionTerminationReason.LOGIN_FROM_ANOTHER_BROWSER)
        }

        session.login(user)
        db.save(session)
        Log.info(objToPrettyFormat(session))
    }
}

```

## common/commands/message-send.kt

```kotlin
// File: message-send.kt
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.*
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.common.services.user_by_username
import au21.engine.framework.commands.*
import au21.engine.framework.commands.interfaces.IAuctionMessage
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.is_blank

class MessageSendCommand(
    val auction_id: String,
    val message: String
) : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        val session = db.session_non_terminated_or_alert(session_id)
        val a:Auction = db.auction_or_alert(auction_id)
        fail_if(message.is_blank(), "Message is blank")

        val u: Person = db.user_by_username(session.user?.username)
            ?: fail("Session has no user.")

        when (u.role) {
            AuUserRole.AUCTIONEER -> {
            }
            AuUserRole.TRADER ->
                fail_if_not(
                    a.has_trader(u.company),
                    "You are not a trader in this auction"
                )
            //else -> alert("You must be an auctioneer or trader to send a message.")
        }

        // creating the message here, because of all the init checks it has:
        val m = when (u.role) {
            AuUserRole.AUCTIONEER ->
                AuctionMessage(
                    message_type_ = AuMessageType.AUCTIONEER_BROADCAST,
                    message_ = message,
                    from_user_ = PersonProxy(u),
                    to_company_ = null
                )
            AuUserRole.TRADER ->
                AuctionMessage(
                    message_type_ = AuMessageType.TRADER_TO_AUCTIONEER,
                    message_ = message,
                    from_user_ = PersonProxy(u),
                    to_company_ = null
                )
            //else -> throw Exception("must be auctioneer or trader to send a message")
        }

        return MessageSendAction(this, db, session, a, m)
    }
}


class MessageSendAction(
    override val command: MessageSendCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    override val auction: Auction,
    override val message: AuctionMessage
) : EngineAction, IAuctionMessage {

    override fun mutate() {
        auction.messages.add(message)
        db.save(auction)
    }
}

```

## common/commands/notice-save.kt

```kotlin
// File: notice-save.kt
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail_if_not_auctioneer
import au21.engine.framework.database.AuEntityManager

class NoticeSaveCommand(
    val auction_id:String,
    val notice:String
) : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)

        val a: Auction = db.auction_or_alert(auction_id)

        return NoticeSaveAction(this, db, session, a, notice)
    }
}


class NoticeSaveAction(
    override val command: NoticeSaveCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val a:Auction,
    val notice:String
) : EngineAction {
    override fun mutate() {
        a.notice = notice
        db.save(a)
    }

}

```

## common/commands/page-set.kt

```kotlin
// File: page-set.kt

package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.framework.PageName
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail
import au21.engine.framework.commands.fail_if
import au21.engine.framework.database.AuEntityManager

// TODO: do we want 'SelectAuction' to be handled here instead?

class PageSetCommand(
    val page:PageName
) : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        val session = db.session_non_terminated_or_alert(session_id)
        fun throw_if_not_auctioneer(){
            if (!session.inRole(AuUserRole.AUCTIONEER))
                fail("Only auctioneers can access page: $page")
        }
        when(page){
            PageName.CREDITOR_AUCTIONEER_PAGE ->
                throw_if_not_auctioneer()
            PageName.CREDITOR_TRADER_PAGE -> {
                // TODO: why did we have this?
                // throw_if_not_auctioneer()
            }
            PageName.HOME_PAGE -> {
                fail_if(!session.is_logged_in(), "Please login first.")
            }
            PageName.LOGIN_PAGE -> {}
            PageName.SESSION_PAGE ->
                throw_if_not_auctioneer()
            PageName.USER_PAGE ->
                throw_if_not_auctioneer()
            PageName.DE_AUCTIONEER_PAGE -> {
                throw_if_not_auctioneer()
            }
            PageName.DE_SETUP_PAGE ->
                throw_if_not_auctioneer()
            PageName.DE_TRADER_PAGE ->
                fail_if(!session.is_trader(), "Only traders can see the trader page")
               // TODO: probably need to check that they can see this auction
            else -> fail("page not recognized: $page")
        }
        return PageSetAction(this, db, session, page)
    }
}


class PageSetAction(
    override val command: PageSetCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val page:PageName
) : EngineAction {
    override fun mutate() {
        session.set_page(page)
        db.save(session)
    }

}

```

## common/commands/session-terminate.kt

```kotlin
// File: session-terminate.kt
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail
import au21.engine.framework.commands.interfaces.ISessionsTerminated
import au21.engine.framework.commands.interfaces.SessionTermination
import au21.engine.framework.database.AuEntityManager


class SessionTerminateCommand(
    val reason: AuSession.SessionTerminationReason
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        // NOTE: this alert won't go anywhere, but will be logged
        val s = db.session_non_terminated_or_alert(session_id)

        // same here, just for logging
        if (s.termination_reason() != null) {
            fail("Session already terminated: $session_id, reason:${s.termination_reason()}")
        }

        // TODO:
        // we could check that you're logged in
        // - but probably we'd still terminate the session

        return SessionTerminateAction(this, db, s, reason)
    }

}

class SessionTerminateAction(
    override val command: SessionTerminateCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val reason: AuSession.SessionTerminationReason
) : EngineAction, ISessionsTerminated {

    override val sessions_terminated = mutableListOf<SessionTermination>()

    override fun mutate() {
        terminate_session(db, session, reason)
    }

}


```

## common/commands/user-delete.kt

```kotlin
// File: user-delete.kt
package au21.engine.domain.common.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Person
import au21.engine.domain.common.services.logged_in_session_for_user
import au21.engine.domain.common.services.open_auctions
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.framework.commands.*
import au21.engine.framework.commands.interfaces.ISessionsTerminated
import au21.engine.framework.commands.interfaces.SessionTermination
import au21.engine.framework.database.AuEntityManager

class UserDeleteCommand(
    val user_id:String
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)

        val p: Person = db.byId(user_id, include_deleted = true) ?: fail("no user found with id: $user_id")

        fail_if(p.deleted, "User has already been deleted.")

        //fail_if(db.open_auctions().any { it.has_trader(p.company) }, "User is a trader in an open auction, first remove them from that auction then delete.")
        fail_if(db.open_auctions().any { it.users_that_have_seen_auction.any { pp -> pp.person_id == p.id } } ,
            "User is a trader in an open auction, first remove them from that auction then delete.")

        // TODO: what if that's the last user in that company??

        return UserDeleteAction(this, db, session, p)
    }
}



class UserDeleteAction(
    override val command: UserDeleteCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val person:Person
) : ISessionsTerminated, EngineAction {

    override val sessions_terminated = mutableListOf<SessionTermination>()

    override fun mutate() {
        person.deleted = true
        db.save(person)
        db.logged_in_session_for_user(person)?.let { s ->
            terminate_session(db, s, AuSession.SessionTerminationReason.USER_DELETED)
        }
    }

}

```

## common/commands/user-save.kt

```kotlin
// File: user-save.kt
package au21.engine.domain.common.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.Company
import au21.engine.domain.common.model.Person
import au21.engine.domain.common.services.logged_in_session_for_user
import au21.engine.domain.common.services.open_auctions
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.common.services.user_by_username
import au21.engine.framework.commands.*
import au21.engine.framework.commands.interfaces.ISessionsTerminated
import au21.engine.framework.commands.interfaces.SessionTermination
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.database.is_same_entity
import au21.engine.framework.utils.is_blank

class UserSaveCommand(
    val company_id: String,
    val email: String,
//    val is_admin: String,
//    val is_observer: String,
//    val is_tester: String,
    val password: String,
    val phone: String,
    val role: AuUserRole,
    val user_id: String,
    val username: String
) : EngineCommand() {


    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        val is_create: Boolean = user_id.is_blank()

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)


        err_if(username.is_blank(), "Username cannot be blank.")
        err_if(password.is_blank(), "Password cannot be blank.")

        if (username.lowercase() == "admin")
            fail("admin role not supported.")

        val username_trimmed = username.trim()
        val password_trimmed = password.trim()

        err_if(username_trimmed.length > 10, "Username cannot be greater than 10 characters.")
        err_if(password_trimmed.length > 20, "Password cannot be greater than 20 characters.")

        fail_if(username == "mandory", "Username cannot be 'mandatory'")
        fail_if(username == "default", "Username cannot be 'default'")

        val c: Company? = db.byId(company_id)

        if (role == AuUserRole.TRADER) {
            if (company_id.is_blank()) {
                err_if(true, "Traders must have a company.")
            } else if (c == null) {
                err_if(true, "Traders must have a company, and none found with id: $company_id.")
            }
        }
        fail_if_errors()


        if (is_create) {

            fail_if(db.user_by_username(username_trimmed) != null, "Username taken already.")

            return UserSaveAction(
                this, db, session, Person(
                    company = c,
                    email = email,
                    password = password_trimmed,
                    phone = phone,
                    role = role,
                    username = username_trimmed
                )
            )

        } else {

            val existing: Person? = db.byId(user_id)

            fail_if(db.open_auctions().any { it.users_that_have_seen_auction.any { p -> p.person_id == existing?.id } } ,
                "User is a trader in an open auction, first remove them from that auction then delete.")

            if (existing == null) {
                fail("No user found with id: $user_id")
            } else {
                db.user_by_username(username_trimmed)?.let { u ->
                    // ie: we have a user with that username, but it's not the existing one
                    fail_if(
                        is_same_entity(u, existing),
                        "Another user has this username."
                    )
                }

                existing.also {
                    it.company = c
                    it.email = email
                    it.password = password_trimmed
                    it.phone = phone
                    it.role = role
                    it.username = username_trimmed
                }

                return UserSaveAction(this, db, session, existing)
            }

        }

    }
}


class UserSaveAction(
    override val command: UserSaveCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val person: Person
) : EngineAction, ISessionsTerminated {

    override val sessions_terminated = mutableListOf<SessionTermination>()

    override fun mutate() {
        db.save(person)
        db.logged_in_session_for_user(person)?.let { s ->
            terminate_session(db, s, AuSession.SessionTerminationReason.USER_DELETED)
        }
    }

}


/**

package au2019.engine.common.commands

override val session_id: SessionId,
val p: CommonUserSaveRequest) : AuSessionCommand() {

data class CommonUserSaveRequest(
// val sid: String, // TODO: removed -> regenerate generated.ts and fix client
// val credit_limit: Double?, // TODO: removed -> generated.ts
val company_id: String?,
val company_shortname: String?, // TODO: add -> gen
val company_longname: String?, // TODO add -> gen
val email: String?,
val is_admin: Boolean, // TODO: added -> generated.ts
val is_observer: Boolean,
val is_tester: Boolean,
val password: String?,
val phone: String?,
val role: AuUserRole,
val user_id: String?, // TODO: added -> generated.ts
val username: String?
) : AuParameter

val max_username_characters = 10

//    val Company_name_cannot_be_more_than_16_characters = "COMPANY cannot be more than 16 characters."
//    val Username_taken = "Username taken"
//    val Company_name_taken = "COMPANY name taken"
//    val User_account_has_been_inactivated_and_cannot_be_edited = "User account has been inactivated and cannot be edited."

init {

//    # Request Validation
//
//    - [x] error if username blank
//    - [x] error if password blank
//    - [x] error if username > 10 characters
//    - [x] error if another user has this username
//
//
//    If role is TRADER
//    - [x] error if company longname or shortname are blank
//    - [x] error if another user has this company longname or shortname
//
//    If role is TRADER and request is EDIT:
//    - [x] error if company_id is blank
//
//    If EDIT:
//    - [x] cannot switch roles.

err_if_blank(p::username)
err_if_blank(p::password)

if (p.role == TRADER) {
err_if_blank(p::company_shortname)
err_if_blank(p::company_longname)
}
fail_if_errors()

fail_if(p.username!!.length > max_username_characters,
"Username cannot be more than $max_username_characters characters.")

fail_if_errors()
}

val is_create: Boolean = p.user_id.is_blank()

val is_auctioneer: Boolean = p.role == AUCTIONEER
val is_trader: Boolean = p.role == TRADER

var u_by_id: USER? = db.by_id(p.user_id)
var c_by_id: COMPANY? = db.by_id(p.company_id)

val u_by_username: USER? = db.user_by_username(
p.username,
case_insensitive = true,
include_deleted = true)

val c_by_shortname: COMPANY? = db.company_by_shortname(
p.company_shortname,
case_insensitive = true,
include_deleted = false)

val c_by_longname: COMPANY? = db.company_by_longname(
p.company_longname,
case_insensitive = true,
include_deleted = false)

init {

fun same_user_error(msg: String): String = "Another active or deleted user has the same $msg (or case variation)."

when (is_create) {

true  -> {
// CREATION REQUEST:

// cannot have same username as existing user:
err_if(u_by_username != null, same_user_error("username"))

// if trader, cannot have same company shortname or longname
if (is_trader) {
err_if(c_by_shortname != null, same_user_error("company shortname"))
err_if(c_by_longname != null, same_user_error("company longname"))
}
}
false -> {
// EDIT REQUEST:

// must find user by id:
fail_if(p.user_id.is_blank(), "User id cannot be blank.")
fail_if_null(u_by_id, "No user found with id: ${p.user_id}")

// if trader, must find company by id
if (is_trader) {
fail_if(p.company_id.is_blank(), "Company id cannot be blank.")
fail_if_null(c_by_id, "No company found with id: ${p.company_id}")
}

// cannot have the same username or company long/short names as existing user
// unless ... the are this user !
err_unless(
u_by_username == null
|| u_by_username == u_by_id,
same_user_error("username"))

if (!is_auctioneer) {
err_unless(
c_by_shortname == null
|| c_by_shortname == c_by_id,
same_user_error("company shortname"))

err_unless(
c_by_longname == null
|| c_by_shortname == c_by_id,
same_user_error("company longname"))
}

// cannot switch roles
fail_if(u_by_id!!.role != p.role, "Cannot switch roles")

}
}

// TODO: check that not switching to a company if has been in a non-deleted auction

fail_if_errors()

}


override fun mutate() {
when (is_create) {
true  -> {
if (is_trader) {
c_by_id = COMPANY(
longname = p.company_longname!!,
shortname = p.company_shortname!!)
}
u_by_id = USER(
company = c_by_id,
email = p.email ?: "",
is_observer = false,
is_tester = false,
password = p.password!!,
phone = p.phone ?: "",
role = p.role,
username = p.username!!)
}
false -> {
if (is_trader) {
c_by_id?.apply {
longname = p.company_longname!!
shortname = p.company_shortname!!
}
}
u_by_id?.apply {
email = p.email ?: ""
is_observer = false
is_tester = false
password = p.password!!
phone = p.phone ?: ""
username = p.username!!
}
}
}

c_by_id?.let { db.save(it) }
u_by_id?.let { db.save(it) }
}
}



 */

```

## common/model/Activity.kt

```kotlin
// File: Activity.kt
package au21.engine.domain.common.model

import au21.engine.framework.database.AuEntity
import org.joda.time.DateTime
import java.util.*
import javax.jdo.annotations.Index
import javax.persistence.Entity

@Entity
class Activity(
    var heading: String,
    var body: MutableMap<String, String> = mutableMapOf(),  // one could be: "json": "<json>"
    var entities: MutableMap<String, AuEntity> = mutableMapOf() // so that we can pass entities
) : AuEntity() {

    @Index
    var timestamp: Date = DateTime().toDate()
        private set
}

```

## common/model/AuSession.kt

```kotlin
// File: AuSession.kt
package au21.engine.domain.common.model

import au21.engine.domain.common.model.AuSession.ClientSocketState.CLOSED
import au21.engine.domain.common.model.AuSession.ClientSocketState.OPENED
import au21.engine.framework.PageName
import au21.engine.framework.database.AuEntity
import au21.engine.framework.utils.toEnumOrError
import org.joda.time.DateTime
import java.util.*
import javax.persistence.Entity

@Entity
class AuSession(
    var session_id: String,
    var created: Date = DateTime().toDate(),
    var browser_name: String? = null,
    var browser_version: String? = null,
    var browser_os: String? = null
) : AuEntity() {

    enum class ClientSocketState {
        OPENED, CLOSED
    }

    enum class SessionTerminationReason {
        BROWSER_UNLOADED,
        COMPANY_DELETED,
        COMPANY_NAME_EDITED,
        FORCED_OFF, // eg if user or company edited
        LOGIN_FROM_ANOTHER_BROWSER,
        SERVER_REBOOT,
        SERVER_SWEPT_STALE_SESSION,
        SIGNED_OFF,
        USER_EDITED,
        USER_DELETED,
    }

//    enum class SessionStatus {
//        CREATED,
//        LOGGED_IN,
//        MANUAL_LOGOUT,
//        USER_DELETED,
//        SOCKET_DISCONNECTED,
//        SWEPT
//        /*
//            ONLINE,
//            AT_RISK,
//            LOGGED_OUT_MANUAL_SIGNOUT,
//            LOGGED_OUT_BROWSER_UNLOADED,
//            LOGGED_OUT_FAYE_DISCONNECT,
//            LOGGED_OUT_BIDDER_WITH_SAME_ID,
//            LOGGED_OUT_SWEPT
//        */
//    }



    var socket_state_label: String = OPENED.toString()
        private set

    var socket_state: ClientSocketState
        get() = socket_state_label.let { ClientSocketState.valueOf(it) }
        set(state) {
            this.socket_state_label = state.toString()
            when (state) {
                OPENED -> this.socket_last_closed = null
                CLOSED -> if (this.socket_last_closed == null)
                    this.socket_last_closed = DateTime().toDate()
            }
        }

    var socket_last_closed: Date? = null
        private set

    fun hasConnectionProblem(): Boolean =
        this.socket_state == CLOSED && !this.isTerminated() // TODO: Wifi Dropout detection via pings?

    var auction: Auction? = null
        private set

    //  @Index
//    var user_id: String? = null
//        private set

    // @Index
//    var company_id: String? = null
//        private set
//    var company_shortname: String? = null
//        private set
//    var company_longname: String? = null
//        private set

//    var role_label: String? = null
//        private set

    var user: Person? = null
        private set

//    var role: AuUserRole? = user?.role
//        get() = role_label?.let { AuUserRole.valueOf(it) }
//        set(r: AuUserRole?) {
//            this.role_label = r?.toString()
//        }


    fun is_trader(): Boolean = user?.isTrader() ?: false
    fun is_auctioneer(): Boolean = user?.isAuctioneer() ?: false

    // DO WE NEED THIS?
    fun inRole(vararg roles: AuUserRole): Boolean = roles.any { it == user?.role }


    var page_label: String = PageName.LOGIN_PAGE.toString()
        private set

    var page: PageName // mustn't be null
        get () = toEnumOrError<PageName>(page_label)
//        get() = enumValueOfWithDefault(page_label, PageName.LOGIN_PAGE)
        set(value) {
            this.page_label = value.toString()
        }

    fun set_page(p: PageName, a: Auction? = null) {
        // if you move off the auction page, then the auction is set to null, this is a change
        page = p
        auction = a
    }

    fun login(p: Person) {
        set_page(PageName.HOME_PAGE)
        user = p
//        if(user?.isTrader() == true && user?.company == null )
//            throw AlertException("all traders must have a company")
    }

// OLD: NOTE if a person changes company, while logged then MUST call this !!
//    fun set_user(p: Person) {
//        user = p
//        role = p.role
//        username = p.username
//        user_id = p.id_str()
//        company_id = p.company?.id_str()
//        company_shortname = p.company?.shortname
//        company_longname = p.company?.longname
//}


    fun is_logged_in(): Boolean = !isTerminated() && user != null

    // NOT NEEDED
    // - when auction deleted, session bounced to homepage
    // - when homepage is set, then auction will be set to null
//    fun remove_auction() {
//        // ie: if auction deleted, or if removed from auction
//        auction = null
//    }

    var last_ping: Date = DateTime().toDate()
        private set

    var termination_time: Date? = null
        private set

    var termination_reason_label: String? = null
        private set

    fun terminate(tr: SessionTerminationReason) {
        auction = null
        termination_time = DateTime().toDate()
        termination_reason_label = tr.toString()
        // TODO: not sure about adding the last two:
        socket_state = CLOSED
        socket_last_closed = DateTime().toDate()
    }

    // for some reason kotlin not happy with nullable enums!
    fun termination_reason(): SessionTerminationReason? =
        termination_reason_label?.let { SessionTerminationReason.valueOf(it) }

    fun isTerminated(): Boolean = termination_reason() != null

    fun ping() {
        last_ping = DateTime().toDate()
    }


}

```

## common/model/Auction.kt

```kotlin
// File: Auction.kt
package au21.engine.domain.common.model

import au21.engine.framework.database.AuEntity
import java.util.*
import javax.jdo.annotations.Index
import javax.persistence.Entity

// no longer abstract, so can find it more easily
@Entity
abstract class Auction(
    var auction_name: String,
) : AuEntity() {

    @Index
    var closed: Boolean = false
        protected set

    @Index
    var hidden: Boolean = false

    var notice: String = ""

    var starting_time: Date? = null

    var auction_has_started: Boolean = false

    var common_state_text: String = ""
    var auctioneer_state_text: String = ""

    abstract fun starting_time_text(): String

    protected var trading_companies: MutableList<CompanyProxy> = mutableListOf()
        private set

    fun has_trader(c: Company?): Boolean =
        when (c) {
            null -> false
            else -> trading_companies.any { it.company_id == c.id }
        }

    fun show_auction(s: AuSession?): Boolean =
        when {
            s == null -> false
            s.user?.role == AuUserRole.AUCTIONEER -> true
            this.hidden -> false
            this.has_trader(s.user?.company) -> true
            else -> false
        }

    var messages: MutableList<AuctionMessage> = mutableListOf()
        private set

    var users_that_have_seen_auction: MutableSet<PersonProxy> = mutableSetOf()
        private set


    var companies_that_have_seen_auction: MutableSet<CompanyProxy> = mutableSetOf()
        private set

    fun add_trader_that_has_seen_auction(s: AuSession) {
        s.user?.let { u: Person ->
            users_that_have_seen_auction.add(PersonProxy(u))
            u.company?.let { c: Company ->
                trading_companies.find { it.company_id == c.id }
                    ?.let {
                        companies_that_have_seen_auction.add(it)
                    }
            }
        }
    }

    // Counterparty credit:
    var counterparty_credit_limits: MutableList<CounterpartyCreditLimit> = mutableListOf()
        private set

    fun get_counterparty_credit_limit(lender: CompanyProxy, borrower: CompanyProxy): CounterpartyCreditLimit? =
        counterparty_credit_limits.find {
            it.lender == lender && it.borrower == borrower
        }

    fun set_counterparty_credit_limit(lender: CompanyProxy, borrower: CompanyProxy, limit: Double?) {
        (get_counterparty_credit_limit(lender, borrower)
            ?: run {
                CounterpartyCreditLimit(lender, borrower).also {
                    counterparty_credit_limits.add(it)
                }
            }).set_credit_limit(limit)
    }

    //     var state : IAuctionState
//        get() = enumValueOf(state_label) // enumValueOrNull<TeAuctionState>(state_string)!!
//        set(value) {
//            state_label = value.toString()
//        }

    // Another way of doing enums in general
//    var state_label: String = DeState.AUCTION_INIT.toString()
//        private set
//
//    var state: DeState
//        get() = DeState.valueOf(state_label)
//        set(it) {
//            this.state_label = it.toString()
//        }


    // 2) state label (ie: state could be open_for_bidding, and label could be "open for 20:00:00" etc
    // abstract var state_label: String


}

```

## common/model/AuctionMessage.kt

```kotlin
// File: AuctionMessage.kt
package au21.engine.domain.common.model

import au21.engine.domain.common.model.AuMessageType.*
import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.AuFormatter
import org.joda.time.DateTime
import java.util.*
import javax.persistence.Embeddable

@Embeddable
class AuctionMessage(
    message_type_: AuMessageType,
    message_: String,
    from_user_: PersonProxy? = null,
    from_company_:CompanyProxy? = null,
    to_company_: CompanyProxy? = null
) {

    var message_type: AuMessageType = message_type_
        private set

    var message: String = message_
        private set

    var from_user: PersonProxy? = from_user_
        private set

    var from_company: CompanyProxy? = from_company_
        private set

    var to_company: CompanyProxy? = to_company_
        private set

    init {

        fun check_from_auctioneer() {
            if (from_user == null || from_user!!.role_at_auction_item != AuUserRole.AUCTIONEER)
                throw AlertException("Message type $message_type can only be sent by auctioneers")
        }

        fun check_from_trader() {
            if (from_user == null || from_user!!.role_at_auction_item != AuUserRole.TRADER)
                throw AlertException("Message type $message_type can only be sent by a trader.")
        }

        fun check_to_trader() {
            if (to_company == null)
                throw AlertException("Message type $message_type can only be sent to traders.")
        }

        fun check_fromUser_null() {
            if (from_user != null)
                throw AlertException("Message type $message_type should have blank 'from'.")
        }

        fun check_toCompany_null() {
            if (from_user != null && to_company != null)
                throw AlertException("Message type $message_type should have blank 'to' company.")
        }

        when (message_type) {
            AUCTIONEER_BROADCAST -> {
                check_from_auctioneer()
                check_toCompany_null()
            }
            AUCTIONEER_TO_TRADER -> {
                check_from_auctioneer()
                check_to_trader()
            }
            TRADER_TO_AUCTIONEER -> {
                check_from_trader()
                check_toCompany_null()
            }
            SYSTEM_BROADCAST -> {
                check_fromUser_null()
                check_toCompany_null()
            }
            SYSTEM_TO_TRADER -> {
                check_fromUser_null()
                check_to_trader()
            }
            SYSTEM_TO_AUCTIONEER -> {
                check_fromUser_null()
                check_toCompany_null()
            }
        }
    }

    var body: String = message
        private set

    var message_type_label: String = message_type.toString()
        private set

    // we never persist enums, as the order could change
    fun messageType() = valueOf(message_type_label)

    // LABEL:

    var from_label: String =
        when (messageType()) {
            AUCTIONEER_BROADCAST -> "Auctioneer"
            AUCTIONEER_TO_TRADER -> "Auctioneer"
            // TOOD: check that this is tested above when initialized:
            TRADER_TO_AUCTIONEER -> "${from_user!!.username_at_auction_time} (${from_company!!.shortname_at_auction_time})"
            SYSTEM_BROADCAST -> "System"
            SYSTEM_TO_TRADER -> "System"
            SYSTEM_TO_AUCTIONEER -> "System"
        }
        private set

    var to_label: String =
        when (messageType()) {
            AUCTIONEER_BROADCAST -> "all"
            AUCTIONEER_TO_TRADER -> to_company!!.shortname_at_auction_time
            TRADER_TO_AUCTIONEER -> "auctioneer"
            SYSTEM_BROADCAST -> "all"
            SYSTEM_TO_TRADER -> to_company!!.shortname_at_auction_time
            SYSTEM_TO_AUCTIONEER -> "auctioneer"
        }
        private set

    var timestamp: Date = DateTime().toDate()
        private set
    var timestamp_label: String = AuFormatter.date_time_format(timestamp)
        private set

// var message_type: AuMessageType by lazy { AuMessageType.valueOf(message_type_name) }


}

```

## common/model/Company.kt

```kotlin
// File: Company.kt
package au21.engine.domain.common.model

import au21.engine.framework.commands.AlertException
import au21.engine.framework.database.AuEntity
import javax.persistence.Embeddable
import javax.persistence.Entity

// rules entites don't reference other entities, are coherent


@Embeddable
abstract class CompanyProxy(c:Company){
    var company_id:Long = c.id
        private set
    var shortname_at_auction_time:String = c.shortname
    var longname_at_auction_time:String = c.longname
}


@Entity
class Company(
    var longname: String,
    var shortname: String
) : AuEntity() {

    init {
        // NB: remember to trim all command inputs !!
        if (shortname.trim() == "")
            throw AlertException("Company short name cannot be blank.")

        if (shortname.trim().length > 10) {
            throw AlertException("Company short name cannot be greater than 10 characters: $shortname")
        }
    }


}

```

## common/model/CounterpartyCreditLimit.kt

```kotlin
// File: CounterpartyCreditLimit.kt
package au21.engine.domain.common.model

import au21.engine.framework.utils.AuFormatter
import javax.persistence.Embeddable

@Embeddable
class CounterpartyCreditLimit(
    lender_: CompanyProxy,
    borrower_: CompanyProxy) {

    companion object {
        const val no_limit = "no limit"
        const val none = "none"
    }

    var lender: CompanyProxy = lender_
        private set

    var borrower: CompanyProxy = borrower_
        private set

    var credit_limit: Double? = null
        private set

    var credit_limit_str: String? = null
        private set

    fun set_credit_limit(limit: Double?) {
        this.credit_limit = limit
        this.credit_limit_str = when (limit) {
            null -> no_limit
            else -> AuFormatter.format_currency(limit)
        }
    }
}

```

## common/model/Person.kt

```kotlin
// File: Person.kt
package au21.engine.domain.common.model


import au21.engine.framework.database.AuEntity
import au21.engine.framework.utils.toEnumOrError
import javax.persistence.Embeddable
import javax.persistence.Entity

@Embeddable
class PersonProxy(p: Person) {
    var person_id: Long = p.id
        private set
    var username_at_auction_time: String = p.username
    var role_at_auction_item:AuUserRole = p.role
}

@Entity
class Person(
    role: AuUserRole,
    var username: String, // if edit this, then must edit the proxy username at auction time for all open auctions
    var password: String,
    var company: Company? = null,
    var email: String = "",
    var isObserver: Boolean = false,
    var isTester: Boolean = false,
    var phone: String = ""
) : AuEntity() {

    var role_label: String = role.toString()
        private set

    var role: AuUserRole // mustn't be null
        get() = toEnumOrError(role_label)
        set(r) {
            role_label = r.toString()
//            if(r == AuUserRole.AUCTIONEER && company == null)
//                throw AlertException("Traders cannot have a null company")
        }

    // TODO: should these be in extensions?:

    fun inRole(vararg roles: AuUserRole): Boolean =
        roles.contains(role)

    fun isTrader(): Boolean = inRole(AuUserRole.TRADER)
    fun isAuctioneer(): Boolean = inRole(AuUserRole.AUCTIONEER)
  //  fun isAdmin(): Boolean = inRole(AuUserRole.ADMIN)

}



```

## common/model/enums.kt

```kotlin
// File: enums.kt
package au21.engine.domain.common.model

//enum class AuctionDesign {
//    BACKHAUL,
//    DOUBLE,
//    MULTIROUND,
//    TRANSPORT_ENGLISH,
//    PIPELINE,
//    TRANSPORT_OPTIMIZATION
//}


//inline fun <reified T> DeAuction.setState(
//        state: T,
//        cb: ((T) -> DeAuction) = { this })
//        where
//        T : Enum<T>,
//        T : IAuctionState {
//    this.state_label = state.toString()
//    cb(state)
//}
//
//inline fun <reified T> DeAuction.getState(): T
//        where
//        T : Enum<T>,
//        T : IAuctionState = enumValueOf(state_label)
//
//inline fun <reified T> DeAuction.inState(vararg states: T): Boolean
//        where
//        T : Enum<T>,
//        T : IAuctionState = states.contains(getState())

inline fun <reified T> String.enumValueOfWithTrace():T
        where
        T : Enum<T> {
    try {
        return enumValueOf(this)
    }
    catch (e:Throwable){
        println("Unable to get ${T::class.simpleName} from $this")
        throw e
    }
}

enum class AutopilotMode {
    DISENGAGED,
    ENGAGED
}

enum class Crud {
    CREATE,
    READ,
    UPDATE,
    DELETE,
    ADD,
    REMOVE,
    CLEAR
}

enum class AuMessageType {
    AUCTIONEER_BROADCAST,  // Auctioneer to all traders
    AUCTIONEER_TO_TRADER,  // Auctioneer to 1 trader (probably should implement as per Zoom, also, should be for 1 Company)
    TRADER_TO_AUCTIONEER,  // 1 Trader to Auctioneer
    SYSTEM_BROADCAST,      // System to all traders and auctioneers
    SYSTEM_TO_TRADER,      // System to 1 trader (or trading Company when implemented)
    SYSTEM_TO_AUCTIONEER   // System to auctioneers
}

enum class AuctionInstruction {
    HIDE, UNHIDE, DELETE
}

enum class AuUserRole {
    //  ADMIN, // NOTE: there are various tests for AUCTIONEER or alert
    AUCTIONEER, // can be observers
    TRADER      // can be testers
}

enum class Operator(var label: String) {
    GT("greater than"),
    GE("greater than or equal to");

    fun check(x: Double, limit: Double): Boolean =
        when (this) {
            GT -> x > limit
            GE -> x >= limit
        }

    fun check(x: Int, limit: Int): Boolean =
        when (this) {
            GT -> x > limit
            GE -> x >= limit
        }
}

enum class OrderType { BUY, SELL, NONE }

enum class OrderSubmissionType { MANUAL, DEFAULT, MANDATORY }

enum class PriceDirection { UP, DOWN }

//    var price_direction: PriceDirection
//        get() = valueOf(price_direction_label)
//        set(value) {
//            price_direction_label = value.name
//        }


enum class ActivityRule { ABSOLUTE, RATIO }


enum class StopMode {
    LT, LE, NONE;

    fun check(x: Int, limit: Int): Boolean =
        when (this) {
            StopMode.LT -> x < limit
            StopMode.LE -> x <= limit
            NONE -> false
        }
}

enum class Visibility { ALL, FIRST_ROUND, ELIGIBILITY }

```

## common/model/extensions.kt

```kotlin
// File: extensions.kt
package au21.engine.domain.common.model

inline fun <reified T : Auction> AuSession.get_auction(): T = this as T

/*
    COMPANY extensions
 */


fun Company?.fullname(): String =
    when (this) {
        null -> ""
        else -> "$longname + ($shortname)"
    }


//fun AuSession?.has_username(username: String) =
//        this?.person?.username.equals(username) ?: false

/*
     USER extensions
 */

fun Person.label(): String =
    this.username + this.company?.let { c -> " (${c.shortname})" }




```

## common/services/queries.kt

```kotlin
// File: queries.kt
package au21.engine.domain.common.services

import au21.engine.domain.common.model.*
import au21.engine.framework.commands.AlertException
import au21.engine.framework.database.AuEntityManager
import io.quarkus.logging.Log


/*
      Database extensions
 */

fun AuEntityManager.open_auctions(): List<Auction> =
    try {
        val query = "SELECT a FROM ${Auction::class.java.name} a WHERE a.deleted=false AND a.closed=false"
        em.createQuery(query, Auction::class.java).resultList
    } catch (e: Exception) {
        Log.error(e.message)
        listOf()
    }

inline fun <reified A : Auction> AuEntityManager.auction_or_alert(auction_id: String?): A =
    auction_id
        ?.let { this.byId(auction_id) }
        ?: throw  AlertException("no auction found with id: $auction_id")


/**
 * Command related (ie: throw errors)
 */

/** SESSION */

private fun AuEntityManager.non_terminated_sessions(
    logged_in_only: Boolean,
    role: AuUserRole? = null
): List<AuSession> {

    val non_terminated_sessions: List<AuSession> =
        query("select s from AuSession s WHERE s.deleted = false AND s.termination_reason_label == null")

    val first_filter =
        if (logged_in_only)
            non_terminated_sessions.filter { it.user != null }
        else
            non_terminated_sessions

    val second_filter = role?.let {
        first_filter.filter { it.inRole(role) }
    } ?: first_filter

    return second_filter
}

//    try {
//        val cb: CriteriaBuilder = em.criteriaBuilder
//        val cq: CriteriaQuery<AuSession> = cb.createQuery(AuSession::class.java)
//        val root: Root<AuSession> = cq.from(AuSession::class.java)
//
//        val predicates = mutableListOf(
//            cb.isTrue(root.get("deleted")),
//            cb.isNull(root.get<String?>("termination_reason_label"))
//        )
//
//        if (logged_in_only)
//            predicates.add(cb.isNotNull(root.get<String>("user_id")))
//
//        role?.let {
//            predicates.add(cb.equal(root.get<String>("role_label"), it.toString()))
//        }
//        cq.select(root).where(*predicates.toTypedArray())
//        em.createQuery(cq).resultList
//    } catch (t: Throwable) {
//        t.cause?.let { Logger.error(it.message.toString()) }
//        Logger.error(t.message.toString())
//        listOf()
//    }

fun AuEntityManager.sessions_non_terminated(): List<AuSession> =
    non_terminated_sessions(logged_in_only = false)

fun AuEntityManager.sessions_logged_in(): List<AuSession> =
    non_terminated_sessions(logged_in_only = true)

fun AuEntityManager.sessions_logged_in_auctioneers(): List<AuSession> =
    non_terminated_sessions(logged_in_only = true, AuUserRole.AUCTIONEER)

fun AuEntityManager.sessions_logged_in_traders(): List<AuSession> =
    non_terminated_sessions(logged_in_only = true, AuUserRole.TRADER)

fun AuEntityManager.session_by_sid(sid: String?): AuSession? =
    findFirst {
        // Loginfo("looking for: $sid, found: ${it.session_id}")
        it.session_id == sid
    }

fun AuEntityManager.logged_in_session_for_user(u: Person?): AuSession? =
    u?.let {
        this.non_terminated_sessions(logged_in_only = true)
            .find { s: AuSession -> s.user == u }
    }

fun AuEntityManager.is_logged_in(p: Person?): Boolean =
    this.logged_in_session_for_user(p) != null


fun AuEntityManager.session_non_terminated_or_alert(session_id: String?): AuSession =
    when (val s: AuSession? = session_by_sid(session_id)) {
        null -> throw AlertException("no session found with id: $session_id")
        else ->
            if (s.isTerminated())
                throw AlertException("Session already terminated.")
            else
                s
    }

fun AuEntityManager.auctioneer_session_or_alert(session_id: String?): AuSession =
    session_non_terminated_or_alert(session_id)
        .takeIf { it.is_auctioneer() }
        ?: throw AlertException("Only auctioneers can perform this action.")


// TODO: implement JPA Criteria API here instead:
//fun AuEntityManager.sessions_by_sid(sessionIds: List<String>): List<AuSession> =
//    filter { it.logged_in }

fun AuEntityManager.user_by_username(
    username: String?,
    case_insensitive: Boolean = true,
    include_deleted: Boolean = false
): Person? =
    username?.let {
        findFirst(include_deleted) {
            if (case_insensitive)
                it.username.equals(username, ignoreCase = true)
            else
                it.username == username
        }
    }


fun AuEntityManager.traders(): List<Person> =
    findAll<Person>().filter { it.isTrader() }

fun AuEntityManager.company_by_shortname(
    shortname: String?,
    case_insensitive: Boolean = true,
    include_deleted: Boolean = false
): Company? =
    shortname?.let {
        findFirst(include_deleted) {
            if (case_insensitive)
                it.shortname.equals(shortname, ignoreCase = true)
            else it.shortname == shortname
        }
    }

fun AuEntityManager.company_by_longname(
    longname: String?,
    case_insensitive: Boolean = true,
    include_deleted: Boolean = false
): Company? =
    longname?.let {
        findFirst(include_deleted) {
            if (case_insensitive)
                it.longname.equals(longname, ignoreCase = true)
            else it.longname == longname
        }
    }

fun AuEntityManager.company_users(c: Company): List<Person> =
    findAll<Person>().filter { it.company == c }


inline fun <reified T : Auction> AuEntityManager.find_auction_by_name(
    auction_name: String?,
    case_insensitive: Boolean = true,
    include_deleted: Boolean = false
): T? =
    auction_name?.let {
        findFirst(include_deleted) {
            if (case_insensitive)
                it.auction_name.equals(auction_name, ignoreCase = true)
            else
                it.auction_name == auction_name
        }
    }

//
//fun AuDb.counter_parties(lender: Company, borrower: Company): COUNTER_PARTY_CREDIT_LIMIT? =
//    this.filter<COUNTER_PARTY_CREDIT_LIMIT> { it.lender == lender && it.borrower == borrower }
//        .maxBy { it.timestamp } // should get the highest one
//
//fun counterparty_credit_vol_limit(
//    limit: COUNTER_PARTY_CREDIT_LIMIT, // dollars of credit from seller to buyer
//    price: Double, // ie: round price
//    price_volume_to_currency_converter: Double // e.g: price * volume * converter = dollars
//): Int {
// e.g: cents/lb * dollars/cent * MMlb/lb
// = cpp * 0.01 * 1 million
// = cpp * 10,000
// so limit is dollars / (cpp * 10,000)
//
//return (price * price_volume_to_currency_converter).let { denominator: Double ->
//    if (denominator == 0.0)
//        0
//    else
//        (limit.credit_limit / denominator).toInt()
//}
//
//}
//

//// TODO: does not take into account seller eligibility, buyer min and buyer eligibility
//
//

```

## common/viewmodel/AuctionRowElement.kt

```kotlin
// File: AuctionRowElement.kt
package au21.engine.domain.common.viewmodel

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Auction
import au21.engine.framework.client.StoreElement

class AuctionRowTraders(val element: AuctionRowElement)

data class AuctionRowElement(
    override val id: String,
    val auction_design: String,
    val auction_id: String,
    val auction_name: String,
    val isClosed: Boolean,
    val common_state_text: String,
    val isHidden: Boolean,
    val starting_time_text: String,
    // used:
    // - on home page by both traders and auctioneers
    // - on trader page's header
) : StoreElement {


    companion object {
        fun create(a: Auction): AuctionRowElement =
            AuctionRowElement(
                id = a.id_str(),
                auction_design = a::class.java.simpleName,
                auction_id = a.id_str(),
                auction_name = a.auction_name,
                isClosed = a.closed,
                isHidden = a.hidden,
                starting_time_text = a.starting_time_text(),
                common_state_text = a.common_state_text,
            )

        // AUCTIONEERS: elements
//        fun all_elements(db: AuEntityManager, is_auctioneer: Boolean): List<AuctionRowElement> =
//            db.findAll<Auction>()
//                .map { AuctionRowElement(it, is_auctioneer) }


        // NON_AUCTIONEERS:

        // all the auction rows for one person:
        fun elements_for_session(
            auctions: List<Auction>,
            s: AuSession?
        ): List<AuctionRowElement> =
            auctions
                .filter { a -> a.show_auction(s) }
                .map { create(it) } //, s?.inRole(AuUserRole.AUCTIONEER) == true) }

    }
}

```

## common/viewmodel/CompanyElement.kt

```kotlin
// File: CompanyElement.kt
package au21.engine.domain.common.viewmodel


import au21.engine.domain.common.model.Company
import au21.engine.framework.client.StoreElement
import au21.engine.framework.database.AuEntityManager

data class CompanyElement(
    override val id: String,
    val company_id: String,
    val company_longname: String,
    val company_shortname: String,
//    val credit_limit: String = none,
//    val default_risk: String = none
) : StoreElement {


    companion object {

        fun create(c: Company) = CompanyElement(
            id = c.id_str(),
            company_id = c.id_str(),
            company_longname = c.longname,
            company_shortname = c.shortname
        )

        fun all(db: AuEntityManager): List<CompanyElement> =
            db.findAll<Company>().map { create(it) }

    }
}


```

## common/viewmodel/CounterpartyCreditElement.kt

```kotlin
// File: CounterpartyCreditElement.kt
package au21.engine.domain.common.viewmodel

import au21.engine.domain.common.model.Company
import au21.engine.framework.client.StoreElement
import au21.engine.framework.database.is_same_entity

data class CounterpartyCreditElement(
    override val id: String,
    val seller_id: String,
    val seller_longname: String,
    val seller_shortname: String,
    val buyer_id: String,
    val buyer_longname: String,
    val buyer_shortname: String,
    //  val limit: Double?,
    val limit_str: String
) : StoreElement {

    // NOTE: removing long names and shortnames from this class
    // - means that we need to send companies to traders too.
    // - however, if we ever want to add sensitive info to CompanyElement (ie: other than name)
    //  - and/or if we want to limit which counterparties get sent to traders, then that could be a problem.

    companion object {

        fun create(
           // auction: Auction,
            seller: Company,
            buyer: Company
        ): CounterpartyCreditElement {

            //  val limit = seller.get_credit_limit(buyer)

            return CounterpartyCreditElement(
                id = id_str(seller, buyer),
                seller_id = seller.id_str(),
                seller_longname = seller.longname,
                seller_shortname = seller.shortname,
                buyer_id = buyer.id_str(),
                buyer_longname = buyer.longname,
                buyer_shortname = buyer.shortname,
                //   limit = limit,
                limit_str = "TODO" // auction.get_credit_limit(seller, buyer)?.buyer_credit_limit_str ?: CounterpartyCreditLimit.no_limit
            )
        }

        fun id_str(creditor: Company, debtor: Company) =
            "SELLER.${creditor.id_str()}.BUYER.${debtor.id_str()}"

//        fun for_session(db:AuSession, s: AuSession, reset:Boolean) = List<ClientCommand.StoreCommand> =
//            listOf()

        fun all(companies: List<Company>): List<CounterpartyCreditElement> =
            companies.map { seller: Company ->
                // TODO: not sure if we should be filtering?
                companies.filterNot { is_same_entity(it, seller) }
                    .map { buyer: Company ->
                        create(seller, buyer)
                    }
            }.flatten()

    }

}

//    fun onAction(e: CompanyDeleteAction, h: ClientCommandHelper) {
//    fun onAction(e: CompanySaveAction, h: ClientCommandHelper) {
//    fun onAction(e: LoginAction, db: AuEntityManager, h: ClientCommandHelper) {

```

## common/viewmodel/DateTimeValue.kt

```kotlin
// File: DateTimeValue.kt
package au21.engine.domain.common.viewmodel

import au21.engine.framework.client.StoreValue
import org.joda.time.DateTime
import java.util.*

data class DateTimeValue(
    val year: Int,
    val month: Int,// NB: month is new ZERO based !! (but DateTime is 1 based)
    val day_of_week: Int,
    val day_of_month: Int,
    val hour: Int,
    val minutes: Int,
    val seconds: Int,
) : StoreValue {

    fun toDate(): Date = DateTime(
        year,
        month + 1, // because Joda month is 1-based and DateTimeValue is 0-based
        day_of_month,
        hour,
        minutes,
        seconds
    ).toDate()

    companion object {
        fun create(dt: DateTime): DateTimeValue =
            DateTimeValue(
                year = dt.year,
                month = dt.monthOfYear - 1, // NB: month is new ZERO based !! (but DateTime is 1 based)
                day_of_week = dt.dayOfWeek,
                day_of_month = dt.dayOfMonth,
                hour = dt.hourOfDay,
                minutes = dt.minuteOfHour,
                seconds = dt.secondOfMinute
            )
    }
}

```

## common/viewmodel/MessageElement.kt

```kotlin
// File: MessageElement.kt
package au21.engine.domain.common.viewmodel

import au21.engine.domain.common.model.*
import au21.engine.domain.common.model.AuMessageType.*
import au21.engine.framework.client.StoreElement
import au21.engine.framework.database.is_same_entity


data class MessageElement(
    override val id: String,
    val from: String,
    val message: String,
    val message_type: AuMessageType,
    val message_type_label: String,
    val timestamp: Long,
    val timestamp_label: String,
    val to: String
) : StoreElement {

    companion object {

        fun create(m: AuctionMessage) =
            MessageElement(
                id = "AuctionMessage." + m.timestamp.time,
                from = m.from_label,
                message = m.body,
                message_type = m.messageType(),
                message_type_label = m.messageType().toString(),
                timestamp = m.timestamp.time,
                timestamp_label = m.timestamp_label,
                to = m.to_label
            )


//        fun clear(h: ClientCommandHelper, vararg sids: String) {
//            MessageElement::class.java.clear_all(h, *sids)
//        }

        // doesn't remove recipient !!
        fun is_for(auction: Auction, m: AuctionMessage, s: AuSession?): Boolean =
            s?.auction?.let { a ->
                when {
                    !is_same_entity(a, auction) -> false
                    s.user == null -> false // no one to send to
                    s.inRole(AuUserRole.AUCTIONEER) -> true  // auctioneers see everything
                    else ->
                        // ie: for TRADERS:
                        when (m.messageType()) {
                            SYSTEM_TO_AUCTIONEER ->
                                false // never seen by traders.
                            AUCTIONEER_BROADCAST ->
                                true // all sessions on the auction see the auctioneer message
                            SYSTEM_BROADCAST ->
                                true
                            TRADER_TO_AUCTIONEER -> // only if from that trader
                                s.user?.company?.let { it.id == m.from_company?.company_id}?:false // only y if from that trader
                            SYSTEM_TO_TRADER -> // only if to that trader
                                s.user?.company?.let { it.id == m.to_company?.company_id}?:false
                            AUCTIONEER_TO_TRADER -> // again, only if to that trader
                                s.user?.company?.let { it.id == m.to_company?.company_id}?:false
                        }
                }
            } ?: false

        fun recipient_sids_for_message(
            sessions: List<AuSession>,
            a: Auction,
            m: AuctionMessage
        ): List<String> = sessions
            .filter { s -> is_for(a, m, s) }
            .map { it.session_id }
        // .toTypedArray()

//        fun send_message(a: Auction, m: AuctionMessage, h: ClientCommandHelper) {
//            recipient_sids_for_message(h.auction_sessions(a), a, m).let { recipients ->
//
//                // (1) send message to recipients
//                MessageElement(m).send(h, *recipients)
//
//                // (2)
////        - [ ] Send notification to all sessions receiving the message,
////          - EXCEPT for the current session
////          - (no point in notifying yourself, you'll see it in the message list)
//
//                // TODO: note we don't send notification to the sender
//                h.notify(listOf(m.body), *recipients.filter { it != h.sid }.toTypedArray())
//            }
//        }

        fun message_elements_for_session(a: Auction, s: AuSession?): List<MessageElement> =
            a.messages
                .filter { m -> is_for(a, m, s) }
                .map { m -> create(m) }

    }
}

```

## common/viewmodel/SessionUserValue.kt

```kotlin
// File: SessionUserValue.kt
package au21.engine.domain.common.viewmodel

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.framework.PageName
import au21.engine.framework.client.StoreValue


data class SessionUserValue(
    val company_id: String,
    val company_shortname: String,
    val company_longname: String,
    val current_auction_id: String,
    val current_page: PageName,
    val isAuctioneer: Boolean,
    val isOnline: Boolean,
    val role: AuUserRole?,
    val session_id: String,
    val socket_state: AuSession.ClientSocketState,
    val user_id: String,
    val username: String,
) : StoreValue {

    companion object {
        fun create(s: AuSession): SessionUserValue =
            SessionUserValue(
                company_id = s.user?.company?.id_str() ?: "",
                company_shortname = s.user?.company?.shortname ?: "",
                company_longname = s.user?.company?.longname ?: "",
                current_auction_id = s.auction?.id_str() ?: "",
                current_page = s.page,
                isAuctioneer = s.inRole(AuUserRole.AUCTIONEER),
                isOnline = !s.isTerminated(),
                role = s.user?.role,
                session_id = s.session_id,
                socket_state = s.socket_state,
                user_id = s.user?.id_str() ?: "",
                username = s.user?.username ?: ""
            )
    }
}


```

## common/viewmodel/TimeValue.kt

```kotlin
// File: TimeValue.kt
package au21.engine.domain.common.viewmodel

import au21.engine.framework.client.StoreValue
import org.joda.time.DateTime

data class TimeValue(
    val city: String,
    val date_time: DateTimeValue
) : StoreValue {
    companion object {

        fun now(city: String): TimeValue =
            TimeValue(city, DateTimeValue.create(DateTime()))
    }
}


```

## common/viewmodel/UserElement.kt

```kotlin
// File: UserElement.kt
package au21.engine.domain.common.viewmodel

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.Person
import au21.engine.framework.client.StoreElement
import au21.engine.framework.database.AuEntityManager
import java.util.*


data class UserElement(
    override val id: String,

    // should get these from the company element:
    val company_id: String,
    val company_longname: String,
    val company_shortname: String,

    val email: String,
    val isAuctioneer: Boolean,
    val isObserver: Boolean,
    val isTester: Boolean,
    val password: String,
    val phone: String,
    val role: AuUserRole,
    val username: String,
    val user_id: String,

    // FOR TRADER ICON:
    val isOnline: Boolean,
    val current_auction_id: String?,
    val termination_reason: AuSession.SessionTerminationReason?,
    val socket_state: AuSession.ClientSocketState?,
    val socket_state_last_closed: Date?,
    val has_connection_problem: Boolean

) : StoreElement {


    companion object {

        fun create(u: Person, online_session: AuSession?): UserElement {

            return UserElement(
                id = u.id_str(),

                // should get these from the company elements:
                company_id = u.company?.id_str() ?: "",
                company_longname = u.company?.longname ?: "",
                company_shortname = u.company?.shortname ?: "",

                email = u.email,
                isAuctioneer = u.isAuctioneer(),
                isObserver = u.isObserver,
                isTester = u.isTester,
                password = u.password,
                phone = u.phone,
                role = u.role,
                username = u.username,
                user_id = u.id_str(),

                // FOR TRADER ICON:
                isOnline = online_session?.let { !it.isTerminated() } ?: false,
                current_auction_id = online_session?.auction?.id_str(),
                termination_reason = online_session?.termination_reason(),
                socket_state = online_session?.socket_state,
                socket_state_last_closed = online_session?.socket_last_closed,
                has_connection_problem = online_session?.hasConnectionProblem() ?: false
            )

        }

        fun user_elements(db: AuEntityManager, logged_in_sessions: List<AuSession>) =
            db.findAll<Person>().map { u: Person ->
                create(u, logged_in_sessions.find { it.user == u })
            }
    }
}

```

## de/commands/de-auction-award.kt

```kotlin
// File: de-auction-award.kt
package au21.engine.domain.de.commands


import au21.engine.domain.common.model.*
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeRound
import au21.engine.domain.de.model.DeTradingCompany
import au21.engine.domain.de.model.format_round_price
import au21.engine.framework.commands.*
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.thousands

class DeAuctionAwardCommand(
    val auction_id: String,
    val round_number: String,
   // val allocations: Map<String, String>
) : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)


        val de: DeAuction = db.auction_or_alert(auction_id)

        val deRound: DeRound = round_number.toIntOrNull()?.let { num ->
            de.rounds.find { it.number == num }
        } ?: fail("Unable to find round with number: $round_number")

//        val allocations_actual: MutableMap<DeAuction.Trader, Int> = mutableMapOf()
//
//        allocations.forEach { entry ->
//
//            val c: Company? = db.byId(entry.key)
//            val t: DeAuction.Trader? = de.getTrader(c)
//            val alloc: Int? = entry.value.toIntOrNull()
//
//            if (c == null) {
//
//                err_if(true, "Unable to find company with id: ${entry.key}")
//
//            } else if (t == null) {
//
//                err_if(true, "Company is not a trader is this auction: ${c.shortname}")
//
//            } else if (alloc == null || alloc < 0) {
//
//                err_if(true, "Company: ${c.shortname} allocation not a whole number > 0: $alloc")
//
//            } else {
//
//                allocations_actual[t] = alloc
//            }
//        }

        fail_if_errors()

        return DeAuctionAwardAction(this, db, session, de, deRound)
    }
}


class DeAuctionAwardAction(
    override val command: DeAuctionAwardCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de: DeAuction,
    val deRound: DeRound,
 //   val allocations: Map<DeAuction.Trader, Int>
) : EngineAction {

    val messages: MutableList<AuctionMessage> = mutableListOf()

    override fun mutate() {

        val award_price = de.format_round_price(AuUserRole.AUCTIONEER, deRound)

//        allocations.forEach { entry ->
//            val t: DeAuction.Trader = entry.key
//            val volume: Int = entry.value
//            entry.key.awarded_volume = entry.value
//            if (de.companies_that_have_seen_auction.contains(entry.key.company)) {
//                messages.add(
//                    AuctionMessage(
//                        message_type = AuMessageType.SYSTEM_TO_TRADER,
//                        message = "${entry.key.company_shortname} awarded ${volume.thousands()} @ $award_price",
//                        from_user = null,
//                        to_company = t.company
//                    )
//                )
//            }
//        }

        deRound.buyers().forEach { b: DeTradingCompany ->
            val award_vol = deRound.match_vol(b)
            b.award(OrderType.BUY, award_vol)
            if(de.companies_that_have_seen_auction.any{it.company_id == b.company_id}){
                AuctionMessage(
                    message_type_ = AuMessageType.SYSTEM_TO_TRADER,
                    message_ = "${b.shortname_at_auction_time} bought ${award_vol.thousands()} @ $award_price",
                    from_user_ = null,
                    to_company_ = b
                )

            }
        }

        deRound.sellers().forEach { s: DeTradingCompany ->
            val award_vol = deRound.match_vol(s)
            s.award(OrderType.SELL, award_vol)
            if(de.companies_that_have_seen_auction.contains(s)){
                AuctionMessage(
                    message_type_ = AuMessageType.SYSTEM_TO_TRADER,
                    message_ = "${s.shortname_at_auction_time} sold ${award_vol.thousands()} @ $award_price",
                    from_user_ = null,
                    to_company_ = s
                )
            }
        }

        db.save(de)
    }

}

```

## de/commands/de-auction-save.kt

```kotlin
// File: de-auction-save.kt
package au21.engine.domain.de.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeAuctionSettings
import au21.engine.domain.de.model.DePriceRule
import au21.engine.domain.de.services.matcher.DeMatcher
import au21.engine.framework.commands.*
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.is_blank
import org.joda.time.DateTime
import java.util.*

class DeAuctionSaveCommand(
    val auction_id: String,
    val auction_name: String,
    val use_counterparty_credits:String,

    // val starting_time: String,
//    val first_round_duration: String,
//    val following_round_duration: String,
    // val trigger_interval: String,
    // val reporting_duration: String,

    val quantity_label: String,
    val quantity_minimum: String,
    val quantity_step: String,

    val price_change_initial: String,
    val price_change_post_reversal: String,
    val price_label: String,
    val price_decimal_places: String,
    //  val starting_price: String,
    val cost_multiplier: String,

    val excess_level_0_label: String,
    val excess_level_1_label: String,
    val excess_level_2_label: String,
    val excess_level_3_label: String,
    val excess_level_4_label: String,

    val excess_level_1_quantity: String,
    val excess_level_2_quantity: String,
    val excess_level_3_quantity: String,
    val excess_level_4_quantity: String,

    //   val starting_price: String?,
    val starting_price_announcement_mins: String,

    val month_is_1_based: Boolean,
    val starting_year: String,
    val starting_month: String,
    val starting_day: String,
    val starting_hour: String,
    val starting_mins: String,

    val round_red_secs: String,
    val round_orange_secs: String,
    val round_open_min_seconds: String,
    val round_closed_min_secs: String

) : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)

        val is_create: Boolean = auction_id.is_blank()

        err_if_blank(::auction_name)
        err_if_blank(::quantity_label)
        err_if_blank(::price_label)
        err_if_blank(::use_counterparty_credits)

        val use_counterparty_credits_b:Boolean? =
            when(use_counterparty_credits){
                "true" -> true
                "false" -> false
                else -> null
            }

        err_if_null(use_counterparty_credits_b, "Use credit limits cannot be blank.")

        //val starting_time = p.starting_time
//        val first_round_duration_i: Int? = err_unless_Int_GT_zero(::first_round_duration)
//        val following_round_duration_i: Int? = err_unless_Int_GT_zero(::first_round_duration)

        val quantity_minimum_i: Int? = err_unless_Int_GT_zero(::quantity_minimum)
        val quantity_step_i: Int? = err_unless_Int_GT_zero(::quantity_step)

        val price_decimal_places_i: Int? = err_unless_Int_GE_zero(::price_decimal_places)

        // kludge for now because it seems we don't always send cost_multiplier
        // - https://gitlab.com/auctionologies/frontend/au21-frontend/-/issues/67
        val cost_multiplier_d: Double? =
            if (cost_multiplier.is_blank())
                10000.0
            else
                err_unless_Double_GT_zero(::cost_multiplier)


        fun to_error(s: String) = "$s must be a number greater than zero."

        val price_change_initial_double: Double? =
            toDoubleOrError(::price_change_initial, ::is_GT_zero) { to_error("Initial price change") }
        val price_change_post_reversal_double: Double? =
            toDoubleOrError(::price_change_post_reversal, ::is_GT_zero) { to_error("Post reversal price change") }

        price_change_initial_double?.let { inital_price ->
            price_change_post_reversal_double?.let { post_reversal_price ->
                err_if(
                    post_reversal_price >= inital_price,
                    "Price change post reversal must be less than inital price."
                )
            }
        }

        err_if_blank(::excess_level_0_label, "Excess level 0 label")
        err_if_blank(::excess_level_1_label, "Excess level 1 label")
        err_if_blank(::excess_level_2_label, "Excess level 2 label")
        err_if_blank(::excess_level_3_label, "Excess level 3 label")
        err_if_blank(::excess_level_4_label, "Excess level 4 label")

        val excess_level_1_quantity_int: Int? =
            toIntOrError(::excess_level_1_quantity, ::is_GT_zero) { to_error("Excess level 1") }
        val excess_level_2_quantity_int: Int? =
            toIntOrError(::excess_level_2_quantity, ::is_GT_zero) { to_error("Excess level 2") }
        val excess_level_3_quantity_int: Int? =
            toIntOrError(::excess_level_3_quantity, ::is_GT_zero) { to_error("Excess level 3") }
        val excess_level_4_quantity_int: Int? =
            toIntOrError(::excess_level_4_quantity, ::is_GT_zero) { to_error("Excess level 4") }

        fail_if_errors()

        err_if(
            excess_level_4_quantity_int!! < excess_level_3_quantity_int!!,
            "Excess level 4 cannot be less than excess level 3"
        )
        err_if(
            excess_level_3_quantity_int < excess_level_2_quantity_int!!,
            "Excess level 3 cannot be less than excess level 2"
        )
        err_if(
            excess_level_2_quantity_int < excess_level_1_quantity_int!!,
            "Excess level 2 cannot be less than excess level 1"
        )

        fail_if_errors()

        // CURRENT STATE SETTINGS:

//        val starting_price_double: Double? = starting_price?.let { s: String ->
//            s.toDoubleOrNull()?.also { d: Double? ->
//                err_if_null(d, "Starting price not understood: $s")
//            }
//        }

        val starting_price_announcement_mins_int: Int = starting_price_announcement_mins.toIntOrNull() ?: 0

        // STARTING DATE/TIME:
        /*
            monthOfYear – the month of the year, from 1 to 12
            dayOfMonth – the day of the month, from 1 to 31
            hourOfDay – the hour of the day, from 0 to 23
            minuteOfHour – the minute of the hour, from 0 to 59
        */

        val starting_day_int: Int? = toIntOrError(::starting_day) { to_error("starting day") }
        val starting_hour_int: Int? = toIntOrError(::starting_hour) { to_error("starting hour") }
        val starting_mins_int: Int? = toIntOrError(::starting_mins) { to_error("starting minutes") }
        val starting_month_int_raw: Int? = toIntOrError(::starting_month) { to_error("starting month") }
        val starting_year_int: Int? = toIntOrError(::starting_year) { to_error("starting year") }

        val round_red_secs_int: Int? =
            toIntOrError(::round_red_secs, ::is_GT_zero) { to_error("Round red seconds") }

        val round_orange_secs_int: Int? =
            toIntOrError(::round_orange_secs, ::is_GT_zero) { to_error("Round orange seconds") }

        val round_open_min_seconds_int: Int? =
            toIntOrError(::round_open_min_seconds, ::is_GT_zero) { to_error("Round open minimum seconds") }

        val round_closed_min_secs_int: Int? =
            toIntOrError(::round_closed_min_secs, ::is_GT_zero) { to_error("Round closed seconds") }

        err_if(month_is_1_based && starting_month_int_raw == 0, "Starting month cannot be zero")

        fail_if_errors()

        val starting_month_int: Int = when (month_is_1_based) {
            true -> starting_month_int_raw!!
            false -> starting_month_int_raw!! + 1
        }

        val starting_date_time: Date = DateTime(
            starting_year_int!!,
            starting_month_int,
            starting_day_int!!,
            starting_hour_int!!,
            starting_mins_int!!
        ).toDate()

        // TODO: removing this for now, ie: don't need it without autopilot:
        //    err_if(starting_date_time.before(Date()), "Cannot start an auction in the past")

        fail_if_errors()

        val settings = DeAuctionSettings(
            use_counterparty_credits = use_counterparty_credits_b!!,

            round_open_min_secs = round_open_min_seconds_int!!,
            round_closed_min_secs = round_closed_min_secs_int!!,
            round_orange_secs = round_orange_secs_int!!,
            round_red_secs = round_red_secs_int!!,
            starting_price_announcement_mins = starting_price_announcement_mins_int,

            quantity_units = quantity_label,
            quantity_minimum = quantity_minimum_i!!,
            quantity_step = quantity_step_i!!,
            price_units = price_label,
            price_decimal_places = price_decimal_places_i!!,
            cost_multiplier = cost_multiplier_d!!,
            price_rule = DePriceRule(
                price_change_initial = price_change_initial_double!!,
                price_change_post_reversal = price_change_post_reversal_double!!,
                excess_level_1_quantity = excess_level_1_quantity_int,
                excess_level_2_quantity = excess_level_2_quantity_int,
                excess_level_3_quantity = excess_level_3_quantity_int,
                excess_level_4_quantity = excess_level_4_quantity_int,
                excess_level_0_label = excess_level_0_label,
                excess_level_1_label = excess_level_1_label,
                excess_level_2_label = excess_level_2_label,
                excess_level_3_label = excess_level_3_label,
                excess_level_4_label = excess_level_4_label
            ),
        )

        val de: DeAuction = when {

            is_create -> DeAuction(
                auction_name = auction_name,
                settings = settings
            )
            else -> db.auction_or_alert<DeAuction>(auction_id).also {
//                // moved to de-flow-commands.kt
//                if (it.has_started()) {
//                    fail_if(
//                        it.settings.starting_price != starting_price_double,
//                        "Cannot change starting price after the auction has started!"
//                    )
//                }
//                it.auction_name = auction_name
                it.settings = settings
            }
        }

        de.starting_time = starting_date_time

        return DeAuctionSaveAction(this, db, session, de, is_create)
    }

}


class DeAuctionSaveAction(
    override val command: DeAuctionSaveCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de: DeAuction,
    val is_create: Boolean
) : EngineAction {
    override fun mutate() {
        db.save(de)
        // have to reset volume limits if we have a last round (it will return if not):
        DeMatcher.calculate_and_set_matches(de, this)
        db.save(session)
    }


}

```

## de/commands/de-auction-tick.kt

```kotlin
// File: de-auction-tick.kt
package au21.engine.domain.de.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.database.AuEntityManager

class DeAuctionsTickCommand : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)

        // TODO: find tickable auctions an tick them!
       // val auction: DeAuction = db.auction_or_alert()

        return DeAuctionsTickAction(this, db, session)
    }
}


class DeAuctionsTickAction(
    override val command: DeAuctionsTickCommand,
    override val db: AuEntityManager,
    override val session: AuSession
) : EngineAction {
    override fun mutate() {
        TODO("Not yet implemented")
    }

}

```

## de/commands/de-create-db.kt

```kotlin
// File: de-create-db.kt
package au21.engine.domain.de.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.Person
import au21.engine.domain.common.services.find_auction_by_name
import au21.engine.domain.common.services.user_by_username
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.services.sampledb.TradingUserFixture
import au21.engine.domain.de.services.sampledb.create_sample_db_auction
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.database.AuEntityManager
import kotlin.random.Random


class DeCreateSampleDbCommand(
    val auction_count: Int = 1,
    val auctioneer_count: Int = 2,
    val close_last_round: Boolean = false,
    val round_count: Int = 30,
    val trader_count: Int = 20,
    val use_counterparty_credits: Boolean = false,
) : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        return DeCreateSampleDbAction(
            this,
            db,
            null,
            trader_count,
            round_count,
            auction_count,
            auctioneer_count,
            close_last_round,
            use_counterparty_credits
        )
    }
}

class DeCreateSampleDbAction(
    override val command: DeCreateSampleDbCommand,
    override val db: AuEntityManager,
    override val session: AuSession? = null,
    val trader_count: Int,
    val round_count: Int,
    val auction_count: Int,
    val auctioneer_count: Int,
    val close_last_round: Boolean,
    val use_counterparty_credits: Boolean,
) : EngineAction {

    override fun mutate() {

        (1..auctioneer_count).forEach { i ->
            db.user_by_username("a$i") ?: db.save(
                Person(
                    username = "a$i",
                    password = "1",
                    role = AuUserRole.AUCTIONEER
                )
            )
        }

        val trading_users: List<Person> = TradingUserFixture.create(
            db,
            trader_count,
            use_counterparty_credits
        )

        (1..auction_count).forEach { num ->
            Random.nextInt(10000).toString().let { count: String ->
                val auction_name = "Auction $num - $count"
                db.find_auction_by_name<DeAuction>(auction_name) ?: run {
                    val de = create_sample_db_auction(
                        auction_name = auction_name,
                        trading_users = trading_users,
                        round_count = round_count,
                        close_last_round = close_last_round,
                        use_counterparty_credits = use_counterparty_credits
                    )
                    db.save(de)
                }
            }

        }
    }
}

```

## de/commands/de-credit-set.kt

```kotlin
// File: de-credit-set.kt
package au21.engine.domain.de.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeTradingCompany
import au21.engine.framework.commands.*
import au21.engine.framework.database.AuEntityManager

class DeCreditSetCommand(
    val auction_id: String,
    val lender_id: String,
    val borrower_id: String,
    val credit_limit: String,
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        val session = db.session_non_terminated_or_alert(session_id)

        // TODO: check that either auctioneer or session.company_id == seller_id

        val credit_limit_absolute: Double = credit_limit.trim_commas_and_underscores().replace("$", "").toDoubleOrNull()
            ?: throw AlertException("Credit limit is not a valid number: $credit_limit")

        val de: DeAuction =
            db.byId<DeAuction>(auction_id).also {
                when (it) {
                    null -> err_if(true, "Cannot find auction without an id.")
                    else -> {
                        err_if(
                            it.de_trading_companies.none { t -> t.company_id == borrower_id.toLong() },
                            "borrower not in auction"
                        )
                        err_if(
                            it.de_trading_companies.none { t -> t.company_id == lender_id.toLong() },
                            "lender not in auction"
                        )
                    }
                }
            } ?: throw AlertException("Cannot find auction without an id: $auction_id")

        val lender = de.de_trading_companies.firstOrNull { it.company_id == lender_id.toLong() }
            ?: throw AlertException("lender not in auction")
        val borrower = de.de_trading_companies.firstOrNull { it.company_id == borrower_id.toLong() }
            ?: throw AlertException("borrower not in auction")

        val current_absolute_limit: Double? = de.get_counterparty_credit_limit(lender, borrower)?.credit_limit

        err_if(
            current_absolute_limit != null // ie: not 'no limit'
                    && current_absolute_limit < credit_limit_absolute, // ie: we're reducing the buyers credit limit
            "Cannot reduce ${borrower.shortname_at_auction_time}'s (buyer) credit limit with ${lender.shortname_at_auction_time} (seller) because the buyer has a bid in an open auction."
        )

        return DeCreditSetAction(
            command = this,
            db = db,
            session = session,
            de = de,
            lender = lender,
            borrower = borrower,
            credit_limit_absolute = credit_limit_absolute
        )
    }
}


class DeCreditSetAction(
    override val command: DeCreditSetCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de: DeAuction,
    val lender: DeTradingCompany,
    val borrower: DeTradingCompany,
    val credit_limit_absolute: Double,
) : EngineAction {
    override fun mutate() {
        de.set_counterparty_credit_limit(lender, borrower, credit_limit_absolute)
        db.save(de)
    }

}

```

## de/commands/de-flow-control.kt

```kotlin
// File: de-flow-control.kt
package au21.engine.domain.de.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeFlowControlType
import au21.engine.domain.de.model.DeFlowControlType.SET_STARTING_PRICE
import au21.engine.domain.de.services.state.DeControlValidator
import au21.engine.domain.de.services.state.DeControlValidator.MUTATE
import au21.engine.domain.de.services.state.DeMutator
import au21.engine.framework.commands.*
import au21.engine.framework.database.AuEntityManager

class DeFlowControlCommand(
    val auction_id: String,
    val control: DeFlowControlType,
    val starting_price: String?
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val s = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(s)

        val de: DeAuction = db.auction_or_alert(auction_id)
        //val n = de.lastround()

        val starting_price_double: Double? = starting_price?.toDoubleOrNull()

//        val round_price_double: Double? = round_price?.let {
//            it.toDoubleOrNull()
//                ?: alert("Price is not a number.")
//        }

        return when (val result: String? = DeControlValidator.validate(de, control)) {
            null -> DeFlowControlAction(this, db, s, de, false, starting_price_double) // if
            MUTATE -> {
                fail_if(
                    control == SET_STARTING_PRICE && starting_price_double == null,
                    "Round price not valid: $starting_price"
                )
//                alert_if(
//                    de.last_round_has_started(),
//                    "Cannot change round price after the round has started!"
//                )
                DeFlowControlAction(this, db, s, de, true, starting_price_double)
            }
            else -> fail(result)
        }
    }
}


class DeFlowControlAction(
    override val command: DeFlowControlCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de: DeAuction,
    val is_mutatable: Boolean,
    var starting_price: Double?
) : EngineAction {
    override fun mutate() {

        if (is_mutatable) {

            DeMutator.mutate(de, command.control, starting_price)

            // have to reset volume limits:
            //de.set_counterparty_volume_limits( db.findAll())

            db.save(de)
        }
    }

}

```

## de/commands/de-order-submit.kt

```kotlin
// File: de-order-submit.kt
package au21.engine.domain.de.commands


import au21.engine.domain.common.model.*
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.*
import au21.engine.domain.de.services.matcher.DeMatcher
import au21.engine.domain.de.validations.validate_de_order_against_constraints_and_throw_if_fails
import au21.engine.framework.PageName
import au21.engine.framework.commands.*
import au21.engine.framework.commands.interfaces.IAuctionMessage
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.thousands

class DeOrderSubmitCommand(
    val auction_id: String,
    val company_id: String,
    val order_type: OrderType,
    val round: String,
    val quantity: String
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if(
            company_id != session.user?.company?.id_str(),
            "Company id does not match session id."
        )

        fail_if(session.page != PageName.DE_TRADER_PAGE, "You can only submit orders from the auction page.")

        val de: DeAuction = db.auction_or_alert(auction_id)

        //TODO: The below 2 conditions is not required as this is impossible because of line number 28
        val c: Company = db.byId(company_id) ?: fail("no company found")

        // Assumes the all manual bidders have seen the auction !!
        val user_proxy = de.users_that_have_seen_auction.find { it.person_id == session.user?.id }
            ?: fail("You are not a trader in this auction.")

        val t: DeTradingCompany = de.de_trading_companies.firstOrNull{it.company_id == company_id.toLong()}
            ?: fail("You are not a trader in this auction.")

        fail_if(
            de.common_state
                    != DeCommonState.ROUND_OPEN, "Auction not open for bidding."
        )

        val n: DeRound = de.lastround()
        val round_int: Int = err_unless_Int_GT_zero(::round)
            ?: fail("Round should be greater than zero")

        fail_if(round_int != n.number, "Order isn't for round ${n.number}")

        val rbi = n.get_rti(t)
            ?: fail("No round bidder info")

        val quantity_i: Int = err_unless_Int_GE_zero(::quantity)
            ?: fail("quantity must be a whole number")

        val order_type_correct: OrderType = if (quantity_i == 0) OrderType.NONE else order_type

        // these will be set when the round is created!

        val constraints: DeBidConstraints = rbi.constraints


        val order_quantity_type_corrected: OrderType =
            when (quantity_i) {
                0 -> OrderType.NONE
                else -> order_type_correct
            }

        validate_de_order_against_constraints_and_throw_if_fails(
            constraints,
            order_type = order_quantity_type_corrected,
            order_quantity = quantity_i,
            quantity_units = de.settings.quantity_units,
        )

        val message = AuctionMessage(
            AuMessageType.SYSTEM_TO_TRADER,
            "${user_proxy.username_at_auction_time} submitted " + when (order_type) {
                OrderType.BUY -> "buy order for ${quantity_i.thousands()} ${de.settings.quantity_units}"
                OrderType.SELL -> "sell order for ${quantity_i.thousands()} ${de.settings.quantity_units}"
                OrderType.NONE -> "order for 0 ${de.settings.quantity_units}"
            },
            null,
            to_company_ = t,
        )

        return DeOrderSubmitAction(
            this,
            db,
            session,
            de,
            message,
            n,
            t,
            user_proxy,
            c,
            rbi,
            order_quantity_type_corrected,
            quantity_i
        )

    }

}


class DeOrderSubmitAction(
    override val command: DeOrderSubmitCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    override val auction: DeAuction,
    override val message: AuctionMessage,
    val n: DeRound,
    val trading_company: DeTradingCompany,
    val user_proxy: PersonProxy,
    val company: Company,
    val rbi: DeRoundTraderInfo,
    val order_type: OrderType,
    val quantity: Int
) : EngineAction, IAuctionMessage {

    lateinit var order: DeOrder

    override fun mutate() {

        order = auction.create_manual_order(
            r = n,
            t = trading_company,
            u = user_proxy,
            order_type = order_type,
            order_quantity = quantity
        )

        // NB: MUST be done before calcualting actuan_maxflow !!
        //auction.set_counterparty_quantity_limits()

        // matches:
        DeMatcher.calculate_and_set_matches(auction, this)


        // test if round closeable:
        if (n.all_orders_are_non_default()) {
            auction.setState(DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN)
        }

        auction.messages.add(message)
        db.save(auction)

    }

}

```

## de/commands/de-round-history.kt

```kotlin
// File: de-round-history.kt
package au21.engine.domain.de.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeRound
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail
import au21.engine.framework.commands.fail_if_not_auctioneer
import au21.engine.framework.database.AuEntityManager

/**
 * sends trader matrix and other info when round selected
 */
class DeRoundHistoryCommand(
    val auction_id:String,
    val round_number:String
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)

        val de: DeAuction = db.auction_or_alert(auction_id)

        val round_num:Int = round_number.toIntOrNull() ?: fail("round_number invalid: $round_number")
        val deRound: DeRound = de.rounds.find { it.number == round_num }
            ?: fail("no round found with number: $round_num")

        return DeRoundControllerAction(this, db, session, de, deRound)
    }
}


class DeRoundControllerAction(
    override val command: DeRoundHistoryCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de:DeAuction,
    val deRound:DeRound
) : EngineAction {

    override fun mutate() {

    }

}

```

## de/commands/de-template-delete.kt

```kotlin
// File: de-template-delete.kt
package au21.engine.domain.de.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail_if_not_auctioneer
import au21.engine.framework.database.AuEntityManager

class DeTemplateDeleteCommand(
    val template_id:String
): EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)

//        val template:DeAuctionTemplate = db.byId(template_id)
//            ?: alert("no template found with id: $template_id")

        return DeTemplateDeleteAction(this, db, session)
    }
}

class DeTemplateDeleteAction(
    override val command: DeTemplateDeleteCommand,
    override val db:AuEntityManager,
    override val session:AuSession
) : EngineAction {
    override fun mutate() {
        TODO("Not yet implemented")
    }

}

```

## de/commands/de-template-save.kt

```kotlin
// File: de-template-save.kt
package au21.engine.domain.de.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail_if_not_auctioneer
import au21.engine.framework.database.AuEntityManager

class DeTemplateSaveCommand(
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)


        return DeTemplateSaveAction(this, db, session)
    }
}


class DeTemplateSaveAction(
    override val command: DeTemplateSaveCommand,
    override val db: AuEntityManager,
    override val session: AuSession
) : EngineAction {
    override fun mutate() {
        TODO("Not yet implemented")
    }

}

```

## de/commands/de-trader-limits.kt

```kotlin
// File: de-trader-limits.kt
package au21.engine.domain.de.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeRoundTraderInfo
import au21.engine.domain.de.model.DeTradingCompany
import au21.engine.domain.de.model.get_rti
import au21.engine.framework.commands.*
import au21.engine.framework.database.AuEntityManager

//TODO: Need to implement
class DeTraderLimitsCommand(
    val auction_id: String,
    val company_id: String,
    val selling_quantity_limit: String,
//    val selling_cost_limit: String,
//    val buying_quantity_limit: String,
    val buying_cost_limit: String
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        val session = db.session_non_terminated_or_alert(session_id)
        val de: DeAuction =
            db.byId<DeAuction>(auction_id) ?: throw AlertException("Cannot find auction without an id: $auction_id")

        // TODO: FOR NOW PREVENTING CHANGES AFTER AUCTION STARTED.
        fail_if(de.auction_has_started, "Cannot change trader limits after auction has started")
        // - though we might need to allow changes to the limits after the auction has started.
        // - in which case we need to check that the limits are not being reduced if a higher big has already been placed.
        // - BTW, Github Copilot wrote those notes above"

        val company_id_long:Long = err_unless_Long_GT_zero(::company_id)
            ?: throw AlertException("Invalid company id: $company_id")

        val trader = de.de_trading_companies.firstOrNull { it.company_id == company_id_long }
            ?: throw AlertException("Trader not in auction")

        val rti:DeRoundTraderInfo = de.firstround().get_rti(trader)
            ?: throw AlertException("Round trader info not found for trader: ${trader.shortname_at_auction_time}")

     //   val buyer_quantity_limit_i: Int = err_unless_Int_GE_zero(::buying_quantity_limit) ?: 0
        val selling_quantity_limit_i: Int = err_unless_Int_GE_zero(::selling_quantity_limit) ?: 0

        val buying_cost_limit_d: Double = err_unless_Double_GE_zero(::buying_cost_limit) ?: 0.0
    //    val selling_cost_limit_d: Double = err_unless_Double_GE_zero(::selling_cost_limit) ?: 0.0

        fail_if_errors()

        return DeSetTraderLimitsAction(
            command = this,
            db = db,
            session = session,
            de = de,
            trader = trader,
            rti = rti,
            selling_quantity_limit = selling_quantity_limit_i,
//            selling_cost_limit = selling_cost_limit_d,
//            buying_quantity_limit = buyer_quantity_limit_i,
            buying_cost_limit = buying_cost_limit_d
        )
    }
}

class DeSetTraderLimitsAction(
    override val command: DeTraderLimitsCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de: DeAuction,
    val trader: DeTradingCompany,
    val rti: DeRoundTraderInfo,
    val selling_quantity_limit: Int,
//    val selling_cost_limit: Double, // not used yet
//    val buying_quantity_limit: Int, // not used yet
    val buying_cost_limit: Double
) : EngineAction {
    override fun mutate() {
        trader.initial_limits.also {
            it.initial_selling_quantity_limit = selling_quantity_limit
         //   it.initial_selling_cost_limit = selling_cost_limit
         //   it.initial_buying_quantity_limit = buying_quantity_limit
            it.initial_buying_cost_limit = buying_cost_limit
        }
        de.set_credit()
        db.save(de)
    }

}

```

## de/commands/de-traders-add.kt

```kotlin
// File: de-traders-add.kt
package au21.engine.domain.de.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Company
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.is_before_end_of_first_round
import au21.engine.domain.de.services.matcher.DeMatcher
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail
import au21.engine.framework.commands.fail_if_not_auctioneer
import au21.engine.framework.database.AuEntityManager

/**
 * this command will look at the company_ids list and either:
 * a) add if not already in auction
 * b) remove from auction if not on the list
 * - those removed will be 'bounced' out of the auction, and auction row removed
 * - those included will have the auction row added
 */

class DeTradersAddCommand(
    val auction_id: String,
    val company_ids: List<String>
) : EngineCommand() {

    /** De Auction: Adding and removing traders
     * - can only happen before close of round one
     * - if add, check that not already added
     * - if remove, check that has not bid
     */

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)

        val de: DeAuction = db.auction_or_alert(auction_id)

        if (!de.is_before_end_of_first_round()) {
            fail("Traders can only be added before the end of round one")
        }

        // TODO: catch errors and report elegantly (ie: xxx is not a company id) defer
        val company_id_longs:List<Long> = company_ids.map { it.toLong() }
        val existing_company_ids: List<Long> = de.de_trading_companies.map { it.company_id }

        val companies_to_add: List<Company> = company_id_longs
            .filter { !existing_company_ids.contains(it) }
            .mapNotNull { db.byId<Company>(it) }

        return DeTradersAddAction(this, db, session, de, companies_to_add)
    }
}


class DeTradersAddAction(
    override val command: DeTradersAddCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de: DeAuction,
    val companies_to_add: List<Company>
) : EngineAction {
    override fun mutate() {

        companies_to_add.forEach { c ->
            de.create_trader(c)
        }
        // have to reset volume limits (if we have a round!):
        DeMatcher.calculate_and_set_matches(de, this)
        db.save(de)
    }

}

```

## de/commands/de-traders-remove.kt

```kotlin
// File: de-traders-remove.kt
package au21.engine.domain.de.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Company
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.common.services.sessions_logged_in_traders
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.has_non_zero_bid
import au21.engine.domain.de.services.matcher.DeMatcher
import au21.engine.framework.PageName
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail
import au21.engine.framework.commands.fail_if_not_auctioneer
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.database.is_same_entity

/**
 * this command will look at the company_ids list and either:
 * a) add if not already in auction
 * b) remove from auction if not on the list
 * - those removed will be 'bounced' out of the auction, and auction row removed
 * - those included will have the auction row added
 */

class DeTradersRemoveCommand(
    val auction_id: String,
    val company_ids: List<String>
) : EngineCommand() {

    /** De Auction: Adding and removing traders
     * - can only happen before close of round one
     * - if add, check that not already added
     * - if remove, check that has not bid
     */

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)

        val de: DeAuction = db.auction_or_alert(auction_id)

        val existing_company_ids: List<String> = de.de_trading_companies.map { it.company_id.toString() }

        val companies_to_remove: List<Company> = company_ids
            .filter { existing_company_ids.contains(it) }
            .mapNotNull { db.byId(it) }

        // todo: this was done hastily:
        val companies_to_remove_that_have_bid: List<Company> = companies_to_remove
            .filter { c -> de.has_non_zero_bid(c) }

        if (companies_to_remove_that_have_bid.isNotEmpty()) {
            fail(
                "Unable to remove these bidders as they have already bid in this auction: "
                        + companies_to_remove_that_have_bid.joinToString(",") { it.shortname }
            )
        }

        val companies_that_have_seen_auction: List<Company> = companies_to_remove
            .filter {c ->
                de.companies_that_have_seen_auction
                    .any { it.company_id == c.id }
            }

        if (companies_that_have_seen_auction.isNotEmpty()) {
            fail(
                "Unable to remove these bidders as they have already seen this auction: "
                        + companies_that_have_seen_auction.joinToString(",") { it.shortname }
            )
        }

        return DeTradersRemoveAction(this, db, session, de, companies_to_remove)
    }
}


class DeTradersRemoveAction(
    override val command: DeTradersRemoveCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de: DeAuction,
    val companies_removed: List<Company>,
    // val messages: MutableList<String>
) : EngineAction {

    // is needed because below we will remove the auction from the session,
    // - and it won't be found by the genenater online sessions for auction
    // - (as it's then null at that point!!)

//    val removed_company_sessions: MutableList<AuSession> = mutableListOf()
//
//    fun removed_company_sids(): Array<String> = removed_company_sessions.map { it.session_id }.toTypedArray()

    override fun mutate() {

        companies_removed.forEach { removed_company ->

            de.remove_trader(removed_company)

            // VolumeLimit:
            //  n.limits.removeIf { it.buyerId == c.id_str() || it.sellerId == c.id_str() }

            // Bounce:
            db.sessions_logged_in_traders()
                .filter { is_same_entity(it.user?.company, removed_company) }
                .forEach { s: AuSession ->
                    // Bounce to home page
                    s.set_page(PageName.HOME_PAGE)
                    //removed_company_sessions.add(s)
                    db.save(s)
                }
        }

        // need to recalculate max flow:

        // TODO: need to implement this again !!
        //  n.setMaxPotentialFlow(de.calculate_max_from(n))


        // have to reset volume limits:
        DeMatcher.calculate_and_set_matches(de, this)

        db.save(de)
    }

}

```

## de/exp/AlertResult.kt

```kotlin
// File: AlertResult.kt
package au21.engine.domain.de.exp

class AlertResult : Result {
    override val type = ResultType.ALERT
}

```

## de/exp/Result.kt

```kotlin
// File: Result.kt
package au21.engine.domain.de.exp

interface Result {
    val type: ResultType
}


// results:

enum class ResultType {
    ALERT, OBJECT, ARRAY_ITEM
}

class ResultsEnvelope(
    val results: List<Result> = listOf()
)

class UserResult() : Result {
    override val type = ResultType.OBJECT

    lateinit var username: String

    constructor(
        username: String
    ) : this() {
        this.username = username
    }

}

```

## de/model/DeAuction.kt

```kotlin
// File: DeAuction.kt
package au21.engine.domain.de.model

import au21.engine.domain.common.model.*
import au21.engine.domain.de.services.constraints.calculate_first_round_constraints
import au21.engine.domain.de.services.defaultorders.DeOrderInfo
import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.AuFormatter
import au21.engine.framework.utils.TimeFormatter
import org.joda.time.DateTime
import java.util.*
import javax.persistence.Entity

@Entity
class DeAuction(
    auction_name: String,
    var settings: DeAuctionSettings
) : Auction(auction_name) {

    /**
     *   SEE BOTTOM OF CLASS FOR INITIALIZER (setState)
     */

//    @Index
//    var ticking: Boolean = state.in_state(DeState.ROUND_RUNNING)

    private var auctioneer_state_str: String = DeAuctioneerState.STARTING_PRICE_NOT_SET.toString()
    var auctioneer_state: DeAuctioneerState
        get() = DeAuctioneerState.valueOf(auctioneer_state_str)
        private set(s) {
            auctioneer_state_str = s.toString()
        }

    private var common_state_str: String = DeCommonState.SETUP.toString()
    var common_state: DeCommonState
        get() = common_state_str.enumValueOfWithTrace()
        private set(v) {
            common_state_str = v.toString()
        }

    override fun starting_time_text(): String =
        when (common_state) {
            DeCommonState.SETUP,
            DeCommonState.STARTING_PRICE_ANNOUNCED ->
                starting_time?.let { d: Date ->
                    val starting_time_text = "${AuFormatter.auction_row_date_formatter.format(d)} at " +
                            "${AuFormatter.auction_row_time_formatter.format(d)} "

                    val time_to =
                        TimeFormatter.formatDuration(d).let {
                            // TODO: not sure what this is doing??
                            when (it) {
                                "now" -> "(starting now)"
                                else -> "($it from now)"
                            }
                        }

                    return "$starting_time_text $time_to"
                } ?: ""

            DeCommonState.ROUND_OPEN, DeCommonState.ROUND_CLOSED -> "Auction started"
            DeCommonState.AUCTION_CLOSED -> "Auction closed"
        }


//    var round_state_label: String = DeRoundState.NOT_OPEN.toString()
//        private set
//    var round_state: DeRoundState
//        get() = DeRoundState.valueOf(round_state_label)
//        private set(value) {
//            this.round_state_label = value.toString()
//        }

    fun lastround(): DeRound = rounds.lastOrNull() ?: throw AlertException("Auction must have at least on round.")
    fun firstround(): DeRound = rounds.firstOrNull() ?: throw AlertException("Auction must have at least on round.")

    // TODO: should this be pre-calculated ? Probably some value in leaving it as is so that it can be set by server time
    // TOD: probably want to do the same with starting time, btw
    fun announce_time(): Date? = starting_time?.let { d: Date ->
        DateTime(d)
            .minusMinutes(settings.starting_price_announcement_mins)
            .toDate()
    }

    fun setState(state: DeAuctioneerState) {
        // NB: because last round can't be null, we can't call this before the first round is created!

        val n = lastround()
        closed = state == DeAuctioneerState.AUCTION_CLOSED
        auctioneer_state = state
        auctioneer_state_text = when (state) {
            DeAuctioneerState.STARTING_PRICE_NOT_SET -> "Starting price not set"
            DeAuctioneerState.STARTING_PRICE_SET -> "Starting price set"
            DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> "Starting price announced"
            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN ->
                "Round ${n.number} open, all orders not in." + if (price_has_overshot()) " Price has overshoot" else ""

            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN ->
                "Round ${n.number} open, all orders in." + if (price_has_overshot()) " Price has overshoot" else ""

            DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE ->
                "Round ${n.number} closed, auction not awardable." + if (price_has_overshot()) " Price has overshoot" else ""

            DeAuctioneerState.ROUND_CLOSED_AWARDABLE ->
                "Round ${n.number} closed, auction awardable."

            DeAuctioneerState.AUCTION_CLOSED -> "Auction closed"
        }

        common_state = when (state) {
            DeAuctioneerState.STARTING_PRICE_NOT_SET -> DeCommonState.SETUP
            DeAuctioneerState.STARTING_PRICE_SET -> DeCommonState.SETUP
            DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> DeCommonState.STARTING_PRICE_ANNOUNCED
            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> DeCommonState.ROUND_OPEN
            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> DeCommonState.ROUND_OPEN
            DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> DeCommonState.ROUND_CLOSED
            DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> DeCommonState.ROUND_CLOSED
            DeAuctioneerState.AUCTION_CLOSED -> DeCommonState.AUCTION_CLOSED
        }

        common_state_text = when (common_state) {
            DeCommonState.SETUP -> "Waiting for starting price"
            DeCommonState.STARTING_PRICE_ANNOUNCED -> "Starting price announced"
            DeCommonState.ROUND_OPEN -> "Round ${n.number} open for orders!"
            DeCommonState.ROUND_CLOSED -> "Round ${n.number} closed"
            DeCommonState.AUCTION_CLOSED -> "Auction closed"
        }
    }

    fun starting_price_announced(): Boolean = !auctioneer_state.oneOf(
        DeAuctioneerState.STARTING_PRICE_NOT_SET,
        DeAuctioneerState.STARTING_PRICE_SET
    )

    var autopilot_label: String = AutopilotMode.DISENGAGED.toString()
        private set
    var autopilot: AutopilotMode
        get() = AutopilotMode.valueOf(autopilot_label)
        set(value) {
            this.autopilot_label = value.toString()
        }


    // TODO: set in parent
    fun auction_has_started(): Boolean =
        last_round_has_started() || this.rounds.size > 1


    fun last_round_has_started(): Boolean =
        this.auctioneer_state.oneOf(
            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN,
            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN,
            DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE,
            DeAuctioneerState.ROUND_CLOSED_AWARDABLE,
            DeAuctioneerState.AUCTION_CLOSED
        )

    fun create_trader(c: Company): DeTradingCompany {
        // TODO: make sure not already added!
        // note: for tests make sure you have an id!

        if (!is_before_end_of_first_round())
            throw AlertException("Cannot add traders after the first round is closed.")

        if (trading_companies.any { it.company_id == c.id })
            throw Error("Trader already exists with name: ${c.shortname}")

        val t = DeTradingCompany(c).also {
            trading_companies.add(it)
        }

        create_first_round_rti(t)

        return t
    }

    // ASSUMES THAT TRADER DOESN'T HAVE A NON_ZERO BID:
    fun remove_trader(c: Company) {
        de_trading_companies.removeIf { it.company_id == c.id }
        rounds.forEach { r: DeRound ->
            r.trader_infos.removeIf { it.de_trading_company.company_id == c.id }
        }
        trading_companies.removeIf { it.company_id == c.id }
    }


    /**
     *  use this for:
     *  1) traders added
     *  2) starting price set:
     */

    private fun create_first_round_rti(t: DeTradingCompany) {
        // called:
        // a) when adding a trader to an auction
        // b) when editing the trader's initial constraints

        val n = lastround()

        if (n.number != 1)
            throw AlertException("Cannot create first round infos after the first round.")

        // remove if existing:

        n.apply {
            get_rti(t)?.let {
                trader_infos.remove(it)
            }
        }


        // 3: create rti with constraints, and default order
        val constraints = calculate_first_round_constraints(t,  settings, n)
        val default_order = DeOrderInfo.create_default_from_constraints(constraints)

        val rti = DeRoundTraderInfo(
            trading_company_ = t,
            constraints_ = constraints,
            default_order = DeOrder(
                round = n,
                trading_company_ = t,
                u = null,
                order_info = default_order,
                cost_multiplier = settings.cost_multiplier,
                prev_order = null,
            ),
            current_matched_vol = 0,
            fully_opposed_match_vol = 0
        )

        n.trader_infos.add(rti)
    }

    fun set_starting_price(price: Double) {
        if (lastround().number != 1)
            throw AlertException("Cannot create first round infos after the first round.")

        firstround().price = price

        // need to re-create the first round rti's with the new price!
        de_trading_companies.forEach { create_first_round_rti(it) }
    }

//    var time_remaining: Int = settings.first_round_duration
//        private set

//    fun set_time_remaining() {
//        time_remaining = when (lastround().number) {
//            1 -> settings.first_round_duration
//            else -> settings.following_round_duration
//        }
//    }

//    fun decrement_time_remaining() {
//        time_remaining--
//    }

    var price_decimal_places: Int = 3
    var awarded_round: DeRound? = null

    var revised_orders: MutableList<DeOrder> = mutableListOf()
        private set

    var rounds: MutableList<DeRound> = mutableListOf()
        private set

    // TODO: probably a better way of checking this?
    @Suppress("UNCHECKED_CAST")
    var de_trading_companies: MutableList<DeTradingCompany> = mutableListOf()
        get() = trading_companies as MutableList<DeTradingCompany>
        private set


//    fun reset_round() {
//        // TODO: what if there is no last round?
//        lastround()?.let { n ->
//            n.orders.clear()
//            n.trader_infos.clear()
//            traders.forEach { create_rti(n, it) }
//            //set_time_remaining()
//        }
//    }

    fun set_rank(t: DeTradingCompany) {
        // set rank if none:
        if (t.rank == null) {
            // first will be 1
            t.rank = 1 + de_trading_companies.filter { it.rank != null }.size
        }
    }

    // TODO: MOVE THIS OUT OF HERE
    fun create_manual_order(
        r: DeRound,
        t: DeTradingCompany,
        u: PersonProxy,
        order_type: OrderType,
        order_quantity: Int
    ): DeOrder {
        val rti = r.get_rti(t)
            ?: throw AlertException("expected rti for trader: ${t.shortname_at_auction_time}")

//        rti.has_bid_explicitly = true

        set_rank(t) // TODO: do we still need this?

        return DeOrder(
            round = r,
            trading_company_ = t,
            u = u,
            order_info = DeOrderInfo(
                OrderSubmissionType.MANUAL,
                order_type,
                order_quantity
            ),
            cost_multiplier = settings.cost_multiplier,
            prev_order = rti.order
        ).also {
            rti.order = it
        }
    }

    // Do it from
    fun set_credit() {
        // TODO
//        de_trading_companies.forEach { dCompany ->
//            val borrowerCreditLimits = counterparty_credit_limits
//                .asSequence()
//                .filter { it.borrower.company_id == dCompany.company_id }
//                .toList()
//            val credit_limit = borrowerCreditLimits.minByOrNull { it.credit_limit ?: 0.0 }?.credit_limit ?: 0.0
//            dCompany.initial_buying_cost_limit = credit_limit
//        }
    }

    init {
        rounds.add(DeRound(number = 1, price = null, has_reversed_ = false, direction = null)) // no direction
        setState(DeAuctioneerState.STARTING_PRICE_NOT_SET)
    }

}


/*
    Convention: June 24, 2019:
    - collections are only on the root DeAuction // OLD
    - other components embed var constructor pointers
    - lookup is therefore by the root
    - creation is checked by the root
    - extensions are only on the root
    - separate View reports will be used to create json

    Convention: Jan 28, 2021
    - using pattern more convenient for potential move to RavenDB, Documents (Aggregates) should be:
        - Independent:
          - have a separate identity (ie: an Entity)
        - Isolated:
          - a document is changed in a transaction, independent from other documents!!
        - Coherent:
          - a document should be legible on it's own.
    - therefore trying to make sure that documents don't reference other documents.
    - so, for exampl, Traders, will be referenced by short name, and users by username
    - this means that trader names cannot be changed for open auctions, etc. !!

 */


// 1) State: note: the state is set via the enum, but as a string

//     var state : IAuctionState
//        get() = enumvalueOf(state_label) // enumvarueOrNull<TeAuctionState>(state_string)!!
//        set(varue) {
//            state_label = varue.toString()
//        }
//
//    inline fun <reified T> getState(): T
//            where
//            T : Enum<T>,
//            T : IAuctionState = enumvalueOf(state_string)
//
//    inline fun <reified T> inState(vararg states: T): Boolean
//            where
//            T : Enum<T>,
//            T : IAuctionState = states.contains(getState())
//
//    fun <T> setState(state: T) where
//            T : Enum<T>,
//            T : IAuctionState {
//        this.state_string = state.toString()
//    }
//
//    fun setState(state: DeState) {
//        super.setState(state)
//        closed = state.in_state(DeState.AUCTION_CLOSED)
//        ticking = state.in_state(DeState.ROUND_RUNNING) // I think this is the only one?
//    }

```

## de/model/DeAuctionSettings.kt

```kotlin
// File: DeAuctionSettings.kt
package au21.engine.domain.de.model

import javax.persistence.Embeddable

@Embeddable
class DeAuctionSettings(

    var use_counterparty_credits: Boolean,

    // these must'nt be null:
    // var starting_time: Date,
    var starting_price_announcement_mins: Int = 0,

    var round_open_min_secs: Int,
    var round_closed_min_secs: Int,
    var round_orange_secs: Int,
    var round_red_secs: Int,

//  var show_current_round_in_history: Boolean = false, // so that BWP can have it the way they like it
    //  var first_round_duration: Int,
    //  var following_round_duration: Int,

    var cost_multiplier: Double = 10_000.0, // ie: currency = price_units x volume_units x currency_conversion_rateø

    // VOLUME:
    var quantity_units: String = "MMlb",
    var quantity_minimum: Int = 1,
    var quantity_step: Int = 1,

    // PRICE:
    var price_units: String = "cpp",
    var price_decimal_places: Int = 3,

    // var starting_price: Double, // probably not needed, can set on first round
    var price_rule: DePriceRule
)

```

## de/model/DeAuctionTemplate.kt

```kotlin
// File: DeAuctionTemplate.kt
package au21.engine.domain.de.model

import au21.engine.framework.database.AuEntity
import javax.persistence.Entity

@Entity
class DeAuctionTemplate(
        var template_name: String,
        var settings: DeAuctionSettings
) : AuEntity()

```

## de/model/DeBidConstraints.kt

```kotlin
// File: DeBidConstraints.kt
package au21.engine.domain.de.model

import javax.persistence.Embeddable


@Embeddable
class DeBidConstraints(
    var max_buy_quantity: Int,
    var min_buy_quantity: Int,
    var min_sell_quantity: Int,
    var max_sell_quantity: Int
)

```

## de/model/DeInitialLimits.kt

```kotlin
// File: DeInitialLimits.kt
package au21.engine.domain.de.model

import au21.engine.framework.utils.AuFormatter
import au21.engine.framework.utils.thousands
import javax.persistence.Embeddable

/*
  ie: limits before the auction starts, ie: ignoring round price
  - round constraints will be checked against this.
 */
@Embeddable
class DeInitialLimits {
    // FOR NOW JUST USING BUYING COST LIMIT AND SELLING QUANTITY LIMIT
    var initial_buying_cost_limit = 50_000_000.0
        set(value) {
            field = value
            initial_buying_cost_limit_str = AuFormatter.format_currency(field)
        }

    // removing for now, not strictly needed, might add later
//    var initial_selling_cost_limit = 0.0
//        set(value) {
//            field = value
//            initial_selling_cost_limit_str = AuFormatter.format_currency(field)
//        }
    // removing for now, not strictly needed, might add later
//    var initial_buying_quantity_limit = 0
//        set(value) {
//            field = value
//            initial_buying_quantity_limit_str = field.thousands()
//        }

    var initial_selling_quantity_limit = 50
        set(value) {
            field = value
            initial_selling_quantity_limit_str = field.thousands()
        }


    var initial_buying_cost_limit_str:String = ""
   // var initial_selling_cost_limit_str:String = ""
   // var initial_buying_quantity_limit_str:String = ""
    var initial_selling_quantity_limit_str:String = ""

}

```

## de/model/DeMatch.kt

```kotlin
// File: DeMatch.kt
package au21.engine.domain.de.model

import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.AuFormatter
import javax.persistence.Embeddable


@Embeddable
class DeMatch(
    //round: Round,
    sell_order_: DeOrder,
    buy_order_: DeOrder,
    match_: Int,
    capacity_: Int,
    round_price_multiplier: Double, // ie: cost_multiplier * round_price
) {

    init {
        // TODO: we seem to have this check in so many places!
        if (sell_order_.trading_company.company_id == buy_order_.trading_company.company_id)
            throw AlertException(
                "Buyer and seller cannot be the same: " +
                        sell_order_.trading_company.shortname_at_auction_time
            )
    }

    var sell_order: DeOrder = sell_order_
        private set

    var buy_order: DeOrder = buy_order_
        private set

    var buyer_shortname = buy_order.trading_company.shortname_at_auction_time
        private set

    var seller_shortname = sell_order.trading_company.shortname_at_auction_time
        private set

    // TODO: maybe these 3 point back to the initial limits object?
    var selling_quantity_limit: Int =
        sell_order.trading_company.initial_limits.initial_selling_quantity_limit
        private set

    var credit:Double? = sell_order.trading_company.initial_limits.initial_buying_cost_limit
        private set

    var credit_str:String = sell_order.trading_company.initial_limits.initial_buying_cost_limit_str
        private set

    var buy_limit: Int = buy_order.quantity
        private set

    var sell_limit: Int = sell_order.quantity

    // ie: the min of credit_volume_limit, buyer order vol, seller order vol:
    var capacity: Int = capacity_
        private set

    var match: Int = match_
        private set

    var value: Double = match * round_price_multiplier
        private set

    var value_str: String = AuFormatter.format_currency(value)
        private set
}

```

## de/model/DeOrder.kt

```kotlin
// File: DeOrder.kt
package au21.engine.domain.de.model

import au21.engine.domain.common.model.OrderSubmissionType
import au21.engine.domain.common.model.OrderType
import au21.engine.domain.common.model.PersonProxy
import au21.engine.domain.de.services.defaultorders.DeOrderInfo
import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.AuFormatter
import org.joda.time.DateTime
import java.util.*
import javax.persistence.Embeddable

@Embeddable
class DeOrder(
    round: DeRound,
    trading_company_: DeTradingCompany,
    u: PersonProxy?,
    order_info: DeOrderInfo,
    cost_multiplier: Double,
    prev_order: DeOrder?
) {

    init {
        // TODO: pretty sure this is handled in numerous other places !
        order_info.apply {
            if (order_type != OrderType.NONE && quantity == 0) {
                throw AlertException("Order type cannot be $order_type if quantity is zero")
            }
            if (order_type == OrderType.NONE && quantity != 0) {
                throw AlertException("Order type cannot be NONE if quantity is not zero")
            }
        }
        if (order_info.quantity < 0) {
            throw AlertException("Order quantity cannot be negative")
        }
    }

    var user: PersonProxy? = u
        private set

    var trading_company: DeTradingCompany = trading_company_
        private set

    var timestamp: Date = DateTime().toDate() // used so that can mock
        private set

    var price: Double = round.price ?: 0.0
        private set

    var round_number: Int = round.number
        private set


    // NB: if you have an implied buy, then you are a buyer,
    // and will be allocated in n or pen, even if no incremental bid

    var submission_type_label: String = order_info.submission_type.toString()

    var submission_type: OrderSubmissionType = order_info.submission_type
        get() = OrderSubmissionType.valueOf(submission_type_label)
        private set

    var order_type_label: String = order_info.order_type.toString()
        private set

    var type: OrderType = order_info.order_type
        get() = OrderType.valueOf(order_type_label)
        private set

    var quantity: Int = order_info.quantity
        private set

    fun sellVol() = if (type == OrderType.SELL) quantity else 0
    fun buyVol() = if (type == OrderType.BUY) quantity else 0

    var prev_order: DeOrder? = prev_order
        private set

    var cost:Double = price * cost_multiplier * quantity
        private set

    var cost_str: String = AuFormatter.format_currency(cost)
        private set
}

```

## de/model/DePriceRule.kt

```kotlin
// File: DePriceRule.kt
package au21.engine.domain.de.model

import au21.engine.domain.common.model.AuUserRole
import javax.persistence.Embeddable


@Embeddable
class DePriceRule(
    var price_change_initial: Double = 0.5,
    var price_change_post_reversal: Double = 0.125,
    var excess_level_1_quantity: Int = 10,
    var excess_level_2_quantity: Int = 20,
    var excess_level_3_quantity: Int = 30,
    var excess_level_4_quantity: Int = 40,
    var excess_level_0_label: String = "1+",
    var excess_level_1_label: String = "2+",
    var excess_level_2_label: String = "3+",
    var excess_level_3_label: String = "4+",
    var excess_level_4_label: String = "5+",
) {

    // NOTE: Can be either excess demand (UP), or excess supply (DOWN):

    fun get_excess_level(excess: Int, role: AuUserRole): String =
        when {
            excess > excess_level_4_quantity -> excess_level_4_label
            excess > excess_level_3_quantity -> excess_level_3_label
            excess > excess_level_2_quantity -> excess_level_2_label
            excess > excess_level_1_quantity -> excess_level_1_label
            excess > 0 -> excess_level_0_label
            excess == 0 ->
                if (role == AuUserRole.AUCTIONEER) {
                    "0"
                } else {
                    excess_level_0_label
                }
            else ->
                if (role == AuUserRole.AUCTIONEER) {
                    "-"
                } else {
                    excess_level_0_label
                }
        }

//    fun get_excess_label(excess: Int): String =
//        when {
//            excess <= excess_level_1_volume -> excess_level_0_label
//            excess <= excess_level_2_volume -> excess_level_1_label
//            excess <= excess_level_3_volume -> excess_level_2_label
//            excess <= excess_level_4_volume -> excess_level_3_label
//            else -> excess_level_4_label
//        }

//    fun get_price_change_for_excess_level(level: ExcessLevel): Double =
//        when (level) {
//            ExcessLevel.NONE -> 0.0
//            ExcessLevel.ONE_PLUS -> price_level_1
//            ExcessLevel.TWO_PLUS -> price_level_2
//            ExcessLevel.THREE_PLUS -> price_level_3
//            ExcessLevel.FOUR_PLUS -> price_level_4
//        }
}

```

## de/model/DeRound.kt

```kotlin
// File: DeRound.kt
package au21.engine.domain.de.model

import au21.engine.domain.common.model.OrderType
import au21.engine.domain.common.model.PriceDirection
import au21.engine.framework.commands.AlertException
import org.joda.time.DateTime
import java.util.*
import javax.persistence.Embeddable


@Embeddable
class DeRound(
    var number: Int,
    var price: Double?,
    has_reversed_: Boolean,
    direction: PriceDirection? // will be null on the first round
) {

    init {
        if (number == 0)
            throw AlertException("Rounds must always have a number > 0")
        if (number > 1 && price == null)
            throw AlertException("After round 1, rounds must be created with a non-null price.")
    }

    var has_reversed: Boolean = has_reversed_
        private set

    var price_direction_label: String? = direction?.toString()
        private set
    var price_direction: PriceDirection?
        get() = price_direction_label?.let { PriceDirection.valueOf(it) }
        set(varue) {
            this.price_direction_label = varue.toString()
        }

    var open_time: Date? = null
        private set

    fun open() {
        if (this.open_time != null)
            throw AlertException("open_time should be null")
        open_time = DateTime().toDate()
    }

    var closed_time: Date? = null
        private set

    fun close() {
        if (this.closed_time != null)
            throw AlertException("closed time should be null")
        closed_time = DateTime().toDate()
    }

    var time_started: Date? = null
        private set

    fun started() {
        time_started = DateTime().toDate()
    }

    var trader_infos: MutableList<DeRoundTraderInfo> = mutableListOf()
        private set

    // not sure we need to keep these, seem to be only used as part of flow calc?
//        var limits: MutableList<VolumeLimit> = mutableListOf()
//            private set

    fun buy_orders(): List<DeOrder> =
        trader_infos
            .map { it.order }
            .filter { it.type == OrderType.BUY }

    fun sell_orders(): List<DeOrder> =
        trader_infos
            .map { it.order }
            .filter { it.type == OrderType.SELL }

    fun buyers(): List<DeTradingCompany> = buy_orders().map { it.trading_company }
    fun sellers(): List<DeTradingCompany> = sell_orders().map { it.trading_company }

    var matches: MutableList<DeMatch> = mutableListOf()
        private set

    fun setMatches(m: List<DeMatch>) {
        matches.clear()
        m.forEach { matches.add(it) }
    }

    fun getMatch(t: DeTradingCompany): DeMatch? =
        matches.find {
            it.buyer_shortname == t.shortname_at_auction_time ||
                    it.seller_shortname == t.shortname_at_auction_time
        }

    fun match_vol(): Int = matches.sumOf { it.match }
    fun match_vol(t: DeTradingCompany): Int = matches
        // TODO: t.company_id should be either buy or sell but not both!
        .filter {
            it.buy_order.trading_company.company_id == t.company_id
                    || it.sell_order.trading_company.company_id == t.company_id
        } // it's an alertException if they are equal !!
        .sumOf { it.match }

    fun all_orders_are_non_default(): Boolean =
        trader_infos.all { it.has_non_default_bid() }

    fun round_open_seconds(): Int = 0 // we'll tick this

    var max_potential_flow: Int = 0
        private set

    // TODO: reimplement
//        fun setMaxPotentialFlow(mfr: MaxFlowResult) { // from an optimization engine
//            this.max_potential_flow = mfr.max_flow
//        }

//        var match_ratio_label: String = MatchedVolumeRatio.UP_TO_10.toString()
//            private set
//
//        var matched_sell_ratio: MatchedVolumeRatio
//            get() = enumvalueOfWithDefault(match_ratio_label, MatchedVolumeRatio.UP_TO_10)
//            set(it) {
//                this.match_ratio_label = it.toString()
//            }

}

```

## de/model/DeRoundTraderInfo.kt

```kotlin
// File: DeRoundTraderInfo.kt
package au21.engine.domain.de.model

import au21.engine.domain.common.model.OrderSubmissionType
import au21.engine.domain.common.model.OrderType
import javax.persistence.Embeddable

@Embeddable
class DeRoundTraderInfo(
    constraints_: DeBidConstraints,
    trading_company_: DeTradingCompany,
    var default_order: DeOrder,
    var current_matched_vol: Int,
    var fully_opposed_match_vol: Int
) {

    var de_trading_company: DeTradingCompany = trading_company_
        private set

//        var company_id: String = trader.company_id
//            private set

    var constraints: DeBidConstraints = constraints_
        private set

    var order: DeOrder = default_order
//            set(o: Order) {
//                field = o
////                if (o.submission_type == OrderSubmissionType.MANDATORY)
////                    has_bid_explicitly = true
//            }

    var bid_while_closed: Boolean = false

    fun has_non_default_bid(): Boolean =
        when (order.submission_type) {
            OrderSubmissionType.DEFAULT -> false
            OrderSubmissionType.MANUAL -> true
            OrderSubmissionType.MANDATORY -> true
        }

    fun has_non_zero_bid(): Boolean =
        order.type != OrderType.NONE ||
                order.quantity != 0 // not sure this can happen

}

```

## de/model/DeTradingCompany.kt

```kotlin
// File: DeTradingCompany.kt
package au21.engine.domain.de.model

import au21.engine.domain.common.model.Company
import au21.engine.domain.common.model.CompanyProxy
import au21.engine.domain.common.model.OrderType
import javax.persistence.Embeddable

@Embeddable
class DeTradingCompany(c: Company) : CompanyProxy(c){

    var initial_limits:DeInitialLimits = DeInitialLimits()
        private set

    // Set whenever an order is submitted, and when round is created assuming we alllow that to happen:
    var next_round_order_will_be_mandatory:Boolean = false

    // null means 'no limit'
    var awarded_quantity: Int = 0
        private set

    var awarded_order_type: OrderType = OrderType.NONE
        private set

    fun award(order_type: OrderType, quantity: Int) {
        awarded_order_type = order_type
        awarded_quantity = quantity
    }

    var blinded: Boolean = false

    var rank: Int? = null
}

```

## de/model/enums.kt

```kotlin
// File: enums.kt
package au21.engine.domain.de.model

import au21.engine.framework.database.IAuctionState

enum class DeRoundOpenState {
    GREEN, ORANGE, RED
}

enum class DeTimeState {
    BEFORE_ANNOUNCE_TIME,
    BEFORE_START_TIME,
    AUCTION_HAS_STARTED
}

enum class DeAuctioneerInfoLevel {
    NORMAL,
    WARNING,
    ERROR
}

enum class DeAuctioneerState : IAuctionState {
    STARTING_PRICE_NOT_SET,
    STARTING_PRICE_SET,
    STARTING_PRICE_ANNOUNCED,
    ROUND_OPEN_ALL_ORDERS_NOT_IN,
    ROUND_OPEN_ALL_ORDERS_IN,
    ROUND_CLOSED_NOT_AWARDABLE,
    ROUND_CLOSED_AWARDABLE,
    AUCTION_CLOSED;
    override fun oneOf(vararg states: IAuctionState): Boolean {
        return states.contains(this)
    }
}

enum class DeCommonState {
    SETUP,
    STARTING_PRICE_ANNOUNCED,
    ROUND_OPEN,
    ROUND_CLOSED,
    AUCTION_CLOSED
}


/**
 * allows us to avoid sending substates to traders
 */
fun DeAuctioneerState.common_state():DeCommonState =
    when (this) {
        DeAuctioneerState.STARTING_PRICE_NOT_SET -> DeCommonState.SETUP
        DeAuctioneerState.STARTING_PRICE_SET -> DeCommonState.SETUP
        DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> DeCommonState.STARTING_PRICE_ANNOUNCED
        DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> DeCommonState.ROUND_OPEN
        DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> DeCommonState.ROUND_OPEN
        DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> DeCommonState.ROUND_CLOSED
        DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> DeCommonState.ROUND_CLOSED
        DeAuctioneerState.AUCTION_CLOSED -> DeCommonState.AUCTION_CLOSED
    }


enum class DeRoundState {
    NOT_OPEN, GREEN, ORANGE, RED
}

enum class DeFlowControlType {
//    ENGAGE_AUTO_PILOT,
//    DISENGAGE_AUTO_PILOT,
    HEARTBEAT,
    SET_STARTING_PRICE,
    ANNOUNCE_STARTING_PRICE,
    START_AUCTION,
    CLOSE_ROUND,
    REOPEN_ROUND, // TODO: do we need this??
    NEXT_ROUND,
    AWARD_AUCTION
}

enum class DeCreditSetMode {
    MANUAL,
    MINIMUM
}

// unclear if we'd use MatchedVolum, or excess Demand
//enum class MatchedVolumeRatio(val label: String) {
//    UP_TO_10("up to 10%"),
//    UP_TO_20("10+ to 20%"),
//    UP_TO_30("20+ to 30%"),
//    UP_TO_40("30+ to 40%"),
//    UP_TO_50("40+ to 50%"),
//    UP_TO_60("50+ to 60%"),
//    UP_TO_70("60+ to 70%"),
//    UP_TO_80("70+ to 80%"),
//    UP_TO_90("80+ to 90%"),
//    UP_TO_100("90+ to 100%"),
//    GE_100("100+%");
//}

//enum class ExcessLevel {
//    NONE,
//    ONE_PLUS,
//    TWO_PLUS,
//    THREE_PLUS,
//    FOUR_PLUS
//}

//enum class ExcessDemand{
//    DEMAND_LEVEL_1,
//    DEMAND_LEVEL_2,
//    DEMAND_LEVEL_3,
//    DEMAND_LEVEL_4;
//  //  DEMAND_LEVEL_5;
//
//    companion object {
//        fun get_excess_level(excess: Int): ExcessDemand {
//            return when {
//         //       excess > 50 -> DEMAND_LEVEL_5   // greater than 50 -> 2.000 cpp
//                excess <= 50 -> DEMAND_LEVEL_4  // 50 or less -> 1.000 cpp
//                excess <= 40 -> DEMAND_LEVEL_3  // 40 or less -> 0.500 cpp
//                excess <= 30 -> DEMAND_LEVEL_2  // 30 or less -> 0.250
//                excess <= 20 -> DEMAND_LEVEL_1  // 20 or less -> 0.125
//                else -> throw Error("excess has no level: $excess")// shouldn't get here
//            }
//        }
//    }
//}
//

```

## de/model/extensions.kt

```kotlin
// File: extensions.kt
package au21.engine.domain.de.model

import au21.engine.domain.common.model.*
import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.AuFormatter
import au21.engine.framework.utils.thousands
import org.joda.time.DateTime
import kotlin.math.abs


/********************
 * De AUCTION
 */


// can be used during a round:
fun DeAuction.price_has_overshot(): Boolean =
    lastround().let { n ->
        when (n.price_direction) {
            null -> false // ie: first round
            PriceDirection.UP -> excess_supply(n) < 0
            PriceDirection.DOWN -> excess_demand(n) < 0
        }
    }

fun DeAuction.current_price_direction(): PriceDirection? = lastround().let { n: DeRound ->
    if (n.number == 1)
        return null
    penultimate()?.let { pen: DeRound ->
        // NOTE: assumes that after the first round, round prices cannot be null
        if (n.price!! > pen.price!!) PriceDirection.UP
        else if (pen.price!! > n.price!!) PriceDirection.DOWN
        else throw Error("subsequent rounds can't have the same price")
    }
}

fun DeAuction.time_state(): DeTimeState? = DateTime().toDate().let { now ->
    announce_time()?.let {
        if (now.before(it))
            return DeTimeState.BEFORE_ANNOUNCE_TIME
    }
    starting_time?.let {
        return if (now.before(it))
            DeTimeState.BEFORE_START_TIME
        else
            DeTimeState.AUCTION_HAS_STARTED
    }
    return null
}


fun DeAuction.is_awardable(): Boolean = lastround().let { n ->
    val total_demand = total_buy(n)
    val total_supply = total_sell(n)
    when {

        de_trading_companies.all { it.next_round_order_will_be_mandatory } -> true

        n.price == null -> false

        // n.number == 1 -> false
        total_demand > total_supply ->
            // so the question is: did you get here from an up or a down?
            when (n.has_reversed) {
                false -> false // price will reverse
                true -> {
                    val potential_next_price = n.price!! - settings.price_rule.price_change_post_reversal
                    rounds.any { it.price != null && it.price!! <= potential_next_price }
                }
            }

        total_supply > total_demand ->
            // so the question is: did you get here from an up or a down?
            when (n.has_reversed) {
                (n.price == null) -> false
                false -> false
                true -> {
                    val potential_next_price = n.price!! + settings.price_rule.price_change_post_reversal
                    rounds.any { it.price != null && it.price!! >= potential_next_price }
                }
            }

        else -> true // ie: supply == demand !
    }
}


// 1) if supply equals demand then it's awardable

// 2) if demand != supply and we have not reversed then we keep going

//    // at this point we must have a penultimate
//    val pen: DeAuction.Round = penultimate() ?:
//    throw Error("is_awardable() expected penultimate round")
//
//    if (excess_supply(n) > 0) {
//        // so we need to go down, unless the next price is the same as the previous round
//        return small_decrease(n) == pen.price
//    }
//    if (excess_demand(n) > 0) {
//        return small_increase(n) == pen.price
//    }

//    return false
//}

//fun get_trader(p: Person?): Trader? =
//    traders.find { it.company == p?.company }
//
//fun create_trader(u: Person): Trader =
//    Trader(u.company
//        ?: throw Error("user has no company: $u")).also {
//        traders.add(it)
//    }

////////////////////////////////////


/*
        AUCTION extension methods:
 */


fun DeAuction.getTrader(c: Company?): DeTradingCompany? =
    when (c) {
        null -> null
        else -> de_trading_companies.find { it.company_id == c.id }
    }

fun DeAuction.hasTrader(c: Company?): Boolean =
    when (c) {
        null -> false
        else -> de_trading_companies.any { t -> t.company_id == c.id }
    }

fun DeAuction.hasTrader(u: Person?): Boolean =
    when (u) {
        null -> false
        else -> hasTrader(u.company)
    }

//fun DeAuction.state_snapshot(): AuctionStateSnapshot = AuctionStateSnapshot(this)

// ie: for determining closability: (moved to round )
//fun DeAuction.has_non_default_bid(company: Company?) =
//    when (company) {
//        null -> false
//        else -> rounds.any { r ->
//            r.trader_infos
//                .find { it.company == company }?.let { rti: RoundTraderInfo ->
//                    rti.has_non_default_bid()
//                } ?: false
//        }
//    }

// ie: for determining credit editing, trader remove
fun DeAuction.has_non_zero_bid(company: Company?) =
    when (company) {
        null -> false
        else -> rounds.any { r ->
            r.trader_infos
                .find { it.de_trading_company.company_id == company.id }
                ?.let { rti: DeRoundTraderInfo -> rti.has_non_zero_bid() }
                ?: false
        }
    }


fun DeAuction.is_before_end_of_first_round(): Boolean = rounds.size > 1 || !listOf(
    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE,
    DeAuctioneerState.ROUND_CLOSED_AWARDABLE,
    DeAuctioneerState.AUCTION_CLOSED
).contains(auctioneer_state)


//fun DeAuction.round_closeable(): Boolean =
//    lastround()?.let {
//        when {
//            it.open_time == null -> false
//            it.closed_time != null -> false
//            DateTime(it.open_time)
//                .plusSeconds(settings.round_open_min_secs)
//                .isBefore(DateTime()) ->
//                true
//            else -> false
//        }
//    } ?: false

//fun DeAuction.next_round_openable(): Boolean =
//    lastround()?.let {
//        when {
//            it.closed_time == null -> false
//            DateTime(it.closed_time)
//                .plusSeconds(settings.round_closed_min_secs)
//                .isBefore(DateTime()) -> true
//            else -> false
//        }
//    } ?: false

//fun DeAuction.auction_row_datetime_format(): String =
//    when (this) {
////                is TE_AUCTION ->
////                    "${date_time_format(a.window_one_opens)} to ${date_time_format(a.closing_datetime())}"
//        else -> "not implemented"
//    }
/*
var round_counterparty_limits: MutableList<DE_VOLUME_LIMIT> = mutableListOf()
var matches: MutableList<DeAuction.Match> = mutableListOf()
val rbis: MutableMap<DeAuction.Trader, DeAuction.Trader_ROUND> = mutableMapOf()
 */

// moved to queries
//fun AuEntityManager.open_de_auctions(): List<DeAuction> = findAll<DeAuction>().filter { !it.isClosed() }


//fun Round.get_match_potential(buyer_rti: RoundTraderInfo, seller_rti: RoundTraderInfo): Int {
//    return minOf(
//        limits.find {
//            is_same_entity(
//                it.buyer.company,
//                buyer_rti.trader.company
//            ) && is_same_entity(it.seller.company, seller_rti.trader.company)
//        }?.limit ?: 0,
//        buyer_rti.constraints.max_buy_volume,
//        seller_rti.constraints.max_sell_volume
//    )
//}


fun DeAuction.message_auction_state(): String =
// TODO: this is in the wrong place, it is used by OnMessage and MESSAGE,
// TODO: ie: move to Common_Extensions file
    when (auctioneer_state) {
//        DeState.AUCTION_INIT -> "Auction setting up "
//        DeState.ROUND_READY -> "Round " + this.rounds.size.toString() + " ready to start "
//        DeState.ROUND_RUNNING -> "Round " + this.rounds.size.toString() + " ticking @ " + this.time_remaining + " sec "
//        DeState.ROUND_PAUSED -> "Round " + this.rounds.size.toString() + " PAUSED @ " + this.time_remaining + " sec "
//        DeState.ROUND_CLOSED -> "Round " + this.rounds.size.toString() + " closed "
//        DeState.AUCTION_CLOSED -> "Auction closed "
        // else -> return "state not handled: " + this.state
        else -> throw Exception()
    }

fun DeAuction.excess_side(r: DeRound): OrderType =
    when {
        total_buy(r) > total_sell(r) -> OrderType.BUY
        total_sell(r) > total_buy(r) -> OrderType.SELL
        else -> OrderType.NONE
    }

fun DeAuction.excess_quantity(r: DeRound): Int =
    abs(total_buy(r) - total_sell(r))

fun DeAuction.excess_level(r: DeRound, role: AuUserRole): String =
    settings.price_rule.get_excess_level(excess_quantity(r), role)

//fun DeAuction.excess_direction_str(r: Round): String =
//    if (total_buy(r) > total_sell(r)) "Buy"
//    else if (total_sell(r) > total_buy(r)) "Sell"
//    else "none"

fun DeAuction.show_trader_excess(r: DeRound): Boolean = r != lastround() || listOf(
    DeCommonState.AUCTION_CLOSED
).contains(common_state)

fun DeAuction.format_round_price(role: AuUserRole, r: DeRound): String =
    when (role) {
        // auctioneer view: (auctioneer status, blotter, etc):
        AuUserRole.AUCTIONEER -> r.price?.let {
            AuFormatter.format_to_places(it, this.price_decimal_places)
        } ?: "---"
        // trader view: (ie: common status)
        AuUserRole.TRADER -> when {
            r.price == null || !starting_price_announced() -> "waiting"
            else -> AuFormatter.format_to_places(r.price!!, this.price_decimal_places)
        }
    }


// fun DeAuction.de_counterparty_credit_vol_limit(r: DeAuction_ROUND, seller: DeAuction.Trader, buyer: DeAuction.Trader): Int =
//        Common_Getters.common_counterparty_credit_vol_limit (r.price, a.settings.cost_multiplier, seller.person, buyer.person))

/******************
 * De Trader
 */

fun DeAuction.trader_by_company_id(company_id: Long?): DeTradingCompany? =
    when (company_id) {
        null -> null
        else -> de_trading_companies.find { it.company_id == company_id }
    }

fun DeAuction.trader_by_company_id_str(company_id_str: String?): DeTradingCompany? =
    when (company_id_str) {
        null -> null
        else -> de_trading_companies.find { it.company_id.toString() == company_id_str }
    }

//fun DeAuction.get_trader(u: Person?): DeAuction.Trader? =
//        if (u?.Company != null)
//            traders.first { it.Company == u.Company }
//        else
//            null


fun DeAuction.get_trader(s: AuSession?): DeTradingCompany? =
    when (val c = s?.user?.company) {
        null -> null
        else -> getTrader(c)
    }

fun DeAuction.round_sellers(r: DeRound): List<DeTradingCompany> =
    de_trading_companies.filter {
        r.get_order(it).type == OrderType.SELL
    }

fun DeAuction.round_buyers(r: DeRound): List<DeTradingCompany> =
    de_trading_companies.filter {
        r.get_order(it).type == OrderType.BUY
    }

fun DeAuction.seller_count(r: DeRound): Int = round_sellers(r).size

fun DeAuction.buyer_count(r: DeRound): Int = round_buyers(r).size


/**
 *
 * DeOrder
 *
 */


fun DeAuction.get_orders(t: DeTradingCompany): List<DeOrder> =
    rounds.map { it.trader_infos }
        .flatten()
        .filter { it.de_trading_company.company_id == t.company_id }
        .map { it.order }


// Can't compare traders as they aren't entities, and don't have identity !!
fun DeRound.get_order(t: DeTradingCompany): DeOrder =
    trader_infos
        .find { it.de_trading_company.company_id == t.company_id }
        ?.order
        ?: throw AlertException("We should always have a round info and an order for a trader")


//fun Order?.side_str(): String =
//    when {
//        this == null -> "---"
//        isBuy() -> "Buy"
//        isSell() -> "Sell"
//        else -> throw AlertException("ERROR: order is neither buy, sell, or null.")
//    }

//fun DeOrder?.format_order_volume(): String =
//        this?.let { FormatUtil.format_thousands(this.volume) } ?: ""

fun DeOrder?.format(): String = when (this?.type) {
    null -> "---"
    OrderType.BUY -> "Buy: " + buyVol().thousands()
    OrderType.SELL -> "Sell: " + sellVol().thousands()
    OrderType.NONE -> "Zero"
}


fun DeAuction.order_value_str(o: DeOrder?): String = o?.run {
    // note: o.price will be null before the round price is set!
    // TODO: when round price set: existing orders need to be replaced with new price
    AuFormatter.format_currency(o.price * o.quantity * settings.cost_multiplier)
} ?: "---"


// don't need this:
// fun get_orders(a: DeAuction, r: DeAuction_ROUND): List<DeOrder> = r.get_orders()


/***************
 * De Round
 */

fun DeAuction.penultimate(): DeRound? = rounds.takeIf { it.size > 1 }?.let { it[it.size - 2] }

fun DeAuction.total_sell(r: DeRound? = lastround()): Int = r?.sell_orders()?.sumOf { it.quantity } ?: 0

fun DeAuction.total_buy(r: DeRound? = lastround()): Int = r?.buy_orders()?.sumOf { it.quantity } ?: 0

fun DeAuction.excess_demand(r: DeRound? = lastround()): Int = total_buy(r) - total_sell(r)

fun DeAuction.excess_supply(r: DeRound? = lastround()): Int = total_sell(r) - total_buy(r)


/**************
 * De Trader Round
 */

fun DeRound?.get_rti(t: DeTradingCompany?): DeRoundTraderInfo? =
// NB: THIS SHOULD BE THE ONLY PLACE WHERE WE GET RBIs !!
    if (t == null || this == null) {
        null
    } else {
        this.trader_infos.find { it.de_trading_company.company_id == t.company_id }
    }

//fun Round?.get_rti_by_company(company: Company?): RoundTraderInfo? = when {
//    this == null -> null
//    company == null -> null
//    else -> trader_infos.find { it.company == company }
//}

// TODO: do we need to look up by companp_id?
//fun Round?.get_rti_by_company_id(company_id: String?): RoundTraderInfo? = when {
//    this == null -> null
//    company_id == null -> null
//    else -> trader_infos.find { it.company.id_str() == company_id }
//}

fun DeRound?.get_rti_by_company(company: Company?): DeRoundTraderInfo? =
    when {
    this == null -> null
    company == null -> null
    else -> trader_infos.find { it.de_trading_company.company_id == company.id }
}



fun DeRound.sell_quantity(t: DeTradingCompany): Int =
    get_order(t).takeIf { it.type == OrderType.SELL }?.quantity ?: 0


fun DeRound.buy_quantity(t: DeTradingCompany): Int =
    get_order(t).takeIf { it.type == OrderType.BUY }?.quantity ?: 0


//
//fun set_round_price(ratio:MatchedVolumeRatio) {
//    when (ratio) {
//        MatchedVolumeRatio.UP_TO_10  -> price_level_1
//        MatchedVolumeRatio.UP_TO_20  -> price_level_2
//        MatchedVolumeRatio.UP_TO_30  -> price_level_3
//        MatchedVolumeRatio.UP_TO_40  -> price_level_4
//        MatchedVolumeRatio.UP_TO_50  -> price_level_5
//    }
//}

//fun DeAuction.sell_match(): Int =
//        if (?.is_sell() == true) current_matched_vol
//        else 0
//
//fun DeAuction.Trader_ROUND.buy_match(): Int =
//        if (order?.is_buy() == true) current_matched_vol
//        else 0

/**************
 * De Match
 */

// MOVED TO Order.match_vol(t)

//fun DeAuction.Round.volume_limit(seller: DeAuction.Trader, buyer: DeAuction.Trader): Int =
//    this.limits.find { it.sellerId == seller.company_id && it.buyerId == buyer.company_id }?.limit ?: 0
//
//fun DeOrder?.sell_match_vol(t: DeAuction.Trader): Int =
//    if (this == null || this.isBuy()) 0
//    else this.matches.filter { it.company_id == t.company_id }.sumBy { it.volume }
//


//fun DeAuction.Round.total_matched(): Int = {
// NOT SURE EXACTLY WHAT THIS IS DOING: should it be on all the matches, or just this round??
// this.orders.matches.sumBy { it.match_volume }
//    matches.filter { it.buy_order.round_number == this.number || it.sell_order.round_number == this.number }
//        .sumBy { it.match_volume }
////        if (this == null)
//            0
//        else
//            matches.sumBy { it.match_volume }
// or: matches.map { it.match_volume }.sum()

//fun DeAuction_ROUND?.total_match(t: DeAuction.Trader): Int =
//// NOTE: ASSUMES THAT YOU CAN'T BE A BUYER AND A SELLER IN A ROUND
//        if (this == null)
//            0
//        else
//            matches.filter {
//                it.buy_order.trader == t ||
//                        it.sell_order.trader == t
//            }.sumBy { it.match_volume }
//
//fun DeAuction.Round.buy_matches(o:DeOrder?): List<DeAuction.Match> =
//    when{
//        o == null -> emptyList()
//        o.isSell() -> emptyList()
//        else -> o.matches
//    }
//
//fun DeAuction.Round.sell_matches(o:DeOrder?):List<DeAuction.Match> =
//    when{
//        o == null -> emptyList()
//        o.isBuy() -> emptyList()
//        else -> o.matches
//    }
//
//fun DeAuction.Round.buy_match_vol(o:DeOrder?):Int =
//    buy_matches(o).sumBy { it.volume }
//
//fun DeAuction.Round.sell_match_vol(o:DeOrder?):Int =
//    sell_matches(o).sumBy { it.volume }

//
//fun DeAuction_ROUND.match_potential(seller: DeAuction.Trader, buyer: DeAuction.Trader): DeAuction_ROUND.DeCounterPartyVolume? =
//        this.round_counterparty_limits.find {
//            it.buyer == buyer && it.seller == seller
//        }


/*
fun DeAuction.counter_party_potential_match(
    r: DeAuction.Round,
    limit:
): Int {
    // see similar function in DeMatrixEdgeElement
    val buyer: DeAuction.Trader = get_trader(limit.borrower)
    val seller: DeAuction.Trader = get_trader(limit.lender)

    val buyer_rbi: DeAuction.RoundTraderInfo? = r.get_rbi(buyer)
    val seller_rbi: DeAuction.RoundTraderInfo? = r.get_rbi(seller)

    if (buyer_rbi == null || seller_rbi == null)
        return 0

    if (seller_rbi.max_vol <= 0)
        return 0

    // minimum of seller elig, and counter party volume based on credit:
    return seller_rbi.max_vol.coerceAtMost(
        counterparty_credit_vol_limit(limit, r.price, settings.currency_conversion_rate)
    )

}
*/


//fun DeAuction.calculate_value(r: DeAuction_ROUND?, volume: Int): Double {
//    return if (settings.cost_multiplier && r?.price)
//        volume * settings.cost_multiplier * r!!.price
//    else
//        0.0
//}

//fun DeAuction.is_continuable(): Boolean =
//    lastround()?.let {
//        when (settings.initial_price_direction) {
//            UP -> total_buy(it) > total_sell(it)
//            DOWN -> total_sell(it) > total_buy(it)
//        }
//    } ?: false

//fun DeAuction.actual_activity_percentage(r: Round): Int {
//    val match_vol: Int = r.match_vol()
//    val sell_total: Int = total_sell(r)
//    return if (sell_total == 0)
//        0
//    else
//        (100.0 * match_vol / sell_total).roundToInt()
//}

//fun DeAuction.activity_percentage_formatted_auctioneer(r: Round): String =
//    actual_activity_percentage(r).toString() + "%"
//
//fun DeAuction.activity_percentage_formatted_trader(r: Round): String =
//// TODO: is this implemented already?
//    if (r == lastround() && !this.isClosed())
//        "---"
//    else
//        r.matched_sell_ratio.label

//fun DeAuction.seller_activity(r: Round, for_auctioneer: Boolean): String {
//    val label = settings.volume_units
//    val sell_vol = total_sell(r)
//    return if (r == lastround() && !isClosed())
//        "---"
//    else if (sell_vol > 100)
//        "> 100 $label"
//    else if (sell_vol > 75)
//        "76-100 $label"
//    else if (sell_vol > 50)
//        "51-75 $label"
//    else if (sell_vol > 25)
//        "26-50 $label"
//    else
//        "0-25 $label"
//}


//fun DeAuction.round_feedback(r: Round): String =
//    excess_demand(r).let {
//        when {
//            it > 50 -> "++++"
//            it > 40 -> "++++"
//            it > 30 -> "+++"
//            it > 20 -> "+++"
//            it > 10 -> "++"
//            else -> "+"
//        }
//    }

//fun DeAuction.is_ascending_price(): Boolean = settings.initial_price_direction == UP
//fun DeAuction.is_descending_price(): Boolean = settings.initial_price_direction == DOWN

//fun DeAuction.state_display_label(is_top_line: Boolean): String =
//    when (auctioneer_state) {
////        DeState.AUCTION_INIT ->
////            if (is_top_line) "Waiting to open"
////            else ""
////        DeState.ROUND_READY ->
////            if (is_top_line) "Ready to open round " + this.rounds.size.toString()
////            else ""
////        DeState.ROUND_RUNNING ->
////            if (is_top_line) "Round " + this.rounds.size.toString() + " - ROUND_OPEN"
////            else this.time_remaining_label()
////        DeState.ROUND_PAUSED ->
////            if (is_top_line) "Round " + this.rounds.size.toString() + " - PAUSED"
////            else this.time_remaining_label()
//////         DeState.ROUND_TRIGGERED -> {
//////            throw Exception("ROUND_TRIGGERED NOT IMPLMENTED")
//////            return "Round " + String.valueOf(r!!.round_number) + " open @ " + String.valueOf(app.framework.FormatUtil.invokeMethod("format_round_price", arrayOf<Any>(a, r?.price))) + " " + this.settings.price_label + "\nClock paused at: " + String.valueOf(this.time_remaining) + " seconds" + "\nWAITING FOR ALL ORDERS!"
//////        }
////        DeState.ROUND_CLOSED ->
////            if (is_top_line) "Round " + this.rounds.size.toString() + " AUCTION_CLOSED"
////            else ""
////        DeState.AUCTION_CLOSED ->
////            if (is_top_line) "Auction Closed"
////            else ""
//        else ->
//            throw java.lang.Exception()
//    }

//fun DeAuction.time_remaining_label(): String = "TODO"
////    if (state.oneOf(DeState.ROUND_RUNNING, DeState.ROUND_PAUSED))
////        time_remaining.toString() + " seconds remaining"
////    else ""


```

## de/model/starting-time-labels.kt

```kotlin
// File: starting-time-labels.kt
package au21.engine.domain.de.model


/*

// NOT SURE THIS IS ACTUALLY USED?
fun DeAuction._trader_state_label(): String {
    val state = current_state
    return when (state.auction_state.trader_state()) {
        DeAuctionTraderState.SETUP -> "Waiting for auction to start"
        DeAuctionTraderState.STARTING_PRICE_ANNOUNCED -> "Starting price announced"
        DeAuctionTraderState.ROUND_OPEN -> "Round ${rounds.size} open, waiting for orders."
        DeAuctionTraderState.ROUND_CLOSED -> "Round ${rounds.size} closed."
        DeAuctionTraderState.AUCTION_CLOSED -> "Auction closed"

        DeAuctioneerState.STARTING_PRICE_NOT_SET ->
            when (time_state()) {
                DeTimeState.BEFORE_ANNOUNCE_TIME ->
                    settings.auction_starting_time_text() +
                            " starting price will be annouced ${settings.starting_price_announcement_mins} before the auction starts"
                DeTimeState.BEFORE_START_TIME ->
                    settings.auction_starting_time_text() +
                            " starting price will be announced soon"
                DeTimeState.AFTER_START_TIME ->
                    "Starting price will be announced soon"
            }
        DeAuctioneerState.STARTING_PRICE_SET ->
            when (time_state()) {
                DeTimeState.BEFORE_ANNOUNCE_TIME ->
                    settings.auction_starting_time_text() +
                            " starting price will be annouced ${settings.starting_price_announcement_mins} before the auction starts"
                DeTimeState.BEFORE_START_TIME ->
                    settings.auction_starting_time_text() +
                            " starting price will be annouced before the auction starts"
                DeTimeState.AFTER_START_TIME ->
                    "starting price will be announced soon"
            }

        DeAuctioneerState.STARTING_PRICE_ANNOUNCED ->
            when (time_state()) {
                DeTimeState.BEFORE_ANNOUNCE_TIME ->
                    "starting price is ${_starting_price_label()} ${settings.auction_starting_time_text()}"
                DeTimeState.BEFORE_START_TIME ->
                    "starting price is ${_starting_price_label()} ${settings.auction_starting_time_text()}"
                DeTimeState.AFTER_START_TIME ->
                    "starting price is ${_starting_price_label()} auction will start shortly"
            }
        DeAuctioneerState.ROUND_OPEN_WAITING_FOR_BIDS ->
            lastround()?.let { n: DeAuction.Round ->
                "Round ${n.number} open for bidding." +
                        if (n.round_open_seconds() > settings.round_red_secs)
                            " waiting for slow bidders"
                        else ""
            } ?: ""
        DeAuctioneerState.ROUND_OPEN_ALL_BIDS_IN ->
            lastround()?.let { n: DeAuction.Round ->
                "Round ${n.number} open for bidding." +
                        if (n.round_open_seconds() > settings.round_red_secs)
                            " waiting for slow bidders"
                        else ""
            } ?: ""
        DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> "Round ${lastround()?.number} closed."
        DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> "Round ${lastround()?.number} closed."
        DeAuctioneerState.AUCTION_CLOSED -> "Auction closed"


    }
}

 */

```

## de/model/state-labels.kt

```kotlin
// File: state-labels.kt
package au21.engine.domain.de.model


//    override fun toString(): String =
//        "$auction_state_label \n" +
//                "$round_state_label \n" +
//                "$autopilot \n" +
//                "$starting_price \n" +
//                "$starting_price_announcement_mins} \n" +
//                "$starting_time \n" +
//                "${announce_time()} \n" +
//                "${seconds_until(starting_time)} \n" +
//                "-------------------------\n"


//
//
//fun DeAuction._starting_price_label(): String =
//    when (auctioneer_state) {
//        DeAuctioneerState.STARTING_PRICE_NOT_SET -> ""
//        DeAuctioneerState.STARTING_PRICE_SET ->
//            when (settings.starting_price_announcement_mins) {
//                0 -> "Will be announced when the auction starts"
//                1 -> "Will be announced 1 minute before the auction starts"
//                else -> "Will be announced ${settings.starting_price_announcement_mins} before the auction starts"
//                // TODO: probably we could count down to this?
//            }
//        DeAuctioneerState.STARTING_PRICE_ANNOUNCED ->
//            settings.starting_price?.let { " ${AuFormatter.format_currency(it)} ${settings.price_units}" } ?: "---"
//        DeAuctioneerState.ROUND_OPEN_WAITING_FOR_BIDS -> ""
//        DeAuctioneerState.ROUND_OPEN_ALL_BIDS_IN -> ""
//        DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> ""
//        DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> ""
//        DeAuctioneerState.AUCTION_CLOSED -> ""
//    }
//


//fun DeAuction._auction_row_state_display(): String =
//    when (a) {
//        is DeAuction ->
//            a.current_state.let {
//                when (it.auction_state) {
//                    DeAuctioneerState.STARTING_PRICE_NOT_SET ->
//                        auction_starting_time_text(it.starting_time)
//                    DeAuctioneerState.STARTING_PRICE_SET ->
//                        auction_starting_time_text(it.starting_time)
//                    DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> auction_starting_time_text(it.starting_time)
//                    DeAuctioneerState.ROUND_OPEN ->
//                        "Auction round ${a.lastround().number} is open"
//                    DeAuctioneerState.ROUND_CLOSED ->
//                        "Auction round ${a.lastround().number} closed"
//                    DeAuctioneerState.AUCTION_CLOSED ->
//                        "Auction closed"
//                }
//            }
//        else -> "${a::class.java.simpleName} state not implemented"
//    }



```

## de/services/constraints/constraint-calculator.kt

```kotlin
// File: constraint-calculator.kt
package au21.engine.domain.de.services.constraints

import au21.engine.domain.common.model.OrderType
import au21.engine.domain.common.model.PriceDirection
import au21.engine.domain.de.model.DeAuctionSettings
import au21.engine.domain.de.model.DeBidConstraints
import au21.engine.domain.de.model.DeRound
import au21.engine.domain.de.model.DeTradingCompany
import kotlin.math.floor

/**
 * THIS NEEDS TO BE KEPT IN SYNC WITH: DeTraderBidConfirmModal
 */

fun calculate_first_round_constraints(
    t:DeTradingCompany,
    s:DeAuctionSettings,
    n:DeRound) = DeBidConstraints(

        max_buy_quantity = max_quantity_from_round_price_and_cost_limit(
            t.initial_limits.initial_buying_cost_limit,
            s.cost_multiplier,
            n.price ?: 0.0
        ),
        min_buy_quantity = 0,
        min_sell_quantity = 0,
        max_sell_quantity = t.initial_limits.initial_selling_quantity_limit
//        max_quantity_from_round_price_and_cost_limit(
//            t.initial_limits.initial_selling_cost_limit,
//            s.cost_multiplier,
//            n.price ?: 0.0
//        )
    )

 fun max_quantity_from_round_price_and_cost_limit(
    // HELPER:
    cost_limit: Double,
    round_price: Double,
    cost_multiplier: Double,
): Int =
    when {
        round_price == 0.0 -> 0
        cost_limit == 0.0 -> 0
        cost_multiplier == 0.0 -> 0
        else -> floor(cost_limit / (round_price * cost_multiplier)).toInt()
    }

fun calculate_subsequent_round_constraints(
    prev_round_constraints: DeBidConstraints,
    prev_round_order_type: OrderType,
    prev_round_order_quantity: Int,
    next_round_direction: PriceDirection
): DeBidConstraints {

    /**
     * ASSUMES THAT HAS PASSED this method:
     * - validate_de_order_constraints_and_throw_if_fails()
     * (de_order_validations.kt)
     */

    return when (prev_round_order_type) {

        OrderType.NONE ->

            when (next_round_direction) {
                PriceDirection.UP ->
                    DeBidConstraints(
                        min_buy_quantity = 0,
                        max_buy_quantity = 0,
                        min_sell_quantity = 0,
                        max_sell_quantity = prev_round_constraints.max_sell_quantity
                    )
                PriceDirection.DOWN ->
                    DeBidConstraints(
                        min_buy_quantity = 0,
                        max_buy_quantity = prev_round_constraints.max_buy_quantity,
                        min_sell_quantity = 0,
                        max_sell_quantity = 0
                    )
            }
        OrderType.BUY ->
            when (next_round_direction) {
                PriceDirection.UP ->
                    DeBidConstraints(
                        min_buy_quantity = prev_round_constraints.min_buy_quantity,
                        max_buy_quantity = prev_round_order_quantity,
                        min_sell_quantity = prev_round_constraints.min_sell_quantity,
                        max_sell_quantity = prev_round_constraints.max_sell_quantity
                    )
                PriceDirection.DOWN ->
                    DeBidConstraints(
                        min_buy_quantity = prev_round_order_quantity,
                        max_buy_quantity = prev_round_constraints.max_buy_quantity,
                        min_sell_quantity = 0,
                        max_sell_quantity = 0
                    )
            }
        OrderType.SELL ->
            when (next_round_direction) {
                PriceDirection.UP ->
                    DeBidConstraints(
                        min_buy_quantity = 0,
                        max_buy_quantity = 0,
                        min_sell_quantity = prev_round_order_quantity,
                        max_sell_quantity = prev_round_constraints.max_sell_quantity
                    )
                PriceDirection.DOWN ->
                    DeBidConstraints(
                        min_buy_quantity = prev_round_constraints.min_buy_quantity,
                        max_buy_quantity = prev_round_constraints.max_buy_quantity,
                        min_sell_quantity = prev_round_constraints.min_sell_quantity,
                        max_sell_quantity = prev_round_order_quantity
                    )
            }
    }
}

```

## de/services/de-queries.kt

```kotlin
// File: de-queries.kt
package au21.engine.domain.de.services

import au21.engine.domain.de.model.DeAuction
import au21.engine.framework.database.AuEntityManager
import io.quarkus.logging.Log

fun AuEntityManager.open_de_auctions(): List<DeAuction> =
    try {
        val query = "SELECT de FROM ${DeAuction::class.java.name} de where de.closed=false"
        em.createQuery(query, DeAuction::class.java).resultList
    } catch (e: Exception) {
        Log.info(e.message)
        // Log.error(e.message)
        // e.printStackTrace()
        listOf()
    }

```

## de/services/defaultorders/DeDefaultOrder.kt

```kotlin
// File: DeDefaultOrder.kt
package au21.engine.domain.de.services.defaultorders

import au21.engine.domain.common.model.OrderSubmissionType
import au21.engine.domain.common.model.OrderType
import au21.engine.domain.de.model.DeBidConstraints
import au21.engine.framework.commands.AlertException

class DeOrderInfo(
    val submission_type: OrderSubmissionType,
    val order_type: OrderType,
    val quantity: Int
) {
    companion object {

        /**
         * NB: must be run AFTER next round constraints are calculated!
         * - is also used for manual bid creation
         */

        fun create_default_from_constraints(
            constraints: DeBidConstraints
        ): DeOrderInfo {

            constraints.apply {
                if (max_buy_quantity == 0 && max_sell_quantity == 0) {
                    return DeOrderInfo(
                        OrderSubmissionType.MANDATORY,
                        OrderType.NONE,
                        0
                    )
                } else if (min_buy_quantity == 0 && min_sell_quantity == 0) {
                    return DeOrderInfo(
                        OrderSubmissionType.DEFAULT,
                        OrderType.NONE,
                        0
                    )
                } else if (min_buy_quantity > 0) {
                    return if (max_buy_quantity == min_buy_quantity) {
                        DeOrderInfo(
                            OrderSubmissionType.MANDATORY,
                            OrderType.BUY,
                            min_buy_quantity
                        )
                    } else {
                        DeOrderInfo(
                            OrderSubmissionType.DEFAULT,
                            OrderType.BUY,
                            min_buy_quantity
                        )
                    }
                }
                else if (min_sell_quantity > 0) {
                    return if (max_sell_quantity == min_sell_quantity) {
                        DeOrderInfo(
                            OrderSubmissionType.MANDATORY,
                            OrderType.SELL,
                            min_sell_quantity
                        )
                    } else {
                        DeOrderInfo(
                            OrderSubmissionType.DEFAULT,
                            OrderType.SELL,
                            min_sell_quantity
                        )
                    }
                }
                else {
                    // TODO: is this even possible ??
                    throw AlertException("Unable to calculate default order!")
                }
            }

        }
    }
}

```

## de/services/matcher/DeCapacityEdge.kt

```kotlin
// File: DeCapacityEdge.kt
package au21.engine.domain.de.services.matcher

import au21.engine.domain.common.model.OrderType
import au21.engine.domain.de.model.DeOrder
import au21.engine.domain.de.model.DeRoundTraderInfo
import au21.engine.framework.utils.format_table
import java.lang.Integer.min

data class DeCapacityEdge(
    val from: String,    // SELLER | SOURCE
    val to: String,      // BUYER | SINK
    //  val limit: Int? = 0, // NB: null means NO_LIMIT
    val capacity: Int,
) {

    companion object {

        const val SELL_SOURCE = "SELL_SOURCE"
        const val BUY_SINK = "BUY_SINK"

        fun List<DeCapacityEdge>.to_table(): String =
            format_table(this.sortedBy { it.to }.sortedBy { it.from }, listOf("from_seller", "to_buyer", "capacity")
            )

//        fun nodes(r: DeAuction.Round): List<String> = listOf(
//            listOf(SELL_SOURCE),
//            listOf(BUY_SINK),
//            r.trader_infos.map { it.trader.shortname_at_auction_time }
//        ).flatten()


        // ONLY USED BY DeMatcher (other than testing):

        fun to_edges(r: List<DeRoundTraderInfo>): List<DeCapacityEdge> {

            // TODO: probably we want to sort by time, ie: for bfs searching

            val edges = mutableListOf<DeCapacityEdge>()

            // A) source and sink (ie: 4 edges per user):

            r.forEach { rti: DeRoundTraderInfo ->
                rti.order.let { o: DeOrder ->
                    val shortname: String = o.trading_company.shortname_at_auction_time
                    when (o.type) {

                        // 1) SOURCE -> SELL
                        OrderType.SELL -> {

                            edges += DeCapacityEdge(
                                from = SELL_SOURCE, to = shortname, capacity = o.quantity
                            )

                            edges += DeCapacityEdge(
                                from = shortname, to = BUY_SINK, capacity = 0
                            )

                            r.filter { it.order.type == OrderType.BUY }.forEach {
                                edges += DeCapacityEdge(
                                    from = shortname,
                                    to = it.de_trading_company.shortname_at_auction_time,
                                    capacity = min(rti.order.quantity, it.order.quantity)
                                )
                            }
                        }

                        // 2) BUY -> SINK
                        OrderType.BUY -> {

                            edges += DeCapacityEdge(
                                from = SELL_SOURCE, to = shortname, capacity = 0
                            )

                            edges += DeCapacityEdge(
                                from = shortname, to = BUY_SINK, capacity = o.quantity
                            )

                            r.filter { it.order.type == OrderType.SELL }.forEach { it ->
                                edges += DeCapacityEdge(
                                    from = it.de_trading_company.shortname_at_auction_time,
                                    to = shortname,
                                    capacity = min(rti.order.quantity, it.order.quantity)
                                )
                            }
                        }

                        else -> {}
                    }
                }
            }
            return edges
        }
    }
}

// 3) NONE:

//                        OrderType.NONE -> {
//
//                            edges += DeCapacityEdge(
//                                from = SELL_SOURCE, to = shortname, capacity = 0
//                            )
//
//                            edges += DeCapacityEdge(
//                                from = shortname, to = BUY_SINK, capacity = 0
//                            )
//
//                        }

// B) Counterparty capacity edges:
// Edges
//
//                    r.trader_infos
//                        .filter { it.trader != rti.trader }
//                        .forEach { rti: DeAuction.RoundTraderInfo ->
//
//                            edges += DeCapacityEdge(
//                                from = shortname,
//                                to = rcp.buyer_rti.trader.shortname_at_auction_time,
//                                capacity = when (val cl = rcp.credit_quantity_limit) {
//                                    null -> o.quantity // ie: no limit
//                                    else -> min(cl, o.quantity)
//                                }
//                            )


//                    counterparty_limits
//                        // this should have already been filtered:
//                        .filterNot { it.seller_rti.trader.shortname_at_auction_time == it.buyer_rti.trader.shortname_at_auction_time }
//                        .filter { it.seller_rti.trader.shortname_at_auction_time == shortname }
//                        .forEach { rcp: RoundCounterpartyLimits ->
//                            edges += DeCapacityEdge(
//                                from = shortname,
//                                to = rcp.buyer_rti.trader.shortname_at_auction_time,
//                                capacity = when (val cl = rcp.credit_quantity_limit) {
//                                    null -> o.quantity // ie: no limit
//                                    else -> min(cl, o.quantity)
//                                }
//                            )
//                        }
//                    when (o.volume_type) {
//                        // can't use company id for tests without db!
//                        OrderVolumeType.BUY -> {
//                            constraints.add(
//                                MatchFlowEdge(
//                                    from_seller_shortname = SELL_SOURCE,
//                                    to_buyer_shortname = shortname,
//                                    capacity = 0,
//                                    limit = 0
//                                )
//                            )
//                            constraints.add(
//                                MatchFlowEdge(
//                                    from_seller_shortname = shortname,
//                                    to_buyer_shortname = BUY_SINK,
//                                    capacity = o.volume,
//                                    limit = o.volume
//                                )
//                            )
//                        }
//                        OrderVolumeType.SELL -> {
//                            constraints.add(
//                                MatchFlowEdge(
//                                    from_seller_shortname = SELL_SOURCE,
//                                    to_buyer_shortname = shortname,
//                                    capacity = o.volume,
//                                    limit = o.volume
//                                )
//                            )
//                            constraints.add(
//                                MatchFlowEdge(
//                                    from_seller_shortname = shortname,
//                                    to_buyer_shortname = BUY_SINK,
//                                    capacity = 0,
//                                    limit = 0
//                                )
//                            )
//                        }
//                        OrderVolumeType.NONE -> {
//                            constraints.add(
//                                MatchFlowEdge(
//                                    from_seller_shortname = SELL_SOURCE, to_buyer_shortname = shortname, capacity = 0
//                                )
//                            )
//                            constraints.add(
//                                MatchFlowEdge(
//                                    from_seller_shortname = shortname, to_buyer_shortname = BUY_SINK, capacity = 0
//                                )
//                            )
//                        }
//                    }
//                }
//            }
//
//            // (2) set the trader capacities, only needed for each
//            round_volume_limits.forEach { limit: RoundVolumeLimit ->
//                constraints.add(
//                    MatchFlowEdge(
//                        from_seller_shortname = limit.seller.company_shortname,
//                        to_buyer_shortname = limit.buyer.company_shortname,
//                        capacity = limit.limit
//                    )
//                )
//            }
//
//            return constraints
//        }

```

## de/services/matcher/DeFlowResult.kt

```kotlin
// File: DeFlowResult.kt
package au21.engine.domain.de.services.matcher

import au21.engine.framework.utils.format_table
import io.quarkus.logging.Log
import org.psjava.algo.graph.flownetwork.FordFulkersonAlgorithm
import org.psjava.algo.graph.flownetwork.MaximumFlowAlgorithmResult
import org.psjava.algo.graph.pathfinder.BFSPathFinder
import org.psjava.algo.graph.pathfinder.DFSPathFinder
import org.psjava.ds.graph.CapacityEdge
import org.psjava.ds.graph.MutableCapacityGraph
import org.psjava.ds.math.Function
import org.psjava.ds.numbersystrem.IntegerNumberSystem

/**
 * We'll keep this domain free for now!
 */

data class DeFlowResult(
    val from_seller: String,
    val to_buyer: String,
    val flow: Int
) {

    companion object {

        fun List<DeFlowResult>.log_tables(): String =
            format_table(
                this,
                listOf("from_seller", "to_buyer", "flow")
            )


        fun log(g: MutableCapacityGraph<String, Int>, max_flow_function: Function<CapacityEdge<String, Int>, Int>) {
            Log.info("Capacities:")
            val edges = g.vertices.sorted().flatMap { node: String ->
                g.getEdges(node).map { edge: CapacityEdge<String, Int> ->
                    object {
                        val from = edge.from()
                        val to = edge.to()
                        val capacity = edge.capacity()
                        val flow = max_flow_function.get(edge)
                    }
                }
            }
                .sortedBy { it.from }
                .sortedBy { it.to }

            Log.info(
                format_table(
                    edges,
                    listOf("from", "to", "flow", "capacity")
                )
            )
        }

        // only used by DeMatcher (other than testing)

        fun calculate_max_flow_psjava(edges: List<DeCapacityEdge>, isDFS: Boolean = true): List<DeFlowResult> {

            val nodes: Set<String> = mutableSetOf<String>().apply {
                // adding these two in case of Zero edges! (set will remove them)
                add(DeCapacityEdge.SELL_SOURCE)
                add(DeCapacityEdge.BUY_SINK)
                edges.forEach { c: DeCapacityEdge ->
                    add(c.from)
                    add(c.to)
                }
            }.toSet()

            when {
                !nodes.contains(DeCapacityEdge.SELL_SOURCE) -> throw Error("SOURCE node not found.")
                !nodes.contains(DeCapacityEdge.BUY_SINK) -> throw Error("SINK node not found.")
            }

            //  val start = System.nanoTime()

            val capacityGraph = MutableCapacityGraph.create<String, Int>().apply {
                nodes.forEach { n -> insertVertex(n) }
                edges.forEach { e -> addEdge(e.from, e.to, e.capacity) }
            }

            val result: MaximumFlowAlgorithmResult<Int, CapacityEdge<String, Int>> = FordFulkersonAlgorithm
                .getInstance(if (isDFS) DFSPathFinder.getInstance() else BFSPathFinder.getInstance())
                .calc(
                    capacityGraph,
                    DeCapacityEdge.SELL_SOURCE,
                    DeCapacityEdge.BUY_SINK,
                    IntegerNumberSystem.getInstance()
                )

            val maxflow: Int = result.calcTotalFlow()
            Log.info("max flow: $maxflow")


            val max_flow_function: Function<CapacityEdge<String, Int>, Int> = result.calcFlowFunction()

            log(capacityGraph, max_flow_function)

            val flow_results: List<DeFlowResult> = nodes
                .filter { it != DeCapacityEdge.SELL_SOURCE && it != DeCapacityEdge.BUY_SINK }
                .flatMap { trader ->
                    capacityGraph.getEdges(trader)
                        .filter { it.to() != DeCapacityEdge.BUY_SINK }
                        .map { e: CapacityEdge<String, Int> ->
                            DeFlowResult(
                                from_seller = e.from(),
                                to_buyer = e.to(),
                                flow = max_flow_function.get(e)
                            )
                        }
                }
            return flow_results
        }
    }
}

//
//    fun calculate_max_flow_princeton(constraints: List<DeCapacityEdge>): List<DeFlowResult> {
//
//        val vertex_name_to_index: MutableMap<String, Int> =
//            mutableMapOf(DeCapacityEdge.SELL_SOURCE to 0, DeCapacityEdge.BUY_SINK to 1)
//        val index_to_vertex_name: MutableMap<Int, String> =
//            mutableMapOf(0 to DeCapacityEdge.SELL_SOURCE, 1 to DeCapacityEdge.BUY_SINK)
//
//        val nodes: Set<String> = mutableSetOf<String>().apply {
//            add(DeCapacityEdge.SELL_SOURCE)
//            add(DeCapacityEdge.BUY_SINK)
//            constraints.forEach { c: DeCapacityEdge ->
//                if (!contains(c.from_seller)) {
//                    add(c.from_seller)
//                    val index: Int = vertex_name_to_index.size
//                    vertex_name_to_index[c.from_seller] = index
//                    index_to_vertex_name[index] = c.from_seller
//                }
//                if (!contains(c.to_buyer)) {
//                    add(c.to_buyer)
//                    val index: Int = vertex_name_to_index.size
//                    vertex_name_to_index[c.to_buyer] = index
//                    index_to_vertex_name[index] = c.to_buyer
//                }
//            }
//        }.toSet()
//
//        when {
//            !nodes.contains(DeCapacityEdge.SELL_SOURCE) -> throw Error("SOURCE node not found.")
//            !nodes.contains(DeCapacityEdge.BUY_SINK) -> throw Error("SINK node not found.")
//        }
//        //  val start = System.nanoTime()
//
//
//        val flow_network = FlowNetwork(nodes.size)
//        constraints.forEach { c: DeCapacityEdge ->
//            println("adding constraint: from=${c.from_seller}, to=${c.to_buyer}, capacity=${c.capacity}")
//            flow_network.addEdge(
//                FlowEdge(
//                    vertex_name_to_index[c.from_seller]!!,
//                    vertex_name_to_index[c.to_buyer]!!,
//                    c.capacity.toDouble()
//                )
//            )
//        }
//
//        val max_flow: FordFulkerson = FordFulkerson(
//            flow_network,
//            vertex_name_to_index[DeCapacityEdge.SELL_SOURCE]!!,
//            vertex_name_to_index[DeCapacityEdge.BUY_SINK]!!
//        )
//
//        println("max flow: " + max_flow.value())
//        flow_network.edges().forEach { fe: FlowEdge ->
//            println(
//                "from: ${index_to_vertex_name[fe.from()]}, " +
//                        "to: ${index_to_vertex_name[fe.to()]}, " +
//                        "flow: ${fe.flow()} / " +
//                        "capacity: ${fe.capacity()}"
//            )
//        }
//
//        return emptyList()
//    }

```

## de/services/matcher/DeMatcher.kt

```kotlin
// File: DeMatcher.kt
package au21.engine.domain.de.services.matcher

// import au21.engine.domain.de.services.matcher.RoundCounterpartyCapacity.Companion.to_table
import au21.engine.domain.de.commands.DeOrderSubmitAction
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeMatch
import au21.engine.domain.de.model.DeRound
import au21.engine.domain.de.model.DeTradingCompany
import au21.engine.domain.de.services.matcher.DeCapacityEdge.Companion.to_table
import au21.engine.domain.de.services.matcher.DeFlowResult.Companion.calculate_max_flow_psjava
import au21.engine.domain.de.services.matcher.DeFlowResult.Companion.log_tables
import au21.engine.framework.commands.AlertException
import au21.engine.framework.commands.EngineAction
import io.quarkus.logging.Log


/*
 * takes a round
 * calculates constraints
 * returns the flow results
 *
 * Assumes that quantity limits have been set already!
 *
 * There are 3 external functions that are only used by this file:
 *
 * - 1) RoundBuyerSellerVolumeLimit.calculate_round_quantity_limits(de)
 *
 * - 2) MatchFlowEdge.to_edges(n, round_buyer_seller_quantity_limits)
 *
 * - 3) FlowResult.calculate_max_flow_psjava(edges)
 *
 */

object DeMatcher {

    // this one used by actions
    fun calculate_and_set_matches(
        de: DeAuction,
        caller: EngineAction?,
        debug: Boolean = false
    ) {

        calculate_and_set_matches(
            de.lastround(),
            de.settings.cost_multiplier,
            caller,
            debug
        )
    }

    // this one might be easier for testing
    fun calculate_and_set_matches(
        last_De_round: DeRound,
        cost_multiplier: Double,
        caller: EngineAction?,
        debug: Boolean = false
    ) {

        val price_cost_multiplier = (last_De_round.price ?: 0.0) * cost_multiplier

        // 1) Calculate counterparty capacities:
        val counterparty_limits: List<RoundCounterpartyLimits> =
            RoundCounterpartyLimits.calculate_counterparty_capacities(
                round_trader_infos = last_De_round.trader_infos,
                price_cost_multiplier = price_cost_multiplier
            )

        // 2) use them to calculate the MatchFlowEdges:
        val capacity_edges: List<DeCapacityEdge> = DeCapacityEdge.to_edges(last_De_round.trader_infos)

//        last_round.trader_infos.size
//            .let { trader_count ->
//                (trader_count * trader_count) + trader_count
//            }
//            .let { expected_edge_count ->
//                if (expected_edge_count != capacity_edges.size)
//                    throw AlertException("Expected $expected_edge_count edges, but found: ${capacity_edges.size} edges")
//            }

        // 3) Calculate the max-flow
        // TODO: we should experiment with removing the traders with empty edges
        val max_flow_results: List<DeFlowResult> =
            calculate_max_flow_psjava(capacity_edges)

        // 4) Calculate and set the matches
        val matches: List<DeMatch> =
            create_matches_from_flow_results(counterparty_limits, max_flow_results, price_cost_multiplier)

        last_De_round.setMatches(matches)

        // Debug Logging:
        if (debug || caller is DeOrderSubmitAction) {
            //  Log.info(counterparty_capacities.to_table())
            Log.info(capacity_edges.to_table())
            Log.info(max_flow_results.log_tables())
        }
    }

    fun create_matches_from_flow_results(
        counterparty_capacities: List<RoundCounterpartyLimits>,
        max_flow_results: List<DeFlowResult>,
        round_price_multiplier: Double
    ): List<DeMatch> {

        val tradingCompanies: Set<DeTradingCompany> = counterparty_capacities.flatMap {
            listOf(it.buyer_rti.de_trading_company, it.seller_rti.de_trading_company)
        }.toSet()

        return max_flow_results
            .filter { flow: DeFlowResult ->
                listOf(DeCapacityEdge.SELL_SOURCE, DeCapacityEdge.BUY_SINK)
                    .none { it == flow.from_seller || it == flow.to_buyer }
            }
            .mapNotNull { f: DeFlowResult ->

                val seller: DeTradingCompany =
                    tradingCompanies.find { it.shortname_at_auction_time == f.from_seller }
                        ?: throw Error("No buyer found with shortname: ${f.to_buyer}")

                val buyer: DeTradingCompany =
                    tradingCompanies.find { it.shortname_at_auction_time == f.to_buyer }
                        ?: throw Error("No buyer found with shortname: ${f.to_buyer}")

                if (buyer.company_id != seller.company_id) {
                    val rcl: RoundCounterpartyLimits = counterparty_capacities
                        .find {
                            it.buyer_rti.de_trading_company.company_id == buyer.company_id
                                    && it.seller_rti.de_trading_company.company_id == seller.company_id
                        }
                        ?: throw AlertException("unable to find a quantity limit for buyer=${buyer.shortname_at_auction_time}, seller=${seller.shortname_at_auction_time}")

                    DeMatch(
                        sell_order_ = rcl.seller_rti.order,
                        buy_order_ = rcl.buyer_rti.order,
                        match_ = f.flow,
                        capacity_ = rcl.capacity,
                        round_price_multiplier = round_price_multiplier
                    )

                } else {
                    null
                }
            }
    }
}

```

## de/services/matcher/RoundCounterpartyLimits.kt

```kotlin
// File: RoundCounterpartyLimits.kt
package au21.engine.domain.de.services.matcher

import au21.engine.domain.de.model.DeRoundTraderInfo
import au21.engine.framework.commands.AlertException

// TODO: should this be persisted??

class RoundCounterpartyLimits(
    val seller_rti: DeRoundTraderInfo,
    val buyer_rti: DeRoundTraderInfo,
    val price_cost_multiplier: Double
) {
    init {
        if (seller_rti.de_trading_company.company_id == buyer_rti.de_trading_company.company_id)
            throw AlertException("Quantity limit between same buyer and seller not allowed.")
    }

    val buy_max: Int = buyer_rti.constraints.max_buy_quantity
    val buy_limit: Int = buyer_rti.order.quantity
    val sell_max: Int = seller_rti.constraints.max_sell_quantity
    val sell_limit: Int = seller_rti.order.quantity

    // null = no limit
    // note: we're getting this from the trader, not the company!!
//    val sell_quantity_cost_limit: Int =
//        seller_rti.de_trading_company.initial_limits.initial_selling_cost_limit get_credit_limit_quantity(price_cost_multiplier)

    val capacity = minOf(buy_limit, sell_limit) // , sell_quantity_cost_limit)

//    val capacity_potential = when (credit_quantity_limit)
//    {
//        null -> minOf(buy_max, sell_max)
//        else -> minOf(buy_max, sell_max, credit_quantity_limit)
//    }

//    val credit_limit_str = seller_rti.tradingCompany.get_credit_limit_quantity_str(price_cost_multiplier)

    companion object {

        // ONLY USED BY THE DeMatcher (other than testing)
        fun calculate_counterparty_capacities(
            round_trader_infos:List<DeRoundTraderInfo>,
            price_cost_multiplier: Double
        ): List<RoundCounterpartyLimits> {

            return round_trader_infos.flatMap { buyer_rti: DeRoundTraderInfo ->
                round_trader_infos.mapNotNull { seller_rti: DeRoundTraderInfo ->
                    when (buyer_rti.de_trading_company.company_id == seller_rti.de_trading_company.company_id) {
                        true -> null
                        false -> RoundCounterpartyLimits(
                            seller_rti = seller_rti,
                            buyer_rti = buyer_rti,
                            price_cost_multiplier = price_cost_multiplier
                        )
                    }
                }
            }
        }

    }
}

```

## de/services/matcher/de-potential.kt

```kotlin
// File: de-potential.kt
package au21.engine.domain.de.services.matcher


fun potential_maxflow(){
    TODO("not implemented")
}

```

## de/services/nextroundprice/de-price-calculator.kt

```kotlin
// File: de-price-calculator.kt
package au21.engine.domain.de.services.nextroundprice

import au21.engine.domain.common.model.PriceDirection
import au21.engine.framework.commands.AlertException

/**
 * NEXT ROUND PRICE AND DIRECTION
 */

class DeNextRoundPriceInfo(
    val price: Double,
    val direction: PriceDirection,
    val is_post_price_reversal: Boolean
) {
    companion object {

        // TODO: where is the check than price has not gone below/above a prior price, nor is equal to a prev price

        fun create(
            prev_round_number: Int,
            prev_round_price: Double,
            prev_round_direction: PriceDirection?,
            prev_round_is_post_reversal: Boolean,
            prev_round_total_buy: Int,
            prev_round_total_sell: Int,
            price_change_initial: Double,
            price_change_post_reversal: Double
        ): DeNextRoundPriceInfo {

            val next_round_direction: PriceDirection = when {
                prev_round_total_buy > prev_round_total_sell -> PriceDirection.UP
                prev_round_total_sell > prev_round_total_buy -> PriceDirection.DOWN
                else -> throw AlertException("Cannot create subsequent round if prior round supply and demand are equal!")
            }

            /**
             * first round:
             */

            if (prev_round_number == 1) {
                return DeNextRoundPriceInfo(
                    direction = next_round_direction,
                    is_post_price_reversal = false,
                    price = when (next_round_direction) {
                        PriceDirection.UP ->
                            prev_round_price + price_change_initial
                        PriceDirection.DOWN ->
                            prev_round_price - price_change_initial
                    }
                )
            }

            /**
             * subsequent rounds:
             */

            if(prev_round_direction == null)
                throw AlertException("After first round, previous round direction cannot be null.")

            val price_has_reversed = when {
                    prev_round_is_post_reversal -> true // once you've reversed, then you've always reversed!
                    else -> prev_round_direction != next_round_direction
                }

            val next_round_price: Double = when (next_round_direction) {
                    PriceDirection.UP ->
                        when (price_has_reversed) {
                            false -> prev_round_price + price_change_initial
                            true -> prev_round_price + price_change_post_reversal
                        }
                    PriceDirection.DOWN ->
                        when (price_has_reversed) {
                            false -> prev_round_price - price_change_initial
                            true -> prev_round_price - price_change_post_reversal
                        }
                }

            return DeNextRoundPriceInfo(
                price = next_round_price,
                direction = next_round_direction,
                is_post_price_reversal = price_has_reversed
            )
        }
    }
}

```

## de/services/rounds/de-round-creator.kt

```kotlin
// File: de-round-creator.kt
package au21.engine.domain.de.services.rounds

import au21.engine.domain.de.model.*
import au21.engine.domain.de.services.constraints.calculate_subsequent_round_constraints
import au21.engine.domain.de.services.defaultorders.DeOrderInfo
import au21.engine.domain.de.services.matcher.DeMatcher
import au21.engine.domain.de.services.nextroundprice.DeNextRoundPriceInfo
import au21.engine.framework.commands.AlertException

/**
 * THESE MUST BE CALLED AFTER THE ROUND IS CREATED!
 */

/**
 *  CURRENT
 *  - first round always created (ie: when auction created)
 *  - then when traders added, you create the rti's
 */

fun DeAuction.create_subsequent_round(): DeRound {
    /**
     * ALWAYS: if prev supply > demand then price goes up
     * else if demand > supply then price goes down
     * else if supply and demand are equal then stop ?
     */

    val prev_de_round: DeRound = lastround()
    // 1)
    val next_price_info: DeNextRoundPriceInfo = DeNextRoundPriceInfo.create(
        prev_round_number = prev_de_round.number,
        prev_round_price = prev_de_round.price
            ?: throw AlertException("Previous round has no price, therefore cannot create subsequent round"),
        prev_round_direction = prev_de_round.price_direction,
        prev_round_is_post_reversal = prev_de_round.has_reversed,
        prev_round_total_buy = total_buy(prev_de_round),
        prev_round_total_sell = total_sell(prev_de_round),
        price_change_initial = settings.price_rule.price_change_initial,
        price_change_post_reversal = settings.price_rule.price_change_post_reversal,
    )

    // 2)
    val next_de_round = DeRound(
        number = rounds.size + 1,
        price = next_price_info.price,
        has_reversed_ = next_price_info.is_post_price_reversal,
        direction = next_price_info.direction
    )

    if (next_de_round.number < 2) throw AlertException("Expected round number > 1")

    rounds.add(next_de_round)

    de_trading_companies.forEach { t: DeTradingCompany ->

        // remove if existing:
        next_de_round.apply { get_rti(t)?.let { trader_infos.remove(it) } }

        val prev_order: DeOrder = prev_de_round.get_order(t)

        val prev_rti: DeRoundTraderInfo = prev_de_round.get_rti(t)
            ?: throw AlertException("Exepected round trader info for trader: ${t.shortname_at_auction_time} in round ${prev_de_round.number}.")

        val prev_constraints = prev_rti.constraints

        // 3)
        val next_round_constraints:DeBidConstraints = calculate_subsequent_round_constraints(
            prev_constraints, prev_order.type, prev_order.quantity, next_price_info.direction
        )

        // 4)
        val order_info:DeOrderInfo =  DeOrderInfo.create_default_from_constraints(next_round_constraints)

        val default_order: DeOrder = DeOrder(
            round = next_de_round,
            trading_company_ = t,
            u = null,
            order_info = order_info,
            cost_multiplier = settings.cost_multiplier,
            prev_order = null,
        )

        next_de_round.trader_infos.add(
            // 5)
            DeRoundTraderInfo(
                trading_company_ = t,
                constraints_ = next_round_constraints,
                default_order = default_order,
                current_matched_vol = 0,
                fully_opposed_match_vol = 0
            )
        )
        // in case we somehow have default buy and sell bids (not sure if that's possible)
        DeMatcher.calculate_and_set_matches(this, null)
    }
    return next_de_round
}

```

## de/services/sampledb/TradingUserFixture.kt

```kotlin
// File: TradingUserFixture.kt
package au21.engine.domain.de.services.sampledb

import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.Company
import au21.engine.domain.common.model.Person
import au21.engine.framework.database.AuEntityManager
import io.quarkus.logging.Log

class TradingUserFixture {

    companion object {

        fun create(db: AuEntityManager, trader_count: Int, use_counterparty_credits: Boolean): List<Person> {
            create(trader_count, db.findAll<Person>().filter { it.isTrader() }, db.findAll()).map { u: Person ->
                u.also {
                    db.save(it)
                    it.company?.let { c: Company -> db.save(c) }
                }
            }
            return db.findAll<Person>().filter { it.isTrader() }
        }


        fun create(
            trader_count: Int,
            existing_trader_users: List<Person>,
            existing_companies: List<Company>,
            unlimited_credit: Boolean = false,
        ): List<Person> {

            fun uname(i: Int) = "b$i"

            fun shortname(u: Person): String =
                u.company?.shortname ?: u.username.replace("b", "").toInt().let {
                    "c-$it"
                }

            fun longname(u: Person): String =
                u.company?.shortname ?: u.username.replace("b", "").toInt().let {
                    "c-$it long name"
                }

            val new_users: List<Person> = (1..trader_count).mapNotNull { i ->
                val username = uname(i)
                if (existing_trader_users.none { it.username == username }) {
                    Person(
                        username = username,
                        password = "1",
                        role = AuUserRole.TRADER,
                        company = null
                    ).also { u: Person ->
                        Log.debug("created user: " + u.username)
                        u.company = existing_companies.find { it.shortname == shortname(u) }
                            ?: run {
                                Company(shortname = shortname(u), longname = longname(u)).also {
                                    Log.debug("created company: " + it.shortname)
                                }
                            }


                    }
                } else null
            }

            val companies: List<Company> = new_users.mapNotNull { it.company } + existing_companies

            /* TODO("refactor to use Auction get/set credit")
            new_users.forEach { u ->
                u.company?.let { c ->
                    companies.forEach { buyer ->
                        if (c != buyer) {
                            c.set_credit_limit(
                                buyer,
                                when (unlimited_credit) {
                                    true -> null // null = unlimited_credit
                                    false -> Random.nextInt(100) * 100_000.0

                            )
                        }
                    }
                }
            }

             */

            return new_users + existing_trader_users
        }

    }

}

```

## de/services/sampledb/de-sample-db-helper.kt

```kotlin
// File: de-sample-db-helper.kt
package au21.engine.domain.de.services.sampledb

import au21.engine.domain.common.model.OrderType
import au21.engine.domain.common.model.Person
import au21.engine.domain.common.model.PersonProxy
import au21.engine.domain.common.model.PriceDirection
import au21.engine.domain.de.model.*
import au21.engine.domain.de.services.matcher.DeMatcher
import au21.engine.domain.de.services.rounds.create_subsequent_round
import au21.engine.domain.de.services.sampledb.SampleOrderMove.DECREASE
import au21.engine.domain.de.services.sampledb.SampleOrderMove.INCREASE
import io.quarkus.logging.Log
import org.joda.time.DateTime
import kotlin.random.Random


/**
 * ASSUMES BID CONSTRAINTS ALREADY SET WHEN ROUND CREATED
 * ie: RTI
 */

enum class SampleOrderMove { INCREASE, DECREASE }


fun random_bid(de: DeAuction, t: DeTradingCompany, u: Person) {

    val n: DeRound = de.lastround()
    val n_rti: DeRoundTraderInfo =
        n.get_rti(t) ?: throw Error("expected rti for trader: ${t.shortname_at_auction_time}")

    val n_constraints: DeBidConstraints =
        n_rti.constraints

    fun simple_frac(
        min: Int, max: Int,
        divisor_min: Int, divisor_max: Int,
        move: SampleOrderMove,
    ): Int {
        try {
            if (min == max)
                return min
            val random_range: Int = Random.nextInt(min, max)
            val random_change: Int = random_range / Random.nextInt(divisor_min, divisor_max)
            return when (move) {
                INCREASE -> min + random_change
                DECREASE -> max - random_change
            }
        } catch (t: Throwable) {
            throw t
        }
    }

    fun simple_frac_5(min: Int, max: Int, move: SampleOrderMove): Int = simple_frac(min, max, 3, 5, move)

    fun submit(order_type: OrderType, quantity: Int) {

        // need to make sure than volume_type is NONE if vol is zero
        val vt: OrderType = when (quantity) {
            0 -> OrderType.NONE
            else -> order_type
        }

        // and vice-versa, vol is 0 if volume_type is NONE
        val vol: Int = when (order_type) {
            OrderType.NONE -> 0
            else -> quantity
        }

        de.create_manual_order(
            r = n,
            t = t,
            order_quantity = when (vt) {
                OrderType.NONE -> 0
                OrderType.BUY -> Integer.max(
                    Integer.min(vol, n_constraints.max_buy_quantity),
                    n_constraints.min_buy_quantity
                )
                OrderType.SELL -> Integer.max(
                    Integer.min(vol, n_constraints.max_sell_quantity),
                    n_constraints.min_sell_quantity
                )
            },
            order_type = vt,
            u = PersonProxy(u) // TODO: this should not be created here!
        )
        // we can do the match once (in production it's done after every bid)
        DeMatcher.calculate_and_set_matches(de, null)
    }

    fun submit(rti: DeRoundTraderInfo, side: OrderType, move: SampleOrderMove) {
        rti.constraints.apply {
            when (side) {
                OrderType.NONE -> submit(OrderType.NONE, 0)
                OrderType.BUY -> submit(
                    OrderType.BUY,
                    simple_frac_5(min_buy_quantity, max_buy_quantity, move)
                )
                OrderType.SELL -> submit(
                    OrderType.SELL,
                    simple_frac_5(min_sell_quantity, max_sell_quantity, move)
                )
            }
        }
    }

    fun submit(rti: DeRoundTraderInfo, side: OrderType) {
        val constraints = rti.constraints
        // here we have to figure out the move direction:
        val range: Int = constraints.max_buy_quantity + constraints.max_sell_quantity
        val fraction = Random.nextInt(range / 5)
        when (side) {
            OrderType.NONE -> submit(OrderType.NONE, 0)
            OrderType.BUY -> when {
                fraction <= constraints.max_buy_quantity ->
                    submit(OrderType.BUY, fraction)
                else ->
                    submit(OrderType.SELL, fraction - constraints.max_buy_quantity)
            }
            OrderType.SELL -> when {
                fraction <= constraints.max_sell_quantity ->
                    submit(OrderType.SELL, fraction)
                else ->
                    submit(
                        OrderType.BUY,
                        fraction - constraints.max_sell_quantity
                    ) // TODO: might be the other way round
            }
        }
    }

    when (n.number) {
        1 ->  // FIRST ROUND:
            if (Random.nextInt(100) > 80) {
                submit(n_rti, OrderType.SELL, INCREASE)
            } else {
                submit(n_rti, OrderType.BUY, DECREASE)
            }
        else -> {
            val n_direction: PriceDirection = n.price_direction!!
            val pen = de.penultimate()!!
            val pen_order: DeOrder = pen.get_order(t)
            val pen_type: OrderType = pen_order.type
            when (n.number) {
                2 -> when {
                    n_direction == PriceDirection.UP && pen_type == OrderType.BUY ->
                        submit(
                            OrderType.BUY,
                            simple_frac_5(n_constraints.min_buy_quantity, n_constraints.max_buy_quantity, DECREASE)
                        )
                    n_direction == PriceDirection.UP && pen_type == OrderType.SELL ->
                        submit(
                            OrderType.SELL,
                            simple_frac_5(n_constraints.min_sell_quantity, n_constraints.max_sell_quantity, INCREASE)
                        )
                    n_direction == PriceDirection.DOWN && pen_type == OrderType.BUY ->
                        submit(
                            OrderType.BUY,
                            simple_frac_5(n_constraints.min_buy_quantity, n_constraints.max_buy_quantity, INCREASE)
                        )
                    n_direction == PriceDirection.DOWN && pen_type == OrderType.SELL ->
                        submit(
                            OrderType.SELL,
                            simple_frac_5(n_constraints.min_sell_quantity, n_constraints.max_sell_quantity, DECREASE)
                        )
                }
                else -> {
                    val has_reversed = n.has_reversed
                    val pen_pen: DeRound = de.rounds.first { it.number == n.number - 2 }
                    val pen_pen_order: DeOrder = pen_pen.get_order(t)
                    val pen_pen_type: OrderType = pen_pen_order.type
                    when (n_direction) {
                        PriceDirection.UP -> when {
                            pen_pen_type == OrderType.BUY && pen_type == OrderType.BUY ->
                                when (has_reversed) {
                                    false -> submit(n_rti, OrderType.BUY, DECREASE)
                                    true -> submit(n_rti, OrderType.BUY)
                                }
                            pen_pen_type == OrderType.BUY && pen_type == OrderType.SELL ->
                                when (has_reversed) {
                                    false -> submit(n_rti, OrderType.SELL, INCREASE)
                                    true -> submit(n_rti, OrderType.SELL)
                                }
                            pen_pen_type == OrderType.SELL && pen_type == OrderType.SELL ->
                                when (has_reversed) {
                                    false -> submit(n_rti, OrderType.SELL, INCREASE)
                                    true -> submit(n_rti, OrderType.SELL, DECREASE)
                                }
                        }
                        PriceDirection.DOWN -> when {
                            pen_pen_type == OrderType.SELL && pen_type == OrderType.SELL ->
                                when (has_reversed) {
                                    false -> submit(n_rti, OrderType.SELL, DECREASE)
                                    true -> submit(n_rti, OrderType.SELL)
                                }
                            pen_pen_type == OrderType.SELL && pen_type == OrderType.BUY ->
                                when (has_reversed) {
                                    false -> submit(n_rti, OrderType.BUY, INCREASE)
                                    true -> submit(n_rti, OrderType.BUY)
                                }
                            pen_pen_type == OrderType.BUY && pen_type == OrderType.BUY ->
                                when (has_reversed) {
                                    false -> submit(n_rti, OrderType.BUY, INCREASE)
                                    true -> submit(n_rti, OrderType.BUY, DECREASE)
                                }
                        }
                    }
                }

            }
        }
    }
}


fun create_sample_db_auction(
    auction_name: String,
    trading_users: List<Person>,
    round_count: Int,
    close_last_round: Boolean,
    use_counterparty_credits:Boolean
): DeAuction {

    Log.info("creating auction: $auction_name")

    if (trading_users.any { it.company == null })
        throw Error("Traders need to have a non-null companies.")

    // val companies: List<Company> = trading_users.map { it.company!! }

    val de = DeAuction(
        auction_name = auction_name,
        settings = DeAuctionSettings(

            use_counterparty_credits = use_counterparty_credits,

            starting_price_announcement_mins = 1,

            round_red_secs = 30,
            round_orange_secs = 15,
            round_open_min_secs = 5,
            round_closed_min_secs = 5,

            //     show_current_round_in_history = false, // so that BWP can have it the way they like it
            //     first_round_duration = 15,
            //     following_round_duration = 15,
            cost_multiplier = 10_000.0, // ie: currency = price_units x volume_units x currency_conversion_rate
            // VOLUME:
            quantity_units = "MMlb",
            quantity_minimum = 1,
            quantity_step = 1,

            // PRICE:
            price_units = "cpp",
            price_decimal_places = 3,
            price_rule = DePriceRule()
        )
    )

    de.starting_time = DateTime().plusSeconds(4).toDate()

    // NB: MAKE SURE THE FIRST ROUND IS CREATED BEFORE THIS POINT!
    val traders: List<Pair<DeTradingCompany, Person>> =
        trading_users.map {
            val t: DeTradingCompany = de.create_trader(it.company!!)
            Pair(t, it)
        }

    de.firstround().price = 100.0
    de.setState(DeAuctioneerState.STARTING_PRICE_SET)
    de.setState(DeAuctioneerState.STARTING_PRICE_ANNOUNCED)
    de.setState(DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN)

    traders.forEach {
        random_bid(de, it.first, it.second)
    }

    if (round_count > 1 || close_last_round) {
        de.setState(
            if (de.is_awardable())
                DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE
            else
                DeAuctioneerState.ROUND_CLOSED_AWARDABLE
        )
    }

    Log.debug("round count: $round_count")
    if (round_count > 1) {

        (2..round_count).forEach { round_num ->
            if (!de.is_awardable()) {
                de.create_subsequent_round()

                // bid:
                traders.forEach {
                    random_bid(de, it.first, it.second)
                }

                Log.debug("round number = $round_num")

                if (round_num < round_count || close_last_round) {
                    de.setState(
                        if (de.is_awardable())
                            DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE
                        else
                            DeAuctioneerState.ROUND_CLOSED_AWARDABLE
                    )
                }
            }

        }
    }

    return de

}

```

## de/services/state/DeControlValidator.kt

```kotlin
// File: DeControlValidator.kt
package au21.engine.domain.de.services.state

import au21.engine.domain.common.model.AutopilotMode
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeAuctioneerState
import au21.engine.domain.de.model.DeFlowControlType
import au21.engine.domain.de.model.is_awardable
import org.joda.time.DateTime

object DeControlValidator {

    const val MUTATE = "MUTATE"

    /**
     * When the return value is:
     * if null:          no need to continue to mutate
     * else if MUTATE:   can go to mutate (set price needs to validate too, before mutate)
     * else:             return the string as an error (AlertException)
     */

    fun validate(de: DeAuction, command: DeFlowControlType): String? {
        //val n: DeAuction.Round = de.lastround()

        // println("\nvalidate $command")
        //  println("$current")
        val now = DateTime().toDate()

        return when (command) {
//            DeFlowControlType.ENGAGE_AUTO_PILOT ->
//                when (de.autopilot) {
//                    AutopilotMode.ENGAGED -> null
//                    AutopilotMode.DISENGAGED -> when (de.auction_state) {
//                        DeAuctioneerState.STARTING_PRICE_NOT_SET -> "Cannot engage autopilot before starting price set"
//                        DeAuctioneerState.STARTING_PRICE_SET -> MUTATE
//                        DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> MUTATE
//                        DeAuctioneerState.ROUND_OPEN_WAITING_FOR_BIDS -> MUTATE
//                        DeAuctioneerState.ROUND_OPEN_ALL_BIDS_IN -> MUTATE
//                        DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> MUTATE
//                        DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> "Cannot engage autopilot, auction is awardable."
//                        DeAuctioneerState.AUCTION_CLOSED -> "Auction closed"
//                    }
//                }
//            DeFlowControlType.DISENGAGE_AUTO_PILOT ->
//                when (de.autopilot) {
//                    AutopilotMode.ENGAGED -> MUTATE
//                    AutopilotMode.DISENGAGED -> null
//                }
            DeFlowControlType.HEARTBEAT ->
                when (de.autopilot) {
                    AutopilotMode.DISENGAGED -> null
                    AutopilotMode.ENGAGED ->
                        when (de.auctioneer_state) {
                            DeAuctioneerState.STARTING_PRICE_NOT_SET -> null
                            DeAuctioneerState.STARTING_PRICE_SET ->
                                when (now.after(de.announce_time()) && de.firstround().price != null) {
                                    true -> MUTATE
                                    else -> null
                                }
                            DeAuctioneerState.STARTING_PRICE_ANNOUNCED ->
                                when (now.after(de.starting_time) && de.firstround().price != null) {
                                    true -> MUTATE
                                    else -> null
                                }
                            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> null
                            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> null
//                                    when (de.has_all_bids() && de.round_closeable()) {
//                                        true -> MUTATE
//                                        else -> null
//                                    }

                            DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE ->
                                TODO()
//                                    when (de.next_round_openable()) {
//                                        true -> MUTATE
//                                        else -> null
//                                    }

                            DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> null // TODO: check this !

                            // no heartbeat here
                            DeAuctioneerState.AUCTION_CLOSED -> null
                        }
                }
            DeFlowControlType.SET_STARTING_PRICE ->
                when (de.auctioneer_state) {
                    DeAuctioneerState.STARTING_PRICE_NOT_SET -> MUTATE
                    DeAuctioneerState.STARTING_PRICE_SET -> MUTATE
                    DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> MUTATE
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> "Cannot set price after auction started."
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> "Cannot set price after auction started."
                    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> "Cannot set price after auction started."
                    DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> "Cannot set price after auction started."
                    DeAuctioneerState.AUCTION_CLOSED -> "Auction closed."
                }
            DeFlowControlType.ANNOUNCE_STARTING_PRICE ->
                when (de.auctioneer_state) {
                    DeAuctioneerState.STARTING_PRICE_NOT_SET -> "Starting price must be set before you can announce it."
                    DeAuctioneerState.STARTING_PRICE_SET -> MUTATE
                    DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> null
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> "Auction already started."
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> "Auction already started."
                    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> "Auction already started."
                    DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> "Auction already started."
                    DeAuctioneerState.AUCTION_CLOSED -> "Auction closed."
                }
            DeFlowControlType.START_AUCTION ->
                when (de.auctioneer_state) {
                    DeAuctioneerState.STARTING_PRICE_NOT_SET -> "Cannot start auction before starting price announced."
                    DeAuctioneerState.STARTING_PRICE_SET -> "Cannot start auction buferu starting price announced."
                    DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> MUTATE
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> null
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> null
                    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> "Auction already started."
                    DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> "Auction already started."
                    DeAuctioneerState.AUCTION_CLOSED -> "Auction closed."
                }
            DeFlowControlType.CLOSE_ROUND ->
                when (de.auctioneer_state) {
                    DeAuctioneerState.STARTING_PRICE_NOT_SET -> "Auction not started."
                    DeAuctioneerState.STARTING_PRICE_SET -> "Auction not started."
                    DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> "Auction not started."
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> MUTATE
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> MUTATE
                    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> null
                    DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> null
                    DeAuctioneerState.AUCTION_CLOSED -> "Auction closed."
                }
            DeFlowControlType.REOPEN_ROUND -> {
                val msg = "Reset only possible when round is closed."
                return when (de.auctioneer_state) {
                    DeAuctioneerState.STARTING_PRICE_NOT_SET,
                    DeAuctioneerState.STARTING_PRICE_SET,
                    DeAuctioneerState.STARTING_PRICE_ANNOUNCED,
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN,
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> msg
                    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> MUTATE
                    DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> MUTATE
                    DeAuctioneerState.AUCTION_CLOSED -> "Auction closed."
                }
            }
            DeFlowControlType.NEXT_ROUND ->
                if (de.is_awardable())
                    "Auction awardable, cannot continue"
                else when (de.auctioneer_state) {
                    DeAuctioneerState.STARTING_PRICE_NOT_SET -> "Auction not started."
                    DeAuctioneerState.STARTING_PRICE_SET -> "Auction not started."
                    DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> "Auction not started"
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> "Current round still open"
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> "Current round still open"
                    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> MUTATE
                    DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> MUTATE
                    DeAuctioneerState.AUCTION_CLOSED -> "Auction Closed."
                }
            DeFlowControlType.AWARD_AUCTION ->
                when (de.auctioneer_state) {
                    DeAuctioneerState.STARTING_PRICE_NOT_SET -> "Auction not started."
                    DeAuctioneerState.STARTING_PRICE_SET -> "Auction not started."
                    DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> "Auction not started."
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> "Round still open."
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> "Round still open."
                    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> "Auction not awardable" // might want to change this!
                    DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> MUTATE
                    DeAuctioneerState.AUCTION_CLOSED -> "Auction closed."
                }
        }
    }

}

```

## de/services/state/DeMutator.kt

```kotlin
// File: DeMutator.kt
package au21.engine.domain.de.services.state

import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeAuctioneerState
import au21.engine.domain.de.model.DeFlowControlType
import au21.engine.domain.de.model.DeFlowControlType.*
import au21.engine.domain.de.model.is_awardable
import au21.engine.domain.de.services.rounds.create_subsequent_round
import io.quarkus.logging.Log
import org.joda.time.DateTime

object DeMutator {

    /**
     * Assumes that all validation has occured before here.
     * ie: no errors are expected.
     *
     * Optionally returns a String which should be a Message !
     */

//    val mutate: (DeCurrentState, DeFlowControlType) -> String? =
//        { current: DeCurrentState, command: DeFlowControlType ->

    fun mutate(de: DeAuction, command: DeFlowControlType, starting_price: Double?): String? {
        Log.debug("Mutate $command")

//        fun engage_autopilot(): String =
//            when (de.autopilot) {
//                AutopilotMode.ENGAGED -> throw Error("Already engaged")
//                AutopilotMode.DISENGAGED -> {
//                    de.autopilot = AutopilotMode.ENGAGED
//                    "autopilot engaged"
//                }
//            }
//
//        fun disengage_autopilot(): String {
//            de.autopilot = AutopilotMode.DISENGAGED
//            return "autopilot disengaged"
//        }

        fun set_starting_price(): String {
            if (starting_price == null)
                throw Error("Cannot set null starting price")
            de.set_starting_price(starting_price)
            if (de.auctioneer_state != DeAuctioneerState.STARTING_PRICE_ANNOUNCED)
                de.setState(DeAuctioneerState.STARTING_PRICE_SET)
            return "starting price set at: $starting_price"
        }

        fun announce_starting_price(): String {
            de.setState(DeAuctioneerState.STARTING_PRICE_ANNOUNCED)
            return "starting price announced"
        }

        fun start_auction(): String {
            de.setState(DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN)
            //  de.submit_constrained_bids()
            return "Auction started, round open"
        }

        fun close_round(): String {
            if (de.is_awardable()) {
                de.setState(DeAuctioneerState.ROUND_CLOSED_AWARDABLE)
            } else {
                de.setState(DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE)
            }

            // de.current_state.autopilot = AutopilotMode.DISENGAGED
            return "round closed, current state = ${de.auctioneer_state}"
        }

        fun reopen_round(): String = run {
            // TODO: should this be called re-open round?
            TODO("reset round not implemented")
//            if (de.rounds.size > 1)
//                de.rounds.removeLast()
//            "Created round ${de.rounds.size}"
        }

        fun next_round(): String = run {
            de.create_subsequent_round()
            de.setState(DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN)
            "going to next round"
        }

        fun award_auction(): String {
            de.setState(DeAuctioneerState.AUCTION_CLOSED)
            return "auction closed"
        }

        val now = DateTime().toDate()

        return when (command) {
//            ENGAGE_AUTO_PILOT -> engage_autopilot()
//            DISENGAGE_AUTO_PILOT -> disengage_autopilot()
            HEARTBEAT ->
                // assumes prior validation for ENGAGED + after various points, or has oll bids
                // TODO: in warning mode only, not auto-pilot:
                // NOT SURE WHY WE HAVE THIS HERE AND IN THE VALIDATOR?
                when (de.auctioneer_state) {
                    DeAuctioneerState.STARTING_PRICE_NOT_SET -> null
                    DeAuctioneerState.STARTING_PRICE_SET ->
                        when (now.after(de.announce_time()) && de.firstround().price != null) {
                            true -> announce_starting_price()
                            false -> null
                        }
                    DeAuctioneerState.STARTING_PRICE_ANNOUNCED ->
                        when (now.after(de.starting_time) && de.firstround().price != null) {
                            true -> start_auction()
                            false -> null
                        }
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> null
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> close_round()
                    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> next_round()
                    DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> null
                    DeAuctioneerState.AUCTION_CLOSED -> null
                }
            SET_STARTING_PRICE -> set_starting_price()
            ANNOUNCE_STARTING_PRICE -> announce_starting_price()
            START_AUCTION -> start_auction()
            CLOSE_ROUND -> close_round()
            REOPEN_ROUND -> reopen_round()
            NEXT_ROUND -> next_round()
            AWARD_AUCTION -> award_auction()
        }

    }

}

```

## de/services/state/auctioneeer_info_level.kt

```kotlin
// File: auctioneeer_info_level.kt
package au21.engine.domain.de.services.state

// TODO: we should push this to the model I think, and persist it, more efficient

// fun DeAuction.de_auctioneer_info_level(): DeAuctioneerInfoLevel =
//    when (auctioneer_state) {
//
//        DeAuctioneerState.STARTING_PRICE_NOT_SET ->
//            when (time_state()) {
//                DeTimeState.BEFORE_ANNOUNCE_TIME -> WARNING
//                DeTimeState.BEFORE_START_TIME -> ERROR
//                DeTimeState.AFTER_START_TIME -> ERROR
//            }
//
//        DeAuctioneerState.STARTING_PRICE_SET ->
//            when (time_state()) {
//                DeTimeState.BEFORE_ANNOUNCE_TIME -> NORMAL // TODO: check this is right
//                DeTimeState.BEFORE_START_TIME -> ERROR
//                DeTimeState.AFTER_START_TIME -> ERROR
//            }
//
//        DeAuctioneerState.STARTING_PRICE_ANNOUNCED ->
//            when (time_state()) {
//                DeTimeState.BEFORE_ANNOUNCE_TIME -> WARNING // yep, could happen if manually announced
//                DeTimeState.BEFORE_START_TIME -> NORMAL
//                DeTimeState.AFTER_START_TIME -> ERROR
//            }
//
//        DeAuctioneerState.ROUND_OPEN_WAITING_FOR_BIDS -> {
//            TODO() // post 25, 30 seconds, closeable, awardable, post last bid
//        }
//
//        DeAuctioneerState.ROUND_OPEN_ALL_BIDS_IN -> {
//            TODO() // post 25, 30 seconds, closeable, awardable, post last bid
//        }
//
//        DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> WARNING
//
//        DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> NORMAL
//
//        DeAuctioneerState.AUCTION_CLOSED -> NORMAL
//    }

```

## de/services/state/control_state_viewmodel.kt

```kotlin
// File: control_state_viewmodel.kt
package au21.engine.domain.de.services.state

import au21.engine.domain.de.model.*
import au21.engine.domain.de.model.DeAuctioneerState.*
import au21.engine.domain.de.model.DeFlowControlType.*


fun DeAuction.de_controls(): Map<DeFlowControlType, Boolean> =
    enumValues<DeFlowControlType>()
        .filter { it != HEARTBEAT }
        .associateWith {
            when (it) {
//                ENGAGE_AUTO_PILOT -> when (autopilot) {
//                    ENGAGED -> false
//                    DISENGAGED -> when (auction_state) {
//                        STARTING_PRICE_SET,
//                        STARTING_PRICE_ANNOUNCED,
//                        ROUND_OPEN_WAITING_FOR_BIDS,
//                        ROUND_OPEN_ALL_BIDS_IN,
//                        ROUND_CLOSED_NOT_AWARDABLE,
//                        ROUND_CLOSED_AWARDABLE -> true
//                        else -> false
//                    }
//                }
//                DISENGAGE_AUTO_PILOT -> autopilot == ENGAGED
                SET_STARTING_PRICE -> when (auctioneer_state) {
                    STARTING_PRICE_NOT_SET,
                    STARTING_PRICE_SET,
                    STARTING_PRICE_ANNOUNCED -> true
                    else -> false
                }
                ANNOUNCE_STARTING_PRICE -> auctioneer_state == STARTING_PRICE_SET
                START_AUCTION -> auctioneer_state == STARTING_PRICE_ANNOUNCED
                CLOSE_ROUND -> auctioneer_state.common_state() == DeCommonState.ROUND_OPEN
                REOPEN_ROUND, // TODO: do we need this?
                NEXT_ROUND -> auctioneer_state == DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE
                AWARD_AUCTION -> auctioneer_state == ROUND_CLOSED_AWARDABLE
                HEARTBEAT -> throw Error("HEARTBEAT should have been filtered out.")
            }
        }

```

## de/services/state/test.kt

```kotlin
// File: test.kt
package au21.engine.domain.de.services.state

import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeAuctionSettings
import au21.engine.domain.de.model.DeFlowControlType
import au21.engine.domain.de.model.DePriceRule
import au21.engine.domain.de.services.state.DeControlValidator.MUTATE
import org.joda.time.DateTime
import kotlin.concurrent.thread

const val TICK_SECS = 3

fun main() {

    val de = DeAuction(
        auction_name = "auction 1",
        settings = DeAuctionSettings(
            use_counterparty_credits = false,
            round_open_min_secs = 5,
            round_closed_min_secs = 5,
            round_orange_secs = 15,
            round_red_secs = 30,
            starting_price_announcement_mins = 1,
            price_rule = DePriceRule()
        )
    ).apply {
        starting_time = DateTime().plusMinutes(1).toDate()
    }

    fun validate_and_mutate(command: DeFlowControlType) {
        when (val result: String? = DeControlValidator.validate(de, command)) {
            null -> {
            }
            MUTATE -> DeMutator.mutate(de, command, null)
            else -> println(result)
        }
    }

    thread {
        while (true) {
            try {
                validate_and_mutate(DeFlowControlType.HEARTBEAT)
                Thread.sleep(TICK_SECS * 1_000L)
            } catch (e: Throwable) {
                println("error: " + e.message)
            }
        }
    }

    //   validate_and_mutate(DeAuctionCommand.SET_STARTING_PRICE)
    //   validate_and_mutate(DeAuctionCommand.ENGAGE_AUTO_PILOT)

    while (true) {
        readLine()?.let { x ->
            try {
                println("pre: ${de.auctioneer_state}")
                when (x.uppercase()) {
//                    "ON" -> validate_and_mutate(DeFlowControlType.ENGAGE_AUTO_PILOT)
//                    "OFF" -> validate_and_mutate(DeFlowControlType.DISENGAGE_AUTO_PILOT)
                    "BEAT" -> validate_and_mutate(DeFlowControlType.HEARTBEAT)
                    "PRICE" -> validate_and_mutate(DeFlowControlType.SET_STARTING_PRICE)
                    "ANNOUNCE" -> validate_and_mutate(DeFlowControlType.ANNOUNCE_STARTING_PRICE)
                    "START" -> validate_and_mutate(DeFlowControlType.START_AUCTION)
                    "CLOSE" -> validate_and_mutate(DeFlowControlType.CLOSE_ROUND)
                    "RESET" -> validate_and_mutate(DeFlowControlType.REOPEN_ROUND)
                    "NEXT" -> validate_and_mutate(DeFlowControlType.NEXT_ROUND)
                    "AWARD" -> validate_and_mutate(DeFlowControlType.AWARD_AUCTION)
                    else -> println("command not understood: $x")
                }
                println("post: ${de.auctioneer_state}")
                println(de.de_controls())
                println("------------------------------------------------------\n")
            } catch (e: NotImplementedError) {
                println("$x not implemented")
            } catch (e: Throwable) {

            }
        }


    }
}


```

## de/validations/de_order_validations.kt

```kotlin
// File: de_order_validations.kt
package au21.engine.domain.de.validations

import au21.engine.domain.common.model.OrderType
import au21.engine.domain.de.model.DeBidConstraints
import au21.engine.framework.commands.fail_if
import au21.engine.framework.utils.thousands

/**
 * ASSUMPTIONS:
 *
 * assumes that order type is corrected, ie:
 * - if buy or sell volume is 0 then set to OrderType.NONE
 *
 */

fun validate_de_order_against_constraints_and_throw_if_fails(
    constraints: DeBidConstraints,
    order_type: OrderType,
    order_quantity: Int,
    quantity_units: String,
) {

    // TODO: this is a LogException, to auctioneers and log files, not sure what trader sees?
    // for now just sending back to trader (and they won't know what 'OrderType None' is)

    fun with_units(vol: Int): String = "${vol.thousands()} $quantity_units"

    return when (order_type) {
        OrderType.NONE -> {
            fail_if(order_quantity > 0, "Order quantity must be zero if OrderType is None")
            fail_if(
                constraints.min_buy_quantity > 0,
                "You cannot submit a zero quantity bid. It is less than your min buy quantity of ${
                    with_units(constraints.min_buy_quantity)
                }"
            )
            fail_if(
                constraints.min_sell_quantity > 0,
                "You cannot submit a zero quantity bid. It is less than your min sell quantity of ${
                    with_units(constraints.min_sell_quantity)
                }"
            )
        }
        OrderType.BUY -> {
            fail_if(
                constraints.min_sell_quantity > 0,
                "You cannot submit a buy order when you have a minimum sell quantity constraint of (${
                    with_units(constraints.min_sell_quantity)
                })"
            )
            fail_if(
                order_quantity > constraints.max_buy_quantity,
                "You cannot buy more than your maximum buy quantity of ${
                    with_units(constraints.max_buy_quantity)
                }"
            )

            fail_if(
                order_quantity < constraints.min_buy_quantity,
                "You cannot buy less than your minimum buy quantity of ${
                    with_units(constraints.min_buy_quantity)
                }"
            )
        }
        OrderType.SELL -> {
            fail_if(
                constraints.min_buy_quantity > 0,
                "You cannot submit a sell order when you have a minimum buy quantity constraint of (${
                    with_units(constraints.min_buy_quantity)
                })"
            )
            fail_if(
                order_quantity > constraints.max_sell_quantity,
                "You cannot sell more than your maximum sell quantity of ${with_units(constraints.max_sell_quantity)}"
            )

            fail_if(
                order_quantity < constraints.min_sell_quantity,
                "You cannot sell less than your minimum sell quantity of ${with_units(constraints.min_sell_quantity)}"
            )

        }
    }

}

```

## de/viewmodel/DeAuctionValue.kt

```kotlin
// File: DeAuctionValue.kt
package au21.engine.domain.de.viewmodel

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.viewmodel.CounterpartyCreditElement
import au21.engine.domain.common.viewmodel.MessageElement
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeTradingCompany
import au21.engine.framework.client.StoreValue


data class DeAuctionValue(
    val auction_id: String?,
    val auction_counterparty_credits: List<CounterpartyCreditElement>,
    val auctioneer_info: DeAuctioneerInfoValue?,
    val auctioneer_status: DeAuctioneerStatusValue?,
    val award_value: DeAwardValue?,
    val blotter: DeBlotter?,
    val common_status: DeCommonStatusValue?,
    val matrix_last_round: DeMatrixRoundElement?,
    val messages: List<MessageElement>,
    val notice: String,
    val settings: DeSettingsValue?,
    val trader_history_rows: List<DeTraderHistoryRowElement>,
    val trader_info: DeTraderInfoValue?,
    val users_that_have_seen_auction: Set<String> // TODO: we should move this to the Blotter Cell

) : StoreValue {

    companion object {

        val value_for_null_auction =
            DeAuctionValue(
                auction_id = null,
                auction_counterparty_credits = emptyList(),
                auctioneer_info = null,
                auctioneer_status = null,
                award_value = null,
                blotter = DeBlotter.create(null),
                matrix_last_round = null,
                messages = emptyList(),
                notice = "",
                settings = null,
                common_status = null,
                trader_history_rows = emptyList(),
                trader_info = null,
                users_that_have_seen_auction = emptySet()
            )


        // TODO: we should cache this!
        fun value_for_auctioneer(
            de: DeAuction,
            s: AuSession,
            auction_counterparty_credits: List<CounterpartyCreditElement>
        ) =
            DeAuctionValue(
                auction_id = de.id_str(),
                auction_counterparty_credits = auction_counterparty_credits,
                auctioneer_info = DeAuctioneerInfoValue.create(de),
                auctioneer_status = DeAuctioneerStatusValue.create(de),
                award_value = DeAwardValue.create(de),
                blotter = DeBlotter.create(de),
                matrix_last_round = DeMatrixRoundElement.create(de.lastround(), de.settings.cost_multiplier),
                messages = MessageElement.message_elements_for_session(de, s),
                notice = de.notice,
                settings = DeSettingsValue.create(de),
                common_status = DeCommonStatusValue.create(de),
                trader_history_rows = emptyList(),
                trader_info = null,
                users_that_have_seen_auction = de.users_that_have_seen_auction.map { it.person_id.toString() }.toSet()
            )


        fun value_for_trader(de: DeAuction, s: AuSession, t: DeTradingCompany) =
            DeAuctionValue(
                auction_id = de.id_str(),
                auction_counterparty_credits = emptyList(),
                auctioneer_info = null,
                auctioneer_status = null,
                award_value = null,
                blotter = DeBlotter.create(null),
                matrix_last_round = null,
                messages = MessageElement.message_elements_for_session(de, s),
                notice = de.notice,
                settings = DeSettingsValue.create(de),
                common_status = DeCommonStatusValue.create(de),
                trader_history_rows = de.rounds.map { r -> DeTraderHistoryRowElement.create(de, r, t) },
                trader_info = DeTraderInfoValue.create(de, t),
                users_that_have_seen_auction = emptySet()
            )

    }
}

```

## de/viewmodel/DeAuctioneerInfoValue.kt

```kotlin
// File: DeAuctioneerInfoValue.kt
package au21.engine.domain.de.viewmodel

import au21.engine.domain.common.model.PriceDirection
import au21.engine.domain.de.model.*
import au21.engine.framework.client.StoreValue
import au21.engine.framework.utils.thousands
import kotlin.math.abs

data class DeAuctioneerInfoValue(
    val allow_credit_editing: Boolean,
    val pen_round: String,
    val last_round: Int,
    val pen_buyers: String,
    val last_buyers: String,
    val pen_sellers: String,
    val last_sellers: String,
    val pen_total_buy: String,
    val last_total_buy: String,
    val pen_total_sell: String,
    val last_total_sell: String,
    val pen_sell_dec: String,
    val last_sell_dec: String,
    val pen_match: String,
    val last_match: String,
    val pen_excess: String,
    val last_excess: String,
    val potential: String
) : StoreValue {

    companion object {

        fun create(de: DeAuction): DeAuctioneerInfoValue {

//        @Transient
//        private val n: DeAuction.Round? = de.lastround()
//
//        @Transient
//        private val pen: DeAuction.Round? = de.penultimate()

            val n: DeRound? = de.lastround()
            val pen: DeRound? = de.penultimate()


            return DeAuctioneerInfoValue(

                allow_credit_editing = false,

                pen_round = pen?.number?.toString() ?: "---",
                last_round = n?.number ?: 0, // TODO

                pen_buyers = pen?.let { de.buyer_count(it).toString() } ?: "---",
                last_buyers = n?.let { de.buyer_count(it) }?.toString() ?: "---",

                pen_sellers = pen?.let { de.seller_count(it).toString() } ?: "---",
                last_sellers = n?.let { de.seller_count(it).toString() } ?: "---",

                pen_total_buy = pen?.let { de.total_buy(pen).thousands() } ?: "---",
                last_total_buy = n?.let { de.total_buy(it).thousands() } ?: "---",

                pen_total_sell = pen?.let { de.total_sell(pen).thousands() } ?: "---",
                last_total_sell = n?.let { de.total_sell(n) }.thousands(),

                pen_sell_dec = "", //pen ? format_thousands ( total_decrement ( a, pen ) ) : '---'
                last_sell_dec = "", // n.round_number == 1 ? '---' : format_thousands ( total_decrement ( a, n ) )

                pen_match = pen?.match_vol()?.thousands() ?: "---",
                last_match = n?.match_vol()?.thousands() ?: "---",

                pen_excess = pen?.let { r ->
                    val demand = de.total_buy(r)
                    val supply = de.total_sell(r)
                    when (r.price_direction) {
                        PriceDirection.UP -> demand - supply
                        PriceDirection.DOWN -> supply - demand
                        null -> abs(demand - supply) // FIRST ROUND WON'T HAVE a price direction!
                    }.thousands()
                } ?: "---",

                last_excess = n?.let { r ->
                    val demand = de.total_buy(r)
                    val supply = de.total_sell(r)
                    when (r.price_direction) {
                        PriceDirection.UP -> demand - supply
                        PriceDirection.DOWN -> supply - demand
                        null -> abs(demand - supply) // FIRST ROUND WON'T HAVE a price direction!
                    }.thousands()
                } ?: "---",

                potential = n?.let { it.max_potential_flow.thousands() } ?: "---"

            )
        }
    }
}

```

## de/viewmodel/DeAuctioneerStatusValue.kt

```kotlin
// File: DeAuctioneerStatusValue.kt
package au21.engine.domain.de.viewmodel

import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.AutopilotMode
import au21.engine.domain.common.model.OrderType
import au21.engine.domain.de.model.*
import au21.engine.domain.de.services.state.de_controls
import au21.engine.framework.client.StoreValue
import org.joda.time.DateTime
import org.joda.time.Period


data class DeAuctioneerStatusValue(
    val announced: Boolean,
    val auctioneer_state: DeAuctioneerState,
    val auctioneer_state_text: String,
    val autopilot: AutopilotMode,
    val awardable: Boolean = false,
    val controls: Map<DeFlowControlType, Boolean>,
    val excess_side: OrderType,
    val excess_level: String,
    // unlike price_has_reversed, price_has_overshot also looks at current round !
    val price_has_overshot: Boolean,
    val round_open_min_secs: Int?,
    //   val round_state: DeRoundState = de.round_state
    val starting_price: String,
    val time_state: DeTimeState?,
) : StoreValue {

//    @Transient
//    private val n: DeAuction.Round? = de.lastround()

    companion object {
        fun create(de: DeAuction): DeAuctioneerStatusValue {

            val n: DeRound = de.lastround()

            return DeAuctioneerStatusValue(
                announced = de.starting_price_announced(),
                auctioneer_state = de.auctioneer_state,
                auctioneer_state_text = de.auctioneer_state_text,
                autopilot = de.autopilot,
                awardable = false, // TODO de.is_continuable()
                controls = de.de_controls(),
                excess_side = de.excess_side(n),
                excess_level = de.excess_level(n, AuUserRole.AUCTIONEER),
                // unlike price_has_reversed, price_has_overshot also looks at current round !
                price_has_overshot = de.price_has_overshot(),
                round_open_min_secs = n.open_time?.let {
                    Period(DateTime(), DateTime(it)).seconds
                },
                //    round_state: DeRoundState = de.round_state
                starting_price = de.format_round_price(AuUserRole.AUCTIONEER, de.firstround()),
                time_state = de.time_state()
            )
        }
    }


}

```

## de/viewmodel/DeAwardValue.kt

```kotlin
// File: DeAwardValue.kt
package au21.engine.domain.de.viewmodel

import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.OrderType
import au21.engine.domain.de.model.*
import au21.engine.framework.client.StoreValue
import au21.engine.framework.utils.thousands
import kotlin.math.min

data class DeAwardValue(val round_results: List<DeRoundResultVM>) : StoreValue { // value used for ts-generator to find it.
    // these will have to be added if we stop updating DeAwardValue with the auction
//    val price_units = de.settings.price_units
//    val volume_units = de.settings.volume_units

    companion object {
        fun create(de: DeAuction) = DeAwardValue(
            round_results = de.rounds
                .takeLast(min(de.rounds.size, 3))
                .reversed()
                .map { DeRoundResultVM.create(de, it) }
        )
    }
}

data class DeRoundResultVM(
    val round_number: Int,
    val round_price: String,
    val buy_total: String,
    val sell_total: String,
    val match_total: String,
    val trader_flows: List<DeTraderFlowVM>,
    val matches: List<DeScenarioMatchVM>
) {

    companion object {
        fun create(de: DeAuction, r: DeRound) =
            DeRoundResultVM(
                round_number = r.number,
                round_price = de.format_round_price(AuUserRole.AUCTIONEER, r),
                buy_total = r.buy_orders().sumOf { it.quantity }.thousands(),
                sell_total = r.sell_orders().sumOf { it.quantity }.thousands(),
                match_total = r.match_vol().thousands(),
                trader_flows = de.de_trading_companies.map { t: DeTradingCompany ->
                    DeTraderFlowVM(
                        company_shortname = t.shortname_at_auction_time,
                        company_id = t.company_id.toString(),
                        order_type = r.get_order(t).type,
                        quantity = r.match_vol(t).thousands()
                    )
                }.sortedBy { it.company_id.toInt() },
                matches = r.matches.map { m: DeMatch ->
                    DeScenarioMatchVM.create(r, m)
                }.sortedBy { it.buyer_id }
            )
    }
}

data class DeTraderFlowVM(
    val company_shortname: String,
    val company_id: String,
    val order_type: OrderType,
    val quantity: String
)

data class DeScenarioMatchVM(
    val round_number: Int,
    val buyer_id: String,
    val buyer_shortname: String,
    val seller_id: String,
    val seller_shortname: String,
    val actual_match: Int,
    val actual_match_str: String
) {

    companion object {
        fun create(r: DeRound, m: DeMatch) = DeScenarioMatchVM(
            round_number = r.number,
            buyer_id = m.buy_order.trading_company.company_id.toString(),
            buyer_shortname = m.buyer_shortname,
            seller_id = m.sell_order.trading_company.company_id.toString(),
            seller_shortname = m.seller_shortname,
            actual_match = m.match,
            actual_match_str = m.match.thousands()
        )
    }
}

```

## de/viewmodel/DeCommonStatusValue.kt

```kotlin
// File: DeCommonStatusValue.kt
package au21.engine.domain.de.viewmodel

import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.PriceDirection
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeCommonState
import au21.engine.domain.de.model.DeRound
import au21.engine.domain.de.model.format_round_price
import au21.engine.framework.client.StoreValue

data class DeCommonStatusValue(
    // auction state moved back here as it's ok to be seed by both
    // - and needed eg in DeRoundPanel.vue
    //    val starting_price: String,
    val common_state: DeCommonState,
    val common_state_text: String,
    val isClosed: Boolean,
    val price_direction: PriceDirection?,
    val price_has_reversed: Boolean,
    val round_number: Int,
    val round_price: String,
    val round_seconds: Int,
    val starting_price_announced: Boolean,
    val starting_time_text: String,
) : StoreValue {

    /**
     * Seen by all users an and auction (traders and auctioneers)
     * Auctioneer-only info was move to DeAuctioneerStatusValue
     */

    companion object {
        fun create(de: DeAuction): DeCommonStatusValue {

//    @Transient
//    private val n: DeAuction.Round? = de.lastround()

            val n: DeRound = de.lastround()

            return DeCommonStatusValue(
                isClosed = de.closed,
                price_direction = n.price_direction,
                price_has_reversed = n.has_reversed,
                round_number = n.number,
                round_seconds = n.round_open_seconds(),
                round_price = when (de.starting_price_announced()) {
                    false -> "waiting"
                    true -> de.format_round_price(AuUserRole.TRADER, n)
                },
                starting_price_announced = de.starting_price_announced(),
                starting_time_text = de.starting_time_text(),
                common_state = de.common_state,
                common_state_text = de.common_state_text
            )
        }
    }
}


```

## de/viewmodel/DeSettingsValue.kt

```kotlin
// File: DeSettingsValue.kt
package au21.engine.domain.de.viewmodel

import au21.engine.domain.common.viewmodel.DateTimeValue
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeAuctionSettings
import au21.engine.domain.de.model.DePriceRule
import au21.engine.framework.client.StoreValue
import au21.engine.framework.utils.AuFormatter
import au21.engine.framework.utils.thousands
import org.joda.time.DateTime

data class DeSettingsValue(
    // val cost_multiplier: String = settings.cost_multiplier.toString()
    val auction_name: String,
    val use_counterparty_credits: Boolean,

    //   val first_round_duration: String = settings.first_round_duration.toString()
    //   val following_round_duration: String = settings.following_round_duration.toString()

    val price_change_initial: String,
    val price_change_post_reversal: String,
    val price_decimal_places: Int,
    val price_label: String,

    val excess_level_0_label: String,
    val excess_level_1_label: String,
    val excess_level_2_label: String,
    val excess_level_3_label: String,
    val excess_level_4_label: String,

    val excess_level_1_quantity: String,
    val excess_level_2_quantity: String,
    val excess_level_3_quantity: String,
    val excess_level_4_quantity: String,

    val cost_multiplier: String,
    val quantity_label: String,
    val quantity_minimum: String,
    val quantity_step: String,

//    // CURRENT STATE
//    val starting_price: String?,
//    val starting_price_set: Boolean,

    val starting_price_announcement_mins: Int,

    // starting time:
    val starting_time: DateTimeValue?,

    val round_red_secs: Int,
    val round_orange_secs: Int,
    val round_open_min_secs: Int,
    val round_closed_min_secs: Int,

    ) : StoreValue {

//    @Transient
//    private val settings: DeAuctionSettings = de.settings
//
//    @Transient
//    private val pr: DePriceRule = settings.price_rule
//    //  private val clock = a.settings.
//
//    @Transient
//    private val places: Int = settings.price_decimal_places
//
//    @Transient
//    private val starting_date_time: DateTime? = de.starting_time?.let { DateTime(it) }


    companion object {

        fun create(de: DeAuction): DeSettingsValue {

            val settings: DeAuctionSettings = de.settings
            val pr: DePriceRule = settings.price_rule
            val places: Int = settings.price_decimal_places
            val starting_date_time: DateTime? = de.starting_time?.let { DateTime(it) }

            return DeSettingsValue(

                //  cost_multiplier = settings.ue_multiplier.toString()
                auction_name = de.auction_name,
                use_counterparty_credits = settings.use_counterparty_credits,

                //    first_round_duration = settings.first_round_duration.toString()
                //    following_round_duration = settings.following_round_duration.toString()

                price_change_initial = AuFormatter.format_to_places(pr.price_change_initial, places),
                price_change_post_reversal = AuFormatter.format_to_places(pr.price_change_post_reversal, places),
                price_decimal_places = settings.price_decimal_places,
                price_label = settings.price_units,

                excess_level_0_label = pr.excess_level_0_label,
                excess_level_1_label = pr.excess_level_1_label,
                excess_level_2_label = pr.excess_level_2_label,
                excess_level_3_label = pr.excess_level_3_label,
                excess_level_4_label = pr.excess_level_4_label,

                excess_level_1_quantity = pr.excess_level_1_quantity.thousands(),
                excess_level_2_quantity = pr.excess_level_2_quantity.thousands(),
                excess_level_3_quantity = pr.excess_level_3_quantity.thousands(),
                excess_level_4_quantity = pr.excess_level_4_quantity.thousands(),

                quantity_label = settings.quantity_units,
                quantity_minimum = settings.quantity_minimum.toString(),
                quantity_step = settings.quantity_step.toString(),

                cost_multiplier = settings.cost_multiplier.toString(), // TODO: format

                // CURRENT STATE
//                starting_price = starting_price,
//                starting_price_set = starting_price != null,
                starting_price_announcement_mins = de.settings.starting_price_announcement_mins,
                // starting time:
                starting_time = starting_date_time?.let { DateTimeValue.create(it) },

                round_red_secs = de.settings.round_red_secs,
                round_orange_secs = de.settings.round_orange_secs,
                round_open_min_secs = de.settings.round_open_min_secs,
                round_closed_min_secs = de.settings.round_closed_min_secs
                )
        }
    }
}

```

## de/viewmodel/DeTraderHistoryRowElement.kt

```kotlin
// File: DeTraderHistoryRowElement.kt
package au21.engine.domain.de.viewmodel

import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.OrderSubmissionType
import au21.engine.domain.common.model.OrderType
import au21.engine.domain.common.model.PriceDirection
import au21.engine.domain.de.model.*
import au21.engine.framework.client.StoreElement
import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.thousands

data class DeTraderHistoryRowElement(
    override val id: String,
    //  val seller_range: String = a.seller_activity(r)
    // val match_range: DeMatchedSellRatio,
    // val match_range_label: String = a.activity_percentage_formatted_trader(r)
    val auction_id: String,
    val bid_constraints: DeBidConstraints?,
    val company_id: String,
    val excess_side: OrderType?,
    val excess_level: String,
    val order_submitted_by: String,
    val order_submission_type: OrderSubmissionType,
    val order_type: OrderType?,
    val price_direction: PriceDirection?,
    val price_has_reversed: Boolean,
    val price_suffix: String,
    val quantity: String,
    val round_number: String,
    val round_price: String,
    val value: String,
) : StoreElement {

    companion object {

//        @Transient
//        private val o: DeOrder? = r.get_order(t)
//
//        @Transient
//        private val rti: DeAuction.RoundTraderInfo? = r.get_rti(t)


        fun create(
            a: DeAuction,
            r: DeRound,
            t: DeTradingCompany // o: DeOrder?
        ): DeTraderHistoryRowElement {

            val o: DeOrder = r.get_order(t)
            val constraints: DeBidConstraints? = r.get_rti(t)?.constraints

            return DeTraderHistoryRowElement(
                id = "ROUND.${r.number}",
                auction_id = a.id_str(),
                bid_constraints = constraints,
                company_id = t.company_id.toString(),
                //  match_range: DeMatchedSellRatio,
                //  match_range_label = a.activity_percentage_formatted_trader(r)
                price_has_reversed = r.has_reversed,
                price_direction = r.price_direction,
                price_suffix =
                when (o.type) {
                    OrderType.BUY -> "or lower"
                    OrderType.SELL -> "or higher"
                    OrderType.NONE -> "none" // TODO: is this used??
                },
                round_number = r.number.toString(),
                round_price = a.format_round_price(AuUserRole.TRADER, r),
                //   seller_range = a.seller_activity(r)
                excess_level =
                when (a.show_trader_excess(r)) {
                    true -> a.excess_level(r, AuUserRole.TRADER)
                    false -> ""
                },
                excess_side =
                when (a.show_trader_excess(r)) {
                    true -> a.excess_side(r)
                    false -> null
                },
                order_submission_type = o.submission_type,
                order_submitted_by = when (o.submission_type) {
                    OrderSubmissionType.MANUAL -> o.user?.username_at_auction_time
                        ?: throw AlertException("Manual order must have a username")
                    OrderSubmissionType.DEFAULT -> "(default)"
                    OrderSubmissionType.MANDATORY -> "(manual)"
                },
                order_type = o.type,
                // ue = a.order_ue_str(o),
                value = a.order_value_str(o),
                quantity = o.quantity.thousands(),
            )
        }
    }


}

```

## de/viewmodel/DeTraderInfoValue.kt

```kotlin
// File: DeTraderInfoValue.kt
package au21.engine.domain.de.viewmodel

import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.OrderSubmissionType
import au21.engine.domain.common.model.OrderType
import au21.engine.domain.de.model.*
import au21.engine.framework.client.StoreValue
import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.thousands


data class DeTraderInfoValue(
    val auction_id: String,
    val award_direction: String,
    val award_line: String?,
    val awarded_price: String,
    val awarded_round_number: String,
    val awarded_quantity: String,
    val awarded_value: String,
    val initial_limits: DeInitialLimits,
    val bid_constraints: DeBidConstraints, // TODO: we don't really need this, can use history row
    val company_id: String,
    val order_submission_type: OrderSubmissionType,
    val order_type: OrderType,
    val order_quantity: Int,
    val price_label: String,
    val quantity_label: String,
    val round_number: Int,
    val round_price: String,
    val value:String,
) : StoreValue {

//    @Transient
//    private val awarded_round: DeAuction.Round? = a.awarded_round
//
//    @Transient
//    private val n: DeAuction.Round? = a.lastround()
//
//    @Transient
//    private val rti: DeAuction.RoundTraderInfo? = n.get_rti(t)
//
//    @Transient
//    private val o: DeOrder? = n?.get_order(t)

    companion object {

        fun create(
            a: DeAuction,
            t: DeTradingCompany
        ): DeTraderInfoValue {

            val awarded_De_round: DeRound? = a.awarded_round
            val n: DeRound = a.lastround()

            // TODO: not sure where this alert goes exactly?
            val rti: DeRoundTraderInfo = n.get_rti(t) ?: throw AlertException("No trader info for round ${n.number}")

            val o: DeOrder = n.get_order(t)

            return DeTraderInfoValue(
               auction_id = a.id_str(),
               quantity_label = a.settings.quantity_units,
                price_label = a.settings.price_units,
//                var cost:Double = round.price * cost_multiplier * order_info.quantity
//                    private set
//
                company_id = t.company_id.toString(),

                round_number = a.lastround().number,
//                // TODO: need to make sure that we don't show a price if auction not announnnced
//                // - should be, because we only create a round when price announced
                round_price = n.let { a.format_round_price(AuUserRole.TRADER, n) },

                initial_limits = t.initial_limits,
                bid_constraints = rti.constraints,

                order_submission_type = o.submission_type,
                order_quantity = o.quantity,
                order_type = o.type,

                awarded_price = awarded_De_round?.let {
                    "${a.format_round_price(AuUserRole.AUCTIONEER, it)} ${a.settings.price_units}"
                } ?: "---",

                awarded_round_number = awarded_De_round?.let {
                    "round ${it.number}"
                } ?: "---",

                awarded_quantity = awarded_De_round?.let {
                    t.awarded_quantity.thousands()
                } ?: "---",

                awarded_value =
                if (a.closed)
                    "$ TODO" // + a.calculate_ue(a.awarded_round, t.awarded_volume).toLong()
                else "---",

                award_direction = "TODO", // if (t.bought) "Bought" else if (t.sold) "Sold" else ""

                award_line = a.awarded_round?.let {
                    if (t.awarded_quantity == 0)
                        "You were not awarded any volume"
                    // else if (StringGroovyMethods.asBoolean("You " + t.bought)) "bought" else "sold" + " ".plus("$awardeD_VOLUME at the round $awardeD_ROUND_NUMBER price of $awardeD_PRICE ").plus("with a ue of $awardeD_UE") else "---"
                    else
                        "TODO"
                },

                value = a.order_value_str(o),
            )
        }

    }
}

```

## de/viewmodel/de-blotter.kt

```kotlin
// File: de-blotter.kt
package au21.engine.domain.de.viewmodel


import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.OrderSubmissionType
import au21.engine.domain.common.model.OrderType
import au21.engine.domain.common.model.PriceDirection
import au21.engine.domain.de.model.*
import au21.engine.framework.client.StoreElement
import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.AuFormatter
import au21.engine.framework.utils.thousands

// TODO: this could be cached for auctioneers on this auction
data class DeBlotter(
    val rounds: List<DeRoundElement>,
    val traders: List<DeTraderElement>,
    val round_traders: List<DeRoundTraderElement>,
) {
    companion object {
        fun create(de: DeAuction?) = DeBlotter(
            rounds = when (de) {
                null -> emptyList()
                else -> de.rounds.map { r: DeRound -> DeRoundElement.create(de, r) }
            },
            traders = when (de) {
                null -> emptyList()
                else -> de.de_trading_companies
                    .map { t: DeTradingCompany -> DeTraderElement.create(de, t) }
                    .sortedBy { it.company_id } // for purposes of
            },
            round_traders = when (de) {
                null -> emptyList()
                else -> de.rounds.flatMap { r: DeRound ->
                    r.trader_infos.map { rti: DeRoundTraderInfo ->
                        DeRoundTraderElement.create(r, rti)
                    }.sortedBy { it.id }
                }
            }
        )
    }
}

data class DeRoundElement(
    override val id: String,
    val all_orders_in_next_round_will_be_mandatory:Boolean,
    val buy_quantity: Int,
    val buyer_count: Int,
    val excess_side: OrderType,
    val excess_indicator: String,
    val excess_quantity: Int,
    val has_reversed: Boolean,
    val match_quantity_changed: Int,
    val matched: Int,
    val potential: Int,
    val potential_changed: Int,
    val raw_matched: Int,
    val round_direction: PriceDirection?,
    val round_duration: String,
    val round_number: Int,
    val round_price: Double?,
    val round_price_str: String,
    val sell_quantity_change: Int,
    val sell_quantity: Int,
    val seller_count: Int

) : StoreElement {


    companion object {

        fun create(
            a: DeAuction,
            r: DeRound,
            prev: DeRound? = a.penultimate(),
            // long buy_change = r.total_incremental_buy (),
            n_matched: Int = r.match_vol(),
            prev_matched: Int = prev?.match_vol() ?: 0
        ) = DeRoundElement(
            id = "ROUND.${r.number}",
            all_orders_in_next_round_will_be_mandatory = a.de_trading_companies.all { it.next_round_order_will_be_mandatory },
            buy_quantity = a.total_buy(r), //.thousands(),
            buyer_count = a.buyer_count(r), //.toString(),
            excess_side = a.excess_side(r),
            excess_indicator = a.excess_level(r, AuUserRole.AUCTIONEER),
            excess_quantity = a.excess_quantity(r),
            has_reversed = r.has_reversed,
            match_quantity_changed = (n_matched - prev_matched), //.thousands(),
            matched = n_matched, //.thousands(),
            potential = r.max_potential_flow, //.thousands(), // TODO: should this be for the prev round?
            potential_changed = (r.max_potential_flow - (prev?.max_potential_flow ?: 0)), // .thousands(),
            raw_matched = n_matched, //.thousands(),
            round_direction = r.price_direction,
            round_duration = "not implemented",
            round_number = r.number,
            round_price = r.price,
            round_price_str = a.format_round_price(AuUserRole.AUCTIONEER, r),
            sell_quantity_change = a.total_sell(r) - (prev?.let { a.total_sell(it) } ?: 0),
            sell_quantity = a.total_sell(r), //.thousands(),
            seller_count = a.seller_count(r), //.toString()
        )
    }
}

data class DeTraderElement(
    override val id: String,
    val has_seen_auction: Boolean,
    val company_id: String,
    val shortname: String,
    val rank: Int?, // NB CAN BE NULL ?
//    // TODO: do we need these 4 ? //    val last_round_current_volume_type: OrderVolumeType, // because we always have a round, we always have a default order!
//    val last_round_current_volume: String,
//    val last_round_previous_volume_type: OrderVolumeType?,
//    val last_round_previous_volume: String,
//    val value: String,
    //   val current_buy_max: String
) : StoreElement {

//    @Transient
//    private val n: Round? = a.lastround()
//
//    @Transient
//    private val rbi: DeAuction.RoundTraderInfo? = n.get_rti(t)
//
//    @Transient
//    private val n_o: DeOrder? = n?.get_order(t)
//
//    @Transient
//    private var pen_o: DeOrder? = a.penultimate()?.get_order(t)


    // username? = null
    // val approved: Boolean = t.approved
    // val has_seen_auction:Boolean =

    // BUY_MAX = format_thousands ( rbi.buy_max )
    // BUY_MIN = format_thousands ( rbi.buy_implied )
    // SELL_MAX = format_thousands ( rbi.sell_eligibility )

    companion object {

        fun create(a: DeAuction, t: DeTradingCompany): DeTraderElement {

            // val n: Round = a.lastround()
            // val rbi: DeAuction.RoundTraderInfo? = n.get_rti(t)
            //  val n_o: DeOrder = n.get_order(t)
            //  val pen_o: DeOrder? = a.penultimate()?.get_order(t)

            return DeTraderElement(
                id = "COMPANY.${t.company_id}",
                has_seen_auction = a.companies_that_have_seen_auction.contains(t),
                company_id = t.company_id.toString(),
                shortname = t.shortname_at_auction_time,
                rank = t.rank, // NB CAN BE NULL ?
//                last_round_current_volume_type = n_o.volume_type,
//                last_round_current_volume = n_o.volume.thousands(),
//                last_round_previous_volume_type = pen_o?.volume_type,
//                last_round_previous_volume = pen_o?.volume.thousands(),
//                value = a.order_value_str(n_o),
                // TODO: move this to round-trader-element:
                // current_buy_max = rbi?.constraints?.max_buy_volume?.thousands() ?: ""
            )
        }
    }
}


data class DeRoundTraderElement(
    override val id: String,
    //  val auction_id: String = a.id.toString()
    val bid_while_closed: Boolean,
    val buyer_credit_limit:Double,
    val buyer_credit_limit_str: String,

    // ie: the previous order was manual
    val changed: Boolean,
    val cid: String,
    val company_shortname:String,
    val constraints: DeBidConstraints,
    val match: Int,
    val order_submitted_by: String,
    val order_submission_type: OrderSubmissionType, // always has to be an order
    val order_type: OrderType,         // always has to be an order
    val round: Int,
    val timestamp_formatted: String,
    val quantity_int: Int,
    val quantity_str: String,
) : StoreElement {

    companion object {

        fun create(
            //a: DeAuction,
            r: DeRound,
            rti: DeRoundTraderInfo,
        ): DeRoundTraderElement {

            val o: DeOrder = rti.order
            val t: DeTradingCompany = rti.de_trading_company

            return DeRoundTraderElement(
                //  val auction_id: String = a.id.toString()
                id = "ROUND.${r.number}.COMPANY.${t.company_id}",
                bid_while_closed = rti.bid_while_closed,
                buyer_credit_limit = t.initial_limits.initial_buying_cost_limit,
                buyer_credit_limit_str = t.initial_limits.initial_buying_cost_limit_str,
                // ie: the previous order was manual
                changed = o.prev_order?.submission_type == OrderSubmissionType.MANUAL,
                cid = t.company_id.toString(),
                company_shortname = t.shortname_at_auction_time,
                constraints = rti.constraints,
                match = r.getMatch(t)?.match ?: 0,
                order_submitted_by = when (o.submission_type) {
                    OrderSubmissionType.MANUAL -> o.user?.username_at_auction_time
                        ?: throw AlertException("If order is manual, then must have a username")
                    OrderSubmissionType.DEFAULT -> "default"
                    OrderSubmissionType.MANDATORY -> "mandatory"
                },
                order_submission_type = o.submission_type,
                order_type = o.type,
                quantity_int = o.quantity,
                quantity_str = o.quantity.thousands(),
                round = r.number,
                timestamp_formatted = AuFormatter.format_order_time(o.timestamp),
            )
        }
    }
}

```

## de/viewmodel/de-matrix.kt

```kotlin
// File: de-matrix.kt
package au21.engine.domain.de.viewmodel

import au21.engine.domain.common.model.OrderType
import au21.engine.domain.de.model.*
import au21.engine.framework.client.ClientCommand.StoreCommand.AddElements
import au21.engine.framework.client.StoreElement

data class DeMatrixRoundElement(
    override val id: String,
    val round_number: Int,
    val nodes: List<DeMatrixNodeElement>,
    val edges: List<DeMatrixEdgeElement>,
) : StoreElement {

    companion object {

        fun create(r: DeRound, cost_multiplier: Double) = DeMatrixRoundElement(
            id = r.let { "Round.${r.number}" },
            round_number = r.number,
            nodes = DeMatrixNodeElement.create(r),
            edges = DeMatrixEdgeElement.create(r, cost_multiplier)
        )

        fun clear_rounds(): AddElements =
            AddElements(
                DeMatrixRoundElement::class.java,
                null
            )

        fun add_rounds(de: DeAuction, deRounds: List<DeRound>): AddElements =
            AddElements(
                DeMatrixRoundElement::class.java,
                deRounds.map { create(it, de.settings.cost_multiplier) })

    }
}

data class DeMatrixNodeElement(
    override val id: String,
    val round: Int,
    // val auction_id: String = a.id_str()
    val cid: String,
    val shortname: String,

    val buy_match: Int,
    val buy_max: Int,
    val buy_min: Int,
    val buy_quantity: Int,

    val sell_match: Int,
    val sell_max: Int,
    val sell_min: Int,
    val sell_quantity: Int,
    //  val fully_opposed_match: Int = 0 // TODO: //  rbi.fully_opposed_match_vol

    val side: OrderType,
    val cost: Double,
    val cost_str: String,

    ) : StoreElement {

//    @Transient
//    private val rbi: DeAuction.RoundTraderInfo? = r.get_rti(t)
//
//    @Transient
//    private val constraints: DeBidConstraints? = rbi?.constraints
//
//    @Transient
//    private val o: DeOrder? = r?.get_order(t)

    companion object {
        fun create(
            r: DeRound
        ): List<DeMatrixNodeElement> =

            r.trader_infos.map { rbi: DeRoundTraderInfo ->
                val constraints: DeBidConstraints = rbi.constraints
                val round_num: Int = r.number
                val cid: String = rbi.de_trading_company.company_id.toString()
                val shortname: String = rbi.de_trading_company.shortname_at_auction_time

                DeMatrixNodeElement(
                    id = "R_${round_num}_T_${cid}",
                    //  auction_id = a.id_str()
                    //  val fully_opposed_match: Int = 0 // TODO: //  rbi.fully_opposed_match_vol
                    round = round_num,
                    cid,
                    shortname,

                    buy_match = r.matches
                        .filter { rbi.de_trading_company.company_id == it.buy_order.trading_company.company_id }
                        .sumOf { it.match }, // .thousands() else ""
                    buy_max = constraints.max_buy_quantity,
                    buy_min = constraints.min_buy_quantity, // .thousands() // "0"
                    buy_quantity = r.buy_quantity(rbi.de_trading_company), //.thousands()

                    sell_match = r.matches
                        .filter { rbi.de_trading_company.company_id == it.sell_order.trading_company.company_id }
                        .sumOf { it.match },
                    sell_max = constraints.max_sell_quantity,
                    sell_min = constraints.min_sell_quantity, //.thousands()
                    sell_quantity = r.sell_quantity(rbi.de_trading_company), //.thousands(),

                    side = rbi.order.type,
                    cost = rbi.order.cost,
                    cost_str = rbi.order.cost_str
                )
            }.sortedBy { it.id }
    }
}


data class DeMatrixEdgeElement(
    override val id: String,
    val r: Int,

    val buyer_shortname: String, // only needed for debug
    val buyer_cid: String,

    val seller_shortname: String, // only needed for debug
    val seller_cid: String,

    // volumes:
    // val min: Int, ?? how would we show this? can we determine by buyer?
    val credit_quantity_limit: Int,
    val buy_quantity_limit: Int,
    val selling_quantity_limit: Int,
    val capacity: Int, // ie: min(round_credit_volume_limit, buyer.vol, seller.vol),
    val match: Int,

    // value:
    val value: Double,
    val value_str: String,

    val credit_str: String,
    //  val buyer_dollar_credit_limit_at_auction: String // TODO: put this in an auction counterparty credit matrix
) : StoreElement {

    companion object {

        // for testing too:
        fun to_id(
            round_num: Int,
            b_cid: String,
            s_cid: String,
        ): String =
            "R_${round_num}_B_${b_cid}_S_${s_cid}"

        fun create(deRound: DeRound, cost_multiplier: Double): List<DeMatrixEdgeElement> =
            deRound.matches.map { m: DeMatch ->
                val round_number = deRound.number
                val b_cid = m.buy_order.trading_company.company_id.toString()
                val s_cid = m.sell_order.trading_company.company_id.toString()
                // TODO: need to move a lot of these calculations to the match!
                DeMatrixEdgeElement(
                    id = to_id(round_number, b_cid = b_cid, s_cid = s_cid),
                    r = round_number,

                    buyer_shortname = m.buyer_shortname, // only needed for debug
                    buyer_cid = b_cid,

                    seller_shortname = m.seller_shortname, // only needed for debug
                    seller_cid = s_cid,

                    credit_quantity_limit = m.selling_quantity_limit,
                    buy_quantity_limit = m.buy_limit,
                    selling_quantity_limit = m.sell_limit,
                    capacity = m.capacity,
                    match = m.match,

                    credit_str = m.credit_str,
                    value = m.value,
                    value_str = m.value_str

//                    credit_str = AuFormatter.format_currency(
//                        m.seller.company.get_credit_limit(m.buyer.company)
//                    ),
                    //     credit = seller.get_credit_limit(buyer),
                    //   credit_str = m.seller.company.get_credit_limit_str(m.buyer.company)
                )
            } //.sortedBy { it.id })
    }

}


```

