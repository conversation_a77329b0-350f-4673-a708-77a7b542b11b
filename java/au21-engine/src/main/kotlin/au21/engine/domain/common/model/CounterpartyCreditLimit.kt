// File: CounterpartyCreditLimit.kt
package au21.engine.domain.common.model

import au21.engine.framework.utils.AuFormatter
import javax.persistence.Embeddable

@Embeddable
class CounterpartyCreditLimit(
    lender_: CompanyProxy,
    borrower_: CompanyProxy) {

    companion object {
        const val no_limit = "no limit"
        const val none = "none"
    }

    var lender: CompanyProxy = lender_
        private set

    var borrower: CompanyProxy = borrower_
        private set

    var credit_limit: Double? = null
        private set

    var credit_limit_str: String? = null
        private set

    fun set_credit_limit(limit: Double?) {
        this.credit_limit = limit
        this.credit_limit_str = when (limit) {
            null -> no_limit
            else -> AuFormatter.format_currency(limit)
        }
    }
}
