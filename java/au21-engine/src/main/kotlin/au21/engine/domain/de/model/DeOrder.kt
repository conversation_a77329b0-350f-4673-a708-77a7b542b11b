// File: DeOrder.kt
package au21.engine.domain.de.model

import au21.engine.domain.common.model.OrderSubmissionType
import au21.engine.domain.common.model.OrderType
import au21.engine.domain.common.model.PersonProxy
import au21.engine.domain.de.services.defaultorders.DeOrderInfo
import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.AuFormatter
import org.joda.time.DateTime
import java.util.*
import javax.persistence.Embeddable

@Embeddable
class DeOrder(
    round: DeRound,
    trading_company_: DeTradingCompany,
    u: PersonProxy?,
    order_info: DeOrderInfo,
    cost_multiplier: Double,
    prev_order: DeOrder?
) {

    init {
        // TODO: pretty sure this is handled in numerous other places !
        order_info.apply {
            if (order_type != OrderType.NONE && quantity == 0) {
                throw AlertException("Order type cannot be $order_type if quantity is zero")
            }
            if (order_type == OrderType.NONE && quantity != 0) {
                throw AlertException("Order type cannot be NONE if quantity is not zero")
            }
        }
        if (order_info.quantity < 0) {
            throw AlertException("Order quantity cannot be negative")
        }
    }

    var user: PersonProxy? = u
        private set

    var trading_company: DeTradingCompany = trading_company_
        private set

    var timestamp: Date = DateTime().toDate() // used so that can mock
        private set

    var price: Double = round.price ?: 0.0
        private set

    var round_number: Int = round.number
        private set


    // NB: if you have an implied buy, then you are a buyer,
    // and will be allocated in n or pen, even if no incremental bid

    var submission_type_label: String = order_info.submission_type.toString()

    var submission_type: OrderSubmissionType = order_info.submission_type
        get() = OrderSubmissionType.valueOf(submission_type_label)
        private set

    var order_type_label: String = order_info.order_type.toString()
        private set

    var type: OrderType = order_info.order_type
        get() = OrderType.valueOf(order_type_label)
        private set

    var quantity: Int = order_info.quantity
        private set

    fun sellVol() = if (type == OrderType.SELL) quantity else 0
    fun buyVol() = if (type == OrderType.BUY) quantity else 0

    var prev_order: DeOrder? = prev_order
        private set

    var cost:Double = price * cost_multiplier * quantity
        private set

    var cost_str: String = AuFormatter.format_currency(cost)
        private set
}
