// File: TradingUserFixture.kt
package au21.engine.domain.de.services.sampledb

import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.Company
import au21.engine.domain.common.model.Person
import au21.engine.framework.database.AuEntityManager
import io.quarkus.logging.Log

class TradingUserFixture {

    companion object {

        fun create(db: AuEntityManager, trader_count: Int, use_counterparty_credits: <PERSON>olean): List<Person> {
            create(trader_count, db.findAll<Person>().filter { it.isTrader() }, db.findAll()).map { u: Person ->
                u.also {
                    db.save(it)
                    it.company?.let { c: Company -> db.save(c) }
                }
            }
            return db.findAll<Person>().filter { it.isTrader() }
        }


        fun create(
            trader_count: Int,
            existing_trader_users: List<Person>,
            existing_companies: List<Company>,
            unlimited_credit: Boolean = false,
        ): List<Person> {

            fun uname(i: Int) = "b$i"

            fun shortname(u: Person): String =
                u.company?.shortname ?: u.username.replace("b", "").toInt().let {
                    "c-$it"
                }

            fun longname(u: Person): String =
                u.company?.shortname ?: u.username.replace("b", "").toInt().let {
                    "c-$it long name"
                }

            val new_users: List<Person> = (1..trader_count).mapNotNull { i ->
                val username = uname(i)
                if (existing_trader_users.none { it.username == username }) {
                    Person(
                        username = username,
                        password = "1",
                        role = AuUserRole.TRADER,
                        company = null
                    ).also { u: Person ->
                        Log.debug("created user: " + u.username)
                        u.company = existing_companies.find { it.shortname == shortname(u) }
                            ?: run {
                                Company(shortname = shortname(u), longname = longname(u)).also {
                                    Log.debug("created company: " + it.shortname)
                                }
                            }


                    }
                } else null
            }

            val companies: List<Company> = new_users.mapNotNull { it.company } + existing_companies

            /* TODO("refactor to use Auction get/set credit")
            new_users.forEach { u ->
                u.company?.let { c ->
                    companies.forEach { buyer ->
                        if (c != buyer) {
                            c.set_credit_limit(
                                buyer,
                                when (unlimited_credit) {
                                    true -> null // null = unlimited_credit
                                    false -> Random.nextInt(100) * 100_000.0

                            )
                        }
                    }
                }
            }

             */

            return new_users + existing_trader_users
        }

    }

}
