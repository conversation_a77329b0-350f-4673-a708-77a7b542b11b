// File: UserElement.kt
package au21.engine.domain.common.viewmodel

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.Person
import au21.engine.framework.client.StoreElement
import au21.engine.framework.database.AuEntityManager
import java.util.*


data class UserElement(
    override val id: String,

    // should get these from the company element:
    val company_id: String,
    val company_longname: String,
    val company_shortname: String,

    val email: String,
    val isAuctioneer: <PERSON><PERSON><PERSON>,
    val isObserver: <PERSON>olean,
    val isTester: Boolean,
    val password: String,
    val phone: String,
    val role: AuUserRole,
    val username: String,
    val user_id: String,

    // FOR TRADER ICON:
    val isOnline: Boolean,
    val current_auction_id: String?,
    val termination_reason: AuSession.SessionTerminationReason?,
    val socket_state: AuSession.ClientSocketState?,
    val socket_state_last_closed: Date?,
    val has_connection_problem: Boolean

) : StoreElement {


    companion object {

        fun create(u: Person, online_session: AuSession?): UserElement {

            return UserElement(
                id = u.id_str(),

                // should get these from the company elements:
                company_id = u.company?.id_str() ?: "",
                company_longname = u.company?.longname ?: "",
                company_shortname = u.company?.shortname ?: "",

                email = u.email,
                isAuctioneer = u.isAuctioneer(),
                isObserver = u.isObserver,
                isTester = u.isTester,
                password = u.password,
                phone = u.phone,
                role = u.role,
                username = u.username,
                user_id = u.id_str(),

                // FOR TRADER ICON:
                isOnline = online_session?.let { !it.isTerminated() } ?: false,
                current_auction_id = online_session?.auction?.id_str(),
                termination_reason = online_session?.termination_reason(),
                socket_state = online_session?.socket_state,
                socket_state_last_closed = online_session?.socket_last_closed,
                has_connection_problem = online_session?.hasConnectionProblem() ?: false
            )

        }

        fun user_elements(db: AuEntityManager, logged_in_sessions: List<AuSession>) =
            db.findAll<Person>().map { u: Person ->
                create(u, logged_in_sessions.find { it.user == u })
            }
    }
}
