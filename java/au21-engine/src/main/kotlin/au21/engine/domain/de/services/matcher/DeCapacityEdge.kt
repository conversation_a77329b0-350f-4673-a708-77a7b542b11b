// File: DeCapacityEdge.kt
package au21.engine.domain.de.services.matcher

import au21.engine.domain.common.model.OrderType
import au21.engine.domain.de.model.DeOrder
import au21.engine.domain.de.model.DeRoundTraderInfo
import au21.engine.framework.utils.format_table
import java.lang.Integer.min

data class DeCapacityEdge(
    val from: String,    // SELLER | SOURCE
    val to: String,      // BUYER | SINK
    //  val limit: Int? = 0, // NB: null means NO_LIMIT
    val capacity: Int,
) {

    companion object {

        const val SELL_SOURCE = "SELL_SOURCE"
        const val BUY_SINK = "BUY_SINK"

        fun List<DeCapacityEdge>.to_table(): String =
            format_table(this.sortedBy { it.to }.sortedBy { it.from }, listOf("from_seller", "to_buyer", "capacity")
            )

//        fun nodes(r: DeAuction.Round): List<String> = listOf(
//            listOf(SELL_SOURCE),
//            listOf(BUY_SINK),
//            r.trader_infos.map { it.trader.shortname_at_auction_time }
//        ).flatten()


        // ONLY USED BY DeMatcher (other than testing):

        fun to_edges(r: List<DeRoundTraderInfo>): List<DeCapacityEdge> {

            // TODO: probably we want to sort by time, ie: for bfs searching

            val edges = mutableListOf<DeCapacityEdge>()

            // A) source and sink (ie: 4 edges per user):

            r.forEach { rti: DeRoundTraderInfo ->
                rti.order.let { o: DeOrder ->
                    val shortname: String = o.trading_company.shortname_at_auction_time
                    when (o.type) {

                        // 1) SOURCE -> SELL
                        OrderType.SELL -> {

                            edges += DeCapacityEdge(
                                from = SELL_SOURCE, to = shortname, capacity = o.quantity
                            )

                            edges += DeCapacityEdge(
                                from = shortname, to = BUY_SINK, capacity = 0
                            )

                            r.filter { it.order.type == OrderType.BUY }.forEach {
                                edges += DeCapacityEdge(
                                    from = shortname,
                                    to = it.de_trading_company.shortname_at_auction_time,
                                    capacity = min(rti.order.quantity, it.order.quantity)
                                )
                            }
                        }

                        // 2) BUY -> SINK
                        OrderType.BUY -> {

                            edges += DeCapacityEdge(
                                from = SELL_SOURCE, to = shortname, capacity = 0
                            )

                            edges += DeCapacityEdge(
                                from = shortname, to = BUY_SINK, capacity = o.quantity
                            )

                            r.filter { it.order.type == OrderType.SELL }.forEach { it ->
                                edges += DeCapacityEdge(
                                    from = it.de_trading_company.shortname_at_auction_time,
                                    to = shortname,
                                    capacity = min(rti.order.quantity, it.order.quantity)
                                )
                            }
                        }

                        else -> {}
                    }
                }
            }
            return edges
        }
    }
}

// 3) NONE:

//                        OrderType.NONE -> {
//
//                            edges += DeCapacityEdge(
//                                from = SELL_SOURCE, to = shortname, capacity = 0
//                            )
//
//                            edges += DeCapacityEdge(
//                                from = shortname, to = BUY_SINK, capacity = 0
//                            )
//
//                        }

// B) Counterparty capacity edges:
// Edges
//
//                    r.trader_infos
//                        .filter { it.trader != rti.trader }
//                        .forEach { rti: DeAuction.RoundTraderInfo ->
//
//                            edges += DeCapacityEdge(
//                                from = shortname,
//                                to = rcp.buyer_rti.trader.shortname_at_auction_time,
//                                capacity = when (val cl = rcp.credit_quantity_limit) {
//                                    null -> o.quantity // ie: no limit
//                                    else -> min(cl, o.quantity)
//                                }
//                            )


//                    counterparty_limits
//                        // this should have already been filtered:
//                        .filterNot { it.seller_rti.trader.shortname_at_auction_time == it.buyer_rti.trader.shortname_at_auction_time }
//                        .filter { it.seller_rti.trader.shortname_at_auction_time == shortname }
//                        .forEach { rcp: RoundCounterpartyLimits ->
//                            edges += DeCapacityEdge(
//                                from = shortname,
//                                to = rcp.buyer_rti.trader.shortname_at_auction_time,
//                                capacity = when (val cl = rcp.credit_quantity_limit) {
//                                    null -> o.quantity // ie: no limit
//                                    else -> min(cl, o.quantity)
//                                }
//                            )
//                        }
//                    when (o.volume_type) {
//                        // can't use company id for tests without db!
//                        OrderVolumeType.BUY -> {
//                            constraints.add(
//                                MatchFlowEdge(
//                                    from_seller_shortname = SELL_SOURCE,
//                                    to_buyer_shortname = shortname,
//                                    capacity = 0,
//                                    limit = 0
//                                )
//                            )
//                            constraints.add(
//                                MatchFlowEdge(
//                                    from_seller_shortname = shortname,
//                                    to_buyer_shortname = BUY_SINK,
//                                    capacity = o.volume,
//                                    limit = o.volume
//                                )
//                            )
//                        }
//                        OrderVolumeType.SELL -> {
//                            constraints.add(
//                                MatchFlowEdge(
//                                    from_seller_shortname = SELL_SOURCE,
//                                    to_buyer_shortname = shortname,
//                                    capacity = o.volume,
//                                    limit = o.volume
//                                )
//                            )
//                            constraints.add(
//                                MatchFlowEdge(
//                                    from_seller_shortname = shortname,
//                                    to_buyer_shortname = BUY_SINK,
//                                    capacity = 0,
//                                    limit = 0
//                                )
//                            )
//                        }
//                        OrderVolumeType.NONE -> {
//                            constraints.add(
//                                MatchFlowEdge(
//                                    from_seller_shortname = SELL_SOURCE, to_buyer_shortname = shortname, capacity = 0
//                                )
//                            )
//                            constraints.add(
//                                MatchFlowEdge(
//                                    from_seller_shortname = shortname, to_buyer_shortname = BUY_SINK, capacity = 0
//                                )
//                            )
//                        }
//                    }
//                }
//            }
//
//            // (2) set the trader capacities, only needed for each
//            round_volume_limits.forEach { limit: RoundVolumeLimit ->
//                constraints.add(
//                    MatchFlowEdge(
//                        from_seller_shortname = limit.seller.company_shortname,
//                        to_buyer_shortname = limit.buyer.company_shortname,
//                        capacity = limit.limit
//                    )
//                )
//            }
//
//            return constraints
//        }
