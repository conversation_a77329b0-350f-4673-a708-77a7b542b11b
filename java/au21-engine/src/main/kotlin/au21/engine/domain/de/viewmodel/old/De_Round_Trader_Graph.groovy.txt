package au2019.engine.auction.de.results

import app.framework.Child_Payload
import app.framework.Response
import app.model.*
import app.services.de.De_Getters
import groovy.transform.CompileStatic
import groovy.transform.ToString

import static app.framework.Response.*
import static app.services.de.De_Getters.*

@CompileStatic
class De_Round_Trader_Graph {

  static List<Response> create (
          List<ClientSession> to,
          DeAuction a,
          DeRound r
  ) {

    app.framework.Response.flatten_responses ( [

      ( r == null ) ? [

              app.framework.Response.remove_all_children ( to, On_De_Round_Trader_Node ),
              app.framework.Response.remove_all_children ( to, On_De_Round_Trader_Edge ),

              a.rounds.collect { _create_round ( to, a, it ) }

      ] : [

              app.framework.Response.remove_children_by_id_starting_with ( to, On_De_Round_Trader_Node, "ROUND_${r.round_number}_" ),
              app.framework.Response.remove_children_by_id_starting_with ( to, On_De_Round_Trader_Edge, "ROUND_${r.round_number}_" ),

              _create_round ( to, a, r )

      ]
    ] )

  }

  static List<Response> _create_round (
          List<ClientSession> to,
          DeAuction a,
          DeRound r ) {

    app.framework.Response.flatten_responses ( [
            app.framework.Response.upsert_children ( to, On_De_Round_Trader_Node.create_round ( a, r ) ),
            app.framework.Response.upsert_children ( to, On_De_Round_Trader_Edge.create_round ( a, r ) )
    ] )
  }

}

@CompileStatic
@ToString (includeNames = true, includePackage = false)
class On_De_Round_Trader_Node extends Child_Payload {
  Integer ROUND_NUMBER
  String USERNAME
  String COMPANY_NAME

  Long SELL_POTENTIAL
  Long SELL_VOLUME
  Long SELL_MATCH

  Long BUY_POTENTIAL
  Long BUY_VOLUME
  Long BUY_MATCH

  Long FULLY_OPPOSED_MATCH  // can be negative assuming everyone else has max opposide volume

  // String SELL_PERCENTAGE_MATCHED

  On_De_Round_Trader_Node(){}

  On_De_Round_Trader_Node (DeRound r, DeTrader t ) {

    DeRoundTraderInfo rbi = De_Getters.get_rbi (t, r)

    id = "ROUND_${r.round_number}_TRADER_NODE_${t.person.username}"

    ROUND_NUMBER = r.round_number
    USERNAME = t.person.username
    COMPANY_NAME = t.person.company_name


    SELL_POTENTIAL = Math.max(rbi.max_vol, 0)
    SELL_VOLUME = De_Getters.sell_vol ( rbi.order )
    SELL_MATCH = De_Getters.sell_match ( rbi )

    BUY_POTENTIAL = Math.max(-rbi.min_vol, 0)
    BUY_VOLUME = De_Getters.buy_vol ( rbi.order )
    BUY_MATCH = De_Getters.buy_match ( rbi )

    FULLY_OPPOSED_MATCH = rbi.fully_opposed_match_vol
  }

  static List<On_De_Round_Trader_Node> create_round (DeAuction a, DeRound r ) {
    a.traders.collect {
      new On_De_Round_Trader_Node ( r, it )
    }
  }
}


@CompileStatic
@ToString (includeNames = true, includePackage = false)
class On_De_Round_Trader_Edge extends Child_Payload {

  Long ROUND_NUMBER

  String BUY_USERNAME
  String BUY_COMPANY_NAME
  String BUY_TIME
  Long BUY_VOLUME

  String SELL_USERNAME
  String SELL_COMPANY_NAME
  String SELL_TIME
  Long SELL_VOLUME

  Long MATCH_VOLUME
  Long MATCH_POTENTIAL

  On_De_Round_Trader_Edge(){}

  On_De_Round_Trader_Edge (DeAuction a, DeRound r, DeTrader buyer, DeTrader seller ) {
    // we don't have unique match id's (and some cells could be null!)

    // this takes care of no round price set:
    long limit_at_current_round_price = app.services.de.De_Getters.de_counterparty_credit_vol_limit ( a, r, seller, buyer )

    long potential = app.services.de.De_Getters.get_match_potential ( r, seller, buyer.person )?.sell_volume_limit ?: 0

    DeMatch m = r.matches.find {
      ( it.buy_order.username == buyer.person.username ) &&
        ( it.sell_order.username == seller.person.username )
    }

    id = "ROUND_${r.round_number}_BUYER_${buyer.person.username}_SELLER_${seller.person.username}"

    ROUND_NUMBER = r.round_number

    BUY_COMPANY_NAME = buyer.person.company_name
    BUY_TIME = m ? app.framework.FormatUtil.format_order_time ( m.buy_order.order_timestamp ) : ''
    BUY_USERNAME = buyer.person.username
    BUY_VOLUME = app.services.de.De_Getters.buy_vol(m?.buy_order) //m ? format_thousands ( m.buy_order.buy_vol ) : ''

    SELL_COMPANY_NAME = seller.person.company_name
    SELL_TIME = m ? app.framework.FormatUtil.format_order_time ( m.sell_order.order_timestamp ) : ''
    SELL_VOLUME = app.services.de.De_Getters.sell_vol(m?.buy_order) // m ? format_thousands ( m.sell_order.sell_vol ) : ''
    SELL_USERNAME = seller.person.username

    MATCH_VOLUME = m?.match_volume ?: 0
    MATCH_POTENTIAL = potential // format_thousands ( potential )
  }

  static List<On_De_Round_Trader_Edge> create_round (DeAuction a, DeRound r ) {

    List<On_De_Round_Trader_Edge> links = []

    a.traders.forEach { DeTrader buyer ->
      a.traders.forEach { DeTrader seller ->
        if (buyer.person.username != seller.person.username) {
          new On_De_Round_Trader_Edge ( a, r, buyer, seller ).with {
            //if (it.MATCH_POTENTIAL > 0)
              links.add ( it )
          }
        }
      }
    }
    return links
  }

}

/*

2018.02.08: "pre Sell-plus-Buy-minus"

@CompileStatic
@ToString (includeNames = true, includePackage = false)
class On_De_Round_Trader_Node extends Child_Payload {
  Integer ROUND_NUMBER
  String USERNAME
  String COMPANY_NAME

  Long SELL_ELIGIBILITY
  Long SELL_POTENTIAL // circle overlay
  Long SELL_VOLUME
  Long SELL_MATCH

  Long BUY_MAX
  Long BUY_POTENTIAL
  Long BUY_VOLUME
  Long BUY_IMPLIED
  Long BUY_MATCH

  String SELL_PERCENTAGE_MATCHED

  On_De_Round_Trader_Node(){}

  On_De_Round_Trader_Node ( DeRound r, DeTrader t ) {

    DeRoundTraderInfo rbi = t.rbis.get ( r.round_number )

    id = "ROUND_${r.round_number}_TRADER_NODE_${t.person.username}"

    ROUND_NUMBER = r.round_number
    USERNAME = t.person.username
    COMPANY_NAME = t.person.company_name

    SELL_ELIGIBILITY = rbi.sell_eligibility
    //   SELL_VOLUME =  t.current_sell_vol_current
    SELL_POTENTIAL = rbi.sell_match_potential
    SELL_MATCH = rbi.sell_match_actual

    BUY_MAX = rbi.buy_eligibility
    BUY_POTENTIAL = rbi.buy_match_potential
    //  BUY_VOLUME = t.current_buy_vol_current
    BUY_IMPLIED = rbi.buy_min
    BUY_MATCH = rbi.buy_match_actual
  }

  static List<On_De_Round_Trader_Node> create_round ( DE a, DeRound r ) {
    a.traders.collect {
      new On_De_Round_Trader_Node ( r, it )
    }
  }
}
 */


