// File: de-sample-db-helper.kt
package au21.engine.domain.de.services.sampledb

import au21.engine.domain.common.model.OrderType
import au21.engine.domain.common.model.Person
import au21.engine.domain.common.model.PersonProxy
import au21.engine.domain.common.model.PriceDirection
import au21.engine.domain.de.model.*
import au21.engine.domain.de.services.matcher.DeMatcher
import au21.engine.domain.de.services.rounds.create_subsequent_round
import au21.engine.domain.de.services.sampledb.SampleOrderMove.DECREASE
import au21.engine.domain.de.services.sampledb.SampleOrderMove.INCREASE
import io.quarkus.logging.Log
import org.joda.time.DateTime
import kotlin.random.Random


/**
 * ASSUMES BID CONSTRAINTS ALREADY SET WHEN ROUND CREATED
 * ie: RTI
 */

enum class SampleOrderMove { INCREASE, DECREASE }


fun random_bid(de: DeAuction, t: DeTradingCompany, u: Person) {

    val n: DeRound = de.lastround()
    val n_rti: DeRoundTraderInfo =
        n.get_rti(t) ?: throw Error("expected rti for trader: ${t.shortname_at_auction_time}")

    val n_constraints: DeBidConstraints =
        n_rti.constraints

    fun simple_frac(
        min: Int, max: Int,
        divisor_min: Int, divisor_max: Int,
        move: SampleOrderMove,
    ): Int {
        try {
            if (min == max)
                return min
            val random_range: Int = Random.nextInt(min, max)
            val random_change: Int = random_range / Random.nextInt(divisor_min, divisor_max)
            return when (move) {
                INCREASE -> min + random_change
                DECREASE -> max - random_change
            }
        } catch (t: Throwable) {
            throw t
        }
    }

    fun simple_frac_5(min: Int, max: Int, move: SampleOrderMove): Int = simple_frac(min, max, 3, 5, move)

    fun submit(order_type: OrderType, quantity: Int) {

        // need to make sure than volume_type is NONE if vol is zero
        val vt: OrderType = when (quantity) {
            0 -> OrderType.NONE
            else -> order_type
        }

        // and vice-versa, vol is 0 if volume_type is NONE
        val vol: Int = when (order_type) {
            OrderType.NONE -> 0
            else -> quantity
        }

        de.create_manual_order(
            r = n,
            t = t,
            order_quantity = when (vt) {
                OrderType.NONE -> 0
                OrderType.BUY -> Integer.max(
                    Integer.min(vol, n_constraints.max_buy_quantity),
                    n_constraints.min_buy_quantity
                )
                OrderType.SELL -> Integer.max(
                    Integer.min(vol, n_constraints.max_sell_quantity),
                    n_constraints.min_sell_quantity
                )
            },
            order_type = vt,
            u = PersonProxy(u) // TODO: this should not be created here!
        )
        // we can do the match once (in production it's done after every bid)
        DeMatcher.calculate_and_set_matches(de, null)
    }

    fun submit(rti: DeRoundTraderInfo, side: OrderType, move: SampleOrderMove) {
        rti.constraints.apply {
            when (side) {
                OrderType.NONE -> submit(OrderType.NONE, 0)
                OrderType.BUY -> submit(
                    OrderType.BUY,
                    simple_frac_5(min_buy_quantity, max_buy_quantity, move)
                )
                OrderType.SELL -> submit(
                    OrderType.SELL,
                    simple_frac_5(min_sell_quantity, max_sell_quantity, move)
                )
            }
        }
    }

    fun submit(rti: DeRoundTraderInfo, side: OrderType) {
        val constraints = rti.constraints
        // here we have to figure out the move direction:
        val range: Int = constraints.max_buy_quantity + constraints.max_sell_quantity
        val fraction = Random.nextInt(range / 5)
        when (side) {
            OrderType.NONE -> submit(OrderType.NONE, 0)
            OrderType.BUY -> when {
                fraction <= constraints.max_buy_quantity ->
                    submit(OrderType.BUY, fraction)
                else ->
                    submit(OrderType.SELL, fraction - constraints.max_buy_quantity)
            }
            OrderType.SELL -> when {
                fraction <= constraints.max_sell_quantity ->
                    submit(OrderType.SELL, fraction)
                else ->
                    submit(
                        OrderType.BUY,
                        fraction - constraints.max_sell_quantity
                    ) // TODO: might be the other way round
            }
        }
    }

    when (n.number) {
        1 ->  // FIRST ROUND:
            if (Random.nextInt(100) > 80) {
                submit(n_rti, OrderType.SELL, INCREASE)
            } else {
                submit(n_rti, OrderType.BUY, DECREASE)
            }
        else -> {
            val n_direction: PriceDirection = n.price_direction!!
            val pen = de.penultimate()!!
            val pen_order: DeOrder = pen.get_order(t)
            val pen_type: OrderType = pen_order.type
            when (n.number) {
                2 -> when {
                    n_direction == PriceDirection.UP && pen_type == OrderType.BUY ->
                        submit(
                            OrderType.BUY,
                            simple_frac_5(n_constraints.min_buy_quantity, n_constraints.max_buy_quantity, DECREASE)
                        )
                    n_direction == PriceDirection.UP && pen_type == OrderType.SELL ->
                        submit(
                            OrderType.SELL,
                            simple_frac_5(n_constraints.min_sell_quantity, n_constraints.max_sell_quantity, INCREASE)
                        )
                    n_direction == PriceDirection.DOWN && pen_type == OrderType.BUY ->
                        submit(
                            OrderType.BUY,
                            simple_frac_5(n_constraints.min_buy_quantity, n_constraints.max_buy_quantity, INCREASE)
                        )
                    n_direction == PriceDirection.DOWN && pen_type == OrderType.SELL ->
                        submit(
                            OrderType.SELL,
                            simple_frac_5(n_constraints.min_sell_quantity, n_constraints.max_sell_quantity, DECREASE)
                        )
                }
                else -> {
                    val has_reversed = n.has_reversed
                    val pen_pen: DeRound = de.rounds.first { it.number == n.number - 2 }
                    val pen_pen_order: DeOrder = pen_pen.get_order(t)
                    val pen_pen_type: OrderType = pen_pen_order.type
                    when (n_direction) {
                        PriceDirection.UP -> when {
                            pen_pen_type == OrderType.BUY && pen_type == OrderType.BUY ->
                                when (has_reversed) {
                                    false -> submit(n_rti, OrderType.BUY, DECREASE)
                                    true -> submit(n_rti, OrderType.BUY)
                                }
                            pen_pen_type == OrderType.BUY && pen_type == OrderType.SELL ->
                                when (has_reversed) {
                                    false -> submit(n_rti, OrderType.SELL, INCREASE)
                                    true -> submit(n_rti, OrderType.SELL)
                                }
                            pen_pen_type == OrderType.SELL && pen_type == OrderType.SELL ->
                                when (has_reversed) {
                                    false -> submit(n_rti, OrderType.SELL, INCREASE)
                                    true -> submit(n_rti, OrderType.SELL, DECREASE)
                                }
                        }
                        PriceDirection.DOWN -> when {
                            pen_pen_type == OrderType.SELL && pen_type == OrderType.SELL ->
                                when (has_reversed) {
                                    false -> submit(n_rti, OrderType.SELL, DECREASE)
                                    true -> submit(n_rti, OrderType.SELL)
                                }
                            pen_pen_type == OrderType.SELL && pen_type == OrderType.BUY ->
                                when (has_reversed) {
                                    false -> submit(n_rti, OrderType.BUY, INCREASE)
                                    true -> submit(n_rti, OrderType.BUY)
                                }
                            pen_pen_type == OrderType.BUY && pen_type == OrderType.BUY ->
                                when (has_reversed) {
                                    false -> submit(n_rti, OrderType.BUY, INCREASE)
                                    true -> submit(n_rti, OrderType.BUY, DECREASE)
                                }
                        }
                    }
                }

            }
        }
    }
}


fun create_sample_db_auction(
    auction_name: String,
    trading_users: List<Person>,
    round_count: Int,
    close_last_round: Boolean,
    use_counterparty_credits:Boolean
): DeAuction {

    Log.info("creating auction: $auction_name")

    if (trading_users.any { it.company == null })
        throw Error("Traders need to have a non-null companies.")

    // val companies: List<Company> = trading_users.map { it.company!! }

    val de = DeAuction(
        auction_name = auction_name,
        settings = DeAuctionSettings(

            use_counterparty_credits = use_counterparty_credits,

            starting_price_announcement_mins = 1,

            round_red_secs = 30,
            round_orange_secs = 15,
            round_open_min_secs = 5,
            round_closed_min_secs = 5,

            //     show_current_round_in_history = false, // so that BWP can have it the way they like it
            //     first_round_duration = 15,
            //     following_round_duration = 15,
            cost_multiplier = 10_000.0, // ie: currency = price_units x volume_units x currency_conversion_rate
            // VOLUME:
            quantity_units = "MMlb",
            quantity_minimum = 1,
            quantity_step = 1,

            // PRICE:
            price_units = "cpp",
            price_decimal_places = 3,
            price_rule = DePriceRule()
        )
    )

    de.starting_time = DateTime().plusSeconds(4).toDate()

    // NB: MAKE SURE THE FIRST ROUND IS CREATED BEFORE THIS POINT!
    val traders: List<Pair<DeTradingCompany, Person>> =
        trading_users.map {
            val t: DeTradingCompany = de.create_trader(it.company!!)
            Pair(t, it)
        }

    de.firstround().price = 100.0
    de.setState(DeAuctioneerState.STARTING_PRICE_SET)
    de.setState(DeAuctioneerState.STARTING_PRICE_ANNOUNCED)
    de.setState(DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN)

    traders.forEach {
        random_bid(de, it.first, it.second)
    }

    if (round_count > 1 || close_last_round) {
        de.setState(
            if (de.is_awardable())
                DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE
            else
                DeAuctioneerState.ROUND_CLOSED_AWARDABLE
        )
    }

    Log.debug("round count: $round_count")
    if (round_count > 1) {

        (2..round_count).forEach { round_num ->
            if (!de.is_awardable()) {
                de.create_subsequent_round()

                // bid:
                traders.forEach {
                    random_bid(de, it.first, it.second)
                }

                Log.debug("round number = $round_num")

                if (round_num < round_count || close_last_round) {
                    de.setState(
                        if (de.is_awardable())
                            DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE
                        else
                            DeAuctioneerState.ROUND_CLOSED_AWARDABLE
                    )
                }
            }

        }
    }

    return de

}
