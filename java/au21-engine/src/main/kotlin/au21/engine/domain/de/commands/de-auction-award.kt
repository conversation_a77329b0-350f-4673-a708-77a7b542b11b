// File: de-auction-award.kt
package au21.engine.domain.de.commands


import au21.engine.domain.common.model.*
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeRound
import au21.engine.domain.de.model.DeTradingCompany
import au21.engine.domain.de.model.format_round_price
import au21.engine.framework.commands.*
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.thousands

class DeAuctionAwardCommand(
    val auction_id: String,
    val round_number: String,
   // val allocations: Map<String, String>
) : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)


        val de: DeAuction = db.auction_or_alert(auction_id)

        val deRound: DeRound = round_number.toIntOrNull()?.let { num ->
            de.rounds.find { it.number == num }
        } ?: fail("Unable to find round with number: $round_number")

//        val allocations_actual: MutableMap<DeAuction.Trader, Int> = mutableMapOf()
//
//        allocations.forEach { entry ->
//
//            val c: Company? = db.byId(entry.key)
//            val t: DeAuction.Trader? = de.getTrader(c)
//            val alloc: Int? = entry.value.toIntOrNull()
//
//            if (c == null) {
//
//                err_if(true, "Unable to find company with id: ${entry.key}")
//
//            } else if (t == null) {
//
//                err_if(true, "Company is not a trader is this auction: ${c.shortname}")
//
//            } else if (alloc == null || alloc < 0) {
//
//                err_if(true, "Company: ${c.shortname} allocation not a whole number > 0: $alloc")
//
//            } else {
//
//                allocations_actual[t] = alloc
//            }
//        }

        fail_if_errors()

        return DeAuctionAwardAction(this, db, session, de, deRound)
    }
}


class DeAuctionAwardAction(
    override val command: DeAuctionAwardCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de: DeAuction,
    val deRound: DeRound,
 //   val allocations: Map<DeAuction.Trader, Int>
) : EngineAction {

    val messages: MutableList<AuctionMessage> = mutableListOf()

    override fun mutate() {

        val award_price = de.format_round_price(AuUserRole.AUCTIONEER, deRound)

//        allocations.forEach { entry ->
//            val t: DeAuction.Trader = entry.key
//            val volume: Int = entry.value
//            entry.key.awarded_volume = entry.value
//            if (de.companies_that_have_seen_auction.contains(entry.key.company)) {
//                messages.add(
//                    AuctionMessage(
//                        message_type = AuMessageType.SYSTEM_TO_TRADER,
//                        message = "${entry.key.company_shortname} awarded ${volume.thousands()} @ $award_price",
//                        from_user = null,
//                        to_company = t.company
//                    )
//                )
//            }
//        }

        deRound.buyers().forEach { b: DeTradingCompany ->
            val award_vol = deRound.match_vol(b)
            b.award(OrderType.BUY, award_vol)
            if(de.companies_that_have_seen_auction.any{it.company_id == b.company_id}){
                AuctionMessage(
                    message_type_ = AuMessageType.SYSTEM_TO_TRADER,
                    message_ = "${b.shortname_at_auction_time} bought ${award_vol.thousands()} @ $award_price",
                    from_user_ = null,
                    to_company_ = b
                )

            }
        }

        deRound.sellers().forEach { s: DeTradingCompany ->
            val award_vol = deRound.match_vol(s)
            s.award(OrderType.SELL, award_vol)
            if(de.companies_that_have_seen_auction.contains(s)){
                AuctionMessage(
                    message_type_ = AuMessageType.SYSTEM_TO_TRADER,
                    message_ = "${s.shortname_at_auction_time} sold ${award_vol.thousands()} @ $award_price",
                    from_user_ = null,
                    to_company_ = s
                )
            }
        }

        db.save(de)
    }

}
