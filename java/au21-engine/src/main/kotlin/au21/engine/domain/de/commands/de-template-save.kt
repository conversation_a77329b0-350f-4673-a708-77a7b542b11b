// File: de-template-save.kt
package au21.engine.domain.de.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail_if_not_auctioneer
import au21.engine.framework.database.AuEntityManager

class DeTemplateSaveCommand(
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)


        return DeTemplateSaveAction(this, db, session)
    }
}


class DeTemplateSaveAction(
    override val command: DeTemplateSaveCommand,
    override val db: AuEntityManager,
    override val session: AuSession
) : EngineAction {
    override fun mutate() {
        TODO("Not yet implemented")
    }

}
