// File: de-round-history.kt
package au21.engine.domain.de.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeRound
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail
import au21.engine.framework.commands.fail_if_not_auctioneer
import au21.engine.framework.database.AuEntityManager

/**
 * sends trader matrix and other info when round selected
 */
class DeRoundHistoryCommand(
    val auction_id:String,
    val round_number:String
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)

        val de: DeAuction = db.auction_or_alert(auction_id)

        val round_num:Int = round_number.toIntOrNull() ?: fail("round_number invalid: $round_number")
        val deRound: DeRound = de.rounds.find { it.number == round_num }
            ?: fail("no round found with number: $round_num")

        return DeRoundControllerAction(this, db, session, de, deRound)
    }
}


class DeRoundControllerAction(
    override val command: DeRoundHistoryCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de:DeAuction,
    val deRound:DeRound
) : EngineAction {

    override fun mutate() {

    }

}
