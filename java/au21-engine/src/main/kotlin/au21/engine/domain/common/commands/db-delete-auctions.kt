// File: db-delete-auctions.kt
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Auction
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.database.AuEntityManager

/**
 * TODO: must make sure this is not done in production!
 * - deletes all auctions
 */
class DbDeleteAuctionsCommand : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        // TODO: check that it's not production!! and/or backup db first!
        return DbDeleteAuctionsAction(this, db, null)
    }
}

class DbDeleteAuctionsAction(
    override val command: DbDeleteAuctionsCommand,
    override val db: AuEntityManager,
    override val session: AuSession? = null,
) : EngineAction {

    override fun mutate() {
        db.deleteAllIncDeleted<Auction>()
    }
}
