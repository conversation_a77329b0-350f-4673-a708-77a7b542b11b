// File: de-auction-tick.kt
package au21.engine.domain.de.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.database.AuEntityManager

class DeAuctionsTickCommand : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)

        // TODO: find tickable auctions an tick them!
       // val auction: DeAuction = db.auction_or_alert()

        return DeAuctionsTickAction(this, db, session)
    }
}


class DeAuctionsTickAction(
    override val command: DeAuctionsTickCommand,
    override val db: AuEntityManager,
    override val session: AuSession
) : EngineAction {
    override fun mutate() {
        TODO("Not yet implemented")
    }

}
