// File: user-save.kt
package au21.engine.domain.common.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.Company
import au21.engine.domain.common.model.Person
import au21.engine.domain.common.services.logged_in_session_for_user
import au21.engine.domain.common.services.open_auctions
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.common.services.user_by_username
import au21.engine.framework.commands.*
import au21.engine.framework.commands.interfaces.ISessionsTerminated
import au21.engine.framework.commands.interfaces.SessionTermination
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.database.is_same_entity
import au21.engine.framework.utils.is_blank

class UserSaveCommand(
    val company_id: String,
    val email: String,
//    val is_admin: String,
//    val is_observer: String,
//    val is_tester: String,
    val password: String,
    val phone: String,
    val role: AuUser<PERSON>ole,
    val user_id: String,
    val username: String
) : EngineCommand() {


    override fun validate(db: <PERSON><PERSON><PERSON><PERSON>Manager, session_id: String?): EngineAction {
        val is_create: Boolean = user_id.is_blank()

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)


        err_if(username.is_blank(), "Username cannot be blank.")
        err_if(password.is_blank(), "Password cannot be blank.")

        if (username.lowercase() == "admin")
            fail("admin role not supported.")

        val username_trimmed = username.trim()
        val password_trimmed = password.trim()

        err_if(username_trimmed.length > 10, "Username cannot be greater than 10 characters.")
        err_if(password_trimmed.length > 20, "Password cannot be greater than 20 characters.")

        fail_if(username == "mandory", "Username cannot be 'mandatory'")
        fail_if(username == "default", "Username cannot be 'default'")

        val c: Company? = db.byId(company_id)

        if (role == AuUserRole.TRADER) {
            if (company_id.is_blank()) {
                err_if(true, "Traders must have a company.")
            } else if (c == null) {
                err_if(true, "Traders must have a company, and none found with id: $company_id.")
            }
        }
        fail_if_errors()


        if (is_create) {

            fail_if(db.user_by_username(username_trimmed) != null, "Username taken already.")

            return UserSaveAction(
                this, db, session, Person(
                    company = c,
                    email = email,
                    password = password_trimmed,
                    phone = phone,
                    role = role,
                    username = username_trimmed
                )
            )

        } else {

            val existing: Person? = db.byId(user_id)

            fail_if(db.open_auctions().any { it.users_that_have_seen_auction.any { p -> p.person_id == existing?.id } } ,
                "User is a trader in an open auction, first remove them from that auction then delete.")

            if (existing == null) {
                fail("No user found with id: $user_id")
            } else {
                db.user_by_username(username_trimmed)?.let { u ->
                    // ie: we have a user with that username, but it's not the existing one
                    fail_if(
                        is_same_entity(u, existing),
                        "Another user has this username."
                    )
                }

                existing.also {
                    it.company = c
                    it.email = email
                    it.password = password_trimmed
                    it.phone = phone
                    it.role = role
                    it.username = username_trimmed
                }

                return UserSaveAction(this, db, session, existing)
            }

        }

    }
}


class UserSaveAction(
    override val command: UserSaveCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val person: Person
) : EngineAction, ISessionsTerminated {

    override val sessions_terminated = mutableListOf<SessionTermination>()

    override fun mutate() {
        db.save(person)
        db.logged_in_session_for_user(person)?.let { s ->
            terminate_session(db, s, AuSession.SessionTerminationReason.USER_DELETED)
        }
    }

}


/**

package au2019.engine.common.commands

override val session_id: SessionId,
val p: CommonUserSaveRequest) : AuSessionCommand() {

data class CommonUserSaveRequest(
// val sid: String, // TODO: removed -> regenerate generated.ts and fix client
// val credit_limit: Double?, // TODO: removed -> generated.ts
val company_id: String?,
val company_shortname: String?, // TODO: add -> gen
val company_longname: String?, // TODO add -> gen
val email: String?,
val is_admin: Boolean, // TODO: added -> generated.ts
val is_observer: Boolean,
val is_tester: Boolean,
val password: String?,
val phone: String?,
val role: AuUserRole,
val user_id: String?, // TODO: added -> generated.ts
val username: String?
) : AuParameter

val max_username_characters = 10

//    val Company_name_cannot_be_more_than_16_characters = "COMPANY cannot be more than 16 characters."
//    val Username_taken = "Username taken"
//    val Company_name_taken = "COMPANY name taken"
//    val User_account_has_been_inactivated_and_cannot_be_edited = "User account has been inactivated and cannot be edited."

init {

//    # Request Validation
//
//    - [x] error if username blank
//    - [x] error if password blank
//    - [x] error if username > 10 characters
//    - [x] error if another user has this username
//
//
//    If role is TRADER
//    - [x] error if company longname or shortname are blank
//    - [x] error if another user has this company longname or shortname
//
//    If role is TRADER and request is EDIT:
//    - [x] error if company_id is blank
//
//    If EDIT:
//    - [x] cannot switch roles.

err_if_blank(p::username)
err_if_blank(p::password)

if (p.role == TRADER) {
err_if_blank(p::company_shortname)
err_if_blank(p::company_longname)
}
fail_if_errors()

fail_if(p.username!!.length > max_username_characters,
"Username cannot be more than $max_username_characters characters.")

fail_if_errors()
}

val is_create: Boolean = p.user_id.is_blank()

val is_auctioneer: Boolean = p.role == AUCTIONEER
val is_trader: Boolean = p.role == TRADER

var u_by_id: USER? = db.by_id(p.user_id)
var c_by_id: COMPANY? = db.by_id(p.company_id)

val u_by_username: USER? = db.user_by_username(
p.username,
case_insensitive = true,
include_deleted = true)

val c_by_shortname: COMPANY? = db.company_by_shortname(
p.company_shortname,
case_insensitive = true,
include_deleted = false)

val c_by_longname: COMPANY? = db.company_by_longname(
p.company_longname,
case_insensitive = true,
include_deleted = false)

init {

fun same_user_error(msg: String): String = "Another active or deleted user has the same $msg (or case variation)."

when (is_create) {

true  -> {
// CREATION REQUEST:

// cannot have same username as existing user:
err_if(u_by_username != null, same_user_error("username"))

// if trader, cannot have same company shortname or longname
if (is_trader) {
err_if(c_by_shortname != null, same_user_error("company shortname"))
err_if(c_by_longname != null, same_user_error("company longname"))
}
}
false -> {
// EDIT REQUEST:

// must find user by id:
fail_if(p.user_id.is_blank(), "User id cannot be blank.")
fail_if_null(u_by_id, "No user found with id: ${p.user_id}")

// if trader, must find company by id
if (is_trader) {
fail_if(p.company_id.is_blank(), "Company id cannot be blank.")
fail_if_null(c_by_id, "No company found with id: ${p.company_id}")
}

// cannot have the same username or company long/short names as existing user
// unless ... the are this user !
err_unless(
u_by_username == null
|| u_by_username == u_by_id,
same_user_error("username"))

if (!is_auctioneer) {
err_unless(
c_by_shortname == null
|| c_by_shortname == c_by_id,
same_user_error("company shortname"))

err_unless(
c_by_longname == null
|| c_by_shortname == c_by_id,
same_user_error("company longname"))
}

// cannot switch roles
fail_if(u_by_id!!.role != p.role, "Cannot switch roles")

}
}

// TODO: check that not switching to a company if has been in a non-deleted auction

fail_if_errors()

}


override fun mutate() {
when (is_create) {
true  -> {
if (is_trader) {
c_by_id = COMPANY(
longname = p.company_longname!!,
shortname = p.company_shortname!!)
}
u_by_id = USER(
company = c_by_id,
email = p.email ?: "",
is_observer = false,
is_tester = false,
password = p.password!!,
phone = p.phone ?: "",
role = p.role,
username = p.username!!)
}
false -> {
if (is_trader) {
c_by_id?.apply {
longname = p.company_longname!!
shortname = p.company_shortname!!
}
}
u_by_id?.apply {
email = p.email ?: ""
is_observer = false
is_tester = false
password = p.password!!
phone = p.phone ?: ""
username = p.username!!
}
}
}

c_by_id?.let { db.save(it) }
u_by_id?.let { db.save(it) }
}
}



 */
