// File: login.kt
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Person
import au21.engine.domain.common.services.logged_in_session_for_user
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.common.services.user_by_username
import au21.engine.framework.commands.*
import au21.engine.framework.commands.interfaces.ISessionsTerminated
import au21.engine.framework.commands.interfaces.SessionTermination
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.jsonToPrettyFormat
import au21.engine.framework.utils.objToPrettyFormat
import au21.engine.framework.utils.to_json
import io.quarkus.logging.Log

class LoginCommand(
    val username: String,
    val password: String,
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): LoginAction {

        val session = db.session_non_terminated_or_alert(session_id)
        err_if_blank(::username)
        err_if_blank(::password)
        fail_if_errors()

        if(username == "admin")
            fail("admin not implemented")

        val u: Person = db.user_by_username(
            username,
            case_insensitive = true,
            include_deleted = false
        ) ?: fail("No active user found with username: $username")

        fail_if(u.deleted, "Account not active.")
        fail_if(u.password.uppercase() != u.password.uppercase(), "Password incorrect")

        return LoginAction(this, db, session, u)
    }

}

class LoginAction(
    override val command: LoginCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val user: Person
) : EngineAction, ISessionsTerminated {

    override val sessions_terminated = mutableListOf<SessionTermination>()

    override fun mutate() {

        // create a few default users

        // if this user is already logged in then force off the session:
        db.logged_in_session_for_user(user)?.let { existing ->
            terminate_session(db, existing, AuSession.SessionTerminationReason.LOGIN_FROM_ANOTHER_BROWSER)
        }

        session.login(user)
        db.save(session)
        Log.info(objToPrettyFormat(session))
    }
}
