// File: DeFlowResult.kt
package au21.engine.domain.de.services.matcher

import au21.engine.framework.utils.format_table
import io.quarkus.logging.Log
import org.psjava.algo.graph.flownetwork.FordFulkersonAlgorithm
import org.psjava.algo.graph.flownetwork.MaximumFlowAlgorithmResult
import org.psjava.algo.graph.pathfinder.BFSPathFinder
import org.psjava.algo.graph.pathfinder.DFSPathFinder
import org.psjava.ds.graph.CapacityEdge
import org.psjava.ds.graph.MutableCapacityGraph
import org.psjava.ds.math.Function
import org.psjava.ds.numbersystrem.IntegerNumberSystem

/**
 * We'll keep this domain free for now!
 */

data class DeFlowResult(
    val from_seller: String,
    val to_buyer: String,
    val flow: Int
) {

    companion object {

        fun List<DeFlowResult>.log_tables(): String =
            format_table(
                this,
                listOf("from_seller", "to_buyer", "flow")
            )


        fun log(g: MutableCapacityGraph<String, Int>, max_flow_function: Function<CapacityEdge<String, Int>, Int>) {
            Log.info("Capacities:")
            val edges = g.vertices.sorted().flatMap { node: String ->
                g.getEdges(node).map { edge: CapacityEdge<String, Int> ->
                    object {
                        val from = edge.from()
                        val to = edge.to()
                        val capacity = edge.capacity()
                        val flow = max_flow_function.get(edge)
                    }
                }
            }
                .sortedBy { it.from }
                .sortedBy { it.to }

            Log.info(
                format_table(
                    edges,
                    listOf("from", "to", "flow", "capacity")
                )
            )
        }

        // only used by DeMatcher (other than testing)

        fun calculate_max_flow_psjava(edges: List<DeCapacityEdge>, isDFS: Boolean = true): List<DeFlowResult> {

            val nodes: Set<String> = mutableSetOf<String>().apply {
                // adding these two in case of Zero edges! (set will remove them)
                add(DeCapacityEdge.SELL_SOURCE)
                add(DeCapacityEdge.BUY_SINK)
                edges.forEach { c: DeCapacityEdge ->
                    add(c.from)
                    add(c.to)
                }
            }.toSet()

            when {
                !nodes.contains(DeCapacityEdge.SELL_SOURCE) -> throw Error("SOURCE node not found.")
                !nodes.contains(DeCapacityEdge.BUY_SINK) -> throw Error("SINK node not found.")
            }

            //  val start = System.nanoTime()

            val capacityGraph = MutableCapacityGraph.create<String, Int>().apply {
                nodes.forEach { n -> insertVertex(n) }
                edges.forEach { e -> addEdge(e.from, e.to, e.capacity) }
            }

            val result: MaximumFlowAlgorithmResult<Int, CapacityEdge<String, Int>> = FordFulkersonAlgorithm
                .getInstance(if (isDFS) DFSPathFinder.getInstance() else BFSPathFinder.getInstance())
                .calc(
                    capacityGraph,
                    DeCapacityEdge.SELL_SOURCE,
                    DeCapacityEdge.BUY_SINK,
                    IntegerNumberSystem.getInstance()
                )

            val maxflow: Int = result.calcTotalFlow()
            Log.info("max flow: $maxflow")


            val max_flow_function: Function<CapacityEdge<String, Int>, Int> = result.calcFlowFunction()

            log(capacityGraph, max_flow_function)

            val flow_results: List<DeFlowResult> = nodes
                .filter { it != DeCapacityEdge.SELL_SOURCE && it != DeCapacityEdge.BUY_SINK }
                .flatMap { trader ->
                    capacityGraph.getEdges(trader)
                        .filter { it.to() != DeCapacityEdge.BUY_SINK }
                        .map { e: CapacityEdge<String, Int> ->
                            DeFlowResult(
                                from_seller = e.from(),
                                to_buyer = e.to(),
                                flow = max_flow_function.get(e)
                            )
                        }
                }
            return flow_results
        }
    }
}

//
//    fun calculate_max_flow_princeton(constraints: List<DeCapacityEdge>): List<DeFlowResult> {
//
//        val vertex_name_to_index: MutableMap<String, Int> =
//            mutableMapOf(DeCapacityEdge.SELL_SOURCE to 0, DeCapacityEdge.BUY_SINK to 1)
//        val index_to_vertex_name: MutableMap<Int, String> =
//            mutableMapOf(0 to DeCapacityEdge.SELL_SOURCE, 1 to DeCapacityEdge.BUY_SINK)
//
//        val nodes: Set<String> = mutableSetOf<String>().apply {
//            add(DeCapacityEdge.SELL_SOURCE)
//            add(DeCapacityEdge.BUY_SINK)
//            constraints.forEach { c: DeCapacityEdge ->
//                if (!contains(c.from_seller)) {
//                    add(c.from_seller)
//                    val index: Int = vertex_name_to_index.size
//                    vertex_name_to_index[c.from_seller] = index
//                    index_to_vertex_name[index] = c.from_seller
//                }
//                if (!contains(c.to_buyer)) {
//                    add(c.to_buyer)
//                    val index: Int = vertex_name_to_index.size
//                    vertex_name_to_index[c.to_buyer] = index
//                    index_to_vertex_name[index] = c.to_buyer
//                }
//            }
//        }.toSet()
//
//        when {
//            !nodes.contains(DeCapacityEdge.SELL_SOURCE) -> throw Error("SOURCE node not found.")
//            !nodes.contains(DeCapacityEdge.BUY_SINK) -> throw Error("SINK node not found.")
//        }
//        //  val start = System.nanoTime()
//
//
//        val flow_network = FlowNetwork(nodes.size)
//        constraints.forEach { c: DeCapacityEdge ->
//            println("adding constraint: from=${c.from_seller}, to=${c.to_buyer}, capacity=${c.capacity}")
//            flow_network.addEdge(
//                FlowEdge(
//                    vertex_name_to_index[c.from_seller]!!,
//                    vertex_name_to_index[c.to_buyer]!!,
//                    c.capacity.toDouble()
//                )
//            )
//        }
//
//        val max_flow: FordFulkerson = FordFulkerson(
//            flow_network,
//            vertex_name_to_index[DeCapacityEdge.SELL_SOURCE]!!,
//            vertex_name_to_index[DeCapacityEdge.BUY_SINK]!!
//        )
//
//        println("max flow: " + max_flow.value())
//        flow_network.edges().forEach { fe: FlowEdge ->
//            println(
//                "from: ${index_to_vertex_name[fe.from()]}, " +
//                        "to: ${index_to_vertex_name[fe.to()]}, " +
//                        "flow: ${fe.flow()} / " +
//                        "capacity: ${fe.capacity()}"
//            )
//        }
//
//        return emptyList()
//    }
