package com.au21.`exp-cdi`

import javax.annotation.PostConstruct
import javax.annotation.PreDestroy
import javax.enterprise.context.ApplicationScoped
import javax.enterprise.context.RequestScoped
import javax.enterprise.inject.Produces
import javax.ws.rs.ext.Provider

var counter = 0

@RequestScoped
class RequestScopedBean{

    val count = counter++

    init {
        println("request scoped bean created: $count")
    }

    @PostConstruct
    fun postConstruct(){
        println("PostConstruct for bean: $count")
    }

    @PreDestroy
    fun preDestroy(){
        println("Predestroy for bean: $count")
    }
}

@ApplicationScoped
class CDIExp {
//
//    @Produces
//    fun requestScopedBead() = RequestScopedBean()
}
