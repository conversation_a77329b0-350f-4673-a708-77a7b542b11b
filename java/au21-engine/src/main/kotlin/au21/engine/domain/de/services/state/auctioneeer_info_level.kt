// File: auctioneeer_info_level.kt
package au21.engine.domain.de.services.state

// TODO: we should push this to the model I think, and persist it, more efficient

// fun DeAuction.de_auctioneer_info_level(): DeAuctioneerInfoLevel =
//    when (auctioneer_state) {
//
//        DeAuctioneerState.STARTING_PRICE_NOT_SET ->
//            when (time_state()) {
//                DeTimeState.BEFORE_ANNOUNCE_TIME -> WARNING
//                DeTimeState.BEFORE_START_TIME -> ERROR
//                DeTimeState.AFTER_START_TIME -> ERROR
//            }
//
//        DeAuctioneerState.STARTING_PRICE_SET ->
//            when (time_state()) {
//                DeTimeState.BEFORE_ANNOUNCE_TIME -> NORMAL // TODO: check this is right
//                DeTimeState.BEFORE_START_TIME -> ERROR
//                DeTimeState.AFTER_START_TIME -> ERROR
//            }
//
//        DeAuctioneerState.STARTING_PRICE_ANNOUNCED ->
//            when (time_state()) {
//                DeTimeState.BEFORE_ANNOUNCE_TIME -> WARNING // yep, could happen if manually announced
//                DeTimeState.BEFORE_START_TIME -> NORMAL
//                DeTimeState.AFTER_START_TIME -> ERROR
//            }
//
//        DeAuctioneerState.ROUND_OPEN_WAITING_FOR_BIDS -> {
//            TODO() // post 25, 30 seconds, closeable, awardable, post last bid
//        }
//
//        DeAuctioneerState.ROUND_OPEN_ALL_BIDS_IN -> {
//            TODO() // post 25, 30 seconds, closeable, awardable, post last bid
//        }
//
//        DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> WARNING
//
//        DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> NORMAL
//
//        DeAuctioneerState.AUCTION_CLOSED -> NORMAL
//    }
