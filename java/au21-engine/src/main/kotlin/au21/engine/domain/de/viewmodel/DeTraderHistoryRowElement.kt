// File: DeTraderHistoryRowElement.kt
package au21.engine.domain.de.viewmodel

import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.OrderSubmissionType
import au21.engine.domain.common.model.OrderType
import au21.engine.domain.common.model.PriceDirection
import au21.engine.domain.de.model.*
import au21.engine.framework.client.StoreElement
import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.thousands

data class DeTraderHistoryRowElement(
    override val id: String,
    //  val seller_range: String = a.seller_activity(r)
    // val match_range: DeMatchedSellRatio,
    // val match_range_label: String = a.activity_percentage_formatted_trader(r)
    val auction_id: String,
    val bid_constraints: DeBidConstraints?,
    val company_id: String,
    val excess_side: OrderType?,
    val excess_level: String,
    val order_submitted_by: String,
    val order_submission_type: OrderSubmissionType,
    val order_type: OrderType?,
    val price_direction: PriceDirection?,
    val price_has_reversed: Boolean,
    val price_suffix: String,
    val quantity: String,
    val round_number: String,
    val round_price: String,
    val value: String,
) : StoreElement {

    companion object {

//        @Transient
//        private val o: DeOrder? = r.get_order(t)
//
//        @Transient
//        private val rti: DeAuction.RoundTraderInfo? = r.get_rti(t)


        fun create(
            a: DeAuction,
            r: DeRound,
            t: DeTradingCompany // o: DeOrder?
        ): DeTraderHistoryRowElement {

            val o: DeOrder = r.get_order(t)
            val constraints: DeBidConstraints? = r.get_rti(t)?.constraints

            return DeTraderHistoryRowElement(
                id = "ROUND.${r.number}",
                auction_id = a.id_str(),
                bid_constraints = constraints,
                company_id = t.company_id.toString(),
                //  match_range: DeMatchedSellRatio,
                //  match_range_label = a.activity_percentage_formatted_trader(r)
                price_has_reversed = r.has_reversed,
                price_direction = r.price_direction,
                price_suffix =
                when (o.type) {
                    OrderType.BUY -> "or lower"
                    OrderType.SELL -> "or higher"
                    OrderType.NONE -> "none" // TODO: is this used??
                },
                round_number = r.number.toString(),
                round_price = a.format_round_price(AuUserRole.TRADER, r),
                //   seller_range = a.seller_activity(r)
                excess_level =
                when (a.show_trader_excess(r)) {
                    true -> a.excess_level(r, AuUserRole.TRADER)
                    false -> ""
                },
                excess_side =
                when (a.show_trader_excess(r)) {
                    true -> a.excess_side(r)
                    false -> null
                },
                order_submission_type = o.submission_type,
                order_submitted_by = when (o.submission_type) {
                    OrderSubmissionType.MANUAL -> o.user?.username_at_auction_time
                        ?: throw AlertException("Manual order must have a username")
                    OrderSubmissionType.DEFAULT -> "(default)"
                    OrderSubmissionType.MANDATORY -> "(manual)"
                },
                order_type = o.type,
                // ue = a.order_ue_str(o),
                value = a.order_value_str(o),
                quantity = o.quantity.thousands(),
            )
        }
    }


}
