package au21.engine.domain.de.exp

import au21.engine.framework.database.AuEntityManager
import org.eclipse.microprofile.graphql.GraphQLApi
import org.eclipse.microprofile.graphql.Mutation
import org.eclipse.microprofile.graphql.Query
import java.util.*
import javax.inject.Inject

/*
 *  NB: ObjectDB enhancement and Quarkus Graphql are incompatible
 */

class FilmInput {
    var title: String=""
}

@GraphQLApi
class FilmResource {

    @Inject
    lateinit var db: AuEntityManager

    @Query
    fun hello(): String {
//        println(context)
        return "hello"
    }

    @Query
    fun films(): List<Film> {
        return db.findAll<Film>()
        //return emptyList()
    }

    @Mutation
    fun createRandomFilm(): String {
        val title = Date().toString()
        db.transact {
            val f = Film()
            f.title = title
            db.save(f)
        }
        return "created: ${title}"
    }


    @Mutation
    fun creatFilm(input: FilmInput):List<Result>{
        db.transact {
            val f = Film()
            f.title = input.title
            db.save(f)
        }
        return emptyList()
    }


    @Mutation
    fun deleteFilms(): String {
        db.transact {
            db.deleteAll<Film>()
        }
        return "Deleted films"
    }

    @Mutation
    fun saveUser(r: SaveUser): ResultsEnvelope {
        return ResultsEnvelope(
            listOf(
                AlertResult(),
                UserResult(username = r.username)
            )
        )
    }

    @Mutation
    fun saveCompany(r: SaveCompany): ResultsEnvelope {
        return ResultsEnvelope(
            listOf(
                AlertResult(),
                UserResult(username = r.shortName)
            )
        )
    }

}
