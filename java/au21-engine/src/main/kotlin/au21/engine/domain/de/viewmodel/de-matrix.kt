// File: de-matrix.kt
package au21.engine.domain.de.viewmodel

import au21.engine.domain.common.model.OrderType
import au21.engine.domain.de.model.*
import au21.engine.framework.client.ClientCommand.StoreCommand.AddElements
import au21.engine.framework.client.StoreElement

data class DeMatrixRoundElement(
    override val id: String,
    val round_number: Int,
    val nodes: List<DeMatrixNodeElement>,
    val edges: List<DeMatrixEdgeElement>,
) : StoreElement {

    companion object {

        fun create(r: DeRound, cost_multiplier: Double) = DeMatrixRoundElement(
            id = r.let { "Round.${r.number}" },
            round_number = r.number,
            nodes = DeMatrixNodeElement.create(r),
            edges = DeMatrixEdgeElement.create(r, cost_multiplier)
        )

        fun clear_rounds(): AddElements =
            AddElements(
                DeMatrixRoundElement::class.java,
                null
            )

        fun add_rounds(de: DeAuction, deRounds: List<DeRound>): AddElements =
            AddElements(
                DeMatrixRoundElement::class.java,
                deRounds.map { create(it, de.settings.cost_multiplier) })

    }
}

data class DeMatrixNodeElement(
    override val id: String,
    val round: Int,
    // val auction_id: String = a.id_str()
    val cid: String,
    val shortname: String,

    val buy_match: Int,
    val buy_max: Int,
    val buy_min: Int,
    val buy_quantity: Int,

    val sell_match: Int,
    val sell_max: Int,
    val sell_min: Int,
    val sell_quantity: Int,
    //  val fully_opposed_match: Int = 0 // TODO: //  rbi.fully_opposed_match_vol

    val side: OrderType,
    val cost: Double,
    val cost_str: String,

    ) : StoreElement {

//    @Transient
//    private val rbi: DeAuction.RoundTraderInfo? = r.get_rti(t)
//
//    @Transient
//    private val constraints: DeBidConstraints? = rbi?.constraints
//
//    @Transient
//    private val o: DeOrder? = r?.get_order(t)

    companion object {
        fun create(
            r: DeRound
        ): List<DeMatrixNodeElement> =

            r.trader_infos.map { rbi: DeRoundTraderInfo ->
                val constraints: DeBidConstraints = rbi.constraints
                val round_num: Int = r.number
                val cid: String = rbi.de_trading_company.company_id.toString()
                val shortname: String = rbi.de_trading_company.shortname_at_auction_time

                DeMatrixNodeElement(
                    id = "R_${round_num}_T_${cid}",
                    //  auction_id = a.id_str()
                    //  val fully_opposed_match: Int = 0 // TODO: //  rbi.fully_opposed_match_vol
                    round = round_num,
                    cid,
                    shortname,

                    buy_match = r.matches
                        .filter { rbi.de_trading_company.company_id == it.buy_order.trading_company.company_id }
                        .sumOf { it.match }, // .thousands() else ""
                    buy_max = constraints.max_buy_quantity,
                    buy_min = constraints.min_buy_quantity, // .thousands() // "0"
                    buy_quantity = r.buy_quantity(rbi.de_trading_company), //.thousands()

                    sell_match = r.matches
                        .filter { rbi.de_trading_company.company_id == it.sell_order.trading_company.company_id }
                        .sumOf { it.match },
                    sell_max = constraints.max_sell_quantity,
                    sell_min = constraints.min_sell_quantity, //.thousands()
                    sell_quantity = r.sell_quantity(rbi.de_trading_company), //.thousands(),

                    side = rbi.order.type,
                    cost = rbi.order.cost,
                    cost_str = rbi.order.cost_str
                )
            }.sortedBy { it.id }
    }
}


data class DeMatrixEdgeElement(
    override val id: String,
    val r: Int,

    val buyer_shortname: String, // only needed for debug
    val buyer_cid: String,

    val seller_shortname: String, // only needed for debug
    val seller_cid: String,

    // volumes:
    // val min: Int, ?? how would we show this? can we determine by buyer?
    val credit_quantity_limit: Int,
    val buy_quantity_limit: Int,
    val selling_quantity_limit: Int,
    val capacity: Int, // ie: min(round_credit_volume_limit, buyer.vol, seller.vol),
    val match: Int,

    // value:
    val value: Double,
    val value_str: String,

    val credit_str: String,
    //  val buyer_dollar_credit_limit_at_auction: String // TODO: put this in an auction counterparty credit matrix
) : StoreElement {

    companion object {

        // for testing too:
        fun to_id(
            round_num: Int,
            b_cid: String,
            s_cid: String,
        ): String =
            "R_${round_num}_B_${b_cid}_S_${s_cid}"

        fun create(deRound: DeRound, cost_multiplier: Double): List<DeMatrixEdgeElement> =
            deRound.matches.map { m: DeMatch ->
                val round_number = deRound.number
                val b_cid = m.buy_order.trading_company.company_id.toString()
                val s_cid = m.sell_order.trading_company.company_id.toString()
                // TODO: need to move a lot of these calculations to the match!
                DeMatrixEdgeElement(
                    id = to_id(round_number, b_cid = b_cid, s_cid = s_cid),
                    r = round_number,

                    buyer_shortname = m.buyer_shortname, // only needed for debug
                    buyer_cid = b_cid,

                    seller_shortname = m.seller_shortname, // only needed for debug
                    seller_cid = s_cid,

                    credit_quantity_limit = m.selling_quantity_limit,
                    buy_quantity_limit = m.buy_limit,
                    selling_quantity_limit = m.sell_limit,
                    capacity = m.capacity,
                    match = m.match,

                    credit_str = m.credit_str,
                    value = m.value,
                    value_str = m.value_str

//                    credit_str = AuFormatter.format_currency(
//                        m.seller.company.get_credit_limit(m.buyer.company)
//                    ),
                    //     credit = seller.get_credit_limit(buyer),
                    //   credit_str = m.seller.company.get_credit_limit_str(m.buyer.company)
                )
            } //.sortedBy { it.id })
    }

}

