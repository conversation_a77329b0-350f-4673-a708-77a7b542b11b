// File: de-create-db.kt
package au21.engine.domain.de.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.Person
import au21.engine.domain.common.services.find_auction_by_name
import au21.engine.domain.common.services.user_by_username
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.services.sampledb.TradingUserFixture
import au21.engine.domain.de.services.sampledb.create_sample_db_auction
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.database.AuEntityManager
import kotlin.random.Random


class DeCreateSampleDbCommand(
    val auction_count: Int = 1,
    val auctioneer_count: Int = 2,
    val close_last_round: Boolean = false,
    val round_count: Int = 30,
    val trader_count: Int = 20,
    val use_counterparty_credits: Boolean = false,
) : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        return DeCreateSampleDbAction(
            this,
            db,
            null,
            trader_count,
            round_count,
            auction_count,
            auctioneer_count,
            close_last_round,
            use_counterparty_credits
        )
    }
}

class DeCreateSampleDbAction(
    override val command: DeCreateSampleDbCommand,
    override val db: AuEntityManager,
    override val session: AuSession? = null,
    val trader_count: Int,
    val round_count: Int,
    val auction_count: Int,
    val auctioneer_count: Int,
    val close_last_round: Boolean,
    val use_counterparty_credits: Boolean,
) : EngineAction {

    override fun mutate() {

        (1..auctioneer_count).forEach { i ->
            db.user_by_username("a$i") ?: db.save(
                Person(
                    username = "a$i",
                    password = "1",
                    role = AuUserRole.AUCTIONEER
                )
            )
        }

        val trading_users: List<Person> = TradingUserFixture.create(
            db,
            trader_count,
            use_counterparty_credits
        )

        (1..auction_count).forEach { num ->
            Random.nextInt(10000).toString().let { count: String ->
                val auction_name = "Auction $num - $count"
                db.find_auction_by_name<DeAuction>(auction_name) ?: run {
                    val de = create_sample_db_auction(
                        auction_name = auction_name,
                        trading_users = trading_users,
                        round_count = round_count,
                        close_last_round = close_last_round,
                        use_counterparty_credits = use_counterparty_credits
                    )
                    db.save(de)
                }
            }

        }
    }
}
