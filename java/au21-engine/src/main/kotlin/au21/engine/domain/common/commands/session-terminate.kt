// File: session-terminate.kt
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail
import au21.engine.framework.commands.interfaces.ISessionsTerminated
import au21.engine.framework.commands.interfaces.SessionTermination
import au21.engine.framework.database.AuEntityManager


class SessionTerminateCommand(
    val reason: AuSession.SessionTerminationReason
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        // NOTE: this alert won't go anywhere, but will be logged
        val s = db.session_non_terminated_or_alert(session_id)

        // same here, just for logging
        if (s.termination_reason() != null) {
            fail("Session already terminated: $session_id, reason:${s.termination_reason()}")
        }

        // TODO:
        // we could check that you're logged in
        // - but probably we'd still terminate the session

        return SessionTerminateAction(this, db, s, reason)
    }

}

class SessionTerminateAction(
    override val command: SessionTerminateCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val reason: AuSession.SessionTerminationReason
) : EngineAction, ISessionsTerminated {

    override val sessions_terminated = mutableListOf<SessionTermination>()

    override fun mutate() {
        terminate_session(db, session, reason)
    }

}

