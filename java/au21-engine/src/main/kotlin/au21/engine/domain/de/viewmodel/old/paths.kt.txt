package au21.engine.domain.de.viewmodel

import au21.engine.domain.de.model.DeAuction

object DeViewPaths {
    fun resolve(
        auction: DeAuction? = null,
        round: DeAuction.Round? = null,
        trader: DeAuction.Trader? = null,
        order: DeOrder? = null,
        rank: Int? = null): String =
            "".apply {
                auction?.let { plus("/auction/${auction.id_str()}") }
                round?.let { plus("/rounds/${round.number}") }
                trader?.let { plus("/traders/${trader.company_shortname}") }
                // buyer?.let { plus("/buyer/${buyer.company.short_name}") }
                // seller?.let { plus("/seller/${seller.company.short_name}") }
                order?.let {
                    plus("/orders${if (order.isBuy()) "/buy" else "/sell"}/${order.company_shortname}/user/${order.username}")
                }
                rank?.let { plus("/rank/$rank") }
            }
}

