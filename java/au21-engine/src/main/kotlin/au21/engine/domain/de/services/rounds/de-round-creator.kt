// File: de-round-creator.kt
package au21.engine.domain.de.services.rounds

import au21.engine.domain.de.model.*
import au21.engine.domain.de.services.constraints.calculate_subsequent_round_constraints
import au21.engine.domain.de.services.defaultorders.DeOrderInfo
import au21.engine.domain.de.services.matcher.DeMatcher
import au21.engine.domain.de.services.nextroundprice.DeNextRoundPriceInfo
import au21.engine.framework.commands.AlertException

/**
 * THESE MUST BE CALLED AFTER THE ROUND IS CREATED!
 */

/**
 *  CURRENT
 *  - first round always created (ie: when auction created)
 *  - then when traders added, you create the rti's
 */

fun DeAuction.create_subsequent_round(): DeRound {
    /**
     * ALWAYS: if prev supply > demand then price goes up
     * else if demand > supply then price goes down
     * else if supply and demand are equal then stop ?
     */

    val prev_de_round: DeRound = lastround()
    // 1)
    val next_price_info: DeNextRoundPriceInfo = DeNextRoundPriceInfo.create(
        prev_round_number = prev_de_round.number,
        prev_round_price = prev_de_round.price
            ?: throw AlertException("Previous round has no price, therefore cannot create subsequent round"),
        prev_round_direction = prev_de_round.price_direction,
        prev_round_is_post_reversal = prev_de_round.has_reversed,
        prev_round_total_buy = total_buy(prev_de_round),
        prev_round_total_sell = total_sell(prev_de_round),
        price_change_initial = settings.price_rule.price_change_initial,
        price_change_post_reversal = settings.price_rule.price_change_post_reversal,
    )

    // 2)
    val next_de_round = DeRound(
        number = rounds.size + 1,
        price = next_price_info.price,
        has_reversed_ = next_price_info.is_post_price_reversal,
        direction = next_price_info.direction
    )

    if (next_de_round.number < 2) throw AlertException("Expected round number > 1")

    rounds.add(next_de_round)

    de_trading_companies.forEach { t: DeTradingCompany ->

        // remove if existing:
        next_de_round.apply { get_rti(t)?.let { trader_infos.remove(it) } }

        val prev_order: DeOrder = prev_de_round.get_order(t)

        val prev_rti: DeRoundTraderInfo = prev_de_round.get_rti(t)
            ?: throw AlertException("Exepected round trader info for trader: ${t.shortname_at_auction_time} in round ${prev_de_round.number}.")

        val prev_constraints = prev_rti.constraints

        // 3)
        val next_round_constraints:DeBidConstraints = calculate_subsequent_round_constraints(
            prev_constraints, prev_order.type, prev_order.quantity, next_price_info.direction
        )

        // 4)
        val order_info:DeOrderInfo =  DeOrderInfo.create_default_from_constraints(next_round_constraints)

        val default_order: DeOrder = DeOrder(
            round = next_de_round,
            trading_company_ = t,
            u = null,
            order_info = order_info,
            cost_multiplier = settings.cost_multiplier,
            prev_order = null,
        )

        next_de_round.trader_infos.add(
            // 5)
            DeRoundTraderInfo(
                trading_company_ = t,
                constraints_ = next_round_constraints,
                default_order = default_order,
                current_matched_vol = 0,
                fully_opposed_match_vol = 0
            )
        )
        // in case we somehow have default buy and sell bids (not sure if that's possible)
        DeMatcher.calculate_and_set_matches(this, null)
    }
    return next_de_round
}
