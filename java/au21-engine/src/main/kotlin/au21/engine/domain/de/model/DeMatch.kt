// File: DeMatch.kt
package au21.engine.domain.de.model

import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.AuFormatter
import javax.persistence.Embeddable


@Embeddable
class DeMatch(
    //round: Round,
    sell_order_: De<PERSON>rder,
    buy_order_: De<PERSON>rder,
    match_: Int,
    capacity_: Int,
    round_price_multiplier: Double, // ie: cost_multiplier * round_price
) {

    init {
        // TODO: we seem to have this check in so many places!
        if (sell_order_.trading_company.company_id == buy_order_.trading_company.company_id)
            throw AlertException(
                "Buyer and seller cannot be the same: " +
                        sell_order_.trading_company.shortname_at_auction_time
            )
    }

    var sell_order: DeOrder = sell_order_
        private set

    var buy_order: DeOrder = buy_order_
        private set

    var buyer_shortname = buy_order.trading_company.shortname_at_auction_time
        private set

    var seller_shortname = sell_order.trading_company.shortname_at_auction_time
        private set

    // TODO: maybe these 3 point back to the initial limits object?
    var selling_quantity_limit: Int =
        sell_order.trading_company.initial_limits.initial_selling_quantity_limit
        private set

    var credit:Double? = sell_order.trading_company.initial_limits.initial_buying_cost_limit
        private set

    var credit_str:String = sell_order.trading_company.initial_limits.initial_buying_cost_limit_str
        private set

    var buy_limit: Int = buy_order.quantity
        private set

    var sell_limit: Int = sell_order.quantity

    // ie: the min of credit_volume_limit, buyer order vol, seller order vol:
    var capacity: Int = capacity_
        private set

    var match: Int = match_
        private set

    var value: Double = match * round_price_multiplier
        private set

    var value_str: String = AuFormatter.format_currency(value)
        private set
}
