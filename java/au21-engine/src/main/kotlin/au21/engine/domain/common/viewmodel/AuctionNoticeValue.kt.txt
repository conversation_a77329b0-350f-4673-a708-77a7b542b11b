package au21.engine.domain.common.commands.client

import au21.engine.domain.common.commands.engine.AuctionSelectAction
import au21.engine.domain.common.commands.engine.NoticeSaveAction
import au21.engine.domain.common.model.Auction
import ClientCommandHelper
import StoreValue
import send

class AuctionNoticeValue(a: Auction) : StoreValue {
    val auction_id: String = a.id_str()
    val notice: String = a.notice

    companion object {

//        private fun delete(h: ClientCommandHelper){
//            AuctionNoticeValue::class.java.delete(h, h.sid)
//        }

        fun onAction(e: AuctionSelectAction, h: ClientCommandHelper){
            AuctionNoticeValue(e.a).send(h, h.sid)
        }

//        fun onAction(e: DeAuctionSaveAction, h: ClientCommandHelper) {
//            if(e.is_create){
//                delete(h)
//            }
//        }

        fun onAction(e: NoticeSaveAction, h:Client<PERSON>ommandHelper){
            AuctionNoticeValue(e.a).send(h, *h.auction_sids(e.a))
        }


    }
}
