// File: DeAuctioneerStatusValue.kt
package au21.engine.domain.de.viewmodel

import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.AutopilotMode
import au21.engine.domain.common.model.OrderType
import au21.engine.domain.de.model.*
import au21.engine.domain.de.services.state.de_controls
import au21.engine.framework.client.StoreValue
import org.joda.time.DateTime
import org.joda.time.Period


data class DeAuctioneerStatusValue(
    val announced: Boolean,
    val auctioneer_state: DeAuctioneerState,
    val auctioneer_state_text: String,
    val autopilot: AutopilotMode,
    val awardable: Boolean = false,
    val controls: Map<DeFlowControlType, Boolean>,
    val excess_side: OrderType,
    val excess_level: String,
    // unlike price_has_reversed, price_has_overshot also looks at current round !
    val price_has_overshot: Boolean,
    val round_open_min_secs: Int?,
    //   val round_state: DeRoundState = de.round_state
    val starting_price: String,
    val time_state: DeTimeState?,
) : StoreValue {

//    @Transient
//    private val n: DeAuction.Round? = de.lastround()

    companion object {
        fun create(de: DeAuction): DeAuctioneerStatusValue {

            val n: DeRound = de.lastround()

            return DeAuctioneerStatusValue(
                announced = de.starting_price_announced(),
                auctioneer_state = de.auctioneer_state,
                auctioneer_state_text = de.auctioneer_state_text,
                autopilot = de.autopilot,
                awardable = false, // TODO de.is_continuable()
                controls = de.de_controls(),
                excess_side = de.excess_side(n),
                excess_level = de.excess_level(n, AuUserRole.AUCTIONEER),
                // unlike price_has_reversed, price_has_overshot also looks at current round !
                price_has_overshot = de.price_has_overshot(),
                round_open_min_secs = n.open_time?.let {
                    Period(DateTime(), DateTime(it)).seconds
                },
                //    round_state: DeRoundState = de.round_state
                starting_price = de.format_round_price(AuUserRole.AUCTIONEER, de.firstround()),
                time_state = de.time_state()
            )
        }
    }


}
