package com.au21.exp

import com.au21.`exp-cdi`.RequestScopedBean
import org.eclipse.microprofile.graphql.Description
import org.eclipse.microprofile.graphql.GraphQLApi
import org.eclipse.microprofile.graphql.Query
import javax.inject.Inject

@GraphQLApi
class FilmResource {

//    @Inject
//    var context: Context? = null

    @Inject
    lateinit var requestScopedBean: RequestScopedBean

    @Inject
    lateinit var service: GalaxyService

    @get:Description("Get all Films from a galaxy far far away")
    @get:Query("allFilms")
    val allFilms: List<Film>
        get() {
            println(requestScopedBean.count)
//            println(context)
            return service.allFilms
        }


}
