// File: company-save.kt
package au21.engine.domain.common.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.model.Company
import au21.engine.domain.common.model.Person
import au21.engine.domain.common.services.*
import au21.engine.framework.commands.*
import au21.engine.framework.commands.interfaces.ISessionsTerminated
import au21.engine.framework.commands.interfaces.SessionTermination
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.is_blank

class CompanySaveCommand(
    val company_id: String,
    val company_shortname: String,
    val company_longname: String
) : EngineCommand() {


    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        val is_create: Boolean = company_id.is_blank()

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)

        err_if(company_shortname.is_blank(), "Company short name cannot be blank.")
        err_if(company_longname.is_blank(), "Company long name cannot be blank.")

        val shortname_trimmed = company_shortname.trim()
        val longname_trimmed = company_shortname.trim()

        err_if(shortname_trimmed.length > 10, "Company short name cannot be longer than 10 characters.")
        err_if(longname_trimmed.length > 20, "Company long name cannot be longer than 20 characters.")

        err_if(shortname_trimmed.contains("mandory"), "Short name cannot be 'mandatory'")
        err_if(shortname_trimmed.contains("default"), "Short name cannot be 'default'")

        err_if(longname_trimmed.contains("mandory"), "Long name cannot be 'mandatory'")
        err_if(longname_trimmed.contains("default"), "Long name cannot be 'default'")

        fail_if_errors()

        if (is_create) {

            err_if(db.company_by_shortname(shortname_trimmed) != null, "Company short name taken.")
            err_if(db.company_by_longname(longname_trimmed) != null, "Company long name taken.")
            fail_if_errors()

            return CompanySaveAction(
                this, db, session,
                Company(
                    longname = longname_trimmed,
                    shortname = shortname_trimmed
                )
            )

        } else {

            // ie: EDIT

            val company: Company? = db.byId(company_id)

            if (company == null) {

                fail("No company found with id: $company_id")

            } else {

                fail_if(
                    db.open_auctions().any { a: Auction -> a.has_trader(company) },
                    "Company is a trader in an open auction, first remove them from that auction then delete."
                )

                db.company_by_shortname(company_shortname)?.let { c ->
                    // ie: we have a company with that shorname and it's not the existing one:
                    err_if(c != company, "Another company has that short name.")
                }
                db.company_by_longname(company_longname)?.let { c ->
                    // as above we have a company with that longname and it's not the existing one:
                    err_if(c != company, "Another company has that long name.")
                }
                fail_if_errors()

                company.longname = company_longname
                company.shortname = company_shortname

                return CompanySaveAction(this, db, session, company)
            }
        }

    }
}


class CompanySaveAction(
    override val command: CompanySaveCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val company: Company
) : EngineAction, ISessionsTerminated {

    override val sessions_terminated = mutableListOf<SessionTermination>()


    override fun mutate() {
        db.company_users(company).forEach { u: Person ->
            db.logged_in_session_for_user(u)?.let { s: AuSession ->
                terminate_session(db, s, AuSession.SessionTerminationReason.COMPANY_DELETED)
            }
        }

        db.save(company)
    }

}
