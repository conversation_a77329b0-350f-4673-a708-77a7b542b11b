package au21.engine.domain.de.viewmodel.commands

import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.sorted_buy_orders
import au21.engine.domain.de.model.sorted_sell_orders
import StoreElement
import au21.engine.framework.utils.AuFormatter
import kotlin.math.max

//private fun order_row_path(buy_order: DeOrder?, sell_order: DeOrder?): Int =
//        buy_order?.round_number ?: sell_order?.round_number ?: throw Exception("row must have at least one order")

class DeOrderBookRowElement(
    auction: DeAuction,
    round: DeAuction.Round,
    rank: Int,
    buy_order: DeOrder?,
    sell_order: DeOrder?
) : StoreElement {

    val auction_id: String = auction.id_str()

    //    val buy_company_long: String?
    val buyer_company_id:String = buy_order?.companyId ?: ""
    val buy_company_short: String = buy_order?.company_short ?: ""
    val buy_order_id: String = DeViewPaths.resolve(auction = auction, round = round, order = buy_order)
    val buy_time: String = AuFormatter.format_order_time(buy_order?.timestamp)
    val buy_timestamp: Long? = buy_order?.timestamp?.time
    val buy_username: String? = buy_order?.username
    val buy_volume: Int? = buy_order?.volume
    val rank: Int = rank
    val round_number: Int = round.number

    //   val sell_company_long: String,
    val seller_company_id:String = sell_order?.companyId ?: ""
    val sell_company_short: String = sell_order?.company_short ?: ""
    val sell_order_id: String = DeViewPaths.resolve(auction, round, order = sell_order)
    val sell_time: String = AuFormatter.format_order_time(sell_order?.timestamp)
    val sell_timestamp: Long? = sell_order?.timestamp?.time
    val sell_username: String = sell_order?.username ?: ""
    val sell_volume: Int = sell_order?.volume ?: 0
    val show_buy_order: Boolean = buy_order != null
    val show_sell_order: Boolean = sell_order != null


    override val id: String = "ORDER_BOOK.row.${rank}"

    companion object {

        fun all(a: DeAuction): List<DeOrderBookRowElement> {
            val orders = mutableListOf<DeOrderBookRowElement>()
            a.rounds.forEach { r ->

                val buy_orders = a.sorted_buy_orders(r)
                val sell_orders = a.sorted_sell_orders(r)

                val len = max(buy_orders.size, sell_orders.size)
                (0..len).map { i ->
                    val o = DeOrderBookRowElement(
                        auction = a,
                        round = r,
                        rank = i + 1,
                        buy_order = buy_orders.takeIf { it.size > i }?.get(i),
                        sell_order = sell_orders.takeIf { it.size > i }?.get(i)
                    )
                    orders.add(o)
                }
            }
            return orders
        }
    }
}

/*
init {

    val buy_vol_str = if (DefaultGroovyMethods.asBoolean(buy_order)) De_Reader.format_order_volume(buy_order) else ""

    val order_id = object : Closure<String>(this, this) {
        fun doCall(o: DeOrder): String {
            return if (DefaultGroovyMethods.asBoolean(o)) "ROUND_" + String.valueOf(o.getRound_number()) + "_TRADER_" + o.getUsername() else ""
        }

    }

    buY_TIME = FormatUtil.format_order_time(if (buy_order == null) null else buy_order!!.getOrder_timestamp())
    buY_ORDER_ID = order_id.call(buy_order)
    val person = if (buyer == null) null else buyer!!.getPerson()
    val username = if (person == null) null else person!!.getUsername()
    buY_USERNAME = if (StringGroovyMethods.asBoolean(username)) username else ""
    val person1 = if (buyer == null) null else buyer!!.getPerson()
    val name = if (person1 == null) null else person1!!.getCompany_name()
    buY_COMPANY_NAME = if (StringGroovyMethods.asBoolean(name)) name else ""
    buY_VOLUME = De_Reader.buy_vol(buy_order)
    //  IS_IMPLIED_BUY = buy_order && ( buy_order.incremental_buy == 0 )

    selL_TIME = FormatUtil.format_order_time(if (sell_order == null) null else sell_order!!.getOrder_timestamp())
    selL_ORDER_ID = order_id.call(sell_order)
    val person2 = if (seller == null) null else seller!!.getPerson()
    val username1 = if (person2 == null) null else person2!!.getUsername()
    selL_USERNAME = if (StringGroovyMethods.asBoolean(username1)) username1 else ""
    val person3 = if (seller == null) null else seller!!.getPerson()
    val name1 = if (person3 == null) null else person3!!.getCompany_name()
    selL_COMPANY_NAME = if (StringGroovyMethods.asBoolean(name1)) name1 else ""
    selL_VOLUME = De_Reader.sell_vol(sell_order)// format_order_volume ( sell_order ) : ''
}

companion object {

    fun all(a: DeAuction): List<On_De_Orderbook_Row> {
        if (!DefaultGroovyMethods.asBoolean(a)) return ArrayList()

        val rows = ArrayList<On_De_Orderbook_Row>()

        DefaultGroovyMethods.each(a.getRounds(), object : Closure<List<Any>>(null, null) {
            fun doCall(r: DeRound): List<Any> {

                val buy_orders = De_Reader.sorted_buy_orders(a, r)
                val sell_orders = De_Reader.sorted_sell_orders(a, r)

                val len = Math.max(buy_orders.size, sell_orders.size)

                val get_order = object : Closure<DeOrder>(null, null) {
                    fun doCall(orders: List<DeOrder>, i: Int): DeOrder? {
                        return if (i < orders.size) orders[i] else null
                    }

                }

                return DefaultGroovyMethods.each(IntRange(0, len), object : Closure<Boolean>(null, null) {
                    fun doCall(i: Any): Boolean? {
                        if (len > i) {
                            return rows.add(On_De_Orderbook_Row(a, i as Int, get_order.call(buy_orders, i) as DeOrder, get_order.call(sell_orders, i) as DeOrder))
                        }

                    }

                })
            }

        })
        return rows
    }
}
*/

