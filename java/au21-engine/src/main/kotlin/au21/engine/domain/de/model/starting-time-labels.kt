// File: starting-time-labels.kt
package au21.engine.domain.de.model


/*

// NOT SURE THIS IS ACTUALLY USED?
fun DeAuction._trader_state_label(): String {
    val state = current_state
    return when (state.auction_state.trader_state()) {
        DeAuctionTraderState.SETUP -> "Waiting for auction to start"
        DeAuctionTraderState.STARTING_PRICE_ANNOUNCED -> "Starting price announced"
        DeAuctionTraderState.ROUND_OPEN -> "Round ${rounds.size} open, waiting for orders."
        DeAuctionTraderState.ROUND_CLOSED -> "Round ${rounds.size} closed."
        DeAuctionTraderState.AUCTION_CLOSED -> "Auction closed"

        DeAuctioneerState.STARTING_PRICE_NOT_SET ->
            when (time_state()) {
                DeTimeState.BEFORE_ANNOUNCE_TIME ->
                    settings.auction_starting_time_text() +
                            " starting price will be annouced ${settings.starting_price_announcement_mins} before the auction starts"
                DeTimeState.BEFORE_START_TIME ->
                    settings.auction_starting_time_text() +
                            " starting price will be announced soon"
                DeTimeState.AFTER_START_TIME ->
                    "Starting price will be announced soon"
            }
        DeAuctioneerState.STARTING_PRICE_SET ->
            when (time_state()) {
                DeTimeState.BEFORE_ANNOUNCE_TIME ->
                    settings.auction_starting_time_text() +
                            " starting price will be annouced ${settings.starting_price_announcement_mins} before the auction starts"
                DeTimeState.BEFORE_START_TIME ->
                    settings.auction_starting_time_text() +
                            " starting price will be annouced before the auction starts"
                DeTimeState.AFTER_START_TIME ->
                    "starting price will be announced soon"
            }

        DeAuctioneerState.STARTING_PRICE_ANNOUNCED ->
            when (time_state()) {
                DeTimeState.BEFORE_ANNOUNCE_TIME ->
                    "starting price is ${_starting_price_label()} ${settings.auction_starting_time_text()}"
                DeTimeState.BEFORE_START_TIME ->
                    "starting price is ${_starting_price_label()} ${settings.auction_starting_time_text()}"
                DeTimeState.AFTER_START_TIME ->
                    "starting price is ${_starting_price_label()} auction will start shortly"
            }
        DeAuctioneerState.ROUND_OPEN_WAITING_FOR_BIDS ->
            lastround()?.let { n: DeAuction.Round ->
                "Round ${n.number} open for bidding." +
                        if (n.round_open_seconds() > settings.round_red_secs)
                            " waiting for slow bidders"
                        else ""
            } ?: ""
        DeAuctioneerState.ROUND_OPEN_ALL_BIDS_IN ->
            lastround()?.let { n: DeAuction.Round ->
                "Round ${n.number} open for bidding." +
                        if (n.round_open_seconds() > settings.round_red_secs)
                            " waiting for slow bidders"
                        else ""
            } ?: ""
        DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> "Round ${lastround()?.number} closed."
        DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> "Round ${lastround()?.number} closed."
        DeAuctioneerState.AUCTION_CLOSED -> "Auction closed"


    }
}

 */
