// File: de-traders-add.kt
package au21.engine.domain.de.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Company
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.is_before_end_of_first_round
import au21.engine.domain.de.services.matcher.DeMatcher
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail
import au21.engine.framework.commands.fail_if_not_auctioneer
import au21.engine.framework.database.AuEntityManager

/**
 * this command will look at the company_ids list and either:
 * a) add if not already in auction
 * b) remove from auction if not on the list
 * - those removed will be 'bounced' out of the auction, and auction row removed
 * - those included will have the auction row added
 */

class DeTradersAddCommand(
    val auction_id: String,
    val company_ids: List<String>
) : EngineCommand() {

    /** De Auction: Adding and removing traders
     * - can only happen before close of round one
     * - if add, check that not already added
     * - if remove, check that has not bid
     */

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)

        val de: DeAuction = db.auction_or_alert(auction_id)

        if (!de.is_before_end_of_first_round()) {
            fail("Traders can only be added before the end of round one")
        }

        // TODO: catch errors and report elegantly (ie: xxx is not a company id) defer
        val company_id_longs:List<Long> = company_ids.map { it.toLong() }
        val existing_company_ids: List<Long> = de.de_trading_companies.map { it.company_id }

        val companies_to_add: List<Company> = company_id_longs
            .filter { !existing_company_ids.contains(it) }
            .mapNotNull { db.byId<Company>(it) }

        return DeTradersAddAction(this, db, session, de, companies_to_add)
    }
}


class DeTradersAddAction(
    override val command: DeTradersAddCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de: DeAuction,
    val companies_to_add: List<Company>
) : EngineAction {
    override fun mutate() {

        companies_to_add.forEach { c ->
            de.create_trader(c)
        }
        // have to reset volume limits (if we have a round!):
        DeMatcher.calculate_and_set_matches(de, this)
        db.save(de)
    }

}
