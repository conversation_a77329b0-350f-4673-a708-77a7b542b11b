// File: DeTraderInfoValue.kt
package au21.engine.domain.de.viewmodel

import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.OrderSubmissionType
import au21.engine.domain.common.model.OrderType
import au21.engine.domain.de.model.*
import au21.engine.framework.client.StoreValue
import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.thousands


data class DeTraderInfoValue(
    val auction_id: String,
    val award_direction: String,
    val award_line: String?,
    val awarded_price: String,
    val awarded_round_number: String,
    val awarded_quantity: String,
    val awarded_value: String,
    val initial_limits: DeInitialLimits,
    val bid_constraints: DeBidConstraints, // TODO: we don't really need this, can use history row
    val company_id: String,
    val order_submission_type: OrderSubmissionType,
    val order_type: OrderType,
    val order_quantity: Int,
    val price_label: String,
    val quantity_label: String,
    val round_number: Int,
    val round_price: String,
    val value:String,
) : StoreValue {

//    @Transient
//    private val awarded_round: DeAuction.Round? = a.awarded_round
//
//    @Transient
//    private val n: DeAuction.Round? = a.lastround()
//
//    @Transient
//    private val rti: DeAuction.RoundTraderInfo? = n.get_rti(t)
//
//    @Transient
//    private val o: DeOrder? = n?.get_order(t)

    companion object {

        fun create(
            a: DeAuction,
            t: DeTradingCompany
        ): DeTraderInfoValue {

            val awarded_De_round: DeRound? = a.awarded_round
            val n: DeRound = a.lastround()

            // TODO: not sure where this alert goes exactly?
            val rti: DeRoundTraderInfo = n.get_rti(t) ?: throw AlertException("No trader info for round ${n.number}")

            val o: DeOrder = n.get_order(t)

            return DeTraderInfoValue(
               auction_id = a.id_str(),
               quantity_label = a.settings.quantity_units,
                price_label = a.settings.price_units,
//                var cost:Double = round.price * cost_multiplier * order_info.quantity
//                    private set
//
                company_id = t.company_id.toString(),

                round_number = a.lastround().number,
//                // TODO: need to make sure that we don't show a price if auction not announnnced
//                // - should be, because we only create a round when price announced
                round_price = n.let { a.format_round_price(AuUserRole.TRADER, n) },

                initial_limits = t.initial_limits,
                bid_constraints = rti.constraints,

                order_submission_type = o.submission_type,
                order_quantity = o.quantity,
                order_type = o.type,

                awarded_price = awarded_De_round?.let {
                    "${a.format_round_price(AuUserRole.AUCTIONEER, it)} ${a.settings.price_units}"
                } ?: "---",

                awarded_round_number = awarded_De_round?.let {
                    "round ${it.number}"
                } ?: "---",

                awarded_quantity = awarded_De_round?.let {
                    t.awarded_quantity.thousands()
                } ?: "---",

                awarded_value =
                if (a.closed)
                    "$ TODO" // + a.calculate_ue(a.awarded_round, t.awarded_volume).toLong()
                else "---",

                award_direction = "TODO", // if (t.bought) "Bought" else if (t.sold) "Sold" else ""

                award_line = a.awarded_round?.let {
                    if (t.awarded_quantity == 0)
                        "You were not awarded any volume"
                    // else if (StringGroovyMethods.asBoolean("You " + t.bought)) "bought" else "sold" + " ".plus("$awardeD_VOLUME at the round $awardeD_ROUND_NUMBER price of $awardeD_PRICE ").plus("with a ue of $awardeD_UE") else "---"
                    else
                        "TODO"
                },

                value = a.order_value_str(o),
            )
        }

    }
}
