// File: AuSession.kt
package au21.engine.domain.common.model

import au21.engine.domain.common.model.AuSession.ClientSocketState.CLOSED
import au21.engine.domain.common.model.AuSession.ClientSocketState.OPENED
import au21.engine.framework.PageName
import au21.engine.framework.database.AuEntity
import au21.engine.framework.utils.toEnumOrError
import org.joda.time.DateTime
import java.util.*
import javax.persistence.Entity

@Entity
class AuSession(
    var session_id: String,
    var created: Date = DateTime().toDate(),
    var browser_name: String? = null,
    var browser_version: String? = null,
    var browser_os: String? = null
) : AuEntity() {

    enum class ClientSocketState {
        OPENED, CLOSED
    }

    enum class SessionTerminationReason {
        BROWSER_UNLOADED,
        COMPANY_DELETED,
        COMPANY_NAME_EDITED,
        FORCED_OFF, // eg if user or company edited
        LOGIN_FROM_ANOTHER_BROWSER,
        SERVER_REBOOT,
        SERVER_SWEPT_STALE_SESSION,
        SIGNED_OFF,
        USER_EDITED,
        USER_DELETED,
    }

//    enum class SessionStatus {
//        CREATED,
//        LOGGED_IN,
//        MANUAL_LOGOUT,
//        USER_DELETED,
//        SOCKET_DISCONNECTED,
//        SWEPT
//        /*
//            ONLINE,
//            AT_RISK,
//            LOGGED_OUT_MANUAL_SIGNOUT,
//            LOGGED_OUT_BROWSER_UNLOADED,
//            LOGGED_OUT_FAYE_DISCONNECT,
//            LOGGED_OUT_BIDDER_WITH_SAME_ID,
//            LOGGED_OUT_SWEPT
//        */
//    }



    var socket_state_label: String = OPENED.toString()
        private set

    var socket_state: ClientSocketState
        get() = socket_state_label.let { ClientSocketState.valueOf(it) }
        set(state) {
            this.socket_state_label = state.toString()
            when (state) {
                OPENED -> this.socket_last_closed = null
                CLOSED -> if (this.socket_last_closed == null)
                    this.socket_last_closed = DateTime().toDate()
            }
        }

    var socket_last_closed: Date? = null
        private set

    fun hasConnectionProblem(): Boolean =
        this.socket_state == CLOSED && !this.isTerminated() // TODO: Wifi Dropout detection via pings?

    var auction: Auction? = null
        private set

    //  @Index
//    var user_id: String? = null
//        private set

    // @Index
//    var company_id: String? = null
//        private set
//    var company_shortname: String? = null
//        private set
//    var company_longname: String? = null
//        private set

//    var role_label: String? = null
//        private set

    var user: Person? = null
        private set

//    var role: AuUserRole? = user?.role
//        get() = role_label?.let { AuUserRole.valueOf(it) }
//        set(r: AuUserRole?) {
//            this.role_label = r?.toString()
//        }


    fun is_trader(): Boolean = user?.isTrader() ?: false
    fun is_auctioneer(): Boolean = user?.isAuctioneer() ?: false

    // DO WE NEED THIS?
    fun inRole(vararg roles: AuUserRole): Boolean = roles.any { it == user?.role }


    var page_label: String = PageName.LOGIN_PAGE.toString()
        private set

    var page: PageName // mustn't be null
        get () = toEnumOrError<PageName>(page_label)
//        get() = enumValueOfWithDefault(page_label, PageName.LOGIN_PAGE)
        set(value) {
            this.page_label = value.toString()
        }

    fun set_page(p: PageName, a: Auction? = null) {
        // if you move off the auction page, then the auction is set to null, this is a change
        page = p
        auction = a
    }

    fun login(p: Person) {
        set_page(PageName.HOME_PAGE)
        user = p
//        if(user?.isTrader() == true && user?.company == null )
//            throw AlertException("all traders must have a company")
    }

// OLD: NOTE if a person changes company, while logged then MUST call this !!
//    fun set_user(p: Person) {
//        user = p
//        role = p.role
//        username = p.username
//        user_id = p.id_str()
//        company_id = p.company?.id_str()
//        company_shortname = p.company?.shortname
//        company_longname = p.company?.longname
//}


    fun is_logged_in(): Boolean = !isTerminated() && user != null

    // NOT NEEDED
    // - when auction deleted, session bounced to homepage
    // - when homepage is set, then auction will be set to null
//    fun remove_auction() {
//        // ie: if auction deleted, or if removed from auction
//        auction = null
//    }

    var last_ping: Date = DateTime().toDate()
        private set

    var termination_time: Date? = null
        private set

    var termination_reason_label: String? = null
        private set

    fun terminate(tr: SessionTerminationReason) {
        auction = null
        termination_time = DateTime().toDate()
        termination_reason_label = tr.toString()
        // TODO: not sure about adding the last two:
        socket_state = CLOSED
        socket_last_closed = DateTime().toDate()
    }

    // for some reason kotlin not happy with nullable enums!
    fun termination_reason(): SessionTerminationReason? =
        termination_reason_label?.let { SessionTerminationReason.valueOf(it) }

    fun isTerminated(): Boolean = termination_reason() != null

    fun ping() {
        last_ping = DateTime().toDate()
    }


}
