// File: AuctionRowElement.kt
package au21.engine.domain.common.viewmodel

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Auction
import au21.engine.framework.client.StoreElement
import kotlinx.serialization.Serializable

class AuctionRowTraders(val element: AuctionRowElement)

data class AuctionRowElement(
    override val id: String,
    val auction_design: String,
    val auction_id: String,
    val auction_name: String,
    val isClosed: <PERSON>olean,
    val common_state_text: String,
    val isHidden: Boolean,
    val starting_time_text: String,
    // used:
    // - on home page by both traders and auctioneers
    // - on trader page's header
) : StoreElement {


    companion object {
        fun create(a: Auction): AuctionRowElement =
            AuctionRowElement(
                id = a.id_str(),
                auction_design = a::class.java.simpleName,
                auction_id = a.id_str(),
                auction_name = a.auction_name,
                isClosed = a.closed,
                isHidden = a.hidden,
                starting_time_text = a.starting_time_text(),
                common_state_text = a.common_state_text,
            )

        // AUCTIONEERS: elements
//        fun all_elements(db: AuEntityManager, is_auctioneer: <PERSON><PERSON><PERSON>): List<AuctionRowElement> =
//            db.findAll<Auction>()
//                .map { AuctionRowElement(it, is_auctioneer) }


        // NON_AUCTIONEERS:

        // all the auction rows for one person:
        fun elements_for_session(
            auctions: List<Auction>,
            s: AuSession?
        ): List<AuctionRowElement> =
            auctions
                .filter { a -> a.show_auction(s) }
                .map { create(it) } //, s?.inRole(AuUserRole.AUCTIONEER) == true) }

    }
}
