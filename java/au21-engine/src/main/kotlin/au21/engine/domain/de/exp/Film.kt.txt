package au21.engine.domain.de.exp

import au21.engine.framework.database.AuEntity
import java.time.LocalDate
import javax.persistence.Entity

@Entity
open class Film: AuEntity() {
    var title: String? = null
    var episodeID: Int? = null
    var director: String? = null
    var releaseDate: LocalDate? = null
}

@Entity
open class Hero: AuEntity() {
    var name: String? = null
    var surname: String? = null
    var height: Double? = null
    var mass: Int? = null
    var darkSide: Boolean? = null
    var lightSaber: LightSaber? = null
    var episodeIds: MutableList<Int> = mutableListOf()
}

enum class LightSaber {
    RED, BLUE, GREEN
}


