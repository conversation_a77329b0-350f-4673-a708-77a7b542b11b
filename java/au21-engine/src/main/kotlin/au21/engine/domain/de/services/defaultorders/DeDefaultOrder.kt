// File: DeDefaultOrder.kt
package au21.engine.domain.de.services.defaultorders

import au21.engine.domain.common.model.OrderSubmissionType
import au21.engine.domain.common.model.OrderType
import au21.engine.domain.de.model.DeBidConstraints
import au21.engine.framework.commands.AlertException

class DeOrderInfo(
    val submission_type: OrderSubmissionType,
    val order_type: OrderType,
    val quantity: Int
) {
    companion object {

        /**
         * NB: must be run AFTER next round constraints are calculated!
         * - is also used for manual bid creation
         */

        fun create_default_from_constraints(
            constraints: DeBidConstraints
        ): DeOrderInfo {

            constraints.apply {
                if (max_buy_quantity == 0 && max_sell_quantity == 0) {
                    return DeOrderInfo(
                        OrderSubmissionType.MANDATORY,
                        OrderType.NONE,
                        0
                    )
                } else if (min_buy_quantity == 0 && min_sell_quantity == 0) {
                    return DeOrderInfo(
                        OrderSubmissionType.DEFAULT,
                        OrderType.NONE,
                        0
                    )
                } else if (min_buy_quantity > 0) {
                    return if (max_buy_quantity == min_buy_quantity) {
                        DeOrderInfo(
                            OrderSubmissionType.MANDATORY,
                            OrderType.BUY,
                            min_buy_quantity
                        )
                    } else {
                        DeOrderInfo(
                            OrderSubmissionType.DEFAULT,
                            OrderType.BUY,
                            min_buy_quantity
                        )
                    }
                }
                else if (min_sell_quantity > 0) {
                    return if (max_sell_quantity == min_sell_quantity) {
                        DeOrderInfo(
                            OrderSubmissionType.MANDATORY,
                            OrderType.SELL,
                            min_sell_quantity
                        )
                    } else {
                        DeOrderInfo(
                            OrderSubmissionType.DEFAULT,
                            OrderType.SELL,
                            min_sell_quantity
                        )
                    }
                }
                else {
                    // TODO: is this even possible ??
                    throw AlertException("Unable to calculate default order!")
                }
            }

        }
    }
}
