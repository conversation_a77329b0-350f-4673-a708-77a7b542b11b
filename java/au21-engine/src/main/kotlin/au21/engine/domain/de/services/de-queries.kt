// File: de-queries.kt
package au21.engine.domain.de.services

import au21.engine.domain.de.model.DeAuction
import au21.engine.framework.database.AuEntityManager
import io.quarkus.logging.Log

fun AuEntityManager.open_de_auctions(): List<DeAuction> =
    try {
        val query = "SELECT de FROM ${DeAuction::class.java.name} de where de.closed=false"
        em.createQuery(query, DeAuction::class.java).resultList
    } catch (e: Exception) {
        Log.info(e.message)
        // Log.error(e.message)
        // e.printStackTrace()
        listOf()
    }
