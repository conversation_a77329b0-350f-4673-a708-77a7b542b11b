// File: DeInitialLimits.kt
package au21.engine.domain.de.model

import au21.engine.framework.utils.AuFormatter
import au21.engine.framework.utils.thousands
import javax.persistence.Embeddable

/*
  ie: limits before the auction starts, ie: ignoring round price
  - round constraints will be checked against this.
 */
@Embeddable
class DeInitialLimits {
    // FOR NOW JUST USING BUYING COST LIMIT AND SELLING QUANTITY LIMIT
    var initial_buying_cost_limit = 50_000_000.0
        set(value) {
            field = value
            initial_buying_cost_limit_str = AuFormatter.format_currency(field)
        }

    // removing for now, not strictly needed, might add later
//    var initial_selling_cost_limit = 0.0
//        set(value) {
//            field = value
//            initial_selling_cost_limit_str = AuFormatter.format_currency(field)
//        }
    // removing for now, not strictly needed, might add later
//    var initial_buying_quantity_limit = 0
//        set(value) {
//            field = value
//            initial_buying_quantity_limit_str = field.thousands()
//        }

    var initial_selling_quantity_limit = 50
        set(value) {
            field = value
            initial_selling_quantity_limit_str = field.thousands()
        }


    var initial_buying_cost_limit_str:String = ""
   // var initial_selling_cost_limit_str:String = ""
   // var initial_buying_quantity_limit_str:String = ""
    var initial_selling_quantity_limit_str:String = ""

}
