package au21.engine.domain.de.viewmodel.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.de.model.DeAuction
import ClientCommand
import au21.engine.framework.database.AuEntityManager

object DeAuctionClient {

    fun for_session(db: AuEntityManager, s: AuSession, de: DeAuction): List<ClientCommand.StoreCommand> {

        TODO()
    }

    fun reset_round(db: AuEntityManager, s: AuSession, de: DeAuction): List<ClientCommand.StoreCommand> {

        TODO()
    }
}
