// File: DeRoundTraderInfo.kt
package au21.engine.domain.de.model

import au21.engine.domain.common.model.OrderSubmissionType
import au21.engine.domain.common.model.OrderType
import javax.persistence.Embeddable

@Embeddable
class DeRoundTraderInfo(
    constraints_: DeBidConstraints,
    trading_company_: DeTradingCompany,
    var default_order: <PERSON><PERSON><PERSON><PERSON>,
    var current_matched_vol: Int,
    var fully_opposed_match_vol: Int
) {

    var de_trading_company: DeTradingCompany = trading_company_
        private set

//        var company_id: String = trader.company_id
//            private set

    var constraints: DeBidConstraints = constraints_
        private set

    var order: DeOrder = default_order
//            set(o: Order) {
//                field = o
////                if (o.submission_type == OrderSubmissionType.MANDATORY)
////                    has_bid_explicitly = true
//            }

    var bid_while_closed: Boolean = false

    fun has_non_default_bid(): Boolean =
        when (order.submission_type) {
            OrderSubmissionType.DEFAULT -> false
            OrderSubmissionType.MANUAL -> true
            OrderSubmissionType.MANDATORY -> true
        }

    fun has_non_zero_bid(): Boolean =
        order.type != OrderType.NONE ||
                order.quantity != 0 // not sure this can happen

}
