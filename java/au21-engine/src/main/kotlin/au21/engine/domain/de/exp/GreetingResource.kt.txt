package au21.engine.domain.de.exp

// import javax.ws.rs.core.Context

import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.Produces
import jakarta.ws.rs.core.MediaType
import org.eclipse.microprofile.config.inject.ConfigProperty


@Path("/hello")
class GreetingResource {

    @ConfigProperty(name = "OBJECTDB_ACTIVATION_CODE")
    lateinit var objectdbActivationCode:String

    @GET
    @Path("odb")
    fun objectdbActivationCode() = objectdbActivationCode

    @GET
    @Produces(MediaType.TEXT_PLAIN)
    fun hello() = "Hello RESTEasy"

    @GET
    @Path("/dave")
    @Produces(MediaType.TEXT_PLAIN)
    fun dave():String{
        return "dave"
    }
}


// inputs: (inputs have to have no-arg constructors)
class SaveUser {
    var username: String = ""
}

class SaveCompany {
    var shortName: String = ""
    var longName: String = ""
}


