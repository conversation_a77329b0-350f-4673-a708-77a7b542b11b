@Transactional
public Student getStudent(Long id) {
        CriteriaBuilder  criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Student> criteriaQuery = criteriaBuilder.createQuery(Student.class);
        Root<Student> studentRoot = criteriaQuery.from(Student.class);
        criteriaQuery.select(studentRoot);
        criteriaQuery.where(criteriaBuilder.equal(studentRoot.get("id"), id ));
        TypedQuery<Student> typedQuery = entityManager.createQuery(criteriaQuery);
        List<Student> studentList = typedQuery.getResultList();
        return studentList.get(0);
        }
