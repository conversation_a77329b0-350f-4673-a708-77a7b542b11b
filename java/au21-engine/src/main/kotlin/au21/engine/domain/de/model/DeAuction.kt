// File: DeAuction.kt
package au21.engine.domain.de.model

import au21.engine.domain.common.model.*
import au21.engine.domain.de.services.constraints.calculate_first_round_constraints
import au21.engine.domain.de.services.defaultorders.DeOrderInfo
import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.AuFormatter
import au21.engine.framework.utils.TimeFormatter
import org.joda.time.DateTime
import java.util.*
import javax.persistence.Entity

@Entity
class DeAuction(
    auction_name: String,
    var settings: DeAuctionSettings
) : Auction(auction_name) {

    /**
     *   SEE BOTTOM OF CLASS FOR INITIALIZER (setState)
     */

//    @Index
//    var ticking: Boolean = state.in_state(DeState.ROUND_RUNNING)

    private var auctioneer_state_str: String = DeAuctioneerState.STARTING_PRICE_NOT_SET.toString()
    var auctioneer_state: DeAuctioneerState
        get() = DeAuctioneerState.valueOf(auctioneer_state_str)
        private set(s) {
            auctioneer_state_str = s.toString()
        }

    private var common_state_str: String = DeCommonState.SETUP.toString()
    var common_state: DeCommonState
        get() = common_state_str.enumValueOfWithTrace()
        private set(v) {
            common_state_str = v.toString()
        }

    override fun starting_time_text(): String =
        when (common_state) {
            DeCommonState.SETUP,
            DeCommonState.STARTING_PRICE_ANNOUNCED ->
                starting_time?.let { d: Date ->
                    val starting_time_text = "${AuFormatter.auction_row_date_formatter.format(d)} at " +
                            "${AuFormatter.auction_row_time_formatter.format(d)} "

                    val time_to =
                        TimeFormatter.formatDuration(d).let {
                            // TODO: not sure what this is doing??
                            when (it) {
                                "now" -> "(starting now)"
                                else -> "($it from now)"
                            }
                        }

                    return "$starting_time_text $time_to"
                } ?: ""

            DeCommonState.ROUND_OPEN, DeCommonState.ROUND_CLOSED -> "Auction started"
            DeCommonState.AUCTION_CLOSED -> "Auction closed"
        }


//    var round_state_label: String = DeRoundState.NOT_OPEN.toString()
//        private set
//    var round_state: DeRoundState
//        get() = DeRoundState.valueOf(round_state_label)
//        private set(value) {
//            this.round_state_label = value.toString()
//        }

    fun lastround(): DeRound = rounds.lastOrNull() ?: throw AlertException("Auction must have at least on round.")
    fun firstround(): DeRound = rounds.firstOrNull() ?: throw AlertException("Auction must have at least on round.")

    // TODO: should this be pre-calculated ? Probably some value in leaving it as is so that it can be set by server time
    // TOD: probably want to do the same with starting time, btw
    fun announce_time(): Date? = starting_time?.let { d: Date ->
        DateTime(d)
            .minusMinutes(settings.starting_price_announcement_mins)
            .toDate()
    }

    fun setState(state: DeAuctioneerState) {
        // NB: because last round can't be null, we can't call this before the first round is created!

        val n = lastround()
        closed = state == DeAuctioneerState.AUCTION_CLOSED
        auctioneer_state = state
        auctioneer_state_text = when (state) {
            DeAuctioneerState.STARTING_PRICE_NOT_SET -> "Starting price not set"
            DeAuctioneerState.STARTING_PRICE_SET -> "Starting price set"
            DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> "Starting price announced"
            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN ->
                "Round ${n.number} open, all orders not in." + if (price_has_overshot()) " Price has overshoot" else ""

            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN ->
                "Round ${n.number} open, all orders in." + if (price_has_overshot()) " Price has overshoot" else ""

            DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE ->
                "Round ${n.number} closed, auction not awardable." + if (price_has_overshot()) " Price has overshoot" else ""

            DeAuctioneerState.ROUND_CLOSED_AWARDABLE ->
                "Round ${n.number} closed, auction awardable."

            DeAuctioneerState.AUCTION_CLOSED -> "Auction closed"
        }

        common_state = when (state) {
            DeAuctioneerState.STARTING_PRICE_NOT_SET -> DeCommonState.SETUP
            DeAuctioneerState.STARTING_PRICE_SET -> DeCommonState.SETUP
            DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> DeCommonState.STARTING_PRICE_ANNOUNCED
            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> DeCommonState.ROUND_OPEN
            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> DeCommonState.ROUND_OPEN
            DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> DeCommonState.ROUND_CLOSED
            DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> DeCommonState.ROUND_CLOSED
            DeAuctioneerState.AUCTION_CLOSED -> DeCommonState.AUCTION_CLOSED
        }

        common_state_text = when (common_state) {
            DeCommonState.SETUP -> "Waiting for starting price"
            DeCommonState.STARTING_PRICE_ANNOUNCED -> "Starting price announced"
            DeCommonState.ROUND_OPEN -> "Round ${n.number} open for orders!"
            DeCommonState.ROUND_CLOSED -> "Round ${n.number} closed"
            DeCommonState.AUCTION_CLOSED -> "Auction closed"
        }
    }

    fun starting_price_announced(): Boolean = !auctioneer_state.oneOf(
        DeAuctioneerState.STARTING_PRICE_NOT_SET,
        DeAuctioneerState.STARTING_PRICE_SET
    )

    var autopilot_label: String = AutopilotMode.DISENGAGED.toString()
        private set
    var autopilot: AutopilotMode
        get() = AutopilotMode.valueOf(autopilot_label)
        set(value) {
            this.autopilot_label = value.toString()
        }


    // TODO: set in parent
    fun auction_has_started(): Boolean =
        last_round_has_started() || this.rounds.size > 1


    fun last_round_has_started(): Boolean =
        this.auctioneer_state.oneOf(
            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN,
            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN,
            DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE,
            DeAuctioneerState.ROUND_CLOSED_AWARDABLE,
            DeAuctioneerState.AUCTION_CLOSED
        )

    fun create_trader(c: Company): DeTradingCompany {
        // TODO: make sure not already added!
        // note: for tests make sure you have an id!

        if (!is_before_end_of_first_round())
            throw AlertException("Cannot add traders after the first round is closed.")

        if (trading_companies.any { it.company_id == c.id })
            throw Error("Trader already exists with name: ${c.shortname}")

        val t = DeTradingCompany(c).also {
            trading_companies.add(it)
        }

        create_first_round_rti(t)

        return t
    }

    // ASSUMES THAT TRADER DOESN'T HAVE A NON_ZERO BID:
    fun remove_trader(c: Company) {
        de_trading_companies.removeIf { it.company_id == c.id }
        rounds.forEach { r: DeRound ->
            r.trader_infos.removeIf { it.de_trading_company.company_id == c.id }
        }
        trading_companies.removeIf { it.company_id == c.id }
    }


    /**
     *  use this for:
     *  1) traders added
     *  2) starting price set:
     */

    private fun create_first_round_rti(t: DeTradingCompany) {
        // called:
        // a) when adding a trader to an auction
        // b) when editing the trader's initial constraints

        val n = lastround()

        if (n.number != 1)
            throw AlertException("Cannot create first round infos after the first round.")

        // remove if existing:

        n.apply {
            get_rti(t)?.let {
                trader_infos.remove(it)
            }
        }


        // 3: create rti with constraints, and default order
        val constraints = calculate_first_round_constraints(t,  settings, n)
        val default_order = DeOrderInfo.create_default_from_constraints(constraints)

        val rti = DeRoundTraderInfo(
            trading_company_ = t,
            constraints_ = constraints,
            default_order = DeOrder(
                round = n,
                trading_company_ = t,
                u = null,
                order_info = default_order,
                cost_multiplier = settings.cost_multiplier,
                prev_order = null,
            ),
            current_matched_vol = 0,
            fully_opposed_match_vol = 0
        )

        n.trader_infos.add(rti)
    }

    fun set_starting_price(price: Double) {
        if (lastround().number != 1)
            throw AlertException("Cannot create first round infos after the first round.")

        firstround().price = price

        // need to re-create the first round rti's with the new price!
        de_trading_companies.forEach { create_first_round_rti(it) }
    }

//    var time_remaining: Int = settings.first_round_duration
//        private set

//    fun set_time_remaining() {
//        time_remaining = when (lastround().number) {
//            1 -> settings.first_round_duration
//            else -> settings.following_round_duration
//        }
//    }

//    fun decrement_time_remaining() {
//        time_remaining--
//    }

    var price_decimal_places: Int = 3
    var awarded_round: DeRound? = null

    var revised_orders: MutableList<DeOrder> = mutableListOf()
        private set

    var rounds: MutableList<DeRound> = mutableListOf()
        private set

    // TODO: probably a better way of checking this?
    @Suppress("UNCHECKED_CAST")
    var de_trading_companies: MutableList<DeTradingCompany> = mutableListOf()
        get() = trading_companies as MutableList<DeTradingCompany>
        private set


//    fun reset_round() {
//        // TODO: what if there is no last round?
//        lastround()?.let { n ->
//            n.orders.clear()
//            n.trader_infos.clear()
//            traders.forEach { create_rti(n, it) }
//            //set_time_remaining()
//        }
//    }

    fun set_rank(t: DeTradingCompany) {
        // set rank if none:
        if (t.rank == null) {
            // first will be 1
            t.rank = 1 + de_trading_companies.filter { it.rank != null }.size
        }
    }

    // TODO: MOVE THIS OUT OF HERE
    fun create_manual_order(
        r: DeRound,
        t: DeTradingCompany,
        u: PersonProxy,
        order_type: OrderType,
        order_quantity: Int
    ): DeOrder {
        val rti = r.get_rti(t)
            ?: throw AlertException("expected rti for trader: ${t.shortname_at_auction_time}")

//        rti.has_bid_explicitly = true

        set_rank(t) // TODO: do we still need this?

        return DeOrder(
            round = r,
            trading_company_ = t,
            u = u,
            order_info = DeOrderInfo(
                OrderSubmissionType.MANUAL,
                order_type,
                order_quantity
            ),
            cost_multiplier = settings.cost_multiplier,
            prev_order = rti.order
        ).also {
            rti.order = it
        }
    }

    // Do it from
    fun set_credit() {
        // TODO
//        de_trading_companies.forEach { dCompany ->
//            val borrowerCreditLimits = counterparty_credit_limits
//                .asSequence()
//                .filter { it.borrower.company_id == dCompany.company_id }
//                .toList()
//            val credit_limit = borrowerCreditLimits.minByOrNull { it.credit_limit ?: 0.0 }?.credit_limit ?: 0.0
//            dCompany.initial_buying_cost_limit = credit_limit
//        }
    }

    init {
        rounds.add(DeRound(number = 1, price = null, has_reversed_ = false, direction = null)) // no direction
        setState(DeAuctioneerState.STARTING_PRICE_NOT_SET)
    }

}


/*
    Convention: June 24, 2019:
    - collections are only on the root DeAuction // OLD
    - other components embed var constructor pointers
    - lookup is therefore by the root
    - creation is checked by the root
    - extensions are only on the root
    - separate View reports will be used to create json

    Convention: Jan 28, 2021
    - using pattern more convenient for potential move to RavenDB, Documents (Aggregates) should be:
        - Independent:
          - have a separate identity (ie: an Entity)
        - Isolated:
          - a document is changed in a transaction, independent from other documents!!
        - Coherent:
          - a document should be legible on it's own.
    - therefore trying to make sure that documents don't reference other documents.
    - so, for exampl, Traders, will be referenced by short name, and users by username
    - this means that trader names cannot be changed for open auctions, etc. !!

 */


// 1) State: note: the state is set via the enum, but as a string

//     var state : IAuctionState
//        get() = enumvalueOf(state_label) // enumvarueOrNull<TeAuctionState>(state_string)!!
//        set(varue) {
//            state_label = varue.toString()
//        }
//
//    inline fun <reified T> getState(): T
//            where
//            T : Enum<T>,
//            T : IAuctionState = enumvalueOf(state_string)
//
//    inline fun <reified T> inState(vararg states: T): Boolean
//            where
//            T : Enum<T>,
//            T : IAuctionState = states.contains(getState())
//
//    fun <T> setState(state: T) where
//            T : Enum<T>,
//            T : IAuctionState {
//        this.state_string = state.toString()
//    }
//
//    fun setState(state: DeState) {
//        super.setState(state)
//        closed = state.in_state(DeState.AUCTION_CLOSED)
//        ticking = state.in_state(DeState.ROUND_RUNNING) // I think this is the only one?
//    }
