package au2019.engine.components.common.results.auction

import app.framework.Value_Payload
import app.model.Auction
import groovy.transform.CompileStatic
import groovy.transform.ToString

@CompileStatic
@ToString (includeNames = true, includePackage = false)
class On_Help extends Value_Payload {
  String AUCTION_ID
  String HELP_HTML

  On_Help (Auction a ) {
    AUCTION_ID = a.id
    HELP_HTML = a.help
  }
}
