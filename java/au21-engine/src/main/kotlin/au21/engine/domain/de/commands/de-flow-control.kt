// File: de-flow-control.kt
package au21.engine.domain.de.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeFlowControlType
import au21.engine.domain.de.model.DeFlowControlType.SET_STARTING_PRICE
import au21.engine.domain.de.services.state.DeControlValidator
import au21.engine.domain.de.services.state.DeControlValidator.MUTATE
import au21.engine.domain.de.services.state.DeMutator
import au21.engine.framework.commands.*
import au21.engine.framework.database.AuEntityManager

class DeFlowControlCommand(
    val auction_id: String,
    val control: DeFlowControlType,
    val starting_price: String?
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val s = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(s)

        val de: DeAuction = db.auction_or_alert(auction_id)
        //val n = de.lastround()

        val starting_price_double: Double? = starting_price?.toDoubleOrNull()

//        val round_price_double: Double? = round_price?.let {
//            it.toDoubleOrNull()
//                ?: alert("Price is not a number.")
//        }

        return when (val result: String? = DeControlValidator.validate(de, control)) {
            null -> DeFlowControlAction(this, db, s, de, false, starting_price_double) // if
            MUTATE -> {
                fail_if(
                    control == SET_STARTING_PRICE && starting_price_double == null,
                    "Round price not valid: $starting_price"
                )
//                alert_if(
//                    de.last_round_has_started(),
//                    "Cannot change round price after the round has started!"
//                )
                DeFlowControlAction(this, db, s, de, true, starting_price_double)
            }
            else -> fail(result)
        }
    }
}


class DeFlowControlAction(
    override val command: DeFlowControlCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de: DeAuction,
    val is_mutatable: Boolean,
    var starting_price: Double?
) : EngineAction {
    override fun mutate() {

        if (is_mutatable) {

            DeMutator.mutate(de, command.control, starting_price)

            // have to reset volume limits:
            //de.set_counterparty_volume_limits( db.findAll())

            db.save(de)
        }
    }

}
