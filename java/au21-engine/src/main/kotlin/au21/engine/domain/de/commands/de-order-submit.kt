// File: de-order-submit.kt
package au21.engine.domain.de.commands


import au21.engine.domain.common.model.*
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.*
import au21.engine.domain.de.services.matcher.DeMatcher
import au21.engine.domain.de.validations.validate_de_order_against_constraints_and_throw_if_fails
import au21.engine.framework.PageName
import au21.engine.framework.commands.*
import au21.engine.framework.commands.interfaces.IAuctionMessage
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.thousands

class DeOrderSubmitCommand(
    val auction_id: String,
    val company_id: String,
    val order_type: OrderType,
    val round: String,
    val quantity: String
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if(
            company_id != session.user?.company?.id_str(),
            "Company id does not match session id."
        )

        fail_if(session.page != PageName.DE_TRADER_PAGE, "You can only submit orders from the auction page.")

        val de: DeAuction = db.auction_or_alert(auction_id)

        //TODO: The below 2 conditions is not required as this is impossible because of line number 28
        val c: Company = db.byId(company_id) ?: fail("no company found")

        // Assumes the all manual bidders have seen the auction !!
        val user_proxy = de.users_that_have_seen_auction.find { it.person_id == session.user?.id }
            ?: fail("You are not a trader in this auction.")

        val t: DeTradingCompany = de.de_trading_companies.firstOrNull{it.company_id == company_id.toLong()}
            ?: fail("You are not a trader in this auction.")

        fail_if(
            de.common_state
                    != DeCommonState.ROUND_OPEN, "Auction not open for bidding."
        )

        val n: DeRound = de.lastround()
        val round_int: Int = err_unless_Int_GT_zero(::round)
            ?: fail("Round should be greater than zero")

        fail_if(round_int != n.number, "Order isn't for round ${n.number}")

        val rbi = n.get_rti(t)
            ?: fail("No round bidder info")

        val quantity_i: Int = err_unless_Int_GE_zero(::quantity)
            ?: fail("quantity must be a whole number")

        val order_type_correct: OrderType = if (quantity_i == 0) OrderType.NONE else order_type

        // these will be set when the round is created!

        val constraints: DeBidConstraints = rbi.constraints


        val order_quantity_type_corrected: OrderType =
            when (quantity_i) {
                0 -> OrderType.NONE
                else -> order_type_correct
            }

        validate_de_order_against_constraints_and_throw_if_fails(
            constraints,
            order_type = order_quantity_type_corrected,
            order_quantity = quantity_i,
            quantity_units = de.settings.quantity_units,
        )

        val message = AuctionMessage(
            AuMessageType.SYSTEM_TO_TRADER,
            "${user_proxy.username_at_auction_time} submitted " + when (order_type) {
                OrderType.BUY -> "buy order for ${quantity_i.thousands()} ${de.settings.quantity_units}"
                OrderType.SELL -> "sell order for ${quantity_i.thousands()} ${de.settings.quantity_units}"
                OrderType.NONE -> "order for 0 ${de.settings.quantity_units}"
            },
            null,
            to_company_ = t,
        )

        return DeOrderSubmitAction(
            this,
            db,
            session,
            de,
            message,
            n,
            t,
            user_proxy,
            c,
            rbi,
            order_quantity_type_corrected,
            quantity_i
        )

    }

}


class DeOrderSubmitAction(
    override val command: DeOrderSubmitCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    override val auction: DeAuction,
    override val message: AuctionMessage,
    val n: DeRound,
    val trading_company: DeTradingCompany,
    val user_proxy: PersonProxy,
    val company: Company,
    val rbi: DeRoundTraderInfo,
    val order_type: OrderType,
    val quantity: Int
) : EngineAction, IAuctionMessage {

    lateinit var order: DeOrder

    override fun mutate() {

        order = auction.create_manual_order(
            r = n,
            t = trading_company,
            u = user_proxy,
            order_type = order_type,
            order_quantity = quantity
        )

        // NB: MUST be done before calcualting actuan_maxflow !!
        //auction.set_counterparty_quantity_limits()

        // matches:
        DeMatcher.calculate_and_set_matches(auction, this)


        // test if round closeable:
        if (n.all_orders_are_non_default()) {
            auction.setState(DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN)
        }

        auction.messages.add(message)
        db.save(auction)

    }

}
