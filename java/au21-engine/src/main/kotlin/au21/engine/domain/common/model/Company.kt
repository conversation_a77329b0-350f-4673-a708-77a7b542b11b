// File: Company.kt
package au21.engine.domain.common.model

import au21.engine.framework.commands.AlertException
import au21.engine.framework.database.AuEntity
import javax.persistence.Embeddable
import javax.persistence.Entity

// rules entites don't reference other entities, are coherent


@Embeddable
abstract class CompanyProxy(c:Company){
    var company_id:Long = c.id
        private set
    var shortname_at_auction_time:String = c.shortname
    var longname_at_auction_time:String = c.longname
}


@Entity
class Company(
    var longname: String,
    var shortname: String
) : AuEntity() {

    init {
        // NB: remember to trim all command inputs !!
        if (shortname.trim() == "")
            throw AlertException("Company short name cannot be blank.")

        if (shortname.trim().length > 10) {
            throw AlertException("Company short name cannot be greater than 10 characters: $shortname")
        }
    }


}
