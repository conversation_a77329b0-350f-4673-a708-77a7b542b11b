// File: de-auction-save.kt
package au21.engine.domain.de.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeAuctionSettings
import au21.engine.domain.de.model.DePriceRule
import au21.engine.domain.de.services.matcher.DeMatcher
import au21.engine.framework.commands.*
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.is_blank
import org.joda.time.DateTime
import java.util.*

class DeAuctionSaveCommand(
    val auction_id: String,
    val auction_name: String,
    val use_counterparty_credits:String,

    // val starting_time: String,
//    val first_round_duration: String,
//    val following_round_duration: String,
    // val trigger_interval: String,
    // val reporting_duration: String,

    val quantity_label: String,
    val quantity_minimum: String,
    val quantity_step: String,

    val price_change_initial: String,
    val price_change_post_reversal: String,
    val price_label: String,
    val price_decimal_places: String,
    //  val starting_price: String,
    val cost_multiplier: String,

    val excess_level_0_label: String,
    val excess_level_1_label: String,
    val excess_level_2_label: String,
    val excess_level_3_label: String,
    val excess_level_4_label: String,

    val excess_level_1_quantity: String,
    val excess_level_2_quantity: String,
    val excess_level_3_quantity: String,
    val excess_level_4_quantity: String,

    //   val starting_price: String?,
    val starting_price_announcement_mins: String,

    val month_is_1_based: Boolean,
    val starting_year: String,
    val starting_month: String,
    val starting_day: String,
    val starting_hour: String,
    val starting_mins: String,

    val round_red_secs: String,
    val round_orange_secs: String,
    val round_open_min_seconds: String,
    val round_closed_min_secs: String

) : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)

        val is_create: Boolean = auction_id.is_blank()

        err_if_blank(::auction_name)
        err_if_blank(::quantity_label)
        err_if_blank(::price_label)
        err_if_blank(::use_counterparty_credits)

        val use_counterparty_credits_b:Boolean? =
            when(use_counterparty_credits){
                "true" -> true
                "false" -> false
                else -> null
            }

        err_if_null(use_counterparty_credits_b, "Use credit limits cannot be blank.")

        //val starting_time = p.starting_time
//        val first_round_duration_i: Int? = err_unless_Int_GT_zero(::first_round_duration)
//        val following_round_duration_i: Int? = err_unless_Int_GT_zero(::first_round_duration)

        val quantity_minimum_i: Int? = err_unless_Int_GT_zero(::quantity_minimum)
        val quantity_step_i: Int? = err_unless_Int_GT_zero(::quantity_step)

        val price_decimal_places_i: Int? = err_unless_Int_GE_zero(::price_decimal_places)

        // kludge for now because it seems we don't always send cost_multiplier
        // - https://gitlab.com/auctionologies/frontend/au21-frontend/-/issues/67
        val cost_multiplier_d: Double? =
            if (cost_multiplier.is_blank())
                10000.0
            else
                err_unless_Double_GT_zero(::cost_multiplier)


        fun to_error(s: String) = "$s must be a number greater than zero."

        val price_change_initial_double: Double? =
            toDoubleOrError(::price_change_initial, ::is_GT_zero) { to_error("Initial price change") }
        val price_change_post_reversal_double: Double? =
            toDoubleOrError(::price_change_post_reversal, ::is_GT_zero) { to_error("Post reversal price change") }

        price_change_initial_double?.let { inital_price ->
            price_change_post_reversal_double?.let { post_reversal_price ->
                err_if(
                    post_reversal_price >= inital_price,
                    "Price change post reversal must be less than inital price."
                )
            }
        }

        err_if_blank(::excess_level_0_label, "Excess level 0 label")
        err_if_blank(::excess_level_1_label, "Excess level 1 label")
        err_if_blank(::excess_level_2_label, "Excess level 2 label")
        err_if_blank(::excess_level_3_label, "Excess level 3 label")
        err_if_blank(::excess_level_4_label, "Excess level 4 label")

        val excess_level_1_quantity_int: Int? =
            toIntOrError(::excess_level_1_quantity, ::is_GT_zero) { to_error("Excess level 1") }
        val excess_level_2_quantity_int: Int? =
            toIntOrError(::excess_level_2_quantity, ::is_GT_zero) { to_error("Excess level 2") }
        val excess_level_3_quantity_int: Int? =
            toIntOrError(::excess_level_3_quantity, ::is_GT_zero) { to_error("Excess level 3") }
        val excess_level_4_quantity_int: Int? =
            toIntOrError(::excess_level_4_quantity, ::is_GT_zero) { to_error("Excess level 4") }

        fail_if_errors()

        err_if(
            excess_level_4_quantity_int!! < excess_level_3_quantity_int!!,
            "Excess level 4 cannot be less than excess level 3"
        )
        err_if(
            excess_level_3_quantity_int < excess_level_2_quantity_int!!,
            "Excess level 3 cannot be less than excess level 2"
        )
        err_if(
            excess_level_2_quantity_int < excess_level_1_quantity_int!!,
            "Excess level 2 cannot be less than excess level 1"
        )

        fail_if_errors()

        // CURRENT STATE SETTINGS:

//        val starting_price_double: Double? = starting_price?.let { s: String ->
//            s.toDoubleOrNull()?.also { d: Double? ->
//                err_if_null(d, "Starting price not understood: $s")
//            }
//        }

        val starting_price_announcement_mins_int: Int = starting_price_announcement_mins.toIntOrNull() ?: 0

        // STARTING DATE/TIME:
        /*
            monthOfYear – the month of the year, from 1 to 12
            dayOfMonth – the day of the month, from 1 to 31
            hourOfDay – the hour of the day, from 0 to 23
            minuteOfHour – the minute of the hour, from 0 to 59
        */

        val starting_day_int: Int? = toIntOrError(::starting_day) { to_error("starting day") }
        val starting_hour_int: Int? = toIntOrError(::starting_hour) { to_error("starting hour") }
        val starting_mins_int: Int? = toIntOrError(::starting_mins) { to_error("starting minutes") }
        val starting_month_int_raw: Int? = toIntOrError(::starting_month) { to_error("starting month") }
        val starting_year_int: Int? = toIntOrError(::starting_year) { to_error("starting year") }

        val round_red_secs_int: Int? =
            toIntOrError(::round_red_secs, ::is_GT_zero) { to_error("Round red seconds") }

        val round_orange_secs_int: Int? =
            toIntOrError(::round_orange_secs, ::is_GT_zero) { to_error("Round orange seconds") }

        val round_open_min_seconds_int: Int? =
            toIntOrError(::round_open_min_seconds, ::is_GT_zero) { to_error("Round open minimum seconds") }

        val round_closed_min_secs_int: Int? =
            toIntOrError(::round_closed_min_secs, ::is_GT_zero) { to_error("Round closed seconds") }

        err_if(month_is_1_based && starting_month_int_raw == 0, "Starting month cannot be zero")

        fail_if_errors()

        val starting_month_int: Int = when (month_is_1_based) {
            true -> starting_month_int_raw!!
            false -> starting_month_int_raw!! + 1
        }

        val starting_date_time: Date = DateTime(
            starting_year_int!!,
            starting_month_int,
            starting_day_int!!,
            starting_hour_int!!,
            starting_mins_int!!
        ).toDate()

        // TODO: removing this for now, ie: don't need it without autopilot:
        //    err_if(starting_date_time.before(Date()), "Cannot start an auction in the past")

        fail_if_errors()

        val settings = DeAuctionSettings(
            use_counterparty_credits = use_counterparty_credits_b!!,

            round_open_min_secs = round_open_min_seconds_int!!,
            round_closed_min_secs = round_closed_min_secs_int!!,
            round_orange_secs = round_orange_secs_int!!,
            round_red_secs = round_red_secs_int!!,
            starting_price_announcement_mins = starting_price_announcement_mins_int,

            quantity_units = quantity_label,
            quantity_minimum = quantity_minimum_i!!,
            quantity_step = quantity_step_i!!,
            price_units = price_label,
            price_decimal_places = price_decimal_places_i!!,
            cost_multiplier = cost_multiplier_d!!,
            price_rule = DePriceRule(
                price_change_initial = price_change_initial_double!!,
                price_change_post_reversal = price_change_post_reversal_double!!,
                excess_level_1_quantity = excess_level_1_quantity_int,
                excess_level_2_quantity = excess_level_2_quantity_int,
                excess_level_3_quantity = excess_level_3_quantity_int,
                excess_level_4_quantity = excess_level_4_quantity_int,
                excess_level_0_label = excess_level_0_label,
                excess_level_1_label = excess_level_1_label,
                excess_level_2_label = excess_level_2_label,
                excess_level_3_label = excess_level_3_label,
                excess_level_4_label = excess_level_4_label
            ),
        )

        val de: DeAuction = when {

            is_create -> DeAuction(
                auction_name = auction_name,
                settings = settings
            )
            else -> db.auction_or_alert<DeAuction>(auction_id).also {
//                // moved to de-flow-commands.kt
//                if (it.has_started()) {
//                    fail_if(
//                        it.settings.starting_price != starting_price_double,
//                        "Cannot change starting price after the auction has started!"
//                    )
//                }
//                it.auction_name = auction_name
                it.settings = settings
            }
        }

        de.starting_time = starting_date_time

        return DeAuctionSaveAction(this, db, session, de, is_create)
    }

}


class DeAuctionSaveAction(
    override val command: DeAuctionSaveCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de: DeAuction,
    val is_create: Boolean
) : EngineAction {
    override fun mutate() {
        db.save(de)
        // have to reset volume limits if we have a last round (it will return if not):
        DeMatcher.calculate_and_set_matches(de, this)
        db.save(session)
    }


}
