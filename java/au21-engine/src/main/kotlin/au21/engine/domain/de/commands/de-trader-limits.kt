// File: de-trader-limits.kt
package au21.engine.domain.de.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeRoundTraderInfo
import au21.engine.domain.de.model.DeTradingCompany
import au21.engine.domain.de.model.get_rti
import au21.engine.framework.commands.*
import au21.engine.framework.database.AuEntityManager

//TODO: Need to implement
class DeTraderLimitsCommand(
    val auction_id: String,
    val company_id: String,
    val selling_quantity_limit: String,
//    val selling_cost_limit: String,
//    val buying_quantity_limit: String,
    val buying_cost_limit: String
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        val session = db.session_non_terminated_or_alert(session_id)
        val de: DeAuction =
            db.byId<DeAuction>(auction_id) ?: throw AlertException("Cannot find auction without an id: $auction_id")

        // TODO: FOR NOW PREVENTING CHANGES AFTER AUCTION STARTED.
        fail_if(de.auction_has_started, "Cannot change trader limits after auction has started")
        // - though we might need to allow changes to the limits after the auction has started.
        // - in which case we need to check that the limits are not being reduced if a higher big has already been placed.
        // - BTW, Github Copilot wrote those notes above"

        val company_id_long:Long = err_unless_Long_GT_zero(::company_id)
            ?: throw AlertException("Invalid company id: $company_id")

        val trader = de.de_trading_companies.firstOrNull { it.company_id == company_id_long }
            ?: throw AlertException("Trader not in auction")

        val rti:DeRoundTraderInfo = de.firstround().get_rti(trader)
            ?: throw AlertException("Round trader info not found for trader: ${trader.shortname_at_auction_time}")

     //   val buyer_quantity_limit_i: Int = err_unless_Int_GE_zero(::buying_quantity_limit) ?: 0
        val selling_quantity_limit_i: Int = err_unless_Int_GE_zero(::selling_quantity_limit) ?: 0

        val buying_cost_limit_d: Double = err_unless_Double_GE_zero(::buying_cost_limit) ?: 0.0
    //    val selling_cost_limit_d: Double = err_unless_Double_GE_zero(::selling_cost_limit) ?: 0.0

        fail_if_errors()

        return DeSetTraderLimitsAction(
            command = this,
            db = db,
            session = session,
            de = de,
            trader = trader,
            rti = rti,
            selling_quantity_limit = selling_quantity_limit_i,
//            selling_cost_limit = selling_cost_limit_d,
//            buying_quantity_limit = buyer_quantity_limit_i,
            buying_cost_limit = buying_cost_limit_d
        )
    }
}

class DeSetTraderLimitsAction(
    override val command: DeTraderLimitsCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de: DeAuction,
    val trader: DeTradingCompany,
    val rti: DeRoundTraderInfo,
    val selling_quantity_limit: Int,
//    val selling_cost_limit: Double, // not used yet
//    val buying_quantity_limit: Int, // not used yet
    val buying_cost_limit: Double
) : EngineAction {
    override fun mutate() {
        trader.initial_limits.also {
            it.initial_selling_quantity_limit = selling_quantity_limit
         //   it.initial_selling_cost_limit = selling_cost_limit
         //   it.initial_buying_quantity_limit = buying_quantity_limit
            it.initial_buying_cost_limit = buying_cost_limit
        }
        de.set_credit()
        db.save(de)
    }

}
