// File: DeAuctionSettings.kt
package au21.engine.domain.de.model

import javax.persistence.Embeddable

@Embeddable
class DeAuctionSettings(

    var use_counterparty_credits: Boolean,

    // these must'nt be null:
    // var starting_time: Date,
    var starting_price_announcement_mins: Int = 0,

    var round_open_min_secs: Int,
    var round_closed_min_secs: Int,
    var round_orange_secs: Int,
    var round_red_secs: Int,

//  var show_current_round_in_history: Boolean = false, // so that BWP can have it the way they like it
    //  var first_round_duration: Int,
    //  var following_round_duration: Int,

    var cost_multiplier: Double = 10_000.0, // ie: currency = price_units x volume_units x currency_conversion_rateø

    // VOLUME:
    var quantity_units: String = "MMlb",
    var quantity_minimum: Int = 1,
    var quantity_step: Int = 1,

    // PRICE:
    var price_units: String = "cpp",
    var price_decimal_places: Int = 3,

    // var starting_price: Double, // probably not needed, can set on first round
    var price_rule: DePriceRule
)
