// File: SessionUserValue.kt
package au21.engine.domain.common.viewmodel

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.framework.PageName
import au21.engine.framework.client.StoreValue


data class SessionUserValue(
    val company_id: String,
    val company_shortname: String,
    val company_longname: String,
    val current_auction_id: String,
    val current_page: PageName,
    val isAuctioneer: <PERSON><PERSON><PERSON>,
    val isOnline: <PERSON><PERSON><PERSON>,
    val role: AuUserRole?,
    val session_id: String,
    val socket_state: AuSession.ClientSocketState,
    val user_id: String,
    val username: String,
) : StoreValue {

    companion object {
        fun create(s: AuSession): SessionUserValue =
            SessionUserValue(
                company_id = s.user?.company?.id_str() ?: "",
                company_shortname = s.user?.company?.shortname ?: "",
                company_longname = s.user?.company?.longname ?: "",
                current_auction_id = s.auction?.id_str() ?: "",
                current_page = s.page,
                isAuctioneer = s.inRole(AuUserRole.AUCTIONEER),
                isOnline = !s.isTerminated(),
                role = s.user?.role,
                session_id = s.session_id,
                socket_state = s.socket_state,
                user_id = s.user?.id_str() ?: "",
                username = s.user?.username ?: ""
            )
    }
}

