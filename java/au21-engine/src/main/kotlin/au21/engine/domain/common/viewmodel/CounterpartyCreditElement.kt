// File: CounterpartyCreditElement.kt
package au21.engine.domain.common.viewmodel

import au21.engine.domain.common.model.Company
import au21.engine.framework.client.StoreElement
import au21.engine.framework.database.is_same_entity

data class CounterpartyCreditElement(
    override val id: String,
    val seller_id: String,
    val seller_longname: String,
    val seller_shortname: String,
    val buyer_id: String,
    val buyer_longname: String,
    val buyer_shortname: String,
    //  val limit: Double?,
    val limit_str: String
) : StoreElement {

    // NOTE: removing long names and shortnames from this class
    // - means that we need to send companies to traders too.
    // - however, if we ever want to add sensitive info to CompanyElement (ie: other than name)
    //  - and/or if we want to limit which counterparties get sent to traders, then that could be a problem.

    companion object {

        fun create(
           // auction: Auction,
            seller: Company,
            buyer: Company
        ): CounterpartyCreditElement {

            //  val limit = seller.get_credit_limit(buyer)

            return CounterpartyCreditElement(
                id = id_str(seller, buyer),
                seller_id = seller.id_str(),
                seller_longname = seller.longname,
                seller_shortname = seller.shortname,
                buyer_id = buyer.id_str(),
                buyer_longname = buyer.longname,
                buyer_shortname = buyer.shortname,
                //   limit = limit,
                limit_str = "TODO" // auction.get_credit_limit(seller, buyer)?.buyer_credit_limit_str ?: CounterpartyCreditLimit.no_limit
            )
        }

        fun id_str(creditor: Company, debtor: Company) =
            "SELLER.${creditor.id_str()}.BUYER.${debtor.id_str()}"

//        fun for_session(db:AuSession, s: AuSession, reset:Boolean) = List<ClientCommand.StoreCommand> =
//            listOf()

        fun all(companies: List<Company>): List<CounterpartyCreditElement> =
            companies.map { seller: Company ->
                // TODO: not sure if we should be filtering?
                companies.filterNot { is_same_entity(it, seller) }
                    .map { buyer: Company ->
                        create(seller, buyer)
                    }
            }.flatten()

    }

}

//    fun onAction(e: CompanyDeleteAction, h: ClientCommandHelper) {
//    fun onAction(e: CompanySaveAction, h: ClientCommandHelper) {
//    fun onAction(e: LoginAction, db: AuEntityManager, h: ClientCommandHelper) {
