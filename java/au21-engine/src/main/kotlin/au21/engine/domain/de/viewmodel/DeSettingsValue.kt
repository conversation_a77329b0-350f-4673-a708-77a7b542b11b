// File: DeSettingsValue.kt
package au21.engine.domain.de.viewmodel

import au21.engine.domain.common.viewmodel.DateTimeValue
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeAuctionSettings
import au21.engine.domain.de.model.DePriceRule
import au21.engine.framework.client.StoreValue
import au21.engine.framework.utils.AuFormatter
import au21.engine.framework.utils.thousands
import org.joda.time.DateTime

data class DeSettingsValue(
    // val cost_multiplier: String = settings.cost_multiplier.toString()
    val auction_name: String,
    val use_counterparty_credits: Boolean,

    //   val first_round_duration: String = settings.first_round_duration.toString()
    //   val following_round_duration: String = settings.following_round_duration.toString()

    val price_change_initial: String,
    val price_change_post_reversal: String,
    val price_decimal_places: Int,
    val price_label: String,

    val excess_level_0_label: String,
    val excess_level_1_label: String,
    val excess_level_2_label: String,
    val excess_level_3_label: String,
    val excess_level_4_label: String,

    val excess_level_1_quantity: String,
    val excess_level_2_quantity: String,
    val excess_level_3_quantity: String,
    val excess_level_4_quantity: String,

    val cost_multiplier: String,
    val quantity_label: String,
    val quantity_minimum: String,
    val quantity_step: String,

//    // CURRENT STATE
//    val starting_price: String?,
//    val starting_price_set: Boolean,

    val starting_price_announcement_mins: Int,

    // starting time:
    val starting_time: DateTimeValue?,

    val round_red_secs: Int,
    val round_orange_secs: Int,
    val round_open_min_secs: Int,
    val round_closed_min_secs: Int,

    ) : StoreValue {

//    @Transient
//    private val settings: DeAuctionSettings = de.settings
//
//    @Transient
//    private val pr: DePriceRule = settings.price_rule
//    //  private val clock = a.settings.
//
//    @Transient
//    private val places: Int = settings.price_decimal_places
//
//    @Transient
//    private val starting_date_time: DateTime? = de.starting_time?.let { DateTime(it) }


    companion object {

        fun create(de: DeAuction): DeSettingsValue {

            val settings: DeAuctionSettings = de.settings
            val pr: DePriceRule = settings.price_rule
            val places: Int = settings.price_decimal_places
            val starting_date_time: DateTime? = de.starting_time?.let { DateTime(it) }

            return DeSettingsValue(

                //  cost_multiplier = settings.ue_multiplier.toString()
                auction_name = de.auction_name,
                use_counterparty_credits = settings.use_counterparty_credits,

                //    first_round_duration = settings.first_round_duration.toString()
                //    following_round_duration = settings.following_round_duration.toString()

                price_change_initial = AuFormatter.format_to_places(pr.price_change_initial, places),
                price_change_post_reversal = AuFormatter.format_to_places(pr.price_change_post_reversal, places),
                price_decimal_places = settings.price_decimal_places,
                price_label = settings.price_units,

                excess_level_0_label = pr.excess_level_0_label,
                excess_level_1_label = pr.excess_level_1_label,
                excess_level_2_label = pr.excess_level_2_label,
                excess_level_3_label = pr.excess_level_3_label,
                excess_level_4_label = pr.excess_level_4_label,

                excess_level_1_quantity = pr.excess_level_1_quantity.thousands(),
                excess_level_2_quantity = pr.excess_level_2_quantity.thousands(),
                excess_level_3_quantity = pr.excess_level_3_quantity.thousands(),
                excess_level_4_quantity = pr.excess_level_4_quantity.thousands(),

                quantity_label = settings.quantity_units,
                quantity_minimum = settings.quantity_minimum.toString(),
                quantity_step = settings.quantity_step.toString(),

                cost_multiplier = settings.cost_multiplier.toString(), // TODO: format

                // CURRENT STATE
//                starting_price = starting_price,
//                starting_price_set = starting_price != null,
                starting_price_announcement_mins = de.settings.starting_price_announcement_mins,
                // starting time:
                starting_time = starting_date_time?.let { DateTimeValue.create(it) },

                round_red_secs = de.settings.round_red_secs,
                round_orange_secs = de.settings.round_orange_secs,
                round_open_min_secs = de.settings.round_open_min_secs,
                round_closed_min_secs = de.settings.round_closed_min_secs
                )
        }
    }
}
