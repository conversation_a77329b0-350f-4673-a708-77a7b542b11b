// File: de-blotter.kt
package au21.engine.domain.de.viewmodel


import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.OrderSubmissionType
import au21.engine.domain.common.model.OrderType
import au21.engine.domain.common.model.PriceDirection
import au21.engine.domain.de.model.*
import au21.engine.framework.client.StoreElement
import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.AuFormatter
import au21.engine.framework.utils.thousands

// TODO: this could be cached for auctioneers on this auction
data class DeBlotter(
    val rounds: List<DeRoundElement>,
    val traders: List<DeTraderElement>,
    val round_traders: List<DeRoundTraderElement>,
) {
    companion object {
        fun create(de: DeAuction?) = DeBlotter(
            rounds = when (de) {
                null -> emptyList()
                else -> de.rounds.map { r: DeRound -> DeRoundElement.create(de, r) }
            },
            traders = when (de) {
                null -> emptyList()
                else -> de.de_trading_companies
                    .map { t: DeTradingCompany -> DeTraderElement.create(de, t) }
                    .sortedBy { it.company_id } // for purposes of
            },
            round_traders = when (de) {
                null -> emptyList()
                else -> de.rounds.flatMap { r: DeRound ->
                    r.trader_infos.map { rti: DeRoundTraderInfo ->
                        DeRoundTraderElement.create(r, rti)
                    }.sortedBy { it.id }
                }
            }
        )
    }
}

data class DeRoundElement(
    override val id: String,
    val all_orders_in_next_round_will_be_mandatory:Boolean,
    val buy_quantity: Int,
    val buyer_count: Int,
    val excess_side: OrderType,
    val excess_indicator: String,
    val excess_quantity: Int,
    val has_reversed: Boolean,
    val match_quantity_changed: Int,
    val matched: Int,
    val potential: Int,
    val potential_changed: Int,
    val raw_matched: Int,
    val round_direction: PriceDirection?,
    val round_duration: String,
    val round_number: Int,
    val round_price: Double?,
    val round_price_str: String,
    val sell_quantity_change: Int,
    val sell_quantity: Int,
    val seller_count: Int

) : StoreElement {


    companion object {

        fun create(
            a: DeAuction,
            r: DeRound,
            prev: DeRound? = a.penultimate(),
            // long buy_change = r.total_incremental_buy (),
            n_matched: Int = r.match_vol(),
            prev_matched: Int = prev?.match_vol() ?: 0
        ) = DeRoundElement(
            id = "ROUND.${r.number}",
            all_orders_in_next_round_will_be_mandatory = a.de_trading_companies.all { it.next_round_order_will_be_mandatory },
            buy_quantity = a.total_buy(r), //.thousands(),
            buyer_count = a.buyer_count(r), //.toString(),
            excess_side = a.excess_side(r),
            excess_indicator = a.excess_level(r, AuUserRole.AUCTIONEER),
            excess_quantity = a.excess_quantity(r),
            has_reversed = r.has_reversed,
            match_quantity_changed = (n_matched - prev_matched), //.thousands(),
            matched = n_matched, //.thousands(),
            potential = r.max_potential_flow, //.thousands(), // TODO: should this be for the prev round?
            potential_changed = (r.max_potential_flow - (prev?.max_potential_flow ?: 0)), // .thousands(),
            raw_matched = n_matched, //.thousands(),
            round_direction = r.price_direction,
            round_duration = "not implemented",
            round_number = r.number,
            round_price = r.price,
            round_price_str = a.format_round_price(AuUserRole.AUCTIONEER, r),
            sell_quantity_change = a.total_sell(r) - (prev?.let { a.total_sell(it) } ?: 0),
            sell_quantity = a.total_sell(r), //.thousands(),
            seller_count = a.seller_count(r), //.toString()
        )
    }
}

data class DeTraderElement(
    override val id: String,
    val has_seen_auction: Boolean,
    val company_id: String,
    val shortname: String,
    val rank: Int?, // NB CAN BE NULL ?
//    // TODO: do we need these 4 ? //    val last_round_current_volume_type: OrderVolumeType, // because we always have a round, we always have a default order!
//    val last_round_current_volume: String,
//    val last_round_previous_volume_type: OrderVolumeType?,
//    val last_round_previous_volume: String,
//    val value: String,
    //   val current_buy_max: String
) : StoreElement {

//    @Transient
//    private val n: Round? = a.lastround()
//
//    @Transient
//    private val rbi: DeAuction.RoundTraderInfo? = n.get_rti(t)
//
//    @Transient
//    private val n_o: DeOrder? = n?.get_order(t)
//
//    @Transient
//    private var pen_o: DeOrder? = a.penultimate()?.get_order(t)


    // username? = null
    // val approved: Boolean = t.approved
    // val has_seen_auction:Boolean =

    // BUY_MAX = format_thousands ( rbi.buy_max )
    // BUY_MIN = format_thousands ( rbi.buy_implied )
    // SELL_MAX = format_thousands ( rbi.sell_eligibility )

    companion object {

        fun create(a: DeAuction, t: DeTradingCompany): DeTraderElement {

            // val n: Round = a.lastround()
            // val rbi: DeAuction.RoundTraderInfo? = n.get_rti(t)
            //  val n_o: DeOrder = n.get_order(t)
            //  val pen_o: DeOrder? = a.penultimate()?.get_order(t)

            return DeTraderElement(
                id = "COMPANY.${t.company_id}",
                has_seen_auction = a.companies_that_have_seen_auction.contains(t),
                company_id = t.company_id.toString(),
                shortname = t.shortname_at_auction_time,
                rank = t.rank, // NB CAN BE NULL ?
//                last_round_current_volume_type = n_o.volume_type,
//                last_round_current_volume = n_o.volume.thousands(),
//                last_round_previous_volume_type = pen_o?.volume_type,
//                last_round_previous_volume = pen_o?.volume.thousands(),
//                value = a.order_value_str(n_o),
                // TODO: move this to round-trader-element:
                // current_buy_max = rbi?.constraints?.max_buy_volume?.thousands() ?: ""
            )
        }
    }
}


data class DeRoundTraderElement(
    override val id: String,
    //  val auction_id: String = a.id.toString()
    val bid_while_closed: Boolean,
    val buyer_credit_limit:Double,
    val buyer_credit_limit_str: String,

    // ie: the previous order was manual
    val changed: Boolean,
    val cid: String,
    val company_shortname:String,
    val constraints: DeBidConstraints,
    val match: Int,
    val order_submitted_by: String,
    val order_submission_type: OrderSubmissionType, // always has to be an order
    val order_type: OrderType,         // always has to be an order
    val round: Int,
    val timestamp_formatted: String,
    val quantity_int: Int,
    val quantity_str: String,
) : StoreElement {

    companion object {

        fun create(
            //a: DeAuction,
            r: DeRound,
            rti: DeRoundTraderInfo,
        ): DeRoundTraderElement {

            val o: DeOrder = rti.order
            val t: DeTradingCompany = rti.de_trading_company

            return DeRoundTraderElement(
                //  val auction_id: String = a.id.toString()
                id = "ROUND.${r.number}.COMPANY.${t.company_id}",
                bid_while_closed = rti.bid_while_closed,
                buyer_credit_limit = t.initial_limits.initial_buying_cost_limit,
                buyer_credit_limit_str = t.initial_limits.initial_buying_cost_limit_str,
                // ie: the previous order was manual
                changed = o.prev_order?.submission_type == OrderSubmissionType.MANUAL,
                cid = t.company_id.toString(),
                company_shortname = t.shortname_at_auction_time,
                constraints = rti.constraints,
                match = r.getMatch(t)?.match ?: 0,
                order_submitted_by = when (o.submission_type) {
                    OrderSubmissionType.MANUAL -> o.user?.username_at_auction_time
                        ?: throw AlertException("If order is manual, then must have a username")
                    OrderSubmissionType.DEFAULT -> "default"
                    OrderSubmissionType.MANDATORY -> "mandatory"
                },
                order_submission_type = o.submission_type,
                order_type = o.type,
                quantity_int = o.quantity,
                quantity_str = o.quantity.thousands(),
                round = r.number,
                timestamp_formatted = AuFormatter.format_order_time(o.timestamp),
            )
        }
    }
}
