// File: user-delete.kt
package au21.engine.domain.common.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Person
import au21.engine.domain.common.services.logged_in_session_for_user
import au21.engine.domain.common.services.open_auctions
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.framework.commands.*
import au21.engine.framework.commands.interfaces.ISessionsTerminated
import au21.engine.framework.commands.interfaces.SessionTermination
import au21.engine.framework.database.AuEntityManager

class UserDeleteCommand(
    val user_id:String
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)

        val p: Person = db.byId(user_id, include_deleted = true) ?: fail("no user found with id: $user_id")

        fail_if(p.deleted, "User has already been deleted.")

        //fail_if(db.open_auctions().any { it.has_trader(p.company) }, "User is a trader in an open auction, first remove them from that auction then delete.")
        fail_if(db.open_auctions().any { it.users_that_have_seen_auction.any { pp -> pp.person_id == p.id } } ,
            "User is a trader in an open auction, first remove them from that auction then delete.")

        // TODO: what if that's the last user in that company??

        return UserDeleteAction(this, db, session, p)
    }
}



class UserDeleteAction(
    override val command: UserDeleteCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val person:Person
) : ISessionsTerminated, EngineAction {

    override val sessions_terminated = mutableListOf<SessionTermination>()

    override fun mutate() {
        person.deleted = true
        db.save(person)
        db.logged_in_session_for_user(person)?.let { s ->
            terminate_session(db, s, AuSession.SessionTerminationReason.USER_DELETED)
        }
    }

}
