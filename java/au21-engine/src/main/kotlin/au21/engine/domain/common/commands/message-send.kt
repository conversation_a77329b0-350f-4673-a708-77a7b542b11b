// File: message-send.kt
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.*
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.common.services.user_by_username
import au21.engine.framework.commands.*
import au21.engine.framework.commands.interfaces.IAuctionMessage
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.is_blank

class MessageSendCommand(
    val auction_id: String,
    val message: String
) : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        val session = db.session_non_terminated_or_alert(session_id)
        val a:Auction = db.auction_or_alert(auction_id)
        fail_if(message.is_blank(), "Message is blank")

        val u: Person = db.user_by_username(session.user?.username)
            ?: fail("Session has no user.")

        when (u.role) {
            AuUserRole.AUCTIONEER -> {
            }
            AuUserRole.TRADER ->
                fail_if_not(
                    a.has_trader(u.company),
                    "You are not a trader in this auction"
                )
            //else -> alert("You must be an auctioneer or trader to send a message.")
        }

        // creating the message here, because of all the init checks it has:
        val m = when (u.role) {
            AuUserRole.AUCTIONEER ->
                AuctionMessage(
                    message_type_ = AuMessageType.AUCTIONEER_BROADCAST,
                    message_ = message,
                    from_user_ = PersonProxy(u),
                    to_company_ = null
                )
            AuUserRole.TRADER ->
                AuctionMessage(
                    message_type_ = AuMessageType.TRADER_TO_AUCTIONEER,
                    message_ = message,
                    from_user_ = PersonProxy(u),
                    to_company_ = null
                )
            //else -> throw Exception("must be auctioneer or trader to send a message")
        }

        return MessageSendAction(this, db, session, a, m)
    }
}


class MessageSendAction(
    override val command: MessageSendCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    override val auction: Auction,
    override val message: AuctionMessage
) : EngineAction, IAuctionMessage {

    override fun mutate() {
        auction.messages.add(message)
        db.save(auction)
    }
}
