// File: company-delete.kt
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.model.Company
import au21.engine.domain.common.model.Person
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.framework.commands.*
import au21.engine.framework.commands.interfaces.ISessionsTerminated
import au21.engine.framework.commands.interfaces.SessionTermination
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.database.is_same_entity

class CompanyDeleteCommand(
    val company_id: String
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)

        val company: Company? = db.byId(company_id)
        if (company == null) {
            fail("No company found with id: $company_id")
        } else {

            // shouldn't be able to delete company if it is an open auction

            fail_if(db.findAll<Auction>().any { a: Auction ->
                a.companies_that_have_seen_auction.any { it.company_id == company.id}
            }, "Cannot remove a company that has bid in, or seen, any auction.")

            fail_if(
                db.findAll<Person>().any { is_same_entity(it.company, company) },
                "Cannot delete company: ${company.shortname} because it has traders."
            )

            // we're now bouncing logged in users:
//            if (db.company_users(company).any { db.logged_in_session_for_user(it) != null })
//                alert("cannot delete company because some company users are still logged in!")

            return CompanyDeleteAction(this, db, session, company)
        }
    }
}


class CompanyDeleteAction(
    override val command: CompanyDeleteCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val company: Company
) : EngineAction, ISessionsTerminated {

    override val sessions_terminated = mutableListOf<SessionTermination>()

    override fun mutate() {
        // TODO: check this, shouldn't be able to delete a company if they have any users
        // especially if they are logged in!

//        db.company_users(company).forEach { u: Person ->
//            db.logged_in_session_for_user(u)?.let { s: AuSession ->
//                terminate_session(db, s, AuSession.SessionTerminationReason.COMPANY_DELETED)
//            }
//        }
        db.delete(company)
    }

}
