package au21.engine.domain.de.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.OrderType
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeTradingCompany
import au21.engine.domain.de.model.get_orders
import au21.engine.domain.de.model.is_before_end_of_first_round
import au21.engine.framework.commands.*
import au21.engine.framework.database.AuEntityManager

class DeSetLetterOfCreditCommand(
    val auction_id: String,
    val borrower_id: String,
    val buying_credit_limit: String,
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        val session = db.session_non_terminated_or_alert(session_id)

        val buying_credit_limit_absolute: Double =
            buying_credit_limit.trim_commas_and_underscores().replace("$", "").toDoubleOrNull()
                ?: throw AlertException("Credit limit is not a valid number: $buying_credit_limit")

        val de: DeAuction =
            db.byId<DeAuction>(auction_id) ?: throw AlertException("Cannot find auction without an id: $auction_id")

        val borrower = de.de_trading_companies.firstOrNull { it.company_id == borrower_id.toLong() }
            ?: throw AlertException("borrower not in auction")

        err_if(
            de.is_before_end_of_first_round().not(),
            "Cannot set letter of credit limit after the first round has ended"
        )

        de.get_orders(borrower).firstOrNull()?.let {
            if (it.type == OrderType.BUY) {
                err_if(
                    buying_credit_limit_absolute < (it.price?.times(it.quantity) ?: 0.0),
                    "Can't reduce ${borrower.shortname_at_auction_time}'s limit, because it has a bid higher than requested limit in an open auction."
                )
            }
        }

        return DeSetLetterOfCreditAction(
            command = this,
            db = db,
            session = session,
            de = de,
            borrower = borrower,
            buying_credit_limit_absolute = buying_credit_limit_absolute
        )
    }
}


class DeSetLetterOfCreditAction(
    override val command: DeSetLetterOfCreditCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de: DeAuction,
    val borrower: DeTradingCompany,
    val buying_credit_limit_absolute: Double,
) : EngineAction {
    override fun mutate() {
        borrower.initial_buying_cost_limit = buying_credit_limit_absolute
        db.save(de)
    }
}

