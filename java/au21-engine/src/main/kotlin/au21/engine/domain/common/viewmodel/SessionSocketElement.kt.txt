package au21.engine.domain.common.commands.client

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.framework.commands.client.StoreElement
import java.util.*

class SessionSocketElement(
    session: AuSession,
) : StoreElement {
    val company_id: String? = session.company_id
    val role: AuUserRole? = session.role
    val session_id: String = session.session_id
    val socket_last_closed: Date? = session.last_closed
    val socket_state: AuSession.ClientSocketState? = session.socket_state
    val username: String? = session.username

    override val id: String = session.session_id // for now it's the same as the session

    /**
     * TODO: need to think through what's needed on this page
     * - specificially, when do these elements appear and disappear.
     * - for now, a socker open or closed event, or an incremented closed, will put them on the list
     * - but how do they get off?
     * - it might be useful to see the state of all sessions, going back some period of time??
     * - if so, where is that in the database ??
     * - probably easiest if we use an absolute number, like: last 100 sessions.
     * - so then we'd need to drop them off if > 100.
     */
//    companion object {
//        fun onAction(e: HeartbeatAction, h: ClientCommandHelper) {
//            e.incremented_closed_session_sockets
//                .map { s: AuSession -> SessionSocketElement(s) }
//                .send(h, *h.auctioneer_sids)
//        }
//
//        fun onAction(e: SessionSocketAction, h:ClientCommandHelper){
//            SessionSocketElement(e.session).send(h, *h.auctioneer_sids)
//        }
//    }
}

