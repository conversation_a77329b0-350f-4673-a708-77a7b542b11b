// File: DeMutator.kt
package au21.engine.domain.de.services.state

import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeAuctioneerState
import au21.engine.domain.de.model.DeFlowControlType
import au21.engine.domain.de.model.DeFlowControlType.*
import au21.engine.domain.de.model.is_awardable
import au21.engine.domain.de.services.rounds.create_subsequent_round
import io.quarkus.logging.Log
import org.joda.time.DateTime

object DeMutator {

    /**
     * Assumes that all validation has occured before here.
     * ie: no errors are expected.
     *
     * Optionally returns a String which should be a Message !
     */

//    val mutate: (DeCurrentState, DeFlowControlType) -> String? =
//        { current: DeCurrentState, command: DeFlowControlType ->

    fun mutate(de: DeAuction, command: DeFlowControlType, starting_price: Double?): String? {
        Log.debug("Mutate $command")

//        fun engage_autopilot(): String =
//            when (de.autopilot) {
//                AutopilotMode.ENGAGED -> throw Error("Already engaged")
//                AutopilotMode.DISENGAGED -> {
//                    de.autopilot = AutopilotMode.ENGAGED
//                    "autopilot engaged"
//                }
//            }
//
//        fun disengage_autopilot(): String {
//            de.autopilot = AutopilotMode.DISENGAGED
//            return "autopilot disengaged"
//        }

        fun set_starting_price(): String {
            if (starting_price == null)
                throw Error("Cannot set null starting price")
            de.set_starting_price(starting_price)
            if (de.auctioneer_state != DeAuctioneerState.STARTING_PRICE_ANNOUNCED)
                de.setState(DeAuctioneerState.STARTING_PRICE_SET)
            return "starting price set at: $starting_price"
        }

        fun announce_starting_price(): String {
            de.setState(DeAuctioneerState.STARTING_PRICE_ANNOUNCED)
            return "starting price announced"
        }

        fun start_auction(): String {
            de.setState(DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN)
            //  de.submit_constrained_bids()
            return "Auction started, round open"
        }

        fun close_round(): String {
            if (de.is_awardable()) {
                de.setState(DeAuctioneerState.ROUND_CLOSED_AWARDABLE)
            } else {
                de.setState(DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE)
            }

            // de.current_state.autopilot = AutopilotMode.DISENGAGED
            return "round closed, current state = ${de.auctioneer_state}"
        }

        fun reopen_round(): String = run {
            // TODO: should this be called re-open round?
            TODO("reset round not implemented")
//            if (de.rounds.size > 1)
//                de.rounds.removeLast()
//            "Created round ${de.rounds.size}"
        }

        fun next_round(): String = run {
            de.create_subsequent_round()
            de.setState(DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN)
            "going to next round"
        }

        fun award_auction(): String {
            de.setState(DeAuctioneerState.AUCTION_CLOSED)
            return "auction closed"
        }

        val now = DateTime().toDate()

        return when (command) {
//            ENGAGE_AUTO_PILOT -> engage_autopilot()
//            DISENGAGE_AUTO_PILOT -> disengage_autopilot()
            HEARTBEAT ->
                // assumes prior validation for ENGAGED + after various points, or has oll bids
                // TODO: in warning mode only, not auto-pilot:
                // NOT SURE WHY WE HAVE THIS HERE AND IN THE VALIDATOR?
                when (de.auctioneer_state) {
                    DeAuctioneerState.STARTING_PRICE_NOT_SET -> null
                    DeAuctioneerState.STARTING_PRICE_SET ->
                        when (now.after(de.announce_time()) && de.firstround().price != null) {
                            true -> announce_starting_price()
                            false -> null
                        }
                    DeAuctioneerState.STARTING_PRICE_ANNOUNCED ->
                        when (now.after(de.starting_time) && de.firstround().price != null) {
                            true -> start_auction()
                            false -> null
                        }
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> null
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> close_round()
                    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> next_round()
                    DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> null
                    DeAuctioneerState.AUCTION_CLOSED -> null
                }
            SET_STARTING_PRICE -> set_starting_price()
            ANNOUNCE_STARTING_PRICE -> announce_starting_price()
            START_AUCTION -> start_auction()
            CLOSE_ROUND -> close_round()
            REOPEN_ROUND -> reopen_round()
            NEXT_ROUND -> next_round()
            AWARD_AUCTION -> award_auction()
        }

    }

}
