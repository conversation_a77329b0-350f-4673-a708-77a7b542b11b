// File: db-init.kt
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.Person
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.database.AuEntity
import au21.engine.framework.database.AuEntityManager

/**
 * TODO: must make sure this is not done in production!
 * - deletes all entities and creates a1/1
 */
class DbInitCommand : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        // TODO: check that it's not production!! and/or backup db first!
        return DbInitAction(this, db, null)
    }
}

class DbInitAction(
    override val command: DbInitCommand,
    override val db: AuEntityManager,
    override val session: AuSession? = null,
) : EngineAction {

    override fun mutate() {
        db.deleteAllIncDeleted<AuEntity>()
        db.save(
            Person(
                username = "a1",
                password = "1",
                role = AuUserRole.AUCTIONEER
            )
        )
    }
}
