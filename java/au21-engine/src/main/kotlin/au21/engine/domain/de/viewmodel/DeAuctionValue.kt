// File: DeAuctionValue.kt
package au21.engine.domain.de.viewmodel

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.viewmodel.CounterpartyCreditElement
import au21.engine.domain.common.viewmodel.MessageElement
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeTradingCompany
import au21.engine.framework.client.StoreValue


data class DeAuctionValue(
    val auction_id: String?,
    val auction_counterparty_credits: List<CounterpartyCreditElement>,
    val auctioneer_info: DeAuctioneerInfoValue?,
    val auctioneer_status: DeAuctioneerStatusValue?,
    val award_value: DeAwardValue?,
    val blotter: DeBlotter?,
    val common_status: DeCommonStatusValue?,
    val matrix_last_round: DeMatrixRoundElement?,
    val messages: List<MessageElement>,
    val notice: String,
    val settings: DeSettingsValue?,
    val trader_history_rows: List<DeTraderHistoryRowElement>,
    val trader_info: DeTraderInfoValue?,
    val users_that_have_seen_auction: Set<String> // TODO: we should move this to the Blotter Cell

) : StoreValue {

    companion object {

        val value_for_null_auction =
            DeAuctionValue(
                auction_id = null,
                auction_counterparty_credits = emptyList(),
                auctioneer_info = null,
                auctioneer_status = null,
                award_value = null,
                blotter = DeBlotter.create(null),
                matrix_last_round = null,
                messages = emptyList(),
                notice = "",
                settings = null,
                common_status = null,
                trader_history_rows = emptyList(),
                trader_info = null,
                users_that_have_seen_auction = emptySet()
            )


        // TODO: we should cache this!
        fun value_for_auctioneer(
            de: DeAuction,
            s: AuSession,
            auction_counterparty_credits: List<CounterpartyCreditElement>
        ) =
            DeAuctionValue(
                auction_id = de.id_str(),
                auction_counterparty_credits = auction_counterparty_credits,
                auctioneer_info = DeAuctioneerInfoValue.create(de),
                auctioneer_status = DeAuctioneerStatusValue.create(de),
                award_value = DeAwardValue.create(de),
                blotter = DeBlotter.create(de),
                matrix_last_round = DeMatrixRoundElement.create(de.lastround(), de.settings.cost_multiplier),
                messages = MessageElement.message_elements_for_session(de, s),
                notice = de.notice,
                settings = DeSettingsValue.create(de),
                common_status = DeCommonStatusValue.create(de),
                trader_history_rows = emptyList(),
                trader_info = null,
                users_that_have_seen_auction = de.users_that_have_seen_auction.map { it.person_id.toString() }.toSet()
            )


        fun value_for_trader(de: DeAuction, s: AuSession, t: DeTradingCompany) =
            DeAuctionValue(
                auction_id = de.id_str(),
                auction_counterparty_credits = emptyList(),
                auctioneer_info = null,
                auctioneer_status = null,
                award_value = null,
                blotter = DeBlotter.create(null),
                matrix_last_round = null,
                messages = MessageElement.message_elements_for_session(de, s),
                notice = de.notice,
                settings = DeSettingsValue.create(de),
                common_status = DeCommonStatusValue.create(de),
                trader_history_rows = de.rounds.map { r -> DeTraderHistoryRowElement.create(de, r, t) },
                trader_info = DeTraderInfoValue.create(de, t),
                users_that_have_seen_auction = emptySet()
            )

    }
}
