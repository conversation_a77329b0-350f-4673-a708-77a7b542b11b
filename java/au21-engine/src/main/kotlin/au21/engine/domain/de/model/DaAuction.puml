
@startuml
class DeAuction {
    state_label :String
    state :DeAuctionState
    {field} ticking :Boolean (@Index))
    {field} hidden :Boolean (@Index)
    notice :String
    time_remaining :Int
    price_decimal_places: Int
    isClosed() :<PERSON>olean
}

DeAuction *-- AuctionMessage : messages
DeAuction *-- Round : rounds
DeAuction --> Round : awarded_round
DeAuction *-- Trader : traders

class AuctionMessage
class Trader{
    company_id :String
    company_longname :String
    company_shortname :String
    awarded_volume :Int
    blinded :Boolean
}
class Round{
    number :Int
    price :Double
    max_potential_flow :Int
    match_ratio_label :String
    matched_sell_ratio() : MatchedVolumeRatio
    buy_orders()
    sell_orders()
    current_matched()
}

Round *-- RoundTraderInfo :trader_infos
Round *-- VolumeLimit : limits
Round *-- Order : orders

class VolumeLimit{
    sellerId :String
    seller_shortname :String
    buyerId :String
    buyer_shortname :String
    potential_volume :Double
}
class RoundTraderInfo{
    companyId :String
    max_vol :Int
    min_vol :Int
    current_matched_vol :Int
    fully_opposed_match_vol :Int
}
class Match{
    company_id :String
    company_shortname :String
    volume :Int
}
class Order{
    price :Double
    volume :Int
    implicit :Boolean
    priority_timestamp :Date
    timestamp :Date
    companyId :String
    round_number :Int
    side_label :String
    username :String
    company_short :String
    withdrawal_reason :String
    side() :OrderSide
    match_vol() :Int
    withdraw (reason :String)
}
Order *-- Match : matches

@enduml
