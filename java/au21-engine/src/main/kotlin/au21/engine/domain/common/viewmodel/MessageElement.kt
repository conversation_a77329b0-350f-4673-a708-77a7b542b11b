// File: MessageElement.kt
package au21.engine.domain.common.viewmodel

import au21.engine.domain.common.model.*
import au21.engine.domain.common.model.AuMessageType.*
import au21.engine.framework.client.StoreElement
import au21.engine.framework.database.is_same_entity


data class MessageElement(
    override val id: String,
    val from: String,
    val message: String,
    val message_type: AuMessageType,
    val message_type_label: String,
    val timestamp: Long,
    val timestamp_label: String,
    val to: String
) : StoreElement {

    companion object {

        fun create(m: AuctionMessage) =
            MessageElement(
                id = "AuctionMessage." + m.timestamp.time,
                from = m.from_label,
                message = m.body,
                message_type = m.messageType(),
                message_type_label = m.messageType().toString(),
                timestamp = m.timestamp.time,
                timestamp_label = m.timestamp_label,
                to = m.to_label
            )


//        fun clear(h: ClientCommandHelper, vararg sids: String) {
//            MessageElement::class.java.clear_all(h, *sids)
//        }

        // doesn't remove recipient !!
        fun is_for(auction: Auction, m: AuctionMessage, s: AuSession?): Boolean =
            s?.auction?.let { a ->
                when {
                    !is_same_entity(a, auction) -> false
                    s.user == null -> false // no one to send to
                    s.inRole(AuUserRole.AUCTIONEER) -> true  // auctioneers see everything
                    else ->
                        // ie: for TRADERS:
                        when (m.messageType()) {
                            SYSTEM_TO_AUCTIONEER ->
                                false // never seen by traders.
                            AUCTIONEER_BROADCAST ->
                                true // all sessions on the auction see the auctioneer message
                            SYSTEM_BROADCAST ->
                                true
                            TRADER_TO_AUCTIONEER -> // only if from that trader
                                s.user?.company?.let { it.id == m.from_company?.company_id}?:false // only y if from that trader
                            SYSTEM_TO_TRADER -> // only if to that trader
                                s.user?.company?.let { it.id == m.to_company?.company_id}?:false
                            AUCTIONEER_TO_TRADER -> // again, only if to that trader
                                s.user?.company?.let { it.id == m.to_company?.company_id}?:false
                        }
                }
            } ?: false

        fun recipient_sids_for_message(
            sessions: List<AuSession>,
            a: Auction,
            m: AuctionMessage
        ): List<String> = sessions
            .filter { s -> is_for(a, m, s) }
            .map { it.session_id }
        // .toTypedArray()

//        fun send_message(a: Auction, m: AuctionMessage, h: ClientCommandHelper) {
//            recipient_sids_for_message(h.auction_sessions(a), a, m).let { recipients ->
//
//                // (1) send message to recipients
//                MessageElement(m).send(h, *recipients)
//
//                // (2)
////        - [ ] Send notification to all sessions receiving the message,
////          - EXCEPT for the current session
////          - (no point in notifying yourself, you'll see it in the message list)
//
//                // TODO: note we don't send notification to the sender
//                h.notify(listOf(m.body), *recipients.filter { it != h.sid }.toTypedArray())
//            }
//        }

        fun message_elements_for_session(a: Auction, s: AuSession?): List<MessageElement> =
            a.messages
                .filter { m -> is_for(a, m, s) }
                .map { m -> create(m) }

    }
}
