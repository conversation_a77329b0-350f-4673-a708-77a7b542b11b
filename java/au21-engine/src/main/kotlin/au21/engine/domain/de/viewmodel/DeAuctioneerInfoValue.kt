// File: DeAuctioneerInfoValue.kt
package au21.engine.domain.de.viewmodel

import au21.engine.domain.common.model.PriceDirection
import au21.engine.domain.de.model.*
import au21.engine.framework.client.StoreValue
import au21.engine.framework.utils.thousands
import kotlin.math.abs

data class DeAuctioneerInfoValue(
    val allow_credit_editing: Boolean,
    val pen_round: String,
    val last_round: Int,
    val pen_buyers: String,
    val last_buyers: String,
    val pen_sellers: String,
    val last_sellers: String,
    val pen_total_buy: String,
    val last_total_buy: String,
    val pen_total_sell: String,
    val last_total_sell: String,
    val pen_sell_dec: String,
    val last_sell_dec: String,
    val pen_match: String,
    val last_match: String,
    val pen_excess: String,
    val last_excess: String,
    val potential: String
) : StoreValue {

    companion object {

        fun create(de: DeAuction): DeAuctioneerInfoValue {

//        @Transient
//        private val n: DeAuction.Round? = de.lastround()
//
//        @Transient
//        private val pen: DeAuction.Round? = de.penultimate()

            val n: DeRound? = de.lastround()
            val pen: DeRound? = de.penultimate()


            return DeAuctioneerInfoValue(

                allow_credit_editing = false,

                pen_round = pen?.number?.toString() ?: "---",
                last_round = n?.number ?: 0, // TODO

                pen_buyers = pen?.let { de.buyer_count(it).toString() } ?: "---",
                last_buyers = n?.let { de.buyer_count(it) }?.toString() ?: "---",

                pen_sellers = pen?.let { de.seller_count(it).toString() } ?: "---",
                last_sellers = n?.let { de.seller_count(it).toString() } ?: "---",

                pen_total_buy = pen?.let { de.total_buy(pen).thousands() } ?: "---",
                last_total_buy = n?.let { de.total_buy(it).thousands() } ?: "---",

                pen_total_sell = pen?.let { de.total_sell(pen).thousands() } ?: "---",
                last_total_sell = n?.let { de.total_sell(n) }.thousands(),

                pen_sell_dec = "", //pen ? format_thousands ( total_decrement ( a, pen ) ) : '---'
                last_sell_dec = "", // n.round_number == 1 ? '---' : format_thousands ( total_decrement ( a, n ) )

                pen_match = pen?.match_vol()?.thousands() ?: "---",
                last_match = n?.match_vol()?.thousands() ?: "---",

                pen_excess = pen?.let { r ->
                    val demand = de.total_buy(r)
                    val supply = de.total_sell(r)
                    when (r.price_direction) {
                        PriceDirection.UP -> demand - supply
                        PriceDirection.DOWN -> supply - demand
                        null -> abs(demand - supply) // FIRST ROUND WON'T HAVE a price direction!
                    }.thousands()
                } ?: "---",

                last_excess = n?.let { r ->
                    val demand = de.total_buy(r)
                    val supply = de.total_sell(r)
                    when (r.price_direction) {
                        PriceDirection.UP -> demand - supply
                        PriceDirection.DOWN -> supply - demand
                        null -> abs(demand - supply) // FIRST ROUND WON'T HAVE a price direction!
                    }.thousands()
                } ?: "---",

                potential = n?.let { it.max_potential_flow.thousands() } ?: "---"

            )
        }
    }
}
