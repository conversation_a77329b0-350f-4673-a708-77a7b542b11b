// File: extensions.kt
package au21.engine.domain.common.model

inline fun <reified T : Auction> AuSession.get_auction(): T = this as T

/*
    COMPANY extensions
 */


fun Company?.fullname(): String =
    when (this) {
        null -> ""
        else -> "$longname + ($shortname)"
    }


//fun AuSession?.has_username(username: String) =
//        this?.person?.username.equals(username) ?: false

/*
     USER extensions
 */

fun Person.label(): String =
    this.username + this.company?.let { c -> " (${c.shortname})" }



