// File: DeMatcher.kt
package au21.engine.domain.de.services.matcher

// import au21.engine.domain.de.services.matcher.RoundCounterpartyCapacity.Companion.to_table
import au21.engine.domain.de.commands.DeOrderSubmitAction
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeMatch
import au21.engine.domain.de.model.DeRound
import au21.engine.domain.de.model.DeTradingCompany
import au21.engine.domain.de.services.matcher.DeCapacityEdge.Companion.to_table
import au21.engine.domain.de.services.matcher.DeFlowResult.Companion.calculate_max_flow_psjava
import au21.engine.domain.de.services.matcher.DeFlowResult.Companion.log_tables
import au21.engine.framework.commands.AlertException
import au21.engine.framework.commands.EngineAction
import io.quarkus.logging.Log


/*
 * takes a round
 * calculates constraints
 * returns the flow results
 *
 * Assumes that quantity limits have been set already!
 *
 * There are 3 external functions that are only used by this file:
 *
 * - 1) RoundBuyerSellerVolumeLimit.calculate_round_quantity_limits(de)
 *
 * - 2) MatchFlowEdge.to_edges(n, round_buyer_seller_quantity_limits)
 *
 * - 3) FlowResult.calculate_max_flow_psjava(edges)
 *
 */

object DeMatcher {

    // this one used by actions
    fun calculate_and_set_matches(
        de: DeAuction,
        caller: EngineAction?,
        debug: Boolean = false
    ) {

        calculate_and_set_matches(
            de.lastround(),
            de.settings.cost_multiplier,
            caller,
            debug
        )
    }

    // this one might be easier for testing
    fun calculate_and_set_matches(
        last_De_round: DeRound,
        cost_multiplier: Double,
        caller: EngineAction?,
        debug: Boolean = false
    ) {

        val price_cost_multiplier = (last_De_round.price ?: 0.0) * cost_multiplier

        // 1) Calculate counterparty capacities:
        val counterparty_limits: List<RoundCounterpartyLimits> =
            RoundCounterpartyLimits.calculate_counterparty_capacities(
                round_trader_infos = last_De_round.trader_infos,
                price_cost_multiplier = price_cost_multiplier
            )

        // 2) use them to calculate the MatchFlowEdges:
        val capacity_edges: List<DeCapacityEdge> = DeCapacityEdge.to_edges(last_De_round.trader_infos)

//        last_round.trader_infos.size
//            .let { trader_count ->
//                (trader_count * trader_count) + trader_count
//            }
//            .let { expected_edge_count ->
//                if (expected_edge_count != capacity_edges.size)
//                    throw AlertException("Expected $expected_edge_count edges, but found: ${capacity_edges.size} edges")
//            }

        // 3) Calculate the max-flow
        // TODO: we should experiment with removing the traders with empty edges
        val max_flow_results: List<DeFlowResult> =
            calculate_max_flow_psjava(capacity_edges)

        // 4) Calculate and set the matches
        val matches: List<DeMatch> =
            create_matches_from_flow_results(counterparty_limits, max_flow_results, price_cost_multiplier)

        last_De_round.setMatches(matches)

        // Debug Logging:
        if (debug || caller is DeOrderSubmitAction) {
            //  Log.info(counterparty_capacities.to_table())
            Log.info(capacity_edges.to_table())
            Log.info(max_flow_results.log_tables())
        }
    }

    fun create_matches_from_flow_results(
        counterparty_capacities: List<RoundCounterpartyLimits>,
        max_flow_results: List<DeFlowResult>,
        round_price_multiplier: Double
    ): List<DeMatch> {

        val tradingCompanies: Set<DeTradingCompany> = counterparty_capacities.flatMap {
            listOf(it.buyer_rti.de_trading_company, it.seller_rti.de_trading_company)
        }.toSet()

        return max_flow_results
            .filter { flow: DeFlowResult ->
                listOf(DeCapacityEdge.SELL_SOURCE, DeCapacityEdge.BUY_SINK)
                    .none { it == flow.from_seller || it == flow.to_buyer }
            }
            .mapNotNull { f: DeFlowResult ->

                val seller: DeTradingCompany =
                    tradingCompanies.find { it.shortname_at_auction_time == f.from_seller }
                        ?: throw Error("No buyer found with shortname: ${f.to_buyer}")

                val buyer: DeTradingCompany =
                    tradingCompanies.find { it.shortname_at_auction_time == f.to_buyer }
                        ?: throw Error("No buyer found with shortname: ${f.to_buyer}")

                if (buyer.company_id != seller.company_id) {
                    val rcl: RoundCounterpartyLimits = counterparty_capacities
                        .find {
                            it.buyer_rti.de_trading_company.company_id == buyer.company_id
                                    && it.seller_rti.de_trading_company.company_id == seller.company_id
                        }
                        ?: throw AlertException("unable to find a quantity limit for buyer=${buyer.shortname_at_auction_time}, seller=${seller.shortname_at_auction_time}")

                    DeMatch(
                        sell_order_ = rcl.seller_rti.order,
                        buy_order_ = rcl.buyer_rti.order,
                        match_ = f.flow,
                        capacity_ = rcl.capacity,
                        round_price_multiplier = round_price_multiplier
                    )

                } else {
                    null
                }
            }
    }
}
