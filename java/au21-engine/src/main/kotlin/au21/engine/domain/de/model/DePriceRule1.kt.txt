package au21.engine.domain.de.model

import javax.persistence.Embeddable


@Embeddable
class DePriceRule1(
    var price_level_1: Double = 0.125,
    var price_level_2: Double = 0.250,
    var price_level_3: Double = 0.500,
    var price_level_4: Double = 1.000,

    var excess_level_1: Int = 10,
    var excess_level_2: Int = 20,
    var excess_level_3: Int = 30
) {

    // NOTE: Can be either excess demand (UP), or excess supply (DOWN):

    fun get_excess_level(excess:Int):ExcessLevel =
        when{
            excess <= 0 -> ExcessLevel.NONE
            excess <= excess_level_1 ->  ExcessLevel.ONE_PLUS
            excess <= excess_level_2 ->  ExcessLevel.TWO_PLUS
            excess <= excess_level_3 ->  ExcessLevel.THREE_PLUS
            else ->  ExcessLevel.FOUR_PLUS
    }

    fun get_price_change_for_excess_level(level: ExcessLevel): Double =
        when(level) {
            ExcessLevel.NONE -> 0.0
            ExcessLevel.ONE_PLUS -> price_level_1
            ExcessLevel.TWO_PLUS  -> price_level_2
            ExcessLevel.THREE_PLUS -> price_level_3
            ExcessLevel.FOUR_PLUS -> price_level_4
        }
}
