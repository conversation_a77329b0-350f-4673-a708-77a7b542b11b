// File: DeAwardValue.kt
package au21.engine.domain.de.viewmodel

import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.OrderType
import au21.engine.domain.de.model.*
import au21.engine.framework.client.StoreValue
import au21.engine.framework.utils.thousands
import kotlin.math.min

data class DeAwardValue(val round_results: List<DeRoundResultVM>) : StoreValue { // value used for ts-generator to find it.
    // these will have to be added if we stop updating DeAwardValue with the auction
//    val price_units = de.settings.price_units
//    val volume_units = de.settings.volume_units

    companion object {
        fun create(de: DeAuction) = DeAwardValue(
            round_results = de.rounds
                .takeLast(min(de.rounds.size, 3))
                .reversed()
                .map { DeRoundResultVM.create(de, it) }
        )
    }
}

data class DeRoundResultVM(
    val round_number: Int,
    val round_price: String,
    val buy_total: String,
    val sell_total: String,
    val match_total: String,
    val trader_flows: List<DeTraderFlowVM>,
    val matches: List<DeScenarioMatchVM>
) {

    companion object {
        fun create(de: DeAuction, r: DeRound) =
            DeRoundResultVM(
                round_number = r.number,
                round_price = de.format_round_price(AuUserRole.AUCTIONEER, r),
                buy_total = r.buy_orders().sumOf { it.quantity }.thousands(),
                sell_total = r.sell_orders().sumOf { it.quantity }.thousands(),
                match_total = r.match_vol().thousands(),
                trader_flows = de.de_trading_companies.map { t: DeTradingCompany ->
                    DeTraderFlowVM(
                        company_shortname = t.shortname_at_auction_time,
                        company_id = t.company_id.toString(),
                        order_type = r.get_order(t).type,
                        quantity = r.match_vol(t).thousands()
                    )
                }.sortedBy { it.company_id.toInt() },
                matches = r.matches.map { m: DeMatch ->
                    DeScenarioMatchVM.create(r, m)
                }.sortedBy { it.buyer_id }
            )
    }
}

data class DeTraderFlowVM(
    val company_shortname: String,
    val company_id: String,
    val order_type: OrderType,
    val quantity: String
)

data class DeScenarioMatchVM(
    val round_number: Int,
    val buyer_id: String,
    val buyer_shortname: String,
    val seller_id: String,
    val seller_shortname: String,
    val actual_match: Int,
    val actual_match_str: String
) {

    companion object {
        fun create(r: DeRound, m: DeMatch) = DeScenarioMatchVM(
            round_number = r.number,
            buyer_id = m.buy_order.trading_company.company_id.toString(),
            buyer_shortname = m.buyer_shortname,
            seller_id = m.sell_order.trading_company.company_id.toString(),
            seller_shortname = m.seller_shortname,
            actual_match = m.match,
            actual_match_str = m.match.thousands()
        )
    }
}
