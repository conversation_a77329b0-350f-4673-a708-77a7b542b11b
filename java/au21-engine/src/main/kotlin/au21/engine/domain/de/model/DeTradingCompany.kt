// File: DeTradingCompany.kt
package au21.engine.domain.de.model

import au21.engine.domain.common.model.Company
import au21.engine.domain.common.model.CompanyProxy
import au21.engine.domain.common.model.OrderType
import javax.persistence.Embeddable

@Embeddable
class DeTradingCompany(c: Company) : CompanyProxy(c){

    var initial_limits:DeInitialLimits = DeInitialLimits()
        private set

    // Set whenever an order is submitted, and when round is created assuming we alllow that to happen:
    var next_round_order_will_be_mandatory:Boolean = false

    // null means 'no limit'
    var awarded_quantity: Int = 0
        private set

    var awarded_order_type: OrderType = OrderType.NONE
        private set

    fun award(order_type: OrderType, quantity: Int) {
        awarded_order_type = order_type
        awarded_quantity = quantity
    }

    var blinded: Boolean = false

    var rank: Int? = null
}
