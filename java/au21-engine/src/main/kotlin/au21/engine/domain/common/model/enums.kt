// File: enums.kt
package au21.engine.domain.common.model

//enum class AuctionDesign {
//    BACKHAUL,
//    DOUBLE,
//    MULTIROUND,
//    TRANSPORT_ENGLISH,
//    PIPELINE,
//    TRANSPORT_OPTIMIZATION
//}


//inline fun <reified T> DeAuction.setState(
//        state: T,
//        cb: ((T) -> DeAuction) = { this })
//        where
//        T : Enum<T>,
//        T : IAuctionState {
//    this.state_label = state.toString()
//    cb(state)
//}
//
//inline fun <reified T> DeAuction.getState(): T
//        where
//        T : Enum<T>,
//        T : IAuctionState = enumValueOf(state_label)
//
//inline fun <reified T> DeAuction.inState(vararg states: T): Boolean
//        where
//        T : Enum<T>,
//        T : IAuctionState = states.contains(getState())

inline fun <reified T> String.enumValueOfWithTrace():T
        where
        T : Enum<T> {
    try {
        return enumValueOf(this)
    }
    catch (e:Throwable){
        println("Unable to get ${T::class.simpleName} from $this")
        throw e
    }
}

enum class AutopilotMode {
    DISENGAGED,
    ENGAGED
}

enum class Crud {
    CREATE,
    READ,
    UPDATE,
    DELETE,
    ADD,
    REMOVE,
    CLEAR
}

enum class AuMessageType {
    AUCTIONEER_BROADCAST,  // Auctioneer to all traders
    AUCTIONEER_TO_TRADER,  // Auctioneer to 1 trader (probably should implement as per Zoom, also, should be for 1 Company)
    TRADER_TO_AUCTIONEER,  // 1 Trader to Auctioneer
    SYSTEM_BROADCAST,      // System to all traders and auctioneers
    SYSTEM_TO_TRADER,      // System to 1 trader (or trading Company when implemented)
    SYSTEM_TO_AUCTIONEER   // System to auctioneers
}

enum class AuctionInstruction {
    HIDE, UNHIDE, DELETE
}

enum class AuUserRole {
    //  ADMIN, // NOTE: there are various tests for AUCTIONEER or alert
    AUCTIONEER, // can be observers
    TRADER      // can be testers
}

enum class Operator(var label: String) {
    GT("greater than"),
    GE("greater than or equal to");

    fun check(x: Double, limit: Double): Boolean =
        when (this) {
            GT -> x > limit
            GE -> x >= limit
        }

    fun check(x: Int, limit: Int): Boolean =
        when (this) {
            GT -> x > limit
            GE -> x >= limit
        }
}

enum class OrderType { BUY, SELL, NONE }

enum class OrderSubmissionType { MANUAL, DEFAULT, MANDATORY }

enum class PriceDirection { UP, DOWN }

//    var price_direction: PriceDirection
//        get() = valueOf(price_direction_label)
//        set(value) {
//            price_direction_label = value.name
//        }


enum class ActivityRule { ABSOLUTE, RATIO }


enum class StopMode {
    LT, LE, NONE;

    fun check(x: Int, limit: Int): Boolean =
        when (this) {
            StopMode.LT -> x < limit
            StopMode.LE -> x <= limit
            NONE -> false
        }
}

enum class Visibility { ALL, FIRST_ROUND, ELIGIBILITY }
