// File: page-set.kt

package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.framework.PageName
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail
import au21.engine.framework.commands.fail_if
import au21.engine.framework.database.AuEntityManager

// TODO: do we want 'SelectAuction' to be handled here instead?

class PageSetCommand(
    val page:PageName
) : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        val session = db.session_non_terminated_or_alert(session_id)
        fun throw_if_not_auctioneer(){
            if (!session.inRole(AuUserRole.AUCTIONEER))
                fail("Only auctioneers can access page: $page")
        }
        when(page){
            PageName.CREDITOR_AUCTIONEER_PAGE ->
                throw_if_not_auctioneer()
            PageName.CREDITOR_TRADER_PAGE -> {
                // TODO: why did we have this?
                // throw_if_not_auctioneer()
            }
            PageName.HOME_PAGE -> {
                fail_if(!session.is_logged_in(), "Please login first.")
            }
            PageName.LOGIN_PAGE -> {}
            PageName.SESSION_PAGE ->
                throw_if_not_auctioneer()
            PageName.USER_PAGE ->
                throw_if_not_auctioneer()
            PageName.DE_AUCTIONEER_PAGE -> {
                throw_if_not_auctioneer()
            }
            PageName.DE_SETUP_PAGE ->
                throw_if_not_auctioneer()
            PageName.DE_TRADER_PAGE ->
                fail_if(!session.is_trader(), "Only traders can see the trader page")
               // TODO: probably need to check that they can see this auction
            else -> fail("page not recognized: $page")
        }
        return PageSetAction(this, db, session, page)
    }
}


class PageSetAction(
    override val command: PageSetCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val page:PageName
) : EngineAction {
    override fun mutate() {
        session.set_page(page)
        db.save(session)
    }

}
