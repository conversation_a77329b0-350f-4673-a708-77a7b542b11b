// File: de-traders-remove.kt
package au21.engine.domain.de.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Company
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.common.services.sessions_logged_in_traders
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.has_non_zero_bid
import au21.engine.domain.de.services.matcher.DeMatcher
import au21.engine.framework.PageName
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail
import au21.engine.framework.commands.fail_if_not_auctioneer
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.database.is_same_entity

/**
 * this command will look at the company_ids list and either:
 * a) add if not already in auction
 * b) remove from auction if not on the list
 * - those removed will be 'bounced' out of the auction, and auction row removed
 * - those included will have the auction row added
 */

class DeTradersRemoveCommand(
    val auction_id: String,
    val company_ids: List<String>
) : EngineCommand() {

    /** De Auction: Adding and removing traders
     * - can only happen before close of round one
     * - if add, check that not already added
     * - if remove, check that has not bid
     */

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)

        val de: DeAuction = db.auction_or_alert(auction_id)

        val existing_company_ids: List<String> = de.de_trading_companies.map { it.company_id.toString() }

        val companies_to_remove: List<Company> = company_ids
            .filter { existing_company_ids.contains(it) }
            .mapNotNull { db.byId(it) }

        // todo: this was done hastily:
        val companies_to_remove_that_have_bid: List<Company> = companies_to_remove
            .filter { c -> de.has_non_zero_bid(c) }

        if (companies_to_remove_that_have_bid.isNotEmpty()) {
            fail(
                "Unable to remove these bidders as they have already bid in this auction: "
                        + companies_to_remove_that_have_bid.joinToString(",") { it.shortname }
            )
        }

        val companies_that_have_seen_auction: List<Company> = companies_to_remove
            .filter {c ->
                de.companies_that_have_seen_auction
                    .any { it.company_id == c.id }
            }

        if (companies_that_have_seen_auction.isNotEmpty()) {
            fail(
                "Unable to remove these bidders as they have already seen this auction: "
                        + companies_that_have_seen_auction.joinToString(",") { it.shortname }
            )
        }

        return DeTradersRemoveAction(this, db, session, de, companies_to_remove)
    }
}


class DeTradersRemoveAction(
    override val command: DeTradersRemoveCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de: DeAuction,
    val companies_removed: List<Company>,
    // val messages: MutableList<String>
) : EngineAction {

    // is needed because below we will remove the auction from the session,
    // - and it won't be found by the genenater online sessions for auction
    // - (as it's then null at that point!!)

//    val removed_company_sessions: MutableList<AuSession> = mutableListOf()
//
//    fun removed_company_sids(): Array<String> = removed_company_sessions.map { it.session_id }.toTypedArray()

    override fun mutate() {

        companies_removed.forEach { removed_company ->

            de.remove_trader(removed_company)

            // VolumeLimit:
            //  n.limits.removeIf { it.buyerId == c.id_str() || it.sellerId == c.id_str() }

            // Bounce:
            db.sessions_logged_in_traders()
                .filter { is_same_entity(it.user?.company, removed_company) }
                .forEach { s: AuSession ->
                    // Bounce to home page
                    s.set_page(PageName.HOME_PAGE)
                    //removed_company_sessions.add(s)
                    db.save(s)
                }
        }

        // need to recalculate max flow:

        // TODO: need to implement this again !!
        //  n.setMaxPotentialFlow(de.calculate_max_from(n))


        // have to reset volume limits:
        DeMatcher.calculate_and_set_matches(de, this)

        db.save(de)
    }

}
