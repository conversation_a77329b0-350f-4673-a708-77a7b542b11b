// File: de-credit-set.kt
package au21.engine.domain.de.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeTradingCompany
import au21.engine.framework.commands.*
import au21.engine.framework.database.AuEntityManager

class DeCreditSetCommand(
    val auction_id: String,
    val lender_id: String,
    val borrower_id: String,
    val credit_limit: String,
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        val session = db.session_non_terminated_or_alert(session_id)

        // TODO: check that either auctioneer or session.company_id == seller_id

        val credit_limit_absolute: Double = credit_limit.trim_commas_and_underscores().replace("$", "").toDoubleOrNull()
            ?: throw AlertException("Credit limit is not a valid number: $credit_limit")

        val de: DeAuction =
            db.byId<DeAuction>(auction_id).also {
                when (it) {
                    null -> err_if(true, "Cannot find auction without an id.")
                    else -> {
                        err_if(
                            it.de_trading_companies.none { t -> t.company_id == borrower_id.toLong() },
                            "borrower not in auction"
                        )
                        err_if(
                            it.de_trading_companies.none { t -> t.company_id == lender_id.toLong() },
                            "lender not in auction"
                        )
                    }
                }
            } ?: throw AlertException("Cannot find auction without an id: $auction_id")

        val lender = de.de_trading_companies.firstOrNull { it.company_id == lender_id.toLong() }
            ?: throw AlertException("lender not in auction")
        val borrower = de.de_trading_companies.firstOrNull { it.company_id == borrower_id.toLong() }
            ?: throw AlertException("borrower not in auction")

        val current_absolute_limit: Double? = de.get_counterparty_credit_limit(lender, borrower)?.credit_limit

        err_if(
            current_absolute_limit != null // ie: not 'no limit'
                    && current_absolute_limit < credit_limit_absolute, // ie: we're reducing the buyers credit limit
            "Cannot reduce ${borrower.shortname_at_auction_time}'s (buyer) credit limit with ${lender.shortname_at_auction_time} (seller) because the buyer has a bid in an open auction."
        )

        return DeCreditSetAction(
            command = this,
            db = db,
            session = session,
            de = de,
            lender = lender,
            borrower = borrower,
            credit_limit_absolute = credit_limit_absolute
        )
    }
}


class DeCreditSetAction(
    override val command: DeCreditSetCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de: DeAuction,
    val lender: DeTradingCompany,
    val borrower: DeTradingCompany,
    val credit_limit_absolute: Double,
) : EngineAction {
    override fun mutate() {
        de.set_counterparty_credit_limit(lender, borrower, credit_limit_absolute)
        db.save(de)
    }

}
