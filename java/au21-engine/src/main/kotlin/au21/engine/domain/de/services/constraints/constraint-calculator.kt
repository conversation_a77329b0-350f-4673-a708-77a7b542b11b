// File: constraint-calculator.kt
package au21.engine.domain.de.services.constraints

import au21.engine.domain.common.model.OrderType
import au21.engine.domain.common.model.PriceDirection
import au21.engine.domain.de.model.DeAuctionSettings
import au21.engine.domain.de.model.DeBidConstraints
import au21.engine.domain.de.model.DeRound
import au21.engine.domain.de.model.DeTradingCompany
import kotlin.math.floor

/**
 * THIS NEEDS TO BE KEPT IN SYNC WITH: DeTraderBidConfirmModal
 */

fun calculate_first_round_constraints(
    t:DeTradingCompany,
    s:DeAuctionSettings,
    n:DeRound) = DeBidConstraints(

        max_buy_quantity = max_quantity_from_round_price_and_cost_limit(
            t.initial_limits.initial_buying_cost_limit,
            s.cost_multiplier,
            n.price ?: 0.0
        ),
        min_buy_quantity = 0,
        min_sell_quantity = 0,
        max_sell_quantity = t.initial_limits.initial_selling_quantity_limit
//        max_quantity_from_round_price_and_cost_limit(
//            t.initial_limits.initial_selling_cost_limit,
//            s.cost_multiplier,
//            n.price ?: 0.0
//        )
    )

 fun max_quantity_from_round_price_and_cost_limit(
    // HELPER:
    cost_limit: Double,
    round_price: Double,
    cost_multiplier: Double,
): Int =
    when {
        round_price == 0.0 -> 0
        cost_limit == 0.0 -> 0
        cost_multiplier == 0.0 -> 0
        else -> floor(cost_limit / (round_price * cost_multiplier)).toInt()
    }

fun calculate_subsequent_round_constraints(
    prev_round_constraints: DeBidConstraints,
    prev_round_order_type: OrderType,
    prev_round_order_quantity: Int,
    next_round_direction: PriceDirection
): DeBidConstraints {

    /**
     * ASSUMES THAT HAS PASSED this method:
     * - validate_de_order_constraints_and_throw_if_fails()
     * (de_order_validations.kt)
     */

    return when (prev_round_order_type) {

        OrderType.NONE ->

            when (next_round_direction) {
                PriceDirection.UP ->
                    DeBidConstraints(
                        min_buy_quantity = 0,
                        max_buy_quantity = 0,
                        min_sell_quantity = 0,
                        max_sell_quantity = prev_round_constraints.max_sell_quantity
                    )
                PriceDirection.DOWN ->
                    DeBidConstraints(
                        min_buy_quantity = 0,
                        max_buy_quantity = prev_round_constraints.max_buy_quantity,
                        min_sell_quantity = 0,
                        max_sell_quantity = 0
                    )
            }
        OrderType.BUY ->
            when (next_round_direction) {
                PriceDirection.UP ->
                    DeBidConstraints(
                        min_buy_quantity = prev_round_constraints.min_buy_quantity,
                        max_buy_quantity = prev_round_order_quantity,
                        min_sell_quantity = prev_round_constraints.min_sell_quantity,
                        max_sell_quantity = prev_round_constraints.max_sell_quantity
                    )
                PriceDirection.DOWN ->
                    DeBidConstraints(
                        min_buy_quantity = prev_round_order_quantity,
                        max_buy_quantity = prev_round_constraints.max_buy_quantity,
                        min_sell_quantity = 0,
                        max_sell_quantity = 0
                    )
            }
        OrderType.SELL ->
            when (next_round_direction) {
                PriceDirection.UP ->
                    DeBidConstraints(
                        min_buy_quantity = 0,
                        max_buy_quantity = 0,
                        min_sell_quantity = prev_round_order_quantity,
                        max_sell_quantity = prev_round_constraints.max_sell_quantity
                    )
                PriceDirection.DOWN ->
                    DeBidConstraints(
                        min_buy_quantity = prev_round_constraints.min_buy_quantity,
                        max_buy_quantity = prev_round_constraints.max_buy_quantity,
                        min_sell_quantity = prev_round_constraints.min_sell_quantity,
                        max_sell_quantity = prev_round_order_quantity
                    )
            }
    }
}
