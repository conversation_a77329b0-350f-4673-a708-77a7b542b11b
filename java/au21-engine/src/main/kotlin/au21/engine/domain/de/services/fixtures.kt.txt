package au2019.engine.common.domain

import au2019.engine.api.database.AuDatabase
import au2019.engine.api.database.delete_all_entities
import au2019.engine.api.database.findAll
import au2019.engine.api.database.findFirst
import au2019.engine.api.json.to_pretty
import au2019.engine.common.model.COMPANY
import au2019.engine.common.model.SITE
import au2019.engine.common.model.USER
import org.koin.core.KoinComponent
import org.koin.core.inject
import org.tinylog.kotlin.Logger

class ResetDb : KoinComponent {

    val db: AuDatabase by inject()

    init {
        db.transact {
            db.delete_all_entities()
            db.save(SITE()) // to create a site object, might not need
        }
    }
}

class CreateSiteIfNotExists(transaction: Boolean) : KoinComponent {
    val db: AuDatabase by inject()

    init {
        if (db.findFirst<SITE> { true } == null) {
            if (transaction)
                db.transact { db.save(SITE()) }
            else
                db.save(SITE())
        }
    }
}

class DummyPeople(transaction: Boolean) : KoinComponent {

    val db: AuDatabase by inject()

    init {

        if (transaction) {
            db.transact { create() }
        } else {
            create()
        }
    }

    fun create() {
        // MUST BE IN A TRANSACTION ?

        // create admin user:

        if (db.user_by_username("admin",
                        case_insensitive = true,
                        include_deleted = false) == null) {

            db.save(USER(
                    username = "admin",
                    password = "1",
                    role = USER.AuUserRole.ADMIN))

            println("created admin user")
        }


        (1..3).forEach {
            val username = "a$it"
            if (db.user_by_username(username,
                            case_insensitive = true,
                            include_deleted = false) == null) {
                db.save(USER(
                        username = username,
                        password = "1",
                        role = USER.AuUserRole.AUCTIONEER))

                println("created auctioneer: $username")
            }
        }

        (1..9).forEach {

            // create the company:
            val shortname = "company$it"

            if (db.company_by_shortname(shortname,
                            case_insensitive = true,
                            include_deleted = false
                    ) == null && db.company_by_longname(shortname,
                            case_insensitive = true,
                            include_deleted = false) == null) {

                db.save(COMPANY(shortname = shortname, longname = "$shortname description"))

                println("created $shortname")
            }

            val c: COMPANY = db.company_by_shortname(shortname,
                    case_insensitive = true,
                    include_deleted = false) ?: throw Exception("$shortname should not be null")

            // create the user:
            val u = "b$it"
            if (db.user_by_username(u,
                            case_insensitive = true,
                            include_deleted = false) == null) {
                db.save(USER(
                        username = u,
                        password = "1",
                        role = USER.AuUserRole.TRADER,
                        company = c))

                println("created trader: $u")
            }

        }

        Logger.info("created: " + db.findAll<USER>().map { it.username }.to_pretty())

    }
}
