// File: de-template-delete.kt
package au21.engine.domain.de.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail_if_not_auctioneer
import au21.engine.framework.database.AuEntityManager

class DeTemplateDeleteCommand(
    val template_id:String
): EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)

//        val template:DeAuctionTemplate = db.byId(template_id)
//            ?: alert("no template found with id: $template_id")

        return DeTemplateDeleteAction(this, db, session)
    }
}

class DeTemplateDeleteAction(
    override val command: DeTemplateDeleteCommand,
    override val db:AuEntityManager,
    override val session:AuSession
) : EngineAction {
    override fun mutate() {
        TODO("Not yet implemented")
    }

}
