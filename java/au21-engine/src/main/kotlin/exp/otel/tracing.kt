package exp.otel

import io.opentelemetry.api.trace.Tracer
import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import io.quarkus.logging.Log


@Path("/test")
class TestResource {
    @Inject
    lateinit var tracer: Tracer

    @GET
    @Path("/telemetry")
    fun testTelemetry(): String {
        // Create a root span
        val parentSpan = tracer!!.spanBuilder("test-parent-operation")
            .setAttribute("custom.attribute", "parent-value")
            .startSpan()

        try {
            parentSpan.makeCurrent().use { scope ->
                // Add some events to the parent span
                parentSpan.addEvent("Starting parent operation")


                // Create a child span
                val childSpan = tracer!!.spanBuilder("test-child-operation")
                    .setAttribute("custom.attribute", "child-value")
                    .startSpan()
                try {
                    // Add events to child span
                    childSpan.addEvent("Processing in child span")
                    Thread.sleep(100) // Simulate work
                    childSpan.addEvent("Child operation complete")

                    return "Telemetry test " + System.currentTimeMillis()
                } finally {
                    childSpan.end()
                }
            }
        } finally {
            parentSpan.end()
        }
    }

    @GET
    @Path("/logs")
    fun testLogs(): String {
        Log.trace("This is a TRACE message")
        Log.debug("This is a DEBUG message")
        Log.info("This is an INFO message")
        Log.warn("This is a WARN message")
        Log.error("This is an ERROR message")

        try {
            throw RuntimeException("Test exception")
        } catch (e: Exception) {
            Log.error("Caught test exception", e)
        }

        return "Logs generated"
    }
}
