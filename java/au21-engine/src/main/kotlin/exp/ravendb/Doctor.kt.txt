package exp

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import net.ravendb.client.documents.DocumentStore
import net.ravendb.client.documents.IDocumentStore


val mapper = jacksonObjectMapper().apply {
    configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
}

class Doctor(var name: String)

// ALSO WORKS: ie: with empty constructor:
//class Doctor(var name: String){
//    constructor(): this("")
//}


fun main() {
    val store: IDocumentStore =
        DocumentStore("http://localhost:8083", "test-db")

    store.conventions.entityMapper = mapper

    store.initialize()

    store.openSession().apply {
        val d = Doctor("fred")
        store(d, "1")
        saveChanges()
    }

    store.openSession().apply {
        val d = load(Doctor::class.java, "1")
    }

    store.close()
}
