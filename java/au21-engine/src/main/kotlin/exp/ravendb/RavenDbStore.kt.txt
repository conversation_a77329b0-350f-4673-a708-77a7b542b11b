package au21.engine.framework.misc.ravendb

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import net.ravendb.client.documents.DocumentStore
import net.ravendb.client.documents.changes.DocumentChange
import net.ravendb.client.documents.changes.IObserver
import net.ravendb.client.documents.session.IDocumentSession
import javax.enterprise.context.ApplicationScoped

open class RavenEntity {
    lateinit var id:String
}

@ApplicationScoped
class RavenDbStore{

    final val url: String = "http://build2.auctionologies.com:8083"
    final val database: String = "sessions"

    val db: DocumentStore = DocumentStore(url, database)
        .apply {
            conventions.apply {
                setFindIdentityProperty { prop -> "id" == prop.name }
                setFindJavaClassByName { name -> Class.forName(name) }
                entityMapper = jacksonObjectMapper().apply {
                    configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                }
            }
            initialize()
        }

    fun openSession(): IDocumentSession = db.openSession()

    fun close() = db.close()
}

inline fun <reified T> RavenDbStore.forDocumentsInCollection(observer: IObserver<DocumentChange>) {
    db.changes()
        .forDocumentsInCollection(T::class.java)
        .subscribe(observer)
}


inline fun <reified T : RavenEntity> IDocumentSession.save(t: T): T =
    this.store(t).run { t }

inline fun <reified T : RavenEntity> IDocumentSession.byId(id: String?): T? =
    this.load(T::class.java, id)


// Extension methods:

fun RavenDbStore?.message(msg: String) =
    mapOf(
        "msg" to msg,
        "url" to this?.url,
        "database" to this?.database
    )
        //.also { Logger.info(it) }
        .toString()

fun RavenDbStore?.session_or_exception(): IDocumentSession =
    this?.openSession() ?: throw Error("Store is null")
