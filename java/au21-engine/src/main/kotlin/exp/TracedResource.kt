package exp

import au21.engine.framework.utils.date_to_iso
import io.opentelemetry.api.trace.*
import io.opentelemetry.context.Context
import io.quarkus.logging.Log
import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.Produces
import jakarta.ws.rs.core.MediaType
import java.util.*


@Path("/hello")
class TracedResource {


    @Inject
    lateinit var tracer: Tracer

    val attr1 = "Hello Dave 11"

    @GET
    @Produces(MediaType.TEXT_PLAIN)
    fun hello(): String {
        Log.info(attr1)
        method1()
        return attr1
    }

    fun method1() {
        val span: Span = tracer.spanBuilder("My custom span")
            .setAttribute("attr", Date().date_to_iso())
            .setAttribute("name", attr1)
            .setParent(Context.current().with(Span.current()))
            .setSpanKind(SpanKind.INTERNAL)
            .startSpan()

        Log.warn("method1")
        span.end()
    }

}
