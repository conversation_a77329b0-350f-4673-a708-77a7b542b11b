package exp.template

import au21.engine.generators.resolve_directory
import gg.jte.CodeResolver
import gg.jte.ContentType
import gg.jte.TemplateEngine
import gg.jte.TemplateOutput
import gg.jte.output.StringOutput

class ExpPage(
    val description: String?,
    val title: String
)

fun main() {

    // This is the directory where your .jte files are located.
    val codeResolver: CodeResolver = resolve_directory(ExpPage::class)

    val templateEngine: TemplateEngine = TemplateEngine.create(codeResolver, ContentType.Plain)

    val output: TemplateOutput = StringOutput()

    templateEngine.render(
        "example.jte",
        ExpPage("desc", "title"),
        output
    )
    println(output)
}
