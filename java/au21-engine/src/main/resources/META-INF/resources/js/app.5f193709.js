(function(e){function n(n){for(var o,r,c=n[0],u=n[1],a=n[2],E=0,_=[];E<c.length;E++)r=c[E],Object.prototype.hasOwnProperty.call(i,r)&&i[r]&&_.push(i[r][0]),i[r]=0;for(o in u)Object.prototype.hasOwnProperty.call(u,o)&&(e[o]=u[o]);d&&d(n);while(_.length)_.shift()();return s.push.apply(s,a||[]),t()}function t(){for(var e,n=0;n<s.length;n++){for(var t=s[n],o=!0,r=1;r<t.length;r++){var c=t[r];0!==i[c]&&(o=!1)}o&&(s.splice(n--,1),e=u(u.s=t[0]))}return e}var o={},r={app:0},i={app:0},s=[];function c(e){return u.p+"js/"+({}[e]||e)+"."+{"chunk-7b38282e":"13d4369e"}[e]+".js"}function u(n){if(o[n])return o[n].exports;var t=o[n]={i:n,l:!1,exports:{}};return e[n].call(t.exports,t,t.exports,u),t.l=!0,t.exports}u.e=function(e){var n=[],t={"chunk-7b38282e":1};r[e]?n.push(r[e]):0!==r[e]&&t[e]&&n.push(r[e]=new Promise((function(n,t){for(var o="css/"+({}[e]||e)+"."+{"chunk-7b38282e":"783be326"}[e]+".css",i=u.p+o,s=document.getElementsByTagName("link"),c=0;c<s.length;c++){var a=s[c],E=a.getAttribute("data-href")||a.getAttribute("href");if("stylesheet"===a.rel&&(E===o||E===i))return n()}var _=document.getElementsByTagName("style");for(c=0;c<_.length;c++){a=_[c],E=a.getAttribute("data-href");if(E===o||E===i)return n()}var d=document.createElement("link");d.rel="stylesheet",d.type="text/css",d.onload=n,d.onerror=function(n){var o=n&&n.target&&n.target.src||i,s=new Error("Loading CSS chunk "+e+" failed.\n("+o+")");s.code="CSS_CHUNK_LOAD_FAILED",s.request=o,delete r[e],d.parentNode.removeChild(d),t(s)},d.href=i;var l=document.getElementsByTagName("head")[0];l.appendChild(d)})).then((function(){r[e]=0})));var o=i[e];if(0!==o)if(o)n.push(o[2]);else{var s=new Promise((function(n,t){o=i[e]=[n,t]}));n.push(o[2]=s);var a,E=document.createElement("script");E.charset="utf-8",E.timeout=120,u.nc&&E.setAttribute("nonce",u.nc),E.src=c(e);var _=new Error;a=function(n){E.onerror=E.onload=null,clearTimeout(d);var t=i[e];if(0!==t){if(t){var o=n&&("load"===n.type?"missing":n.type),r=n&&n.target&&n.target.src;_.message="Loading chunk "+e+" failed.\n("+o+": "+r+")",_.name="ChunkLoadError",_.type=o,_.request=r,t[1](_)}i[e]=void 0}};var d=setTimeout((function(){a({type:"timeout",target:E})}),12e4);E.onerror=E.onload=a,document.head.appendChild(E)}return Promise.all(n)},u.m=e,u.c=o,u.d=function(e,n,t){u.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:t})},u.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},u.t=function(e,n){if(1&n&&(e=u(e)),8&n)return e;if(4&n&&"object"===typeof e&&e&&e.__esModule)return e;var t=Object.create(null);if(u.r(t),Object.defineProperty(t,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var o in e)u.d(t,o,function(n){return e[n]}.bind(null,o));return t},u.n=function(e){var n=e&&e.__esModule?function(){return e["default"]}:function(){return e};return u.d(n,"a",n),n},u.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},u.p="/",u.oe=function(e){throw console.error(e),e};var a=window["webpackJsonp"]=window["webpackJsonp"]||[],E=a.push.bind(a);a.push=n,a=a.slice();for(var _=0;_<a.length;_++)n(a[_]);var d=E;s.push([0,"chunk-vendors"]),t()})({0:function(e,n,t){e.exports=t("cd49")},"0311":function(e,n,t){e.exports=t.p+"js/0.e4a3ece1.worker.js"},"16b3":function(e,n,t){"use strict";t.d(n,"a",(function(){return o}));class o{}},"27b9":function(e,n,t){"use strict";t.d(n,"a",(function(){return s}));var o=t("0f9e"),r=t("c571"),i=t("1c82");let s=class extends r["a"]{constructor(){super(...arguments),this.subscriptions=[],this.subs=new Map}subscribe(e,n){this.subscriptions.push({eventName:e,callback:n})}unsubscribe(e,n){const t=this.findSubscription(e,n);this.subscriptions=this.subscriptions.filter(e=>e!==t)}findSubscription(e,n){return this.subscriptions.find(t=>e===t.eventName&&n===t.callback)||null}onEvent(e,n){const t=this.subscriptions.filter(n=>e===n.eventName);t.forEach(e=>e.callback(n))}};s=Object(o["b"])([i["Singleton"],i["OnlyInstantiableByContainer"]],s)},3857:function(e,n,t){"use strict";t.d(n,"a",(function(){return s}));var o=t("0f9e"),r=t("c571"),i=t("1c82");let s=class{constructor(){this.live_store=new r["z"],this.stale_store=new r["G"],console.log("CharlizeStore constructed")}};s=Object(o["b"])([i["Singleton"],i["OnlyInstantiableByContainer"],Object(o["e"])("design:paramtypes",[])],s)},4127:function(e,n,t){"use strict";(function(e){t.d(n,"a",(function(){return d}));var o=t("0f9e"),r=t("c571"),i=t("1c82"),s=t("16b3"),c=t("48a6"),u=t("9ba2"),a=t("4c52"),E=t("1b60"),_=t("3425");let d=class{constructor(){this.config=i["Container"].get(r["h"]),this.handler=i["Container"].get(s["a"]),this.client=i["Container"].get(r["a"]),this.store=i["Container"].get(r["c"]),this.ws=null,this.connected=!1,this.queue=[],this.started=!1}connect(){var n,t;return Object(o["a"])(this,void 0,void 0,(function*(){if(this.started)return;this.started=!0,console.log("this.config:",Object(c["g"])(this.config)),u&&window.addEventListener("beforeunload",e=>{}),this.cmd_worker&&(yield a["a"].terminate(this.cmd_worker)),this.cmd_worker=yield Object(E["a"])(new _["a"](e)),this.config.show_connector_log&&console.log("SocketConnector.connect()"),((null===(n=this.ws)||void 0===n?void 0:n.CONNECTING)||(null===(t=this.ws)||void 0===t?void 0:t.OPEN))&&this.ws.close(),this.ws=new WebSocket(this.config.websocket_url_actual),this.ws.binaryType="arraybuffer",this.ws.onopen=()=>{this.connected=!0,this.queue.forEach(e=>this.publish(e))};this.ws.onmessage=e=>Object(o["a"])(this,void 0,void 0,(function*(){try{const n=yield e.data,t=yield this.cmd_worker(n);t&&(this.config.show_connector_log&&(["SetLiveStore","AddElements"].includes(t.command)||console.log("received from socket: "+Object(c["g"])(t))),this.handler.handle(t))}catch(e){console.error({e:e})}})),this.ws.onclose=e=>{this.connected=!1,console.log("SocketConnector is closed. Reconnect will be attempted in 1 second.",e.reason),setTimeout(()=>{this.store.seconds_since_last_message_received++,this.connect()},1e3)},this.ws.onerror=e=>{console.error("SocketConnector encountered error: ",{err:e},"Closing socket"),this.ws.close()}}))}publish(e){try{if(e.session_id=this.config.session_id,this.connected){const n=JSON.stringify(e);this.ws.send(n),this.config.show_connector_log&&(console.log(">>> sending engine command:"),console.log(Object(c["g"])({command:e})),console.log("<<<"))}else this.queue.push(e),this.config.show_connector_log&&console.log("not connected, putting message on the queue:",JSON.stringify(e,null,2))}catch(n){console.error({e:n})}}};d=Object(o["b"])([i["Singleton"],i["OnlyInstantiableByContainer"],Object(o["e"])("design:paramtypes",[])],d)}).call(this,t("0311"))},"48a6":function(e,n,t){"use strict";t.d(n,"c",(function(){return r})),t.d(n,"s",(function(){return i})),t.d(n,"p",(function(){return s})),t.d(n,"a",(function(){return c})),t.d(n,"e",(function(){return u})),t.d(n,"d",(function(){return a})),t.d(n,"f",(function(){return E})),t.d(n,"l",(function(){return d})),t.d(n,"m",(function(){return l})),t.d(n,"h",(function(){return h})),t.d(n,"j",(function(){return m})),t.d(n,"n",(function(){return O})),t.d(n,"k",(function(){return R})),t.d(n,"o",(function(){return A})),t.d(n,"i",(function(){return T})),t.d(n,"q",(function(){return N})),t.d(n,"r",(function(){return S})),t.d(n,"t",(function(){return C})),t.d(n,"g",(function(){return b})),t.d(n,"b",(function(){return p}));var o=t("9ec3");function r(e,n,t=!1){return Array.apply(null,Array(n)).map((n,o)=>e(Number(o+(t?1:0))))}const i=(e,n)=>-1===e.indexOf(n)?[...e,n]:e.filter(e=>e!==n),s=(e,n)=>{const t=Math.pow(10,(n-e).toFixed().length-1),r=Math.ceil(e/t)*t,i=Math.ceil(n/t)*t;return Object(o["range"])(r,i,t)};t("0f9e"),t("e8c8");class c{constructor(){this.resolved=!1,this.promise=new Promise(e=>{this.resolveCallback=e})}resolve(e){this.resolved=!0,this.resolveCallback(e)}}function u(){return-1!==window.navigator.userAgent.indexOf("MSIE ")||-1!==navigator.userAgent.indexOf("Trident")}function a(e,n=0){e||(e="0");const[t,o]=e.split(".");if(!n)return t;let r=Array.from(o||"");const i=n-r.length;return i>0&&(r=[...r,...Array(i).fill("0")]),i<0&&(r.length=n),`${t}.${r.join("")}`}const E=()=>!0;var _=t("0b30");const d=(e=100)=>Math.ceil(Math.random()*(e+1)),l=(e=100)=>d(e)+"",h=()=>Object(o["sample"])([!0,!1]),m=e=>{let n,t=0;for(const o in e)Math.random()<1/++t&&(n=o);return n},O=(e=5)=>{let n="";const t="abcdefghijklmnopqrstuvwxyz";for(let o=0;o<e;o++)n+=t.charAt(Math.floor(Math.random()*t.length));return n},R=e=>Object(o["sample"])(e),A=()=>Date.now()+d(1e6),T=()=>Object(_["a"])(A(),"MM/dd/yyyy hh:mm:ss"),N=e=>{e.scrollTop=e.scrollHeight};var f=t("c89c");const S=(e=0)=>new Promise(n=>setTimeout(n,e));function C(){const e="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split("");let n,t=0;return Object(o["range"])(36).map(o=>8==o||13==o||18==o||23==o?"-":14==o?"4":(t<=2&&(t=33554432+16777216*Math.random()|0),n=15&t,t>>=4,e[19==o?3&n|8:n])).join("").slice(-12)}const D=(e,n)=>void 0===n?"undefined":n,b=e=>"\n"+f["render"](JSON.parse(JSON.stringify(e||{},D)));var g=t("9869");const p=new g["default"]({data(){return{scrollbar_size:0,height:Math.max(window.innerHeight,document.body.clientHeight),width:Math.max(window.innerWidth,document.body.clientHeight)}},created(){this.scrollbar_size=(()=>{const e=window.document.createElement("div"),n=window.document.createElement("div");e.style.width="100px",e.style.overflowX="scroll",n.style.width="100px",window.document.body.appendChild(e),window.document.body.appendChild(n);const t=e.offsetHeight-n.offsetHeight;return window.document.body.removeChild(e),window.document.body.removeChild(n),t})(),window.addEventListener("resize",()=>this.refresh()),this.refresh()},methods:{refresh(){this.height=Math.max(window.innerHeight,document.body.clientHeight),this.width=Math.max(window.innerWidth,document.body.clientHeight)}}})},"90be":function(e,n,t){"use strict";t.d(n,"a",(function(){return a}));var o,r=t("0f9e"),i=t("1c82"),s=t("666f"),c=t.n(s),u=t("c571");(function(e){e[e["RED_GREEN"]=0]="RED_GREEN",e[e["PINK_GREEN"]=1]="PINK_GREEN"})(o||(o={}));let a=class{constructor(){this.current_theme=o.RED_GREEN,this.au_purple="hsl(262, 71%, 72%)",this.au_round=this.au_purple,this.au_red="hsl(0, 92%, 75%)",this.sell_pink_4="hsl(331, 95%, 70%)",this.au_green="hsl(120, 47%, 64%)",this.buy_green_4="hsl(91, 74%, 60%)",this.au_blue="hsl(187, 50%, 60%)",this.au_blue_bright="hsla(187, 73.2%, 70.8%, 1)",this.au_text_color="ccc"}switch_theme(){this.current_theme=this.current_theme===o.RED_GREEN?o.PINK_GREEN:o.RED_GREEN}order_bright(e){switch(e){case u["B"].BUY:return this.au_buy_bright();case u["B"].SELL:return this.au_sell_bright();default:return"hsl(0, 0%, 90%)"}}order_quantity_text_color(e){switch(e){case u["B"].BUY:return this.au_buy();case u["B"].SELL:return this.au_sell();default:return this.au_none()}}order_quantity_opposite_text_color(e){switch(e){case u["B"].BUY:return this.au_sell();case u["B"].SELL:return this.au_buy();default:return this.au_none()}}au_sell(){return this.current_theme===o.RED_GREEN?this.au_red:this.sell_pink_4}au_sell_bright(){return this.current_theme===o.RED_GREEN?"hsl(0, 100%, 60%)":"hsl(331, 100%, 75%)"}au_sell_dim(){return c()(this.au_sell()).alpha(.5).hex()}au_sell_dimmedX2(){return c()(this.au_sell()).alpha(.15).hex()}au_sell_dark(){return c()(this.au_sell()).luminance(.1).hex()}au_buy(){return this.current_theme===o.RED_GREEN?this.au_green:this.buy_green_4}au_buy_bright(){return this.current_theme===o.RED_GREEN?"hsl(120, 100%, 50%)":"hsl(91, 84%, 70%)"}au_buy_dimmed(){return c()(this.au_buy()).alpha(.5).hex()}au_buy_dimmedX2(){return c()(this.au_buy()).alpha(.15).hex()}au_buy_dark(){return c()(this.au_buy()).luminance(.1).hex()}au_none(){return"hsl(0,0%,90%)"}au_match(){return this.current_theme===o.RED_GREEN?this.au_blue:this.au_blue_bright}au_match_dimmed(){return c()(this.au_match()).alpha(.3).hex()}au_match_dimmedX2(){return c()(this.au_match()).alpha(.15).hex()}au_match_dark(){return c()(this.au_match()).luminance(.1).hex()}au_excess(){return"hsl(50, 10%, 60%)"}getPriceColor(e){switch(e){case u["D"].DOWN:return this.au_sell();case u["D"].UP:return this.au_buy();default:return this.au_text_color}}};a=Object(r["b"])([i["Singleton"],i["OnlyInstantiableByContainer"]],a)},c571:function(e,n,t){"use strict";t.d(n,"b",(function(){return r})),t.d(n,"i",(function(){return i})),t.d(n,"E",(function(){return s})),t.d(n,"d",(function(){return c})),t.d(n,"e",(function(){return u})),t.d(n,"k",(function(){return E})),t.d(n,"A",(function(){return d})),t.d(n,"B",(function(){return l})),t.d(n,"D",(function(){return h})),t.d(n,"o",(function(){return T})),t.d(n,"s",(function(){return N})),t.d(n,"u",(function(){return S})),t.d(n,"C",(function(){return p})),t.d(n,"f",(function(){return U})),t.d(n,"I",(function(){return w})),t.d(n,"J",(function(){return L})),t.d(n,"K",(function(){return v})),t.d(n,"L",(function(){return y})),t.d(n,"U",(function(){return M})),t.d(n,"V",(function(){return B})),t.d(n,"W",(function(){return k})),t.d(n,"X",(function(){return j})),t.d(n,"Y",(function(){return x})),t.d(n,"Z",(function(){return H})),t.d(n,"M",(function(){return F})),t.d(n,"N",(function(){return W})),t.d(n,"O",(function(){return Y})),t.d(n,"P",(function(){return V})),t.d(n,"Q",(function(){return q})),t.d(n,"R",(function(){return J})),t.d(n,"S",(function(){return X})),t.d(n,"T",(function(){return K})),t.d(n,"c",(function(){return z})),t.d(n,"z",(function(){return $})),t.d(n,"G",(function(){return Q})),t.d(n,"a",(function(){return Z})),t.d(n,"F",(function(){return ee["a"]})),t.d(n,"g",(function(){return ne["a"]})),t.d(n,"h",(function(){return se}));var o,r,i,s,c,u,a,E,_,d,l,h,m,O,R,A,T,N,f,S,C,D,b,g,p,I,U,P;t("ab33");function G(e,n,t){return{session_id:"",simplename:e,classname:n,command:t}}(function(e){e["ABSOLUTE"]="ABSOLUTE",e["RATIO"]="RATIO"})(o||(o={})),function(e){e["AUCTIONEER_BROADCAST"]="AUCTIONEER_BROADCAST",e["AUCTIONEER_TO_TRADER"]="AUCTIONEER_TO_TRADER",e["TRADER_TO_AUCTIONEER"]="TRADER_TO_AUCTIONEER",e["SYSTEM_BROADCAST"]="SYSTEM_BROADCAST",e["SYSTEM_TO_TRADER"]="SYSTEM_TO_TRADER",e["SYSTEM_TO_AUCTIONEER"]="SYSTEM_TO_AUCTIONEER"}(r||(r={})),function(e){e["OPENED"]="OPENED",e["CLOSED"]="CLOSED"}(i||(i={})),function(e){e["BROWSER_UNLOADED"]="BROWSER_UNLOADED",e["COMPANY_DELETED"]="COMPANY_DELETED",e["COMPANY_NAME_EDITED"]="COMPANY_NAME_EDITED",e["FORCED_OFF"]="FORCED_OFF",e["LOGIN_FROM_ANOTHER_BROWSER"]="LOGIN_FROM_ANOTHER_BROWSER",e["SERVER_REBOOT"]="SERVER_REBOOT",e["SERVER_SWEPT_STALE_SESSION"]="SERVER_SWEPT_STALE_SESSION",e["SIGNED_OFF"]="SIGNED_OFF",e["USER_EDITED"]="USER_EDITED",e["USER_DELETED"]="USER_DELETED"}(s||(s={})),function(e){e["AUCTIONEER"]="AUCTIONEER",e["TRADER"]="TRADER"}(c||(c={})),function(e){e["HIDE"]="HIDE",e["UNHIDE"]="UNHIDE",e["DELETE"]="DELETE"}(u||(u={})),function(e){e["DISENGAGED"]="DISENGAGED",e["ENGAGED"]="ENGAGED"}(a||(a={})),function(e){e["CREATE"]="CREATE",e["READ"]="READ",e["UPDATE"]="UPDATE",e["DELETE"]="DELETE",e["ADD"]="ADD",e["REMOVE"]="REMOVE",e["CLEAR"]="CLEAR"}(E||(E={})),function(e){e["label"]="label",e["GT"]="GT",e["GE"]="GE"}(_||(_={})),function(e){e["MANUAL"]="MANUAL",e["DEFAULT"]="DEFAULT",e["MANDATORY"]="MANDATORY"}(d||(d={})),function(e){e["BUY"]="BUY",e["SELL"]="SELL",e["NONE"]="NONE"}(l||(l={})),function(e){e["UP"]="UP",e["DOWN"]="DOWN"}(h||(h={})),function(e){e["LT"]="LT",e["LE"]="LE",e["NONE"]="NONE"}(m||(m={})),function(e){e["ALL"]="ALL",e["FIRST_ROUND"]="FIRST_ROUND",e["ELIGIBILITY"]="ELIGIBILITY"}(O||(O={})),function(e){e["ALERT"]="ALERT",e["OBJECT"]="OBJECT",e["ARRAY_ITEM"]="ARRAY_ITEM"}(R||(R={})),function(e){e["NORMAL"]="NORMAL",e["WARNING"]="WARNING",e["ERROR"]="ERROR"}(A||(A={})),function(e){e["STARTING_PRICE_NOT_SET"]="STARTING_PRICE_NOT_SET",e["STARTING_PRICE_SET"]="STARTING_PRICE_SET",e["STARTING_PRICE_ANNOUNCED"]="STARTING_PRICE_ANNOUNCED",e["ROUND_OPEN_ALL_ORDERS_NOT_IN"]="ROUND_OPEN_ALL_ORDERS_NOT_IN",e["ROUND_OPEN_ALL_ORDERS_IN"]="ROUND_OPEN_ALL_ORDERS_IN",e["ROUND_CLOSED_NOT_AWARDABLE"]="ROUND_CLOSED_NOT_AWARDABLE",e["ROUND_CLOSED_AWARDABLE"]="ROUND_CLOSED_AWARDABLE",e["AUCTION_CLOSED"]="AUCTION_CLOSED"}(T||(T={})),function(e){e["SETUP"]="SETUP",e["STARTING_PRICE_ANNOUNCED"]="STARTING_PRICE_ANNOUNCED",e["ROUND_OPEN"]="ROUND_OPEN",e["ROUND_CLOSED"]="ROUND_CLOSED",e["AUCTION_CLOSED"]="AUCTION_CLOSED"}(N||(N={})),function(e){e["MANUAL"]="MANUAL",e["MINIMUM"]="MINIMUM"}(f||(f={})),function(e){e["HEARTBEAT"]="HEARTBEAT",e["SET_STARTING_PRICE"]="SET_STARTING_PRICE",e["ANNOUNCE_STARTING_PRICE"]="ANNOUNCE_STARTING_PRICE",e["START_AUCTION"]="START_AUCTION",e["CLOSE_ROUND"]="CLOSE_ROUND",e["REOPEN_ROUND"]="REOPEN_ROUND",e["NEXT_ROUND"]="NEXT_ROUND",e["AWARD_AUCTION"]="AWARD_AUCTION"}(S||(S={})),function(e){e["GREEN"]="GREEN",e["ORANGE"]="ORANGE",e["RED"]="RED"}(C||(C={})),function(e){e["NOT_OPEN"]="NOT_OPEN",e["GREEN"]="GREEN",e["ORANGE"]="ORANGE",e["RED"]="RED"}(D||(D={})),function(e){e["BEFORE_ANNOUNCE_TIME"]="BEFORE_ANNOUNCE_TIME",e["BEFORE_START_TIME"]="BEFORE_START_TIME",e["AUCTION_HAS_STARTED"]="AUCTION_HAS_STARTED"}(b||(b={})),function(e){e["INCREASE"]="INCREASE",e["DECREASE"]="DECREASE"}(g||(g={})),function(e){e["CREDITOR_AUCTIONEER_PAGE"]="CREDITOR_AUCTIONEER_PAGE",e["CREDITOR_TRADER_PAGE"]="CREDITOR_TRADER_PAGE",e["HOME_PAGE"]="HOME_PAGE",e["LOGIN_PAGE"]="LOGIN_PAGE",e["SESSION_PAGE"]="SESSION_PAGE",e["USER_PAGE"]="USER_PAGE",e["BH_AUCTIONEER_PAGE"]="BH_AUCTIONEER_PAGE",e["BH_SETUP_PAGE"]="BH_SETUP_PAGE",e["BH_TRADER_PAGE"]="BH_TRADER_PAGE",e["DE_AUCTIONEER_PAGE"]="DE_AUCTIONEER_PAGE",e["DE_SETUP_PAGE"]="DE_SETUP_PAGE",e["DE_TRADER_PAGE"]="DE_TRADER_PAGE",e["MR_AUCTIONEER_PAGE"]="MR_AUCTIONEER_PAGE",e["MR_SETUP_PAGE"]="MR_SETUP_PAGE",e["MR_TRADER_PAGE"]="MR_TRADER_PAGE",e["TE_AUCTIONEER_PAGE"]="TE_AUCTIONEER_PAGE",e["TE_SETUP_PAGE"]="TE_SETUP_PAGE",e["TE_TRADER_PAGE"]="TE_TRADER_PAGE",e["TO_AUCTIONEER_PAGE"]="TO_AUCTIONEER_PAGE",e["TO_SETUP_PAGE"]="TO_SETUP_PAGE",e["TO_TRADER_PAGE"]="TO_TRADER_PAGE"}(p||(p={})),function(e){e["SUCCESS"]="SUCCESS",e["INFO"]="INFO",e["WARNING"]="WARNING",e["AUCTIONEER_MESSAGE"]="AUCTIONEER_MESSAGE",e["TRADER_MESSAGE"]="TRADER_MESSAGE",e["SYSTEM_MESSAGE"]="SYSTEM_MESSAGE",e["ORDER_CONFIRMATION"]="ORDER_CONFIRMATION"}(I||(I={})),function(e){e["ALERT"]="ALERT",e["NOTIFICATION"]="NOTIFICATION"}(U||(U={})),function(e){e["CommandSucceeded"]="CommandSucceeded",e["ShowMessage"]="ShowMessage",e["TerminateSession"]="TerminateSession",e["NetworkDown"]="NetworkDown",e["NetworkUp"]="NetworkUp",e["SetLiveStore"]="SetLiveStore",e["AddElements"]="AddElements"}(P||(P={}));const w=e=>G("AuctionRowCommand","au21.engine.domain.common.commands.AuctionRowCommand",e),L=e=>G("AuctionSelectCommand","au21.engine.domain.common.commands.AuctionSelectCommand",e),v=e=>G("CompanyDeleteCommand","au21.engine.domain.common.commands.CompanyDeleteCommand",e),y=e=>G("CompanySaveCommand","au21.engine.domain.common.commands.CompanySaveCommand",e),M=e=>G("LoginCommand","au21.engine.domain.common.commands.LoginCommand",e),B=e=>G("MessageSendCommand","au21.engine.domain.common.commands.MessageSendCommand",e),k=e=>G("PageSetCommand","au21.engine.domain.common.commands.PageSetCommand",e),j=e=>G("SessionTerminateCommand","au21.engine.domain.common.commands.SessionTerminateCommand",e),x=e=>G("UserDeleteCommand","au21.engine.domain.common.commands.UserDeleteCommand",e),H=e=>G("UserSaveCommand","au21.engine.domain.common.commands.UserSaveCommand",e),F=e=>G("DeAuctionAwardCommand","au21.engine.domain.de.commands.DeAuctionAwardCommand",e),W=e=>G("DeAuctionSaveCommand","au21.engine.domain.de.commands.DeAuctionSaveCommand",e),Y=e=>G("DeCreditSetCommand","au21.engine.domain.de.commands.DeCreditSetCommand",e),V=e=>G("DeFlowControlCommand","au21.engine.domain.de.commands.DeFlowControlCommand",e),q=e=>G("DeOrderSubmitCommand","au21.engine.domain.de.commands.DeOrderSubmitCommand",e),J=e=>G("DeRoundHistoryCommand","au21.engine.domain.de.commands.DeRoundHistoryCommand",e),X=e=>G("DeTradersAddCommand","au21.engine.domain.de.commands.DeTradersAddCommand",e),K=e=>G("DeTradersRemoveCommand","au21.engine.domain.de.commands.DeTradersRemoveCommand",e);class z{constructor(){this.time=null,this.seconds_since_last_message_received=0}}class $ extends z{constructor(){super(...arguments),this.auction_rows=[],this.companies=[],this.counterparty_credits=[],this.de_auction=null,this.session_user=null,this.time=null,this.users=[]}}class Q{constructor(){this.stale_de_matrix_rounds=[]}}class Z{}var ee=t("4127"),ne=t("16b3"),te=t("0f9e"),oe=t("48a6"),re=t("1c82");const ie=(e,n)=>"ws://localhost:4040/socket/"+n;let se=class{constructor(){this.session_id=Object(oe["t"])(),this.DISCONNECT_RELOAD_MSEC=15e3,this.NODE_ENV="production",this.SESSION_PING_INTERVAL=5e3,this.SHOW_CONNECTOR_LOG="true",this.VUE_APP_LOGINS=Object({NODE_ENV:"production",BASE_URL:"/",FOOTER_TEXT:"Copyright 2011-2021 Auctionologies LLC, Version: 0.0.9",SHOW_CONNECTOR_LOG:"true",WEBSOCKET_URL:"ws://localhost:4040/socket",APP_MODE:"app",VUE_APP_SHOW_DEBUG:"true"}).VUE_APP_LOGINS,this.VUE_APP_LOG_MESSAGES=Object({NODE_ENV:"production",BASE_URL:"/",FOOTER_TEXT:"Copyright 2011-2021 Auctionologies LLC, Version: 0.0.9",SHOW_CONNECTOR_LOG:"true",WEBSOCKET_URL:"ws://localhost:4040/socket",APP_MODE:"app",VUE_APP_SHOW_DEBUG:"true"}).VUE_APP_LOG_MESSAGES,this.VUE_APP_SHOW_DEBUG="true",this.WEBSOCKET_URL="ws://localhost:4040/socket",this.is_production="production"===this.NODE_ENV,this.show_connector_log="true"===this.SHOW_CONNECTOR_LOG,this.show_debug="true"===this.VUE_APP_SHOW_DEBUG}setURL(e,n){this.websocket_url_actual=`${ie(e,this.session_id)}?browser_name=${n.name}&browser_version=${n.version}&browser_os=${n.os}`}};se=Object(te["b"])([re["Singleton"]],se)},cd49:function(e,n,t){"use strict";t.r(n);t("ab33");var o=t("1c82"),r=t("c571"),i=t("3857"),s=t("27b9"),c=t("0f9e"),u=(t("faf8"),t("b76b"));function a(e,n){return E(e,n)}function E(e,n){performance.now();const t=Object(u["b"])(e,n);performance.now();Object(u["a"])(e,t)}let _=class extends r["g"]{constructor(){super(...arguments),this.client=o["Container"].get(s["a"]),this.store=o["Container"].get(i["a"]),this.showLog=["true",void 0].includes("true"),this.handlers={CommandSucceeded:e=>{this.client.onEvent("CommandSucceeded",e)},ShowMessage:e=>{this.client.onEvent("ShowMessage",e)},TerminateSession:e=>{this.client.onEvent("TerminateSession",e)},NetworkDown:e=>{},NetworkUp:e=>{},SetLiveStore:e=>{a(this.store.live_store,e.store)},AddElements:e=>{const n=this.store.stale_store;if("DeMatrixRoundElement"===e.path)if(e.elements){const t=e.elements,o=t.map(e=>e.round_number);n.stale_de_matrix_rounds=n.stale_de_matrix_rounds.filter(e=>!o.includes(e.round_number)),t.forEach(e=>n.stale_de_matrix_rounds.push(e))}else this.store.stale_store.stale_de_matrix_rounds=[]}}}handle(e){const n=this.handlers[e.command];n?n(e):alert("Unable to find handler for command: "+e.command)}};_=Object(c["b"])([o["Singleton"],o["OnlyInstantiableByContainer"]],_);var d=t("7e44"),l=t("48a6");d["f"].directive("test",(e,n)=>{Object(l["f"])()&&e.setAttribute("test-data",n.arg)});var h=t("90be"),m=t("9588");o["Container"].configure({bind:r["a"],to:s["a"]},{bind:h["a"]},{bind:r["c"],to:i["a"]},{bind:r["g"],to:_},{bind:r["h"],to:r["h"]},{bind:r["F"]});const O=o["Container"].get(r["h"]),R=o["Container"].get(r["F"]),{name:A,version:T,os:N}=Object(m["detect"])();O.setURL(window.location,{name:A,version:T,os:N}),R.connect().catch(console.error),console.log("************* ClientConnectorConfig **************"),console.log(Object(l["g"])(O)),console.log("**************************************************");const f=o["Container"].get(i["a"]);window.remoteStore=f,console.log("APP_MODE=app"),t.e("chunk-7b38282e").then(t.bind(null,"fa9a"))}});