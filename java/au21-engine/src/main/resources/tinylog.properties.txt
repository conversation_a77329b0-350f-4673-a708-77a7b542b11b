

#writer               = json
## optional
#writer.level         = debug
## required, absolute or relative path
#writer.file          = log.json
## short for {level}
#writer.field.level   = level
#writer.field.source  = {class}.{method}()
## short for {message}
#writer.field.message = message
## optional
#writer.charset       = UTF-8
## optional, default: false
#writer.append        = true
## optional, default: false
#writer.buffered      = true

writer1=console
#writer1.exception = unpack: java.lang.RuntimeException
writer1.exception = unpack: java.lang.Exception
writer1.format={date:yyyy-MM-dd HH:mm:ss:S} {level}: {class} - {message}
# NB: set to info (or warning) for production !
writer1.level=info
# writer1.level=trace
##writer1.level=error


#writer2               = json
## optional
#writer2.level         = debug
## required, absolute or relative path
#writer2.file          = log.json
## short for {level}
#writer2.field.level   = level
#writer2.field.source  = {class}.{method}()
## short for {message}
#writer2.field.message = message
## optional
#writer2.charset       = UTF-8
## optional, default: false
#writer2.append        = true
## optional, default: false
#writer2.buffered      = true

#GELF
# https://github.com/chaudhryfaisal/tinylog2-gelf
# couldn't find the jar on maven, no current tinylog gelf
