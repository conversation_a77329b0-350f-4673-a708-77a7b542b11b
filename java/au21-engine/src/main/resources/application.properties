#quarkus.test.continuous-testing=enabled

## ++++++++++++++++++++++++++++++++++++++++++++++++++++
# ALL CONFIGS: https://quarkus.io/guides/all-config
# GOOD REFERENCE: https://quarkus.io/guides/config-reference
# ++++++++++++++++++++++++++++++++++++++++++++++++++++

# ============================================
# Redis settings
# NOTE: using Lettuce, not Quarkus/Mutiny/Vertx
# ============================================
#
# quarkus.redis.hosts=redis://localhost:6379
# quarkus.redis.health.enabled=true
#REDIS_URL=redis://localhost:6379
#ENGINE_INPUT_CHANNEL=ENGINE_INPUT_CHANNEL
#ENGINE_OUTPUT_CHANNEL=ENGINE_OUTPUT_CHANNEL


# ============================================
# Objectdb settings
# ============================================
#
# (1) OBJECTDB_ACTIVATION_CODE:
# - If not set here, then it must be passed in as an environment variable.
# - it is specific to each host machine.

OBJECTDB_ACTIVATION_CODE=A812-7Y7H-GR1Y-1JT0-91KK

# (2) OBJECTDB_DB_HOME:
# - this is relative to: <user.home>
# - NB: with docker this is relative to: /root/ !!# (2) Docker:
# - ie: with docker, the volume to map is:  /root/<OBJECTDB_DB_HOME>
# - The database, logs, and config will all be under this directory relative to user home.
#
OBJECTDB_DB_HOME=db/objectdb
#
# (3) OBJECTDB_CONFIG_TEMPLATE_PATH
# - the configuration template is read at startup
# - variables are substituted and it is saved the location specified in OBJECTDB_CONFIG_GENERATED_PATH
# - this template is relative to src/main/resources:
OBJECTDB_CONFIG_TEMPLATE_PATH=config/objectdb/au21-engine.objectdb.template.xml
#
# (4) OBJECTDB_CONFIG_GENERATED_PATH:
# - generated from above template
# - save relative to <user.home>/<OBJECTDB_DB_HOME>
# - ie: for docker this is relative to: /root/<OBJECTDB_DB_HOME>/
OBJECTDB_CONFIG_GENERATED_PATH=config/objectdb/au21-engine.objectdb.generated.xml
#
# (5) OBJECTDB_URL:
# In addition to location, the type of database is determined by the OBJECTDB_URL:
#
# Jan 4, 2021 note: the default is now test.mem, use an environment variable
#   - override with eg: au21-engine.db
#   - or use client/server
#
# (a) if OBJECTDB_URL ends in .mem then an in-memory database is created,
#   - this is used for example in build.gradle under test {}
#
#OBJECTDB_URL=test.mem
#
# (b) else if OBJECTDB_URL starts with "objectdb://" then is client/server mode
#   - and is created under /db in the server directory
# OBJECTDB_URL=objectdb://localhost:6136/dev.odb;user=admin;password=admin
#
# (c) else an embedded database is created as follows
# - /db is prepended
# - and the db is created relative to: <user.home>/<OBJECTDB_DB_HOME>/<OBJECTDB_URL>/db
# - eg: if OBJECTDB_DB_HOME=objectdb,
# - and OBJECTDB_URL=au21-engine.odb
# - and this is a docker container,
# - then the database will be: /root/objectdb/db/au21-engine.odb
#
# CURRENT DEPLOYMENT: (use environment variable to override!)
#
OBJECTDB_URL=test.odb
# OBJECTDB_URL=au21-engine.odb

# (6) OBJECTDB_ACTIVATION_CODE:
#
# OBJECTDB_ACTIVATION_CODE is set in environment: ie: .env, or docker-compose file
# Ryzen March 9, 2021:
#OBJECTDB_ACTIVATION_CODE=A812-7Y7H-GR1Y-1JT0-91KK
#
# (7) client/server username/password:
# - only needed for client/server
# OBJECTDB_ADMIN_PASSWORD=
# OBJECTDB_ADMIN_USER=

# ============================================
## JACOCO: trying to get graceful shutdown
# ============================================

quarkus.shutdown.timeout=2

#------------------------------------------
# General Application Settings
#------------------------------------------
CREATE_SAMPLE_DB=true
%dev.HEARTBEAT=OFF
%prod.HEARTBEAT=ON

quarkus.live-reload.instrumentation=true

quarkus.http.port=4040
quarkus.http.test-port=0
quarkus.http.enable-compression=true

quarkus.http.cors.enabled=true
%dev.quarkus.http.cors.origins=/.*/
%prod.quarkus.http.cors.origins=https://your-production-domain.com,http://localhost:4040
%test.quarkus.http.cors.origins=/.*/

%test.quarkus.http.auth.basic=false
%test.quarkus.security.users.embedded.enabled=false

# Add both paths if needed
quarkus.http.auth.permission.ws-permit.paths=/socket/*,/auction-socket/*
quarkus.http.auth.permission.ws-permit.policy=permit

quarkus.smallrye-graphql.ui.always-include=true

quarkus.http.access-log.enabled=false
quarkus.http.record-request-start-time=false

quarkus.application.name=au21-engine


#------------------------------------------
# Open Telemetry
# - you can either use the combined settings with overrides
# - or the individual Complete settings
#------------------------------------------

### --- COMBINED SETTINGS --------------------------------------------
# IE: common settings, with overrides for Seq, LTGM, SigNoz etc
### --------------------------------------------------------------------

#
## COMMON:
quarkus.otel.service.name=${quarkus.application.name}
## DM: experimenting with intrumenting:
#quarkus.otel.instrument.jvm-metrics=true
#quarkus.otel.instrument.http-server-metrics=true
#quarkus.otel.instrument.rest=true
#quarkus.otel.instrument.resteasy=true
#quarkus.otel.instrument.vertx-event-bus=true
#quarkus.otel.instrument.vertx-http=true
#quarkus.micrometer.binder.jvm=true


#quarkus.otel.logs.enabled=true
#quarkus.otel.logs.exporter=otlp
#quarkus.otel.metrics.enabled=true
#quarkus.otel.metrics.exporter=otlp
#quarkus.otel.traces.enabled=true
#quarkus.otel.traces.exporter=otlp
#
## turn off for testing.
## TODO: should we just set enabled to false???
#%test.quarkus.otel.metrics.exporter=none
#%test.quarkus.otel.logs.exporter=none
#%test.quarkus.otel.traces.exporter=none

#
## see below for overrides
#quarkus.otel.exporter.otlp.logs.endpoint=http://localhost:4317
#quarkus.otel.exporter.otlp.metrics.endpoint=http://localhost:4317
#quarkus.otel.exporter.otlp.traces.endpoint=http://localhost:4317

## LTGM overrides:
##quarkus.log.json.include-mdc=true
#
## SIGNOZ overrides:
##quarkus.otel.exporter.otlp.headers=Authorization=Bearer <your_token>
#
## SEQ overrides:
##quarkus.otel.exporter.otlp.headers=X-Seq-ApiKey=<your-seq-api-key>
##quarkus.otel.exporter.otlp.logs.endpoint=http://localhost:5341/ingest/otlp/v1/logs
##quarkus.otel.exporter.otlp.logs.protocol=http/protobuf
##quarkus.otel.exporter.otlp.traces.endpoint=http://localhost:5341/ingest/otlp/v1/traces
##quarkus.otel.exporter.otlp.traces.protocol=http/protobuf


### --- INDIVIDUAL COMPLETE SETTINGS -----------------------------------
# UNCOMMENT FOR THE FULL CONFIG (ie: Comment out the COMBINED ABOVE)
### --------------------------------------------------------------------

## 1) Jaeger Tracing only (these are set above in the defaults)
# - NB FOR JAEGER YOU JUST NEED THESE TWO:
quarkus.otel.exporter.otlp.traces.endpoint=http://localhost:4317
%test.quarkus.otel.traces.exporter=none

# Disable OTLP exporter in development and test modes to avoid connection errors
# This keeps OpenTelemetry SDK active (spans generated, trace IDs in logs)
# but disables export to external collector when not available
# Can be toggled in Quarkus Dev UI under OpenTelemetry section
%dev.quarkus.otel.exporter.otlp.enabled=false
%test.quarkus.otel.exporter.otlp.enabled=false

## 2) LTGM (loki, tempo, grafana) - working
## Enable OpenTelemetry Tracing (implicitly enabled by quarkus-opentelemetry usually, but explicit is fine)
#quarkus.otel.traces.eusp.enabled.enabled=true
## Use the OTLP exporter for traces
#quarkus.otel.traces.exporter=otlp
## Send traces via OTLP/gRPC to the OTel Collector
#quarkus.otel.exporter.otlp.traces.endpoint=http://localhost:4317
#
## Enable OpenTelemetry Log Exporting
#quarkus.otel.logs.enabled=true
## Use the OTLP exporter for logs
#quarkus.otel.logs.exporter=otlp
## Send logs via OTLP/gRPC to the OTel Collector
#quarkus.otel.exporter.otlp.logs.endpoint=http://localhost:4317
#
## Enable OpenTelemetry Metrics (as you already have)
#quarkus.otel.metrics.enabled=true
## Use the OTLP exporter for metrics (required for Mimir via Collector)
#quarkus.otel.metrics.exporter=otlp
## Send metrics via OTLP/gRPC to the OTel Collector
#quarkus.otel.exporter.otlp.metrics.endpoint=http://localhost:4317
#
## Disable OTel export during tests (as you already have)
#%test.quarkus.otel.traces.exporter=none
#%test.quarkus.otel.logs.exporter=none
#%test.quarkus.otel.metrics.exporter=none
#
## Optional: Configure logging format if needed
## quarkus-logging-json helps, but OTel export might handle structure.
## Ensure Trace ID and Span ID are included for correlation.
## Quarkus OTel integration usually injects these automatically into MDC
## The default console log format often includes MDC fields.
#quarkus.log.console.format=%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%c{3.}] (%t) %X{traceId}/%X{spanId} %s%e%n
## For JSON logging, ensure MDC fields are included if not by default
## quarkus.log.json.include-mdc=true # Might be default
#
## Optional: Set the service name explicitly for OTel
#quarkus.otel.service.name=${quarkus.application.name}

# 3.a) Joeger and Seq
#
## --- OpenTelemetry Common Settings ---
## Explicitly set the service name used by OTel
#quarkus.otel.service.name=${quarkus.application.name}
#
## --- OpenTelemetry Tracing -> Jaeger ---
## Enable Tracing (usually default if extension present, but good to be explicit)
#quarkus.otel.traces.enabled=true
## Use the OTLP exporter for traces
#quarkus.otel.traces.exporter=otlp
## Endpoint for Jaeger's OTLP gRPC receiver (defaults to grpc)
#quarkus.otel.exporter.otlp.traces.endpoint=http://localhost:4317
#
## --- OpenTelemetry Logging -> Seq ---
## Enable OTel Log exporting
#quarkus.otel.logs.enabled=true
## Use the OTLP exporter for logs
#quarkus.otel.logs.exporter=otlp
## Endpoint for Seq's OTLP HTTP receiver
## NOTE: Port 5341 and the path /ingest/otlp/v1/logs are common defaults for Seq OTLP/HTTP.
##       Verify this path in your Seq instance under Settings -> Ingestion -> API Keys / OTLP.
#quarkus.otel.exporter.otlp.logs.endpoint=http://localhost:5341/ingest/otlp/v1/logs
## Protocol for logs - MUST be http/protobuf for the HTTP endpoint
#quarkus.otel.exporter.otlp.logs.protocol=http/protobuf
## Headers for Seq - Add your Seq API Key here if required
## Replace <your-seq-api-key> with an actual API key from Seq Settings -> API Keys
#quarkus.otel.exporter.otlp.headers=X-Seq-ApiKey=<your-seq-api-key>
#
## --- OpenTelemetry Metrics -> Disabled ---
## Disable Metrics (as neither Jaeger nor Seq are primary metric backends)
#quarkus.otel.metrics.enabled=false
## Or just disable export:
## quarkus.otel.metrics.exporter=none
#
## --- Test Profile Overrides ---
## Disable OTel export during tests
#%test.quarkus.otel.traces.exporter=none
#%test.quarkus.otel.logs.exporter=none
#%test.quarkus.otel.metrics.exporter=none
#

### 3.b) Seq logging and tracing:
## --- Application Settings ---
#
## --- OpenTelemetry Common Settings ---
## Explicitly set the service name used by OTel
#
#
## --- OpenTelemetry Tracing -> Seq ---
## Enable Tracing
#quarkus.otel.traces.enabled=true
## Use the OTLP exporter
#quarkus.otel.traces.exporter=otlp
## Endpoint for Seq's OTLP HTTP receiver for TRACES
## NOTE: Verify this path in your Seq settings!
#quarkus.otel.exporter.otlp.traces.endpoint=http://localhost:5341/ingest/otlp/v1/traces
## Protocol MUST be http/protobuf for Seq's HTTP endpoint
#quarkus.otel.exporter.otlp.traces.protocol=http/protobuf
#
## --- OpenTelemetry Logging -> Seq ---
## Enable OTel Log exporting
#quarkus.otel.logs.enabled=true
## Use the OTLP exporter
#quarkus.otel.logs.exporter=otlp
## Endpoint for Seq's OTLP HTTP receiver for LOGS
## NOTE: Verify this path in your Seq settings!
#quarkus.otel.exporter.otlp.logs.endpoint=http://localhost:5341/ingest/otlp/v1/logs
## Protocol MUST be http/protobuf for Seq's HTTP endpoint
#quarkus.otel.exporter.otlp.logs.protocol=http/protobuf
#
## --- Common OTLP Exporter Settings for Seq ---
## Headers for Seq - Add your Seq API Key here (used for both logs and traces)
## Replace <your-seq-api-key> with an actual API key from Seq Settings -> API Keys
#quarkus.otel.exporter.otlp.headers=X-Seq-ApiKey=<your-seq-api-key>
#
## --- OpenTelemetry Metrics -> Disabled ---
## Disable Metrics export (Seq isn't primarily a metrics backend)
#quarkus.otel.metrics.exporter=none
## Or disable collection entirely:
## quarkus.otel.metrics.enabled=false
#
## --- Test Profile Overrides ---
## Disable OTel export during tests
#%test.quarkus.otel.traces.exporter=none
#%test.quarkus.otel.logs.exporter=none
#%test.quarkus.otel.metrics.exporter=none

## 4) Signoz
#
## --- OpenTelemetry Common Settings ---
## Explicitly set the service name used by OTel
#quarkus.otel.service.name=${quarkus.application.name}
#
## --- OpenTelemetry Tracing -> SigNoz ---
#quarkus.otel.traces.enabled=true
## Use the OTLP exporter
#quarkus.otel.traces.exporter=otlp
## Endpoint for SigNoz's OTLP gRPC receiver (protocol on 4317 defaults to grpc)
#quarkus.otel.exporter.otlp.traces.endpoint=http://localhost:4317
#
## --- OpenTelemetry Logging -> SigNoz ---
#quarkus.otel.logs.enabled=true
## Use the OTLP exporter
#quarkus.otel.logs.exporter=otlp
## Endpoint for SigNoz's OTLP gRPC receiver (same endpoint for all signals)
#quarkus.otel.exporter.otlp.logs.endpoint=http://localhost:4317
#
## --- OpenTelemetry Metrics -> SigNoz ---
#quarkus.otel.metrics.enabled=true
## Use the OTLP exporter
#quarkus.otel.metrics.exporter=otlp
## Endpoint for SigNoz's OTLP gRPC receiver (same endpoint for all signals)
#quarkus.otel.exporter.otlp.metrics.endpoint=http://localhost:4317
#
## --- Optional: Authentication (If needed for your SigNoz setup) ---
## If your SigNoz OTLP endpoint requires headers (e.g., for multi-tenancy or auth)
## quarkus.otel.exporter.otlp.headers=Authorization=Bearer <your_token>
#
## --- Test Profile Overrides ---
## Disable OTel export during tests
#%test.quarkus.otel.traces.exporter=none
#%test.quarkus.otel.logs.exporter=none
#%test.quarkus.otel.metrics.exporter=none

## 5) New Relic
## - https://aistudio.google.com/prompts/new_chat?model=gemini-2.5-pro-exp-03-25&utm_source=deepmind.google&utm_medium=referral&utm_campaign=gdm&utm_content=
## - https://grok.com/chat/c5b5e33c-8e9e-47dc-970e-76beac631ed4


#------------------------------------------
# Logging Configuration
#------------------------------------------

# --- Default Logging Settings (Applied to all profiles unless overridden) ---
quarkus.log.level=INFO
quarkus.log.console.enable=true
quarkus.log.console.format=%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%c{3.}] (%t) %X{traceId}/%X{spanId} %s%e%n
# Default format is text, default JSON is disabled.


# --- Dev Profile Logging ---
# Inherits quarkus.log.level=INFO (or set %dev.quarkus.log.level=DEBUG if needed)
# Explicitly DISABLE the JSON console logging for the dev profile using the CORRECT key.
%dev.quarkus.log.console.json.enabled=false

# --- Test Profile Logging ---
# Inherits quarkus.log.level=INFO (or set %test.quarkus.log.level=DEBUG if needed)
# Explicitly DISABLE the JSON console logging for the test profile using the CORRECT key.
%test.quarkus.log.console.json.enabled=false

# --- Prod Profile Logging ---
# Inherits quarkus.log.level=INFO
# Explicitly ENABLE the JSON console logging ONLY for the prod profile using the CORRECT key.
%prod.quarkus.log.console.json.enabled=true
# Keep prod logs compact (not pretty-printed) for log aggregators.
%prod.quarkus.log.console.json.pretty-print=false
# Ensure the text format isn't applied to prod by the default setting (might be redundant now, but safe).
# Clear the text format for prod
%prod.quarkus.log.console.format=

# --- Category Specific Levels ---


# --- Category Specific Levels ---
# (Keep comments on separate lines)
# Categories are currently commented out as requested.
## Application specific code (DEBUG for dev/test)
#%dev,test.quarkus.log.category."au21.engine".level=INFO
## Add your specific package
#%dev,test.quarkus.log.category."org.acme".level=DEBUG
#
## Standard WebSocket implementation (DEBUG for dev/test when needed)
## Use DEBUG when troubleshooting WebSocket connection/lifecycle issues
#%dev,test.quarkus.log.category."io.undertow.websockets.jsr".level=INFO
#
## Vert.x HTTP (might be verbose, use DEBUG when needed)
#%dev,test.quarkus.log.category."io.quarkus.vertx.http".level=INFO
#
## Security (DEBUG useful for test profile)
#%test.quarkus.log.category."io.quarkus.security".level=DEBUG
## Usually INFO is enough unless debugging Elytron
#%test.quarkus.log.category."org.wildfly.security".level=INFO

# --- Optional File Logging (Example for Prod) ---
# %prod.quarkus.log.file.enable=true
# %prod.quarkus.log.file.path=/var/log/quarkus-app.log # Or your desired path
# %prod.quarkus.log.file.rotation.max-file-size=10M
# %prod.quarkus.log.file.rotation.max-backup-index=5
# %prod.quarkus.log.file.format=%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%c{1.}] (%t) %s%e%n
# %prod.quarkus.log.file.level=INFO # Log INFO and above to file in prod
