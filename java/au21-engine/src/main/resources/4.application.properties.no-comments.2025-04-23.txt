CREATE_SAMPLE_DB=true
%dev.HEARTBEAT=OFF
%prod.HEARTBEAT=ON

quarkus.shutdown.timeout=2

OBJECTDB_ACTIVATION_CODE=A812-7Y7H-GR1Y-1JT0-91KK

OBJECTDB_DB_HOME=db/objectdb

OBJECTDB_CONFIG_TEMPLATE_PATH=config/objectdb/au21-engine.objectdb.template.xml

OBJECTDB_CONFIG_GENERATED_PATH=config/objectdb/au21-engine.objectdb.generated.xml

OBJECTDB_URL=test.odb

quarkus.websocket.dispatch-to-worker=true

quarkus.package.jar.enabled=true

quarkus.live-reload.instrumentation=true

quarkus.http.port=4040
%test.quarkus.http.port=4040
quarkus.http.test-port=4040
quarkus.http.enable-compression=true

quarkus.http.cors.origins=*

quarkus.http.auth.basic=true
quarkus.security.users.embedded.enabled=true
quarkus.security.users.embedded.plain-text=true
quarkus.security.users.embedded.users.book=vuebook
quarkus.security.users.embedded.roles.book=book
quarkus.http.auth.policy.book-policy.roles-allowed=book
quarkus.http.auth.permission.book-permission.paths=/book/*
quarkus.http.auth.permission.book-permission.policy=book-policy

quarkus.smallrye-graphql.ui.always-include=true

quarkus.log.console.enable=true

%dev.quarkus.log.console.level=TRACE
%dev.quarkus.log.console.format.regexp=%-5p %c{1}:%L - %s%e%n

%dev.quarkus.log.seq.batch-size=1
%prod.quarkus.log.seq.batch-size=10
quarkus.log.console.json.pretty=false

quarkus.http.access-log.enabled=false
quarkus.http.record-request-start-time=false

quarkus.log.console.format=%d{HH:mm:ss} %-5p traceId=%X{traceId}, parentId=%X{parentId}, spanId=%X{spanId}, sampled=%X{sampled} [%c{2.}] (%t) %s%e%n
