<!-- ObjectDB Configuration NOTE: 6/13/24: I moved this file here so that it
could be found by ci, because user.dir defined differently there? -->
<objectdb>

    <general>
        <temp path="$temp/ObjectDB" threshold="200mb"/>
        <network inactivity-timeout="0"/>
        <url-history size="50" user="true" password="true"/>
        <log path="$objectdb/log/" max="100mb" stdout="false" stderr="true"/>
        <log-archive path="$objectdb/log/archive/" retain="900"/>
        <!--		<logger name="*" level="fatal" />-->
        <!--		<logger name="*" level="error" />-->
        <!--		<logger name="*" level="warning" />-->
        <logger name="*" level="info"/>
        <!--		<logger name="*" level="trace" />-->
        <!--		<logger name="*" level="debug" />-->
    </general>

    <database>
        <!--		<size initial="5mb" resize="5mb" page="2kb" />-->
        <size initial="256kb" resize="256kb" page="2kb"/>
        <recovery enabled="true" sync="false" path="." max="200mb"/>
        <recording enabled="false" sync="false" path="." mode="write"/>
        <locking version-check="true"/> <!-- Mar 5 2021 -->
        <processing cache="500mb" max-threads="5"/> <!-- Mar 5 2021 -->
        <index-update enabled="true" priority="40"/> <!-- Mar 5 2021 -->
        <query-cache results="500mb" programs="500"/>
        <extensions drop="tmp" memory="mem"/>

        <!-- xps jan 2021 -->
        <!--<activation code="A812-7Y7H-GR1Y-VK4V-NBR1" />-->
        <!-- March 2021: dev1.auctionologies.com -->
        <activation code="A812-7Y7H-GR1Y-1JT0-91KK"/>
        <activation code="A812-7Y7H-GR1Y-N4TZ-2EQQ"/>
        <!-- this will be replaced: -->
        <activation code="OBJECTDB_ACTIVATION_CODE"/>

    </database>

    <entities>
        <!--		<enhancement agent="true" reflection="warning" />-->
        <!--		<cache ref="strong" level2="200mb" />-->
        <!--		<fetch hollow="true" />-->
        <!--		<persist serialization="false" />-->
        <!--		<cascade-persist always="auto" on-persist="true" on-commit="true" />-->
        <!--		<dirty-tracking arrays="false" />-->

        <enhancement agent="true" reflection="warning"/> <!--allowed for hot reload of entities changes, should be error-->
        <cache ref="strong" level2="500mb"/>
        <!--		<fetch hollow="false" />-->
        <fetch hollow="false"/>
        <persist serialization="true"/> <!--allowed for hot reload of entities changes, should be false-->
        <cascade-persist always="auto" on-persist="true" on-commit="true"/>
        <dirty-tracking arrays="true"/>
    </entities>

    <schema>
    </schema>

    <server>
        <connection port="6136" max="100"/>
        <data path="$objectdb/db"/>
    </server>

    <users>
        <user username="OBJECTDB_ADMIN_USER" password="OBJECTDB_ADMIN_PASSWORD">
            <dir path="/" permissions="access,modify,create,delete"/>
        </user>
    </users>

    <ssl enabled="false">
        <server-keystore path="$objectdb/ssl/server-kstore" password="pwd"/>
        <client-truststore path="$objectdb/ssl/client-tstore" password="pwd"/>
    </ssl>

</objectdb>
