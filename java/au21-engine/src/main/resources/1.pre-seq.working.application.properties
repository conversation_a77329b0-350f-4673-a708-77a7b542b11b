#quarkus.test.continuous-testing=enabled

## ++++++++++++++++++++++++++++++++++++++++++++++++++++
# ALL CONFIGS: https://quarkus.io/guides/all-config
# GOOD REFERENCE: https://quarkus.io/guides/config-reference
# ++++++++++++++++++++++++++++++++++++++++++++++++++++

# ============================================
# APPLICATION settings:
# ============================================

CREATE_SAMPLE_DB=true
%dev.HEARTBEAT=OFF
%prod.HEARTBEAT=ON

# ============================================
## JACOCO: trying to get graceful shutdown
# ============================================

quarkus.shutdown.timeout=2


# ============================================
# C. Redis settings
# NOTE: using Lettuce, not Quarkus/Mutiny/Vertx
# ============================================
#
# quarkus.redis.hosts=redis://localhost:6379
# quarkus.redis.health.enabled=true
#REDIS_URL=redis://localhost:6379
#ENGINE_INPUT_CHANNEL=ENGINE_INPUT_CHANNEL
#ENGINE_OUTPUT_CHANNEL=ENGINE_OUTPUT_CHANNEL
#
# ============================================
# D. Objectdb settings
# ============================================
#
# (1) OBJECTDB_ACTIVATION_CODE:
# - If not set here, then it must be passed in as an environment variable.
# - it is specific to each host machine.

OBJECTDB_ACTIVATION_CODE=A812-7Y7H-GR1Y-1JT0-91KK

# (2) OBJECTDB_DB_HOME:
# - this is relative to: <user.home>
# - NB: with docker this is relative to: /root/ !!# (2) Docker:
# - ie: with docker, the volume to map is:  /root/<OBJECTDB_DB_HOME>
# - The database, logs, and config will all be under this directory relative to user home.
#
OBJECTDB_DB_HOME=db/objectdb
#
# (3) OBJECTDB_CONFIG_TEMPLATE_PATH
# - the configuration template is read at startup
# - variables are substituted and it is saved the location specified in OBJECTDB_CONFIG_GENERATED_PATH
# - this template is relative to src/main/resources:
OBJECTDB_CONFIG_TEMPLATE_PATH=config/objectdb/au21-engine.objectdb.template.xml
#
# (4) OBJECTDB_CONFIG_GENERATED_PATH:
# - generated from above template
# - save relative to <user.home>/<OBJECTDB_DB_HOME>
# - ie: for docker this is relative to: /root/<OBJECTDB_DB_HOME>/
OBJECTDB_CONFIG_GENERATED_PATH=config/objectdb/au21-engine.objectdb.generated.xml
#
# (5) OBJECTDB_URL:
# In addition to location, the type of database is determined by the OBJECTDB_URL:
#
# Jan 4, 2021 note: the default is now test.mem, use an environment variable
#   - override with eg: au21-engine.db
#   - or use client/server
#
# (a) if OBJECTDB_URL ends in .mem then an in-memory database is created,
#   - this is used for example in build.gradle under test {}
#
#OBJECTDB_URL=test.mem
#
# (b) else if OBJECTDB_URL starts with "objectdb://" then is client/server mode
#   - and is created under /db in the server directory
# OBJECTDB_URL=objectdb://localhost:6136/dev.odb;user=admin;password=admin
#
# (c) else an embedded database is created as follows
# - /db is prepended
# - and the db is created relative to: <user.home>/<OBJECTDB_DB_HOME>/<OBJECTDB_URL>/db
# - eg: if OBJECTDB_DB_HOME=objectdb,
# - and OBJECTDB_URL=au21-engine.odb
# - and this is a docker container,
# - then the database will be: /root/objectdb/db/au21-engine.odb
#
# CURRENT DEPLOYMENT: (use environment variable to override!)
#
OBJECTDB_URL=test.odb
# OBJECTDB_URL=au21-engine.odb

# (6) OBJECTDB_ACTIVATION_CODE:
#
# OBJECTDB_ACTIVATION_CODE is set in environment: ie: .env, or docker-compose file
# Ryzen March 9, 2021:
#OBJECTDB_ACTIVATION_CODE=A812-7Y7H-GR1Y-1JT0-91KK
#
# (7) client/server username/password:
# - only needed for client/server
# OBJECTDB_ADMIN_PASSWORD=
# OBJECTDB_ADMIN_USER=


# ============================================
#     Q U A R K U S   S E T T I N G S
# ============================================


# https://docs.honeycomb.io/getting-data-in/opentelemetry/
#quarkus.opentelemetry.tracer.exporter.otlp.endpoint=http://localhost:55680
# quarkus.opentelemetry.tracer.exporter.otlp.headers=Authorization=Bearer 2d35ce08-f385-4d4f-81a3-e67d852624c7
#, x-honeycomb-dataset=ryzen

quarkus.websocket.dispatch-to-worker=true

## packackaging:
# default, ie: jar = fast-jar
quarkus.package.jar.enabled=true

# TODO: research live-reloading in remote containers!
# - not sure live reload is working in 1.13.7
quarkus.live-reload.instrumentation=true
#quarkus.live-reload.password=changeit
#quarkus.live-reload.url=http://my.cluster.host.com:8080
#

# quarkus.application.name=weather-consumer-${HOSTNAME:1}
quarkus.http.port=4040
quarkus.http.enable-compression=true

# Root path: (NOT WORKING AS EXPECTED, using instead: --strip-components=1 ??
# quarkus.http.root-path=/charlize

# If GraphQL UI should be included every time. By default this is only included when the application is running in dev mode.
#cors:
quarkus.http.cors=true
quarkus.http.cors.origins=*
#quarkus.http.cors.origins=http://127.0.0.1:19090,http://127.0.0.1:8081
#quarkus.http.cors.headers=accept, authorization, content-type, x-requested-with
#quarkus.http.cors.methods=GET, OPTIONS, PUT, DELETE

# ============================================
# SECURITY
# ============================================

quarkus.http.auth.basic=true
quarkus.security.users.embedded.enabled=true
quarkus.security.users.embedded.plain-text=true
# user: book, password: vuebook, role: book
quarkus.security.users.embedded.users.book=vuebook
quarkus.security.users.embedded.roles.book=book
# book-policy: role=book, path=/book/*
quarkus.http.auth.policy.book-policy.roles-allowed=book
quarkus.http.auth.permission.book-permission.paths=/book/*
quarkus.http.auth.permission.book-permission.policy=book-policy

# ============================================
# MISC:
# ============================================

quarkus.smallrye-graphql.ui.always-include=true

# ============================================
# LOGS:
# ============================================


# Enable console logging
quarkus.log.console.enable=true

## For dev we can exclude all the json fields by setting json=false:
%dev.quarkus.log.console.level=TRACE
%dev.quarkus.log.console.json=false
%dev.quarkus.log.console.format=%-5p %c{1}:%L - %s%e%n
#%dev.quarkus.log.console.format=%d{yyyy-MM-dd HH:mm:ss} %-5p %c{1}:%L - %s%e%n

# For prod we can have Seq (requires json)
%prod.quarkus.log.console.level=INFO
%prod.quarkus.log.console.json=true
%prod.quarkus.log.console.json.pretty-print=false

# gelf -> seq-input-gelf -> seq
%dev.quarkus.log.handler.gelf.enabled=true
%prod.quarkus.log.handler.gelf.enabled=true
%ci.quarkus.log.handler.gelf.enabled=false
quarkus.log.handler.gelf.host=localhost
quarkus.log.handler.gelf.port=12201

# Enable file logging if required
# quarkus.log.file.enable=true
# quarkus.log.file.path=logs/quarkus.log
# quarkus.log.file.format=%d{yyyy-MM-dd HH:mm:ss} %-5p [%s] (%t) %c{1}.%M(%L) - %s%e%n
# quarkus.log.file.level=INFO

# Access logs:
quarkus.http.access-log.enabled=false
quarkus.http.record-request-start-time=false

# TODO: ?OLD? currently turning off console.logging because we get every
#  heartbeat trace!
# java.util.logging.manager # TODO for testing, see: https://quarkus.io/guides/getting-started-testing
# dev.quarkus.log.console.json=false
# Console:
#test.quarkus.log.console.enabled=false
#dev.quarkus.log.console.enabled=true
#prod.quarkus.log.console.enabled=true
#quarkus.log.console.format=%d{HH:mm:ss} %-5p traceId=%X{traceId}, spanId=%X{spanId}, sampled=%X{sampled} [%c{2.}] (%t) %s%e%n
#quarkus.log.console.format=%d{HH:mm:ss} %-5p traceId=%X{traceId}, parentId=%X{parentId}, spanId=%X{spanId}, sampled=%X{sampled} [%c{2.}] (%t) %s%e%n


# ============================================
# TRACING:
# ============================================

quarkus.application.name=au21-engine
quarkus.otel.resource.attributes=service.name=test-service
quarkus.opentelemetry.resource.attributes=service.name=your-service-name
quarkus.opentelemetry.enabled=true
quarkus.opentelemetry.tracer.enabled=true
# Additional logging configuration for tracing
quarkus.log.console.format=%d{HH:mm:ss} %-5p traceId=%X{traceId}, parentId=%X{parentId}, spanId=%X{spanId}, sampled=%X{sampled} [%c{2.}] (%t) %s%e%n

# Jaeger (working);
#quarkus.opentelemetry.tracer.exporter.otlp.endpoint=http://localhost:4317

# Seq: not working! #
#quarkus.otel.exporter.otlp.protocol=http/protobuf
#quarkus.otel.exporter.otlp.endpoint=http://dev1.auctionologies.com:5341/ingest/otlp/v1/traces
#quarkus.otel.sdk.disabled=false
#quarkus.otel.exporter.otlp.traces.ssl-enabled=false
#quarkus.otel.exporter.otlp.headers=Content-Type=application/x-protobuf
#quarkus.otel.traces.sampler=always_on
#quarkus.otel.resource.attributes=service.name=test-service

# OpenTelemetry (Jun 4, 2024): these are for the grafana image (not sure working):
#quarkus.otel.exporter.otlp.traces.protocol=http/protobuf
#%test.quarkus.otel.exporter.otlp.traces.endpoint=http://${quarkus.otel-collector.url}
#%dev.quarkus.otel.exporter.otlp.traces.endpoint=http://${quarkus.otel-collector.url}
#%prod.quarkus.otel.exporter.otlp.traces.endpoint=http://localhost:4318
#
## Micrometer OTLP registry
#%test.quarkus.micrometer.export.otlp.url=http://${quarkus.otel-collector.url}/v1/metrics
#%dev.quarkus.micrometer.export.otlp.url=http://${quarkus.otel-collector.url}/v1/metrics
#%prod.quarkus.micrometer.export.otlp.url=http://localhost:4318/v1/metrics
#quarkus.otel.exporter.otlp.traces.protocol=grpc
##%test.quarkus.otel.exporter.otlp.traces.endpoint=http://${quarkus.otel-collector.url}:14250
##%dev.quarkus.otel.exporter.otlp.traces.endpoint=http://${quarkus.otel-collector.url}:14250
##%prod.quarkus.otel.exporter.otlp.traces.endpoint=http://localhost:14250
#quarkus.otel.exporter.otlp.traces.endpoint=http://localhost:14250

#quarkus.application.name=myservice
#quarkus.otel.exporter.otlp.traces.endpoint=http://localhost:4317
#quarkus.otel.exporter.otlp.traces.headers=authorization=Bearer my_secret
#quarkus.log.console.format=%d{HH:mm:ss} %-5p traceId=%X{traceId}, parentId=%X{parentId}, spanId=%X{spanId}, sampled=%X{sampled} [%c{2.}] (%t) %s%e%n
#
## Alternative to the console log
#quarkus.http.access-log.pattern="...traceId=%{X,traceId} spanId=%{X,spanId}"

# Aug 31, 2024: Attempt to get Traces to seq:


# NOTE:
# a) Tinylog won't log to GELF, and now Log.info includes class and line
# without anything else, so we don't need Tinylog !
# and console.json logging includes all fields, so Log.info is verbose, and

# json actually unpacked
#
# So, we either need to:
# - custom json logger to exclude fields (can't be confiigured) or
# - extend tinylog to create a GELF logger, or
# - tail with SeqClie, or
# - use quarkus jboss console logging with json pretty print?

# NOTE:
# see: https://community.inkdrop.app/note/c47fbe830e04dc02b1f684240da7971c/note:CkwtWeSp3
# a) Tinylog won't log to GELF, and now Log.info includes class and line
# without anything else, so we don't need Tinylog !
# b) but jboss json console logging includes all fields, so is verbose and also  json actually unpacked
#
# So, we either of these
# - to use json with dev, create a custom json logger to exclude fields
# - or tail with SeqClie, or
