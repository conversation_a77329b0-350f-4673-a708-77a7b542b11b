writer               = json
# optional
writer.level         = debug
# required, absolute or relative path
writer.file          = log.json
# short for {level}
writer.field.level   = level
writer.field.source  = {class}.{method}()
# short for {message}
writer.field.message = message
# optional
writer.charset       = UTF-8
# optional, default: false
writer.append        = true
# optional, default: false
writer.buffered      = true
