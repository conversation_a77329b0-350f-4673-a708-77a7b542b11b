
###
GET http://localhost:8080/hello

<> 2024-02-18T222734.200.txt
<> 2024-02-18T222733.200.txt
<> 2024-02-18T222657.200.txt
<> 2024-02-18T222644.200.txt

###

WEBSOCKET ws://localhost:8080/chat/dave
Content-Type: application-json // Used for content highlighting only

// Highlighting is applied for each message independently.

{
    "message": "Send when the WebSocket connection is opened"
}
=== wait-for-server
{
    "message": "Wait for the server message and send the current message as response"
}
===
{
    "message": "Send after previous message without waiting for the server message"
}
###

GRAPHQL http://localhost:8080/graphql

query {
    say<PERSON><PERSON>(name: "david")
}
