@baseUrl = http://localhost:4040/graphql

### Get GraphQL Schema
GET {{baseUrl}}/schema.graphql

### initDB
GRAPHQL {{baseUrl}}

mutation {
    initDb
}

### recreateDummyDb
GRAPHQL {{baseUrl}}

mutation {
  reCreateDummyDb(
    auction_count:1,
    auctioneer_count:2,
    trader_count:10,
    round_count:4,
    close_last_round:false,
    use_counterparty_credits:false
  )
}


### Heartbeat on
GRAPHQL {{baseUrl}}

mutation {
    heartbeat(on:true)
}

### Heartbeat off
GRAPHQL {{baseUrl}}

mutation {
    heartbeat(on:false)
}

### Auction names
GRAPHQL {{baseUrl}}

query {
   auctions {
        auction_name
   }
}

### ALL AUCTIONS
GRAPHQL {{baseUrl}}

query {
   auctions {
     auction_has_started
     auction_name
     auctioneer_state_text
     closed
     common_state_text
     deleted
     hidden
     id
     notice
     starting_time
     users_that_have_seen_auction {
       person_id
       role_at_auction_item
       username_at_auction_time
     }
     companies_that_have_seen_auction {
       company_id
       longname_at_auction_time
       shortname_at_auction_time
     }
     counterparty_credit_limits {
       borrower {
         company_id
         longname_at_auction_time
         shortname_at_auction_time
       }
       credit_limit
       credit_limit_str
       lender {
         company_id
         longname_at_auction_time
         shortname_at_auction_time
       }
     }
     messages {
       body
       from_label
       message
       message_type
       message_type_label
       timestamp
       timestamp_label
       to_label
     }
   }
 }
