package au21.engine.domain.de.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Company
import au21.engine.domain.common.model.Person
import au21.engine.framework.commands.AlertException
import au21.engine.test.helpers.base.command.CommandTestBase
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.quarkus.test.junit.QuarkusTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@QuarkusTest
internal class DeCreditSetCommandTest : CommandTestBase() {

    lateinit var auctioneerSession: AuSession
    lateinit var lender: Company
    lateinit var borrower: Company
    lateinit var user: Person

    @BeforeEach
    fun setUp() {
        db_init_command()
        auctioneerSession = create_auctioneer_session_and_login()
        lender = create_company(auctioneerSession, "c1")
        borrower = create_company(auctioneerSession, "c2")
    }

    @Test
    fun `throw error if session is not known to the system`() {
        val creditSetCommand = DeCreditSetCommand("123", "1", "2", "$100")
        val exception = shouldThrow<AlertException> {
            handle(AuSession("unknown"), creditSetCommand)
        }
        exception.message shouldBe "no session found with id: unknown"
    }

    @Test
    fun `invalid credit limit value`() {
        val creditSetCommand = DeCreditSetCommand("123", lender.id_str(), borrower.id_str(), "12abc")
        val exception = shouldThrow<AlertException> {
            handle(auctioneerSession, creditSetCommand)
        }
        exception.message shouldBe "Credit limit is not a valid number: 12abc"
    }

    @Test
    fun `auction doesn't exits`() {
        val creditSetCommand = DeCreditSetCommand("123", lender.id_str(), borrower.id_str(), "$100")
        val exception = shouldThrow<AlertException> {
            handle(auctioneerSession, creditSetCommand)
        }
        exception.message shouldBe "Cannot find auction without an id: 123"
    }

    @Test
    fun `auction doesn't have borrower`() {
        val auction = create_auction(auctioneerSession, "test")
        de_traders_add_command(auctioneerSession, auction, listOf(lender))
        val creditSetCommand = DeCreditSetCommand(auction.id_str(), lender.id_str(), borrower.id_str(), "$100")
        val exception = shouldThrow<AlertException> {
            handle(auctioneerSession, creditSetCommand)
        }
        exception.message shouldBe "borrower not in auction"
    }

    @Test
    fun `auction doesn't have lender`() {
        val auction = create_auction(auctioneerSession, "test")
        de_traders_add_command(auctioneerSession, auction, listOf(borrower))
        val creditSetCommand = DeCreditSetCommand(auction.id_str(), lender.id_str(), borrower.id_str(), "$100")
        val exception = shouldThrow<AlertException> {
            handle(auctioneerSession, creditSetCommand)
        }
        exception.message shouldBe "lender not in auction"
    }

    @Test
    fun `successfully set the credit limit`() {
        // arrange
        val auction = create_auction(auctioneerSession, "test")
        de_traders_add_command(auctioneerSession, auction, listOf(borrower, lender))
        val creditSetCommand = DeCreditSetCommand(auction.id_str(), lender.id_str(), borrower.id_str(), "$100")
        // act
        handle(auctioneerSession, creditSetCommand)

        // assert
        val auctionFromDb = findAuction(auction.auction_name)
        auctionFromDb?.de_trading_companies?.find { it.company_id == borrower.id }?.initial_buying_cost_limit shouldBe 100
    }

    @Test
    @Disabled("DeCreditSetCommand: Not fully implemented")
    fun `fail if current credit limit is less than existing `() {
        // arrange
        val auction = create_auction(auctioneerSession, "test")
        de_traders_add_command(auctioneerSession, auction, listOf(borrower, lender))
        val initialCreditSetCommand = DeCreditSetCommand(auction.id_str(), lender.id_str(), borrower.id_str(), "$100")
        // act
        handle(auctioneerSession, initialCreditSetCommand)
        val reducedCreditSetCommand = DeCreditSetCommand(auction.id_str(), lender.id_str(), borrower.id_str(), "$80")

        // assert
        val exception = shouldThrow<AlertException> {
            handle(auctioneerSession, reducedCreditSetCommand)
        }
        exception.message shouldBe "seller: c1 not in auction"
    }
}
