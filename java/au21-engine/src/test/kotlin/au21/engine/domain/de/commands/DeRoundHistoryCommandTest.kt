package au21.engine.domain.de.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Company
import au21.engine.domain.common.model.Person
import au21.engine.domain.de.model.DeAuction
import au21.engine.framework.commands.AlertException
import au21.engine.test.helpers.base.command.CommandTestBase
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.quarkus.test.junit.QuarkusTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

@QuarkusTest
internal class DeRoundHistoryCommandTest : CommandTestBase() {
    lateinit var auctioneerSession: AuSession
    lateinit var first_company: Company
    lateinit var user: Person
    lateinit var auction: DeAuction

    @BeforeEach
    fun setUp() {
        db_init_command()
        auctioneerSession = create_auctioneer_session_and_login()
        first_company = create_company(auctioneerSession, "c1")
        auction = create_auction(auctioneerSession, "test")
    }

    @Test
    fun `throw error if session is not known to the system`() {
        val deRoundHistoryCommand = DeRoundHistoryCommand("123", "1")
        val exception = shouldThrow<AlertException> {
            handle(AuSession("unknown"), deRoundHistoryCommand)
        }
        exception.message shouldBe "no session found with id: unknown"
    }

    @Test
    fun `throw error if session is not auctioneer`() {
        val deRoundHistoryCommand = DeRoundHistoryCommand("123", "1")
        val exception = shouldThrow<AlertException> {
            handle(create_session(), deRoundHistoryCommand)
        }
        exception.message shouldBe "Only auctioneers can do this."
    }

    @Test
    fun `throw error if auction not found`() {
        val deRoundHistoryCommand = DeRoundHistoryCommand("123", "1")
        val exception = shouldThrow<AlertException> {
            handle(auctioneerSession, deRoundHistoryCommand)
        }
        exception.message shouldBe "no auction found with id: 123"
    }

    @Test
    fun `throw error for invalid round number`() {
        val deRoundHistoryCommand = DeRoundHistoryCommand(auction.id_str(), "abc123")
        val exception = shouldThrow<AlertException> {
            handle(auctioneerSession, deRoundHistoryCommand)
        }
        exception.message shouldBe "round_number invalid: abc123"
    }

    @Test
    fun `throw error if round not found`() {
        de_traders_add_command(auctioneerSession, auction, listOf(first_company))
        val deRoundHistoryCommand = DeRoundHistoryCommand(auction.id_str(), "2")
        val exception = shouldThrow<AlertException> {
            handle(auctioneerSession, deRoundHistoryCommand)
        }
        exception.message shouldBe "no round found with number: 2"
    }
}