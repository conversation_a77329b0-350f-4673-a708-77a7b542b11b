package au21.engine.domain.common.commands.actions

import au21.engine.domain.common.commands.LoginAction
import au21.engine.domain.common.commands.LoginCommand
import au21.engine.domain.common.model.AuSession
import au21.engine.test.helpers.base.action.CommonActionTestBase
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.quarkus.test.junit.QuarkusTest
import org.junit.jupiter.api.Test

private val loginCommand = LoginCommand("userName", "1")

@QuarkusTest
class LoginActionTest : CommonActionTestBase() {

    @Test
    fun `check login action attaches the session with user`() {
        val user = create_user("username")
        val session = create_session()

        val loginAction = LoginAction(loginCommand, db, session, user)
        loginAction.mutate()
        val userSession = findSession(session.session_id)
        userSession?.user shouldBe user
        userSession?.socket_state shouldBe AuSession.ClientSocketState.OPENED
    }

    @Test
    fun `terminate existing user session`() {
        val user = create_user("username")
        val session = create_session()

        val loginAction = LoginAction(loginCommand, db, session, user)
        loginAction.mutate()

        val newSession = create_session()
        val againLoginWithNewSession = LoginAction(loginCommand, db, newSession, user)
        againLoginWithNewSession.mutate()

        val dbOldSession = findSession(session.session_id)

        dbOldSession?.termination_time shouldNotBe null
        dbOldSession?.socket_state shouldBe AuSession.ClientSocketState.CLOSED
        dbOldSession?.termination_reason_label shouldBe AuSession.SessionTerminationReason.LOGIN_FROM_ANOTHER_BROWSER.toString()

        val dbNewSession = findSession(newSession.session_id)
        dbNewSession?.user shouldBe user
        dbNewSession?.socket_state shouldBe AuSession.ClientSocketState.OPENED
    }
}