package au21.engine.domain.de.commands

import au21.engine.domain.common.commands.DbInitCommand
import au21.engine.domain.common.model.Company
import au21.engine.domain.de.model.DeAuction
import au21.engine.framework.commands.AlertException
import au21.engine.test.helpers.base.command.CommandTestBase
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.quarkus.test.junit.QuarkusTest
import org.junit.jupiter.api.Test

@QuarkusTest
class DeAuctionSaveCommandTest : CommandTestBase() {

    @Test
    fun `should validate required fields`() {
        val command = createMinimalDeAuctionSaveCommand(
            auction_id = "",
            auction_name = ""
        )
        val session = create_session()
        val result = command.validate(db, session.id.toString())
        result shouldNotBe null
    }

    @Test
    fun `should validate successfully with valid data`() {
        val command = createMinimalDeAuctionSaveCommand(
            auction_id = "test-auction",
            auction_name = "Test Auction"
        )
        val session = create_session()
        val result = command.validate(db, session.id.toString())
        result shouldNotBe null
    }

    @Test
    fun `should execute successfully`() {
        // Initialize database first
        val initCommand = DbInitCommand()
        handle(null, initCommand)

        // Create session after database initialization
        val session = create_session()

        val command = createMinimalDeAuctionSaveCommand(
            auction_id = "test-auction",
            auction_name = "Test Auction"
        )
        val result = handle(session, command)
        result shouldNotBe null
    }

    @Test
    fun `should validate auction_id is not blank`() {
        val command = createMinimalDeAuctionSaveCommand(auction_id = "")

        // Initialize database first
        val initCommand = DbInitCommand()
        handle(null, initCommand)

        // Create session after database initialization
        val session = create_session()

        val exception = shouldThrow<AlertException> {
            handle(session, command)
        }
        exception.message shouldBe "Auction ID cannot be blank"
    }

    @Test
    fun `should validate auction_name is not blank`() {
        val command = createMinimalDeAuctionSaveCommand(auction_name = "")
        val session = create_session()
        val initCommand = DbInitCommand()
        handle(session, initCommand)
        
        val exception = shouldThrow<AlertException> {
            handle(session, command)
        }
        exception.message shouldBe "Auction name cannot be blank"
    }

    @Test
    fun `should execute successfully with valid data`() {
        val command = createValidCommand()
        val session = create_session()
        val initCommand = DbInitCommand()
        handle(session, initCommand)
        
        val result = handle(session, command)
        result shouldNotBe null
    }

    @Test
    fun `should create auction with correct properties`() {
        val command = createValidCommand()
        val session = create_session()
        val initCommand = DbInitCommand()
        handle(session, initCommand)
        
        handle(session, command)
        
        // Verify auction was created
        val auction = db.findFirst<DeAuction> { it.auction_name == command.auction_name }
        auction shouldNotBe null
        auction?.auction_name shouldBe command.auction_name
        // auction?.quantity_label shouldBe command.quantity_label // Uncomment if quantity_label exists in DeAuction
    }
    
    private fun createMinimalDeAuctionSaveCommand(
        auction_id: String = "test-auction",
        auction_name: String = "Test Auction"
    ): DeAuctionSaveCommand {
        return DeAuctionSaveCommand(
            auction_id = auction_id,
            auction_name = auction_name,
            use_counterparty_credits = "true",
            quantity_label = "MW",
            quantity_minimum = "1",
            quantity_step = "1",
            price_change_initial = "0.1",
            price_change_post_reversal = "0.05",
            price_label = "$/MWh",
            price_decimal_places = "2",
            cost_multiplier = "1.0",
            excess_level_0_label = "Level 0",
            excess_level_1_label = "Level 1",
            excess_level_2_label = "Level 2",
            excess_level_3_label = "Level 3",
            excess_level_4_label = "Level 4",
            excess_level_1_quantity = "100",
            excess_level_2_quantity = "200",
            excess_level_3_quantity = "300",
            excess_level_4_quantity = "400",
            starting_price_announcement_mins = "5",
            month_is_1_based = true,
            starting_year = "2024",
            starting_month = "1",
            starting_day = "1",
            starting_hour = "10",
            starting_mins = "0",
            round_red_secs = "30",
            round_orange_secs = "60",
            round_open_min_seconds = "120",
            round_closed_min_secs = "30"
        )
    }
    
    private fun createValidCommand() = DeAuctionSaveCommand(
        auction_id = "test-auction",
        auction_name = "Test Auction",
        use_counterparty_credits = "true",
        quantity_label = "MW",
        quantity_minimum = "1",
        quantity_step = "1",
        price_change_initial = "0.1",
        price_change_post_reversal = "0.05",
        price_label = "$/MWh",
        price_decimal_places = "2",
        cost_multiplier = "1.0",
        excess_level_0_label = "Level 0",
        excess_level_1_label = "Level 1",
        excess_level_2_label = "Level 2",
        excess_level_3_label = "Level 3",
        excess_level_4_label = "Level 4",
        excess_level_1_quantity = "100",
        excess_level_2_quantity = "200",
        excess_level_3_quantity = "300",
        excess_level_4_quantity = "400",
        starting_price_announcement_mins = "5",
        month_is_1_based = true,
        starting_year = "2024",
        starting_month = "1",
        starting_day = "1",
        starting_hour = "10",
        starting_mins = "0",
        round_red_secs = "30",
        round_orange_secs = "60",
        round_open_min_seconds = "120",
        round_closed_min_secs = "30"
    )
}