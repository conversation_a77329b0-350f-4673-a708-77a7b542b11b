package au21.engine.domain.de.validations

import au21.engine.domain.common.model.OrderType
import au21.engine.domain.de.model.DeBidConstraints
import au21.engine.framework.commands.AlertException
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
internal class DeOrderValidationsTest {

    @ParameterizedTest
    @MethodSource("testCases")
    fun `validate de order validations`(data: TestData) {

        assertThrows<AlertException> {
            validate_de_order_against_constraints_and_throw_if_fails(
                data.constraints,
                data.orderType,
                data.orderQuantity,
                data.quantityUnits
            )
        }.message shouldBe data.errorMessage

    }

    private fun testCases() = Stream.of(
        // None order
        TestData(
            constraints = DeBidConstraints(
                max_buy_quantity = 50,
                min_buy_quantity = 0,
                min_sell_quantity = 0,
                max_sell_quantity = 50
            ),
            orderType = OrderType.NONE,
            orderQuantity = 1,
            errorMessage = "Order quantity must be zero if OrderType is None"
        ),
        TestData(
            constraints =
            DeBidConstraints(
                max_buy_quantity = 50,
                min_buy_quantity = 1,
                min_sell_quantity = 0,
                max_sell_quantity = 50
            ),
            orderType = OrderType.NONE,
            orderQuantity = 0,
            errorMessage = "You cannot submit a zero quantity bid. It is less than your min buy quantity of 1 MMlb"
        ),
        TestData(
            constraints =
            DeBidConstraints(
                max_buy_quantity = 50,
                min_buy_quantity = 0,
                min_sell_quantity = 1,
                max_sell_quantity = 50
            ),
            orderType = OrderType.NONE,
            orderQuantity = 0,
            errorMessage = "You cannot submit a zero quantity bid. It is less than your min sell quantity of 1 MMlb"
        ),
        // Buy order
        TestData(
            constraints =
            DeBidConstraints(
                max_buy_quantity = 50,
                min_buy_quantity = 0,
                min_sell_quantity = 1,
                max_sell_quantity = 50
            ),
            orderType = OrderType.BUY,
            orderQuantity = 2,
            errorMessage = "You cannot submit a buy order when you have a minimum sell quantity constraint of (1 MMlb)"
        ),
        TestData(
            constraints =
            DeBidConstraints(
                max_buy_quantity = 50,
                min_buy_quantity = 0,
                min_sell_quantity = 0,
                max_sell_quantity = 50
            ),
            orderType = OrderType.BUY,
            orderQuantity = 52,
            errorMessage = "You cannot buy more than your maximum buy quantity of 50 MMlb"
        ),
        TestData(
            constraints =
            DeBidConstraints(
                max_buy_quantity = 50,
                min_buy_quantity = 10,
                min_sell_quantity = 0,
                max_sell_quantity = 50
            ),
            orderType = OrderType.BUY,
            orderQuantity = 9,
            errorMessage = "You cannot buy less than your minimum buy quantity of 10 MMlb"
        ),
        // Sell order
        TestData(
            constraints =
            DeBidConstraints(
                max_buy_quantity = 50,
                min_buy_quantity = 1,
                min_sell_quantity = 0,
                max_sell_quantity = 50
            ),
            orderType = OrderType.SELL,
            orderQuantity = 2,
            errorMessage = "You cannot submit a sell order when you have a minimum buy quantity constraint of (1 MMlb)"
        ),
        TestData(
            constraints =
            DeBidConstraints(
                max_buy_quantity = 50,
                min_buy_quantity = 0,
                min_sell_quantity = 0,
                max_sell_quantity = 50
            ),
            orderType = OrderType.SELL,
            orderQuantity = 52,
            errorMessage = "You cannot sell more than your maximum sell quantity of 50 MMlb"
        ),
        TestData(
            constraints =
            DeBidConstraints(
                max_buy_quantity = 50,
                min_buy_quantity = 0,
                min_sell_quantity = 10,
                max_sell_quantity = 50
            ),
            orderType = OrderType.SELL,
            orderQuantity = 9,
            errorMessage = "You cannot sell less than your minimum sell quantity of 10 MMlb"
        )
    )

    data class TestData(
        val constraints: DeBidConstraints,
        val orderType: OrderType,
        val orderQuantity: Int,
        val quantityUnits: String = "MMlb",
        val errorMessage: String
    )
}
