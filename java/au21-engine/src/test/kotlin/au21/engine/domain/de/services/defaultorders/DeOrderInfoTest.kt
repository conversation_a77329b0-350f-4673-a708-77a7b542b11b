package au21.engine.domain.de.services.defaultorders

import au21.engine.domain.common.model.OrderSubmissionType
import au21.engine.domain.common.model.OrderType
import au21.engine.domain.de.model.DeBidConstraints
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
internal class DeOrderInfoTest {

    @ParameterizedTest
    @MethodSource("testCases")
    fun `verify de order info based on constraints`(testData: TestData) {
        val deOrderInfo =
            DeOrderInfo.create_default_from_constraints(testData.bidConstraint)

        testData.expedtedDeOrderInfo.submission_type shouldBe deOrderInfo.submission_type
        testData.expedtedDeOrderInfo.order_type shouldBe deOrderInfo.order_type
        testData.expedtedDeOrderInfo.quantity shouldBe deOrderInfo.quantity
    }

    private fun testCases() = Stream.of(
        TestData(
            DeBidConstraints(
                max_buy_quantity = 0,
                min_buy_quantity = 0,
                min_sell_quantity = 0,
                max_sell_quantity = 0
            ),
            DeOrderInfo(OrderSubmissionType.MANDATORY, OrderType.NONE, 0)
        ),
        TestData(
            DeBidConstraints(
                max_buy_quantity = 10,
                min_buy_quantity = 0,
                min_sell_quantity = 0,
                max_sell_quantity = 10
            ),
            DeOrderInfo(OrderSubmissionType.DEFAULT, OrderType.NONE, 0)
        ),
        TestData(
            DeBidConstraints(
                max_buy_quantity = 10,
                min_buy_quantity = 10,
                min_sell_quantity = 0,
                max_sell_quantity = 10
            ),
            DeOrderInfo(OrderSubmissionType.MANDATORY, OrderType.BUY, 10)
        ),
        TestData(
            DeBidConstraints(
                max_buy_quantity = 0,
                min_buy_quantity = 10,
                min_sell_quantity = 0,
                max_sell_quantity = 10
            ),
            DeOrderInfo(OrderSubmissionType.DEFAULT, OrderType.BUY, 10)
        ),
        TestData(
            DeBidConstraints(
                max_buy_quantity = 0,
                min_buy_quantity = 0,
                min_sell_quantity = 20,
                max_sell_quantity = 20
            ),
            DeOrderInfo(OrderSubmissionType.MANDATORY, OrderType.SELL, 20)
        ),
        TestData(
            DeBidConstraints(
                max_buy_quantity = 0,
                min_buy_quantity = 0,
                min_sell_quantity = 20,
                max_sell_quantity = 30
            ),
            DeOrderInfo(OrderSubmissionType.DEFAULT, OrderType.SELL, 20)
        )

    )

    data class TestData(val bidConstraint: DeBidConstraints, val expedtedDeOrderInfo: DeOrderInfo)
}
