package au21.engine.domain.de.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Company
import au21.engine.domain.common.model.Person
import au21.engine.domain.de.model.DeAuction
import au21.engine.framework.commands.AlertException
import au21.engine.test.helpers.base.command.CommandTestBase
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.quarkus.test.junit.QuarkusTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

@QuarkusTest
class DeEligibilitySetCommandTest : CommandTestBase() {
    lateinit var auctioneerSession: AuSession
    lateinit var first_company: Company
    lateinit var user: Person
    lateinit var userSession: AuSession
    lateinit var auction: DeAuction

    @BeforeEach
    fun setUp() {
        db_init_command()
        auctioneerSession = create_auctioneer_session_and_login()
        first_company = create_company(auctioneerSession, "c1")
        auction = create_auction(auctioneerSession, "test")
        user = createUser(auctioneerSession, "u1", first_company)
        userSession = create_session()
        login_command(userSession, "u1", "1")
    }

    @Test
    fun `throw error if session is not known to the system`() {
        val deEligibilitySetCommand = DeEligibilitySetCommand("123", "1", "100", "10")
        val exception = shouldThrow<AlertException> {
            handle(AuSession("unknown"), deEligibilitySetCommand)
        }
        exception.message shouldBe "no session found with id: unknown"
    }

    @Test
    fun `throw error if session is not auctioneer`() {
        val deEligibilitySetCommand = DeEligibilitySetCommand("123", "1", "100", "10")
        val exception = shouldThrow<AlertException> {
            handle(userSession, deEligibilitySetCommand)
        }
        exception.message shouldBe "Only auctioneers can perform this action."
    }

    @Test
    fun `throw error if trader not in auction`() {
        val deEligibilitySetCommand = DeEligibilitySetCommand(auction.id_str(), "1", "100", "10")
        val exception = shouldThrow<AlertException> {
            handle(auctioneerSession, deEligibilitySetCommand)
        }
        exception.message shouldBe "No trader found with company id: 1"
    }

    @Test
    fun `throw error if buyer credit limit is invalid`() {
        de_traders_add_command(auctioneerSession, auction, listOf(first_company))
        val deEligibilitySetCommand = DeEligibilitySetCommand(auction.id_str(), first_company.id_str(), "0.0", "10")
        val exception = shouldThrow<AlertException> {
            handle(auctioneerSession, deEligibilitySetCommand)
        }
        exception.message shouldBe "Buyer credit limit must be a number greater than zero."
    }

    @Test
    fun `throw error if seller quantity limit is invalid`() {
        de_traders_add_command(auctioneerSession, auction, listOf(first_company))
        val deEligibilitySetCommand = DeEligibilitySetCommand(auction.id_str(), first_company.id_str(), "10", "-1")
        val exception = shouldThrow<AlertException> {
            handle(auctioneerSession, deEligibilitySetCommand)
        }
        exception.message shouldBe "Seller quantity limit must be a number greater than or equal to zero."
    }

    @Test
    fun `check trader credit limit are set for the trader in auction`() {
        de_traders_add_command(auctioneerSession, auction, listOf(first_company))
        val deEligibilitySetCommand = DeEligibilitySetCommand(auction.id_str(), first_company.id_str(), "10", "20")
        handle(auctioneerSession, deEligibilitySetCommand)

        // assert
        val auction = findAuction(auction.auction_name)
        auction?.de_trading_companies?.first()?.initial_buying_cost_limit shouldBe 10
        auction?.de_trading_companies?.first()?.initial_selling_quantity_limit shouldBe 20
    }

}
