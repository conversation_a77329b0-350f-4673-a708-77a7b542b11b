package au21.engine.domain.de.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.OrderType
import au21.engine.domain.de.model.DeFlowControlType
import au21.engine.framework.PageName
import au21.engine.framework.commands.AlertException
import au21.engine.test.helpers.base.command.CommandTestBase
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.quarkus.test.junit.QuarkusTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

@QuarkusTest
internal class DeTradersRemoveCommandTest : CommandTestBase() {

    @BeforeEach
    fun initDatabase() {
        db_init_command()
    }

    @Test
    fun `throw error if session is not found`() {
        val deRemoveTraderCommand = DeTradersRemoveCommand("foo", listOf("bar"))
        val exception = shouldThrow<AlertException> {
            handle(AuSession("foo"), deRemoveTraderCommand)
        }
        exception.message shouldBe "no session found with id: foo"
    }

    @Test
    fun `throw error if session is not of auctioneer`() {
        val userSession = create_session()
        val deRemoveTraderCommand = DeTradersRemoveCommand("foo", listOf("bar"))
        val exception = shouldThrow<AlertException> {
            handle(userSession, deRemoveTraderCommand)
        }
        exception.message shouldBe "Only auctioneers can do this."
    }

    @Test
    fun `throw error when auction not found`() {
        val auctioneerSession = create_auctioneer_session_and_login()
        val deRemoveTraderCommand = DeTradersRemoveCommand("foo", listOf("bar"))
        val exception = shouldThrow<AlertException> {
            handle(auctioneerSession, deRemoveTraderCommand)
        }
        exception.message shouldBe "no auction found with id: foo"
    }

    @Test
    fun `remove company with an existing bid out of auction should fail the request`() {
        val auctioneerSession = create_auctioneer_session_and_login()
        val auctionName = "Test"
        val auction = create_auction(auctioneerSession, auctionName)
        val companyOne = create_company(auctioneerSession, "abc")
        val companyTwo = create_company(auctioneerSession, "xyz")
        val u = createUser(auctioneerSession, "u1", companyOne)
        val user_session = create_session()
        login_command(user_session, u.username, u.password)

        // Add trader
        de_traders_add_command(auctioneerSession,auction, listOf(companyOne, companyTwo))

        // Submit bid
        de_flow_control_command(auctioneerSession, auction, DeFlowControlType.SET_STARTING_PRICE, "100.000")
        de_flow_control_command(auctioneerSession, auction, DeFlowControlType.ANNOUNCE_STARTING_PRICE)
        de_flow_control_command(auctioneerSession, auction, DeFlowControlType.START_AUCTION)
        page_set_command(user_session, PageName.DE_TRADER_PAGE)
        auction_select_command(user_session, auction)
        de_order_submit_command(user_session, auction, OrderType.BUY, 1, "0")

        // Remove trader
        val deRemoveTraderCommand = DeTradersRemoveCommand(auction.id_str(), listOf(companyOne.id_str()))
        val exception = shouldThrow<AlertException> {
            handle(auctioneerSession, deRemoveTraderCommand)
        }
        exception.message shouldBe "Unable to remove these bidders as they have already seen this auction: abc"
    }

    @Test
    fun `remove companies out of auction`() {
        val auctioneerSession = create_auctioneer_session_and_login()
        val auctionName = "Test"
        val auction = create_auction(auctioneerSession, auctionName)
        val companyOne = create_company(auctioneerSession, "abc")
        val companyTwo = create_company(auctioneerSession, "xyz")
        val u = createUser(auctioneerSession, "u1", companyOne)
        val user_session = create_session()
        login_command(user_session, u.username, u.password)

        // Add trader
        de_traders_add_command(auctioneerSession,auction, listOf(companyOne, companyTwo))
        page_set_command(user_session, PageName.DE_TRADER_PAGE)

        // Remove trader
        val deRemoveTraderCommand = DeTradersRemoveCommand(auction.id_str(), listOf(companyOne.id_str()))
        handle(auctioneerSession, deRemoveTraderCommand)

        val traders = findAuction(auctionName)?.de_trading_companies!!
        traders shouldHaveSize 1
        traders.component1().shortname_at_auction_time shouldBe companyTwo.shortname
        findSession(user_session.session_id)?.page shouldBe PageName.HOME_PAGE
    }
}
