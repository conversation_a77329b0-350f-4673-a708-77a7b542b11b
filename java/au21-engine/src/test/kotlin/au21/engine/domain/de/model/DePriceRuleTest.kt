package au21.engine.domain.de.model

import au21.engine.domain.common.model.AuUserRole
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
internal class DePriceRuleTest {

    @Test
    fun `when excess is zero and the role is trader return access level zero label`() {
        val accessLevelZeroLabel = "0 label"
        val dePriceRule = DePriceRule(excess_level_0_label = accessLevelZeroLabel)
        val excessLevel = dePriceRule.get_excess_level(0, AuUserRole.TRADER)
        excessLevel shouldBe accessLevelZeroLabel
    }

    @Test
    fun `when excess is zero and the role is auctioneer return access level as zero`() {
        val dePriceRule = DePriceRule(excess_level_0_label = "0 label")
        val excessLevel = dePriceRule.get_excess_level(0, AuUserRole.AUCTIONEER)
        excessLevel shouldBe "0"
    }

    @Test
    fun `when excess is negative and the role is auctioneer return access level as zero`() {
        val dePriceRule = DePriceRule(excess_level_0_label = "0 label")
        val excessLevel = dePriceRule.get_excess_level(-1, AuUserRole.AUCTIONEER)
        excessLevel shouldBe "-"
    }

    @Test
    fun `when excess is negative and the role is trader return access level as zero`() {
        val dePriceRule = DePriceRule(excess_level_0_label = "0 label")
        val excessLevel = dePriceRule.get_excess_level(-1, AuUserRole.TRADER)
        excessLevel shouldBe "0 label"
    }

    private fun dePriceRule(): DePriceRule = DePriceRule(
        excess_level_0_label = "0_LABEL",
        excess_level_1_label = "GREATER_THAN_50",
        excess_level_2_label = "GREATER_THAN_70",
        excess_level_3_label = "GREATER_THAN_90",
        excess_level_4_label = "GREATER_THAN_110",
        excess_level_1_quantity = 50,
        excess_level_2_quantity = 70,
        excess_level_3_quantity = 90,
        excess_level_4_quantity = 110,
    )

    @ParameterizedTest
    @MethodSource("testCases")
    fun `test proper labels returned to corresponding level`(data: TestData) {
        dePriceRule().get_excess_level(data.quantity, AuUserRole.TRADER) shouldBe data.label
    }

    private fun testCases() = Stream.of(
        TestData(-1, "0_LABEL"),
        TestData(0, "0_LABEL"),
        TestData(50, "0_LABEL"),
        TestData(51, "GREATER_THAN_50"),
        TestData(71, "GREATER_THAN_70"),
        TestData(91, "GREATER_THAN_90"),
        TestData(91, "GREATER_THAN_90"),
        TestData(111, "GREATER_THAN_110"),
        TestData(500, "GREATER_THAN_110"),
    )

    data class TestData(val quantity:Int, val label: String)
}