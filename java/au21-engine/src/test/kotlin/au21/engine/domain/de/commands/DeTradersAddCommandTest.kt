package au21.engine.domain.de.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.framework.commands.AlertException
import au21.engine.test.helpers.base.command.CommandTestBase
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.quarkus.test.junit.QuarkusTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

@QuarkusTest
internal class DeTradersAddCommandTest : CommandTestBase() {

    @BeforeEach
    fun initDatabase() {
        db_init_command()
    }

    @Test
    fun `throw error if session is not found`() {
        val deAddTraderCommand = DeTradersAddCommand("foo", listOf("bar"))
        val exception = shouldThrow<AlertException> {
            handle(AuSession("foo"), deAddTraderCommand)
        }
        exception.message shouldBe "no session found with id: foo"
    }

    @Test
    fun `throw error if session is terminated`() {
        val auctioneerSession = create_auctioneer_session_and_login()
        session_terminate_command(auctioneerSession, AuSession.SessionTerminationReason.SIGNED_OFF)
        val deAddTraderCommand = DeTradersAddCommand("foo", listOf("bar"))
        val exception = shouldThrow<AlertException> {
            handle(auctioneerSession, deAddTraderCommand)
        }
        exception.message shouldBe "Session already terminated."
    }

    @Test
    fun `throw error when auction not found`() {
        val auctioneerSession = create_auctioneer_session_and_login()
        val deAddTraderCommand = DeTradersAddCommand("foo", listOf("bar"))
        val exception = shouldThrow<AlertException> {
            handle(auctioneerSession, deAddTraderCommand)
        }
        exception.message shouldBe "no auction found with id: foo"
    }

    @Test
    fun `add multiple companies to auction`() {
        val auctioneerSession = create_auctioneer_session_and_login()
        val auctionName = "Test"
        val auction = create_auction(auctioneerSession, auctionName)
        val companyOne = create_company(auctioneerSession, "abc")
        val companyTwo = create_company(auctioneerSession, "xyz")
        val deAddTraderCommand = DeTradersAddCommand(auction.id_str(), listOf(companyOne.id_str(), companyTwo.id_str()))
        handle(auctioneerSession, deAddTraderCommand)

        val traders = findAuction(auctionName)?.de_trading_companies!!
        traders shouldHaveSize 2
        traders.component1().shortname_at_auction_time shouldBe companyOne.shortname
        traders.component2().shortname_at_auction_time shouldBe companyTwo.shortname
    }

    @Test
    fun `add company to existing list of companies to auction`() {
        val auctioneerSession = create_auctioneer_session_and_login()
        val auctionName = "Test"
        val auction = create_auction(auctioneerSession, auctionName)
        val companyOne = create_company(auctioneerSession, "abc")
        handle(auctioneerSession, DeTradersAddCommand(auction.id_str(), listOf(companyOne.id_str())))

        val companyTwo = create_company(auctioneerSession, "xyz")
        handle(auctioneerSession, DeTradersAddCommand(auction.id_str(), listOf(companyTwo.id_str())))

        val traders = findAuction(auctionName)?.de_trading_companies!!
        traders shouldHaveSize 2
        traders.component1().shortname_at_auction_time shouldBe companyOne.shortname
        traders.component2().shortname_at_auction_time shouldBe companyTwo.shortname
    }
}

