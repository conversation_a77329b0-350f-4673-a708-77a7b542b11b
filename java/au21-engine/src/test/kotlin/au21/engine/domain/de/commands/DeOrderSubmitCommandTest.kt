package au21.engine.domain.de.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Company
import au21.engine.domain.common.model.OrderType
import au21.engine.domain.common.model.Person
import au21.engine.domain.de.model.DeFlowControlType
import au21.engine.framework.PageName
import au21.engine.framework.commands.AlertException
import au21.engine.test.helpers.base.command.CommandTestBase
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.quarkus.test.junit.QuarkusTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

@QuarkusTest
internal class DeOrderSubmitCommandTest : CommandTestBase() {

    lateinit var auctioneerSession: AuSession
    lateinit var company: Company
    lateinit var user: Person
    lateinit var userSession: AuSession

    @BeforeEach
    fun setUp() {
        println("Setting up all the parties: auctioneers, traders")
        db_init_command()
        auctioneerSession = create_auctioneer_session_and_login()
        company = create_company(auctioneerSession, "c1")
        user = createUser(auctioneerSession, "foo", company)
        userSession = create_session()
        login_command(userSession, "foo", "1")
    }

    @Test
    fun `throw error if user is not known to the system`() {
        val orderSubmitCommand = DeOrderSubmitCommand("123", "456", OrderType.BUY, "1", "10")
        val exception = shouldThrow<AlertException> {
            handle(AuSession("unknown"), orderSubmitCommand)
        }
        exception.message shouldBe "no session found with id: unknown"
    }

    @Test
    fun `throw error if session is not from same company id`() {
        val orderSubmitCommand = DeOrderSubmitCommand("123", "456", OrderType.BUY, "1", "10")
        val exception = shouldThrow<AlertException> {
            handle(userSession, orderSubmitCommand)
        }
        exception.message shouldBe "Company id does not match session id."
    }

    @Test
    fun `throw error if session page is not DE trader page`() {
        page_set_command(userSession, PageName.HOME_PAGE)
        val orderSubmitCommand = DeOrderSubmitCommand("123", company.id_str(), OrderType.BUY, "1", "10")
        val exception = shouldThrow<AlertException> {
            handle(userSession, orderSubmitCommand)
        }
        exception.message shouldBe "You can only submit orders from the auction page."
    }

    @Test
    fun `throw error if invalid auction id`() {
        page_set_command(userSession, PageName.HOME_PAGE)
        val orderSubmitCommand = DeOrderSubmitCommand("123", company.id_str(), OrderType.BUY, "1", "10")
        val exception = shouldThrow<AlertException> {
            handle(userSession, orderSubmitCommand)
        }
        exception.message shouldBe "You can only submit orders from the auction page."
    }

    @Test
    fun `throw error if not a trader in auction`() {
        page_set_command(userSession, PageName.DE_TRADER_PAGE)
        val auction = create_auction(auctioneerSession, "Test")
        val orderSubmitCommand = DeOrderSubmitCommand(auction.id_str(), company.id_str(), OrderType.BUY, "1", "10")
        val exception = shouldThrow<AlertException> {
            handle(userSession, orderSubmitCommand)
        }
        exception.message shouldBe "You are not a trader in this auction."
    }

    @Test
    fun `throw error if auction not open for bidding`() {
        page_set_command(userSession, PageName.DE_TRADER_PAGE)
        val auction = create_auction(auctioneerSession, "Test")
        de_traders_add_command(auctioneerSession, auction, listOf(company))
        auction_select_command(userSession, auction)
        de_flow_control_command(auctioneerSession, auction, DeFlowControlType.SET_STARTING_PRICE, "100.000")
        de_flow_control_command(auctioneerSession, auction, DeFlowControlType.ANNOUNCE_STARTING_PRICE)

        val orderSubmitCommand = DeOrderSubmitCommand(auction.id_str(), company.id_str(), OrderType.BUY, "1", "10")
        val exception = shouldThrow<AlertException> {
            handle(userSession, orderSubmitCommand)
        }
        exception.message shouldBe "Auction not open for bidding."
    }

    @Test
    fun `throw error if auction has no round`() {
        page_set_command(userSession, PageName.DE_TRADER_PAGE)
        val auction = create_auction(auctioneerSession, "Test")
        de_traders_add_command(auctioneerSession, auction, listOf(company))
        auction_select_command(userSession, auction)
        de_flow_control_command(auctioneerSession, auction, DeFlowControlType.SET_STARTING_PRICE, "100.000")
        de_flow_control_command(auctioneerSession, auction, DeFlowControlType.ANNOUNCE_STARTING_PRICE)
        de_flow_control_command(auctioneerSession, auction, DeFlowControlType.START_AUCTION)

        val orderSubmitCommand = DeOrderSubmitCommand(auction.id_str(), company.id_str(), OrderType.BUY, "-1", "10")
        val exception = shouldThrow<AlertException> {
            handle(userSession, orderSubmitCommand)
        }
        exception.message shouldBe "Round should be greater than zero"
    }

    @Test
    fun `throw error if quantity is zero`() {
        page_set_command(userSession, PageName.DE_TRADER_PAGE)
        val auction = create_auction(auctioneerSession, "Test")
        de_traders_add_command(auctioneerSession, auction, listOf(company))
        auction_select_command(userSession, auction)
        de_flow_control_command(auctioneerSession, auction, DeFlowControlType.SET_STARTING_PRICE, "100.000")
        de_flow_control_command(auctioneerSession, auction, DeFlowControlType.ANNOUNCE_STARTING_PRICE)
        de_flow_control_command(auctioneerSession, auction, DeFlowControlType.START_AUCTION)

        val orderSubmitCommand = DeOrderSubmitCommand(auction.id_str(), company.id_str(), OrderType.BUY, "1", "-1")
        val exception = shouldThrow<AlertException> {
            handle(userSession, orderSubmitCommand)
        }
        exception.message shouldBe "quantity must be a whole number"
    }
}
