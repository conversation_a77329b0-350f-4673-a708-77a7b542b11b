package au21.engine.domain.de.services.constraints

import au21.engine.domain.common.model.OrderType
import au21.engine.domain.common.model.PriceDirection
import au21.engine.domain.de.model.DeBidConstraints
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

internal class ConstraintCalculationTest {

    @Test
    fun `(None) verify constraints based on previous round is Down`() {
        val prevRoundConstraints = DeBidConstraints(50, 0, 0, 50)
        val newConstraints = calculate_subsequent_round_constraints(prevRoundConstraints, OrderType.NONE, 10, PriceDirection.DOWN)

        newConstraints.max_buy_quantity shouldBe 50
        newConstraints.min_buy_quantity shouldBe 0
        newConstraints.min_sell_quantity shouldBe 0
        newConstraints.max_sell_quantity shouldBe 0
    }

    @Test
    fun `(None) verify constraints based on previous round is UP`() {
        val prevRoundConstraints = DeBidConstraints(50, 0, 0, 50)
        val newConstraints = calculate_subsequent_round_constraints(prevRoundConstraints, OrderType.NONE, 10, PriceDirection.UP)

        newConstraints.max_buy_quantity shouldBe 0
        newConstraints.min_buy_quantity shouldBe 0
        newConstraints.min_sell_quantity shouldBe 0
        newConstraints.max_sell_quantity shouldBe 50
    }

    @Test
    fun `verify constraints based on previous round when price movement is UP`() {
        val prevRoundConstraints = DeBidConstraints(50, 0, 0, 50)
        val newConstraints = calculate_subsequent_round_constraints(prevRoundConstraints, OrderType.BUY, 10, PriceDirection.UP)

        newConstraints.max_buy_quantity shouldBe 10
        newConstraints.min_buy_quantity shouldBe 0
        newConstraints.min_sell_quantity shouldBe 0
        newConstraints.max_sell_quantity shouldBe 50
    }

    @Test
    fun `(Buy) verify constraints based on previous round when price movement is Down`() {
        val prevRoundConstraints = DeBidConstraints(50, 0, 0, 50)
        val newConstraints = calculate_subsequent_round_constraints(
            prev_round_constraints = prevRoundConstraints,
            prev_round_order_type = OrderType.BUY,
            prev_round_order_quantity = 10,
            PriceDirection.DOWN
        )

        newConstraints.max_buy_quantity shouldBe 50
        newConstraints.min_buy_quantity shouldBe 10
        newConstraints.min_sell_quantity shouldBe 0
        newConstraints.max_sell_quantity shouldBe 0
    }

    @Test
    fun `(Sell) verify constraints based on previous round when price movement is Up`() {
        val prevRoundConstraints = DeBidConstraints(50, 0, 0, 50)
        val newConstraints = calculate_subsequent_round_constraints(
            prev_round_constraints = prevRoundConstraints,
            prev_round_order_type = OrderType.SELL,
            prev_round_order_quantity = 10,
            PriceDirection.UP
        )

        newConstraints.max_buy_quantity shouldBe 0
        newConstraints.min_buy_quantity shouldBe 0
        newConstraints.min_sell_quantity shouldBe 10
        newConstraints.max_sell_quantity shouldBe 50
    }

    @Test
    fun `(Sell) verify constraints based on previous round when price movement is Down`() {
        val prevRoundConstraints = DeBidConstraints(50, 0, 0, 50)
        val newConstraints = calculate_subsequent_round_constraints(
            prev_round_constraints = prevRoundConstraints,
            prev_round_order_type = OrderType.SELL,
            prev_round_order_quantity = 10,
            PriceDirection.DOWN
        )

        newConstraints.max_buy_quantity shouldBe 50
        newConstraints.min_buy_quantity shouldBe 0
        newConstraints.min_sell_quantity shouldBe 0
        newConstraints.max_sell_quantity shouldBe 10
    }
}
