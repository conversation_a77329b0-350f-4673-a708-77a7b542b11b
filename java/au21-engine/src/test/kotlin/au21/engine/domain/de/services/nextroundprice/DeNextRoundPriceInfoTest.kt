package au21.engine.domain.de.services.nextroundprice

import au21.engine.domain.common.model.PriceDirection
import au21.engine.framework.commands.AlertException
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

internal class DeNextRoundPriceInfoTest {
    @Test
    fun `if demand and supply are equal an alert exception is thrown`() {
        val data = TestData(prev_round_total_sell = 100, prev_round_total_buy = 100)
        val exception = shouldThrow<AlertException> {
            DeNextRoundPriceInfo.create(
                data.prev_round_number,
                data.prev_round_price,
                data.prev_round_direction,
                data.prev_round_is_post_reversal,
                data.prev_round_total_buy,
                data.prev_round_total_sell,
                data.price_change_initial,
                data.price_change_post_reversal
            )
        }
        exception.message shouldBe "Cannot create subsequent round if prior round supply and demand are equal!"
    }

    @Test
    fun `check price direction for first round in case of higher demand`() {
        val data = TestData(prev_round_number = 1, prev_round_total_sell = 100, prev_round_total_buy = 200)
        val nextRoundPriceInfo = DeNextRoundPriceInfo.create(
            data.prev_round_number,
            data.prev_round_price,
            data.prev_round_direction,
            data.prev_round_is_post_reversal,
            data.prev_round_total_buy,
            data.prev_round_total_sell,
            data.price_change_initial,
            data.price_change_post_reversal
        )
        nextRoundPriceInfo.direction shouldBe PriceDirection.UP
        nextRoundPriceInfo.is_post_price_reversal shouldBe false
        nextRoundPriceInfo.price shouldBe data.prev_round_price.plus(data.price_change_initial)
    }

    @Test
    fun `check price direction for first round in case of lower demand`() {
        val data = TestData(prev_round_number = 1, prev_round_total_sell = 200, prev_round_total_buy = 100)
        val nextRoundPriceInfo = DeNextRoundPriceInfo.create(
            data.prev_round_number,
            data.prev_round_price,
            data.prev_round_direction,
            data.prev_round_is_post_reversal,
            data.prev_round_total_buy,
            data.prev_round_total_sell,
            data.price_change_initial,
            data.price_change_post_reversal
        )
        nextRoundPriceInfo.direction shouldBe PriceDirection.DOWN
        nextRoundPriceInfo.is_post_price_reversal shouldBe false
        nextRoundPriceInfo.price shouldBe data.prev_round_price.minus(data.price_change_initial)
    }

    @Test
    fun `exception is thrown if prev round direction is null for subsequent round`() {
        val data = TestData(prev_round_number = 2, prev_round_direction = null)
        val exception = shouldThrow<AlertException> {
            DeNextRoundPriceInfo.create(
                data.prev_round_number,
                data.prev_round_price,
                data.prev_round_direction,
                data.prev_round_is_post_reversal,
                data.prev_round_total_buy,
                data.prev_round_total_sell,
                data.price_change_initial,
                data.price_change_post_reversal
            )
        }
        exception.message shouldBe "After first round, previous round direction cannot be null."
    }

    @Test
    fun `price movement for subsequent round if demand is higher than supply`() {
        val data = TestData(
            prev_round_number = 2,
            prev_round_direction = PriceDirection.UP,
            prev_round_total_buy = 200,
            prev_round_total_sell = 100
        )
        val nextRoundPriceInfo = DeNextRoundPriceInfo.create(
            data.prev_round_number,
            data.prev_round_price,
            data.prev_round_direction,
            data.prev_round_is_post_reversal,
            data.prev_round_total_buy,
            data.prev_round_total_sell,
            data.price_change_initial,
            data.price_change_post_reversal
        )
        nextRoundPriceInfo.direction shouldBe PriceDirection.UP
        nextRoundPriceInfo.is_post_price_reversal shouldBe false
        nextRoundPriceInfo.price shouldBe data.prev_round_price.plus(data.price_change_initial)
    }

    @Test
    fun `price movement for subsequent round if supply is higher than demand`() {
        val data = TestData(
            prev_round_number = 2,
            prev_round_direction = PriceDirection.DOWN,
            prev_round_total_buy = 100,
            prev_round_total_sell = 200
        )
        val nextRoundPriceInfo = DeNextRoundPriceInfo.create(
            data.prev_round_number,
            data.prev_round_price,
            data.prev_round_direction,
            data.prev_round_is_post_reversal,
            data.prev_round_total_buy,
            data.prev_round_total_sell,
            data.price_change_initial,
            data.price_change_post_reversal
        )
        nextRoundPriceInfo.direction shouldBe PriceDirection.DOWN
        nextRoundPriceInfo.is_post_price_reversal shouldBe false
        nextRoundPriceInfo.price shouldBe data.prev_round_price.minus(data.price_change_initial)
    }

    @Test
    fun `verify next round price when price is reversed from DOWN To UP`() {
        val data = TestData(
            prev_round_number = 2,
            prev_round_direction = PriceDirection.DOWN,
            prev_round_total_buy = 200,
            prev_round_total_sell = 100
        )
        val nextRoundPriceInfo = DeNextRoundPriceInfo.create(
            data.prev_round_number,
            data.prev_round_price,
            data.prev_round_direction,
            data.prev_round_is_post_reversal,
            data.prev_round_total_buy,
            data.prev_round_total_sell,
            data.price_change_initial,
            data.price_change_post_reversal
        )
        nextRoundPriceInfo.direction shouldBe PriceDirection.UP
        nextRoundPriceInfo.is_post_price_reversal shouldBe true
        nextRoundPriceInfo.price shouldBe data.prev_round_price.plus(data.price_change_post_reversal)
    }

    @Test
    fun `verify next round price when price is reversed from UP To DOWN`() {
        val data = TestData(
            prev_round_number = 2,
            prev_round_direction = PriceDirection.UP,
            prev_round_total_buy = 100,
            prev_round_total_sell = 200
        )
        val nextRoundPriceInfo = DeNextRoundPriceInfo.create(
            data.prev_round_number,
            data.prev_round_price,
            data.prev_round_direction,
            data.prev_round_is_post_reversal,
            data.prev_round_total_buy,
            data.prev_round_total_sell,
            data.price_change_initial,
            data.price_change_post_reversal
        )
        nextRoundPriceInfo.direction shouldBe PriceDirection.DOWN
        nextRoundPriceInfo.is_post_price_reversal shouldBe true
        nextRoundPriceInfo.price shouldBe data.prev_round_price.minus(data.price_change_post_reversal)
    }

    @Test
    fun `verify next round price when price is reversed and demand is higher than supply`() {
        val data = TestData(
            prev_round_number = 2,
            prev_round_direction = PriceDirection.UP,
            prev_round_total_buy = 200,
            prev_round_total_sell = 100,
            prev_round_is_post_reversal = true
        )
        val nextRoundPriceInfo = DeNextRoundPriceInfo.create(
            data.prev_round_number,
            data.prev_round_price,
            data.prev_round_direction,
            data.prev_round_is_post_reversal,
            data.prev_round_total_buy,
            data.prev_round_total_sell,
            data.price_change_initial,
            data.price_change_post_reversal
        )
        nextRoundPriceInfo.direction shouldBe PriceDirection.UP
        nextRoundPriceInfo.is_post_price_reversal shouldBe true
        nextRoundPriceInfo.price shouldBe data.prev_round_price.plus(data.price_change_post_reversal)
    }

    @Test
    fun `verify next round price when price is reversed and suplly is higher than demand`() {
        val data = TestData(
            prev_round_number = 2,
            prev_round_direction = PriceDirection.UP,
            prev_round_total_buy = 100,
            prev_round_total_sell = 200,
            prev_round_is_post_reversal = true
        )
        val nextRoundPriceInfo = DeNextRoundPriceInfo.create(
            data.prev_round_number,
            data.prev_round_price,
            data.prev_round_direction,
            data.prev_round_is_post_reversal,
            data.prev_round_total_buy,
            data.prev_round_total_sell,
            data.price_change_initial,
            data.price_change_post_reversal
        )
        nextRoundPriceInfo.direction shouldBe PriceDirection.DOWN
        nextRoundPriceInfo.is_post_price_reversal shouldBe true
        nextRoundPriceInfo.price shouldBe data.prev_round_price.minus(data.price_change_post_reversal)
    }

    data class TestData(
        val prev_round_number: Int = 1,
        val prev_round_price: Double = 100.0,
        val prev_round_direction: PriceDirection? = null,
        val prev_round_is_post_reversal: Boolean = false,
        val prev_round_total_buy: Int = 200,
        val prev_round_total_sell: Int = 400,
        val price_change_initial: Double = 1.0,
        val price_change_post_reversal: Double = 0.125,
    )
}