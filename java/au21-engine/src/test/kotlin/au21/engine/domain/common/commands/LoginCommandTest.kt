package au21.engine.domain.common.commands

import au21.engine.framework.commands.AlertException
import au21.engine.test.helpers.base.action.CommonActionTestBase
import au21.engine.test.helpers.base.command.CommandTestBase
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.quarkus.test.junit.QuarkusTest
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream

@QuarkusTest
internal class LoginCommandTest : CommandTestBase() {

    @TestInstance(TestInstance.Lifecycle.PER_CLASS)
    @Nested
    inner class Validation {

        @Test
        fun `throw error if user is not known to the system`() {
            val loginCommand = LoginCommand("foo", "bar")
            val session = create_session()
            val exception = shouldThrow<AlertException> {
                handle(session, loginCommand)
            }
            exception.message shouldBe "No active user found with username: foo"
        }

        @ParameterizedTest
        @MethodSource("testCases")
        fun `valid login command id parameter`(data: TestData) {
            val loginCommand = LoginCommand(data.username, data.password)
            val session = create_session()
            val exception = shouldThrow<AlertException> {
                handle(session, loginCommand)
            }
            exception.message shouldBe data.expectedMessage
        }

        private fun testCases() = Stream.of(
            TestData(username = "user", password = "", expectedMessage = "Password cannot be blank."),
            TestData(username = "", password = "password", expectedMessage = "Username cannot be blank."),
            TestData(username = "admin", password = "password", expectedMessage = "admin not implemented"),
        )
    }

    data class TestData(
        val username: String, val password: String, val expectedMessage: String
    )
}
