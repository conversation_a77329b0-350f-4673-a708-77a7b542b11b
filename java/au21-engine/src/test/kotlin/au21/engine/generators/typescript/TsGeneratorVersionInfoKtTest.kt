package au21.engine.generators.typescript

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.io.File

class TsGeneratorVersionInfoKtTest {

    private val testFilePath = "test-version.json"

//    @Test
//    fun testUpdateVersion() {
//        // Set up the test by creating a temporary version.json file
//        val testJson = """{"version": "1.2.3"}"""
//        File(testFilePath).writeText(testJson)
//
//        // Call the function to update the version
//        val updatedVersion = updateVersion(testFilePath)
//
//        // Verify that the version was updated correctly
//        assertEquals("1.2.4", updatedVersion)
//
//        // Clean up the test by deleting the temporary version.json file
//        File(testFilePath).delete()
//    }
}