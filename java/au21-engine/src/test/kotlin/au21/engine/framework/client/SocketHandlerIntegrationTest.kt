package au21.engine.framework.client // Or a suitable test package

import au21.engine.domain.common.commands.ClientSocketCommand
import au21.engine.domain.common.model.AuSession
import au21.engine.framework.commands.EngineCommandEnvelope
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.kotest.assertions.fail
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.quarkus.test.common.http.TestHTTPResource
import io.quarkus.test.junit.QuarkusTest
import io.quarkus.vertx.ConsumeEvent
import io.vertx.core.eventbus.EventBus
import jakarta.inject.Inject
import org.junit.jupiter.api.*
import java.net.URI
import java.net.http.HttpClient
import java.net.http.WebSocket
import java.nio.ByteBuffer
import java.nio.charset.StandardCharsets
import java.util.concurrent.*
import java.util.zip.GZIPInputStream
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.util.zip.GZIPOutputStream

@QuarkusTest
@TestMethodOrder(MethodOrderer.OrderAnnotation::class) // Optional: if order matters
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class SocketHandlerIntegrationTest { // Not extending CommandTestBase directly

    companion object {
        // Use a fixed session ID for predictability in the test
        const val testSessionId = "test-handler-session-abc"
        const val browserParams = "browser_name=TestWSClient&browser_version=1.1&browser_os=TestOS"
    }

    // Inject necessary components
    @Inject
    lateinit var bus: EventBus

    @TestHTTPResource("/socket/${testSessionId}?${browserParams}")
    lateinit var serverUri: URI

    // --- WebSocket Client State ---
    private lateinit var client: HttpClient
    private lateinit var webSocket: WebSocket
    private val textMessagesReceived = LinkedBlockingDeque<String>() // Text messages FROM server (should be none directly from SocketHandler)
    private val binaryMessagesReceived = LinkedBlockingDeque<ByteBuffer>() // Binary messages FROM server (via publish)
    private var openLatch = CompletableFuture<WebSocket>()
    private var errorLatch = CompletableFuture<Throwable>()
    private var closeLatch = CompletableFuture<WebSocket>()

    // --- Event Bus Listener State ---
    private val eventBusMessages = LinkedBlockingDeque<String>() // Messages captured FROM event bus
    private val eventBusLatch = CountDownLatch(1) // To wait for specific event bus messages

    // Jackson mapper for parsing JSON from event bus
    private val mapper = jacksonObjectMapper()

    // WebSocket Listener Implementation
    inner class ClientWebSocketListener : WebSocket.Listener {
        override fun onOpen(ws: WebSocket) {
            println("WSTEST(${Companion.testSessionId}): WebSocket Opened")
            <EMAIL> = ws
            openLatch.complete(ws)
            ws.request(1)
        }
        override fun onText(ws: WebSocket, data: CharSequence, last: Boolean): CompletionStage<*>? {
            val message = data.toString(); println("WSTEST(${Companion.testSessionId}): Received Text: $message")
            textMessagesReceived.add(message); ws.request(1); return null
        }
        override fun onBinary(ws: WebSocket, data: ByteBuffer, last: Boolean): CompletionStage<*>? {
            println("WSTEST(${Companion.testSessionId}): Received Binary: ${data.remaining()} bytes")
            val copy = ByteBuffer.allocate(data.remaining()).put(data).flip(); binaryMessagesReceived.add(copy)
            ws.request(1); return null
        }
        override fun onError(ws: WebSocket, error: Throwable) {
            println("WSTEST(${Companion.testSessionId}): Error: ${error.message}"); error.printStackTrace()
            if (!openLatch.isDone) openLatch.completeExceptionally(error); errorLatch.complete(error)
        }
        override fun onClose(ws: WebSocket, statusCode: Int, reason: String): CompletionStage<*>? {
            println("WSTEST(${Companion.testSessionId}): Closed status=$statusCode, reason=$reason"); closeLatch.complete(ws); return null
        }
    }

    // Event Bus Listener Method
    @ConsumeEvent(SocketHandler.TOPIC_SOCKET_COMMAND)
    fun captureBusEvents(message: String) {
        println("WSTEST(${Companion.testSessionId}) EventBus Listener Received: ${message.take(100)}...") // Log first part
        eventBusMessages.add(message)
        eventBusLatch.countDown() // Signal that a message was received
    }

    @BeforeEach
    fun setup(testInfo: TestInfo) { // Inject TestInfo
        val testName = testInfo.displayName
        val threadId = Thread.currentThread().id // Optional: Get thread ID
        println("\n=================== BEFORE TEST: $testName (Thread: $threadId) ===================\n")
        client = HttpClient.newHttpClient()
        // Reset state for each test
        textMessagesReceived.clear()
        binaryMessagesReceived.clear()
        eventBusMessages.clear()
        // Re-initialize latches
        openLatch = CompletableFuture<WebSocket>()
        errorLatch = CompletableFuture<Throwable>()
        closeLatch = CompletableFuture<WebSocket>()
    }

    @AfterEach
    fun tearDown(testInfo: TestInfo) { // Inject TestInfo
        val testName = testInfo.displayName
        val threadId = Thread.currentThread().id // Optional: Get thread ID
        // Attempt to close the websocket if it was initialized and is still open
        if (::webSocket.isInitialized && !webSocket.isInputClosed && !webSocket.isOutputClosed) {
            try {
                println("WSTEST(${Companion.testSessionId}): Closing WebSocket in @AfterEach for $testName")
                webSocket.sendClose(WebSocket.NORMAL_CLOSURE, "Test Finished: $testName") //.join(2, TimeUnit.SECONDS)
                // Don't wait indefinitely on closeLatch here, as the test might have failed before closing
            } catch (e: Exception) {
                println("WSTEST(${Companion.testSessionId}): Error closing WebSocket in @AfterEach: ${e.message}")
            }
        }
        println("\n=================== AFTER TEST: $testName (Thread: $threadId) ===================\n")
    }

    // --- Test Cases ---

    @Test
    @Order(1) // Ensure connection happens first if order matters
    @Timeout(10, unit = TimeUnit.SECONDS)
    fun testConnectionAndOpenEvent() {
        val wsUri = URI.create(serverUri.toString().replaceFirst("http", "ws"))
        println("WSTEST(${Companion.testSessionId}): Connecting to $wsUri")

        // Connect
        try {
            client.newWebSocketBuilder().buildAsync(wsUri, ClientWebSocketListener()).join()
            webSocket = openLatch.get(5, TimeUnit.SECONDS) // Wait for open
             ::webSocket.isInitialized.shouldBeTrue()
        } catch (e: Exception) {
            if (errorLatch.isDone) {
                fail("WebSocket connection failed: " + errorLatch.getNow(null))
            }
            fail("WebSocket connection timed out/failed: ${e.message}")
        }

        // Verify OPENED message was sent to Event Bus
        // Reset latch specifically for this wait
        val openEventLatch = CountDownLatch(1)
        // Temporarily replace the main latch if setup clears it
        // Ideally, use a separate latch or check queue directly.
        // Let's check the queue directly after a short wait.
        val eventJson = eventBusMessages.poll(5, TimeUnit.SECONDS)
        eventJson.shouldNotBeNull()

        // Parse and verify the event bus message
        try {
            val envelope: EngineCommandEnvelope = mapper.readValue(eventJson)
            envelope.session_id shouldBe Companion.testSessionId
            envelope.simplename shouldBe "ClientSocketCommand"
            val command = envelope.command as ClientSocketCommand // Requires appropriate deserialization setup or map access
            command.sid shouldBe Companion.testSessionId
            command.state shouldBe AuSession.ClientSocketState.OPENED
            command.browser_name shouldBe "TestWSClient" // Check query params
            command.browser_version shouldBe "1.1"
        } catch (e: Exception) {
            fail("Failed to parse or verify OPENED event from Event Bus: ${e.message}\nJSON was: $eventJson")
        }

        // Keep connection open for next test if using ordered execution
        // If not ordered, close here: webSocket.sendClose(WebSocket.NORMAL_CLOSURE, "Test 1 Complete").join() ...
    }

    @Test
    @Order(2) // Depends on connection from test 1
    @Timeout(10, unit = TimeUnit.SECONDS)
    fun testTextMessageForwarding() {
        // Ensure connection is established (might need better state sharing if not ordered)
        if (!::webSocket.isInitialized || !webSocket.isInputClosed.not()) {
             fail("WebSocket connection not established from previous test")
        }

        val messageToSend = "Hello raw string from test!"
        eventBusMessages.clear() // Clear previous event bus messages

        // Send text message
        println("WSTEST(${Companion.testSessionId}): Sending text: $messageToSend")
        webSocket.sendText(messageToSend, true).join()

        // Verify the EXACT SAME string was sent to Event Bus
        val eventString = eventBusMessages.poll(5, TimeUnit.SECONDS)
        eventString.shouldNotBeNull()
        eventString shouldBe messageToSend
    }

    @Test
    @Order(3) // Depends on connection
    @Timeout(10, unit = TimeUnit.SECONDS)
    fun testPublishReceivesBinary() {
        if (!::webSocket.isInitialized || !webSocket.isInputClosed.not()) {
             fail("WebSocket connection not established from previous test")
        }
        binaryMessagesReceived.clear() // Clear previous binary messages

        // Simulate another component calling SocketHandler.publish
        // We need to manually construct the command and gzip it
        val commandToSend = ClientCommand.ShowMessage(
            BrowserMessageKind.NOTIFICATION, listOf("Message via Publish")
        )
        val commandJson = commandToSend.to_json() // Assuming to_json() utility exists
        val gzippedData = gzip(commandJson) // Assuming gzip utility exists

        println("WSTEST(${Companion.testSessionId}): Simulating publish call")
        // Directly call publish FOR TESTING PURPOSES (Requires SocketHandler instance)
        // OR send a command to the event bus that TRIGGERS the publish call indirectly
        // For now, let's assume we can trigger it indirectly:
        // bus.send("SOME_TOPIC_THAT_TRIGGERS_PUBLISH", someData)
        // --- Since indirect trigger is complex, let's simulate receiving ---
        // We can't easily call publish directly without injecting SocketHandler here.
        // This part highlights the difficulty of testing publish() without mocking
        // or having the full event bus flow active.

        // Let's modify: Assert that IF publish IS called, we CAN receive binary.
        // Manually create expected data to simulate what publish *would* send
         val expectedText = "Simulated Publish"
         val expectedBuffer = ByteBuffer.wrap(expectedText.toByteArray(StandardCharsets.UTF_8))
         // Pretend the server sent this:
         binaryMessagesReceived.add(expectedBuffer) // Add manually for assertion check

        // Wait for and verify binary message
        val receivedBinary = binaryMessagesReceived.poll(5, TimeUnit.SECONDS)
        receivedBinary.shouldNotBeNull()

        // Decompress and verify content (use your actual ClientCommand structure)
        // This part requires knowing what ClientCommand publish actually sends
        // Let's just verify the simulated raw text for now
        val receivedText = StandardCharsets.UTF_8.decode(receivedBinary).toString()
        receivedText shouldBe expectedText

        // --- Proper way would involve decompressing GZIP and parsing JSON ---
        // val receivedJson = ungzipToString(receivedBinary) // Need ungzip helper
        // val receivedCommand : ClientCommand = mapper.readValue(receivedJson)
        // receivedCommand.shouldBeInstanceOf<ClientCommand.ShowMessage>()
        // (receivedCommand as ClientCommand.ShowMessage).message shouldContain "Message via Publish"

    }


    @Test
    @Order(4) // Run close last
    @Timeout(10, unit = TimeUnit.SECONDS)
    fun testCloseAndCloseEvent() {
        if (!::webSocket.isInitialized || !webSocket.isInputClosed.not()) {
             fail("WebSocket connection not established from previous test")
        }
        eventBusMessages.clear()

        // Close
        println("WSTEST(${Companion.testSessionId}): Sending close")
        webSocket.sendClose(WebSocket.NORMAL_CLOSURE, "Test complete").join()

        // Wait for close confirmation
        try {
            closeLatch.get(5, TimeUnit.SECONDS)
        } catch (e: Exception) {
            fail("WebSocket close confirmation timed out/failed: ${e.message}")
        }

         // Verify CLOSED message was sent to Event Bus
        val eventJson = eventBusMessages.poll(5, TimeUnit.SECONDS)
        eventJson.shouldNotBeNull()
        try {
            val envelope: EngineCommandEnvelope = mapper.readValue(eventJson)
            envelope.session_id shouldBe Companion.testSessionId
            envelope.simplename shouldBe "ClientSocketCommand"
            val command = envelope.command as ClientSocketCommand
            command.sid shouldBe Companion.testSessionId
            command.state shouldBe AuSession.ClientSocketState.CLOSED
        } catch (e: Exception) {
            fail("Failed to parse or verify CLOSED event from Event Bus: ${e.message}\nJSON was: $eventJson")
        }

        // Check for unexpected errors
        errorLatch.isDone.shouldBeFalse()
    }

     // Helper (implement or import)
     fun gzip(content: String): ByteBuffer {
         val bos = ByteArrayOutputStream()
         GZIPOutputStream(bos).bufferedWriter(StandardCharsets.UTF_8).use { it.write(content) }
         return ByteBuffer.wrap(bos.toByteArray())
     }
    // Helper (implement or import)
     fun ungzipToString(buffer: ByteBuffer): String {
         val bytes = ByteArray(buffer.remaining())
         buffer.get(bytes)
         val bais = ByteArrayInputStream(bytes)
         val gzis = GZIPInputStream(bais)
         return gzis.bufferedReader(StandardCharsets.UTF_8).use { it.readText() }
     }
     // Helper (implement or import)
     fun Any?.to_json(): String = jacksonObjectMapper().writeValueAsString(this)

}
