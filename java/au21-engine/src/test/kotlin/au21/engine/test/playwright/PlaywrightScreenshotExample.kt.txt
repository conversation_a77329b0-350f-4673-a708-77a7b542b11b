package au21.engine.test.playwright

import com.microsoft.playwright.BrowserType.LaunchOptions
import com.microsoft.playwright.Page
import com.microsoft.playwright.Playwright
import org.junit.jupiter.api.Disabled
import java.nio.file.Paths


@Disabled
object PlaywrightScreenshotExample {
    @JvmStatic
    fun main(args: Array<String>) {
        Playwright.create().use { playwright ->
            val browser = playwright.chromium().launch(LaunchOptions().setHeadless(false).setSlowMo(10_000.0))
            val page = browser.newPage()
            val username = "a1"
            val password = "1"
            page.navigate("http:/localhost:8080/")
            page.fill("input[type='text']", username);
            page.fill("input[type='password']", password);
            page.click("button:has-text('Sign in')");
            page.screenshot(Page.ScreenshotOptions().setPath(Paths.get("example.png")))
        }
    }
}
