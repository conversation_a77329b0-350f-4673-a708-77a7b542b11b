package au21.engine.test.services.de.nextroundprice

import au21.engine.domain.common.model.PriceDirection
import au21.engine.domain.de.services.nextroundprice.DeNextRoundPriceInfo
import au21.engine.framework.commands.AlertException
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.quarkus.test.junit.QuarkusTest
import org.junit.jupiter.api.TestInstance

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class DeNextRoundPriceSpec : DescribeSpec({

    val prev_round_price = 100.0

    val large_price_change = 0.500
    val small_price_change = 0.125

    val small_demand = 50
    val large_demand = 100

    val small_supply = 50
    val large_supply = 100

    describe("exceptions") {
        describe("previous round supply == demand") {
            it("should throw exception if supply = demand") {
                shouldThrow<AlertException> {
                    DeNextRoundPriceInfo.create(
                        prev_round_number = 1,
                        prev_round_price = prev_round_price,
                        prev_round_direction = null,
                        prev_round_is_post_reversal = false,
                        prev_round_total_buy = 20,
                        prev_round_total_sell = 20,
                        price_change_initial = large_price_change,
                        price_change_post_reversal = small_price_change
                    )
                }.message.shouldBe("Cannot create subsequent round if prior round supply and demand are equal!")
            }

            it("should throw exception if prev round has no direction after the first round.") {
                shouldThrow<AlertException> {
                    DeNextRoundPriceInfo.create(
                        prev_round_number = 2,
                        prev_round_price = prev_round_price,
                        prev_round_direction = null,
                        prev_round_is_post_reversal = false,
                        prev_round_total_buy = large_demand,
                        prev_round_total_sell = small_supply,
                        price_change_initial = large_price_change,
                        price_change_post_reversal = small_price_change
                    )
                }.message.shouldBe("After first round, previous round direction cannot be null.")
            }

        }

    }

    describe("After first round") {

        describe("if demand greater than supply") {

            it("price should increase by large amount") {
                DeNextRoundPriceInfo.create(
                    prev_round_number = 1,
                    prev_round_price = prev_round_price,
                    prev_round_direction = null,
                    prev_round_is_post_reversal = false,
                    prev_round_total_buy = large_demand,
                    prev_round_total_sell = small_supply,
                    price_change_initial = large_price_change,
                    price_change_post_reversal = small_price_change
                ).run {
                    price shouldBe (100 + large_price_change)
                    direction shouldBe (PriceDirection.UP)
                    is_post_price_reversal shouldBe (false)
                }
            }
        }

        describe("if supply greater than demand") {

            it("price should decrease by large amount") {
                DeNextRoundPriceInfo.create(
                    prev_round_number = 1,
                    prev_round_price = prev_round_price,
                    prev_round_direction = null,
                    prev_round_is_post_reversal = false,
                    prev_round_total_buy = small_demand,
                    prev_round_total_sell = large_supply,
                    price_change_initial = large_price_change,
                    price_change_post_reversal = small_price_change
                ).run {
                    price shouldBe (100 - large_price_change)
                    direction shouldBe (PriceDirection.DOWN)
                    is_post_price_reversal shouldBe (false)
                }
            }
        }
    }

    describe("After subsequent rounds") {

        describe("if price has not reversed") {

            describe("if prev round increased") {

                describe("if demand still greater than supply") {

                    it("price should continue to increase by large amount") {
                        DeNextRoundPriceInfo.create(
                            prev_round_number = 2,
                            prev_round_price = prev_round_price,
                            prev_round_direction = PriceDirection.UP,
                            prev_round_is_post_reversal = false,
                            prev_round_total_buy = large_demand,
                            prev_round_total_sell = small_supply,
                            price_change_initial = large_price_change,
                            price_change_post_reversal = small_price_change
                        ).run {
                            price shouldBe (100 + large_price_change)
                            direction shouldBe (PriceDirection.UP)
                            is_post_price_reversal shouldBe (false)
                        }
                    }
                }

                // TODO: supply == demand

                describe("if demand is now less than supply") {

                    it("price should reverse and decrease by small amount") {
                        DeNextRoundPriceInfo.create(
                            prev_round_number = 2,
                            prev_round_price = prev_round_price,
                            prev_round_direction = PriceDirection.UP,
                            prev_round_is_post_reversal = false,
                            prev_round_total_buy = small_demand,
                            prev_round_total_sell = large_supply,
                            price_change_initial = large_price_change,
                            price_change_post_reversal = small_price_change
                        ).run {
                            price shouldBe (100 - small_price_change)
                            direction shouldBe (PriceDirection.DOWN)
                            is_post_price_reversal shouldBe (true)
                        }
                    }
                }
            }

            describe("if prev round decreased") {

                describe("if supply still greater than demand") {

                    it("price should continue to decrease by large amount") {
                        DeNextRoundPriceInfo.create(
                            prev_round_number = 2,
                            prev_round_price = prev_round_price,
                            prev_round_direction = PriceDirection.DOWN,
                            prev_round_is_post_reversal = false,
                            prev_round_total_buy = small_demand,
                            prev_round_total_sell = large_supply,
                            price_change_initial = large_price_change,
                            price_change_post_reversal = small_price_change
                        ).run {
                            price shouldBe (100 - large_price_change)
                            direction shouldBe (PriceDirection.DOWN)
                            is_post_price_reversal shouldBe (false)
                        }
                    }
                }

                // TODO: need to add supply == demand !!

                describe("if supply is now less that than demand") {
                    it("price should reverse and increase by small amount") {
                        DeNextRoundPriceInfo.create(
                            prev_round_number = 2,
                            prev_round_price = prev_round_price,
                            prev_round_direction = PriceDirection.DOWN,
                            prev_round_is_post_reversal = false,
                            prev_round_total_buy = large_demand,
                            prev_round_total_sell = small_supply,
                            price_change_initial = large_price_change,
                            price_change_post_reversal = small_price_change
                        ).run {
                            price shouldBe (100 + small_price_change)
                            direction shouldBe (PriceDirection.UP)
                            is_post_price_reversal shouldBe (true)
                        }
                    }
                }
            }

        }

        describe("if price has reversed") {

            describe("if prev round decreased") {

                describe("if demand still less than supply") {

                    it("price should continue to decrease by small amount") {
                        DeNextRoundPriceInfo.create(
                            prev_round_number = 2,
                            prev_round_price = prev_round_price,
                            prev_round_direction = PriceDirection.DOWN,
                            prev_round_is_post_reversal = true,
                            prev_round_total_buy = small_demand,
                            prev_round_total_sell = large_supply,
                            price_change_initial = large_price_change,
                            price_change_post_reversal = small_price_change
                        ).run {
                            price shouldBe (100 - small_price_change)
                            direction shouldBe (PriceDirection.DOWN)
                            is_post_price_reversal shouldBe (true)
                        }
                    }
                }
            }

            describe("if prev round increased") {

                describe("if supply still less than demand") {

                    it("price should continue to increase by small amount") {
                        DeNextRoundPriceInfo.create(
                            prev_round_number = 2,
                            prev_round_price = prev_round_price,
                            prev_round_direction = PriceDirection.UP,
                            prev_round_is_post_reversal = true,
                            prev_round_total_buy = large_demand,
                            prev_round_total_sell = small_supply,
                            price_change_initial = large_price_change,
                            price_change_post_reversal = small_price_change
                        ).run {
                            price shouldBe (100 + small_price_change)
                            direction shouldBe (PriceDirection.UP)
                            is_post_price_reversal shouldBe (true)
                        }
                    }
                }
            }
        }

    }
})
