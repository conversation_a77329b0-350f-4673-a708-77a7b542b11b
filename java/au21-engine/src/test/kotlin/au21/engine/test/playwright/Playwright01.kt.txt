package au21.engine.test.playwright

import io.quarkus.test.junit.QuarkusTest
import org.junit.jupiter.api.TestInstance


@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class Playwright01 {

//    @Test
//    fun scenario_1(){
//        Playwright.create().use { playwright ->
//            val browser: Browser = playwright.chromium().launch(
//                BrowserType.LaunchOptions()
//                    .setHeadless(false)
//            )
//            val context: BrowserContext = browser.newContext()
//            // Open new page
//            val page: Page = context.newPage()
//            // Go to http://localhost:8080/
//            page.navigate("http://localhost:8080/")
//            // Click button:has-text("a1")
//            page.click("button:has-text(\"a1\")")
//            // Click text=starting: Jun 25 20:38:16 Delete auction 1
//            page.click("text=starting: Jun 25 20:38:16 Delete auction 1")
//        }
//    }
}
