package au21.engine.test.tests.websockets // Keep your correct package

import io.kotest.assertions.fail // Kotest fail
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.nulls.shouldBeNull
import io.quarkus.test.common.http.TestHTTPResource
import io.quarkus.test.junit.QuarkusTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test // Still use JUnit's @Test
import org.junit.jupiter.api.Timeout // Keep timeout
import java.net.URI
import java.net.http.HttpClient
import java.net.http.WebSocket
import java.net.http.WebSocketHandshakeException
import java.nio.ByteBuffer
import java.util.concurrent.*

@QuarkusTest
@Disabled // Keep disabled until assertions reflect actual desired behavior
class ChatTest {

    @TestHTTPResource("/socket/stu?browser_name=TestChatClient&browser_version=2.0")
    lateinit var serverUri: URI

    private lateinit var client: HttpClient
    private lateinit var webSocket: WebSocket
    private val messagesQueue = LinkedBlockingDeque<String>()
    private val openLatch = CompletableFuture<WebSocket>()
    private val errorLatch = CompletableFuture<Throwable>()
    private val closeLatch = CompletableFuture<WebSocket>()

    // Listener Implementation (no changes needed here)
    inner class ClientWebSocketListener : WebSocket.Listener {
        override fun onOpen(webSocket: WebSocket) {
            println("CHAT TEST CLIENT (Kotest): WebSocket Opened")
            <EMAIL> = webSocket
            openLatch.complete(webSocket)
            webSocket.request(1)
        }
        override fun onText(webSocket: WebSocket, data: CharSequence, last: Boolean): CompletionStage<*>? {
            println("CHAT TEST CLIENT (Kotest): Received Text: $data")
            messagesQueue.add(data.toString())
            webSocket.request(1)
            return null
        }
        override fun onBinary(webSocket: WebSocket, data: ByteBuffer, last: Boolean): CompletionStage<*>? {
            println("CHAT TEST CLIENT (Kotest): Received Binary: ${data.remaining()} bytes")
            val copy = ByteBuffer.allocate(data.remaining()).put(data).flip()
            webSocket.request(1)
            return null
        }
        override fun onError(webSocket: WebSocket, error: Throwable) {
            println("CHAT TEST CLIENT (Kotest): Error: ${error.message}")
            if (!openLatch.isDone) openLatch.completeExceptionally(error)
            errorLatch.complete(error)
        }
        override fun onClose(webSocket: WebSocket, statusCode: Int, reason: String): CompletionStage<*>? {
            println("CHAT TEST CLIENT (Kotest): Closed with status $statusCode, reason: $reason")
            closeLatch.complete(webSocket)
            return null
        }
    }

    @BeforeEach
    fun setup() {
        client = HttpClient.newHttpClient()
        messagesQueue.clear()
        openLatch.cancel(false)
        errorLatch.cancel(false)
        closeLatch.cancel(false)
    }

    @Test
    @Timeout(10, unit = TimeUnit.SECONDS)
    @Throws(Exception::class)
    fun testWebsocketChat() {
        val wsUri = URI.create(serverUri.toString().replaceFirst("http", "ws"))
        println("CHAT TEST CLIENT (Kotest): Connecting to $wsUri")

        // Connect
        try {
            client.newWebSocketBuilder()
                .buildAsync(wsUri, ClientWebSocketListener())
                .join()
        } catch (e: CompletionException) {
            val cause = e.cause
            // Use Kotest fail
            if (cause is WebSocketHandshakeException) {
                fail("WebSocket handshake failed: ${cause.message}")
            } else if (cause != null) {
                fail("Failed to initiate WebSocket connection (underlying cause): ${cause.message}")
            } else {
                fail("Failed to initiate WebSocket connection: ${e.message}")
            }
        } catch(e: Exception) {
            fail("Failed to build/connect WebSocket: ${e.message}")
        }

        // 1. Wait for connection
        try {
            webSocket = openLatch.get(5, TimeUnit.SECONDS) // Kotest doesn't have built-in await for CompletableFuture
            println("CHAT TEST CLIENT (Kotest): Connection confirmed open.")
        } catch (e: Exception) {
            if (errorLatch.isDone) {
                val error = errorLatch.getNow(null)
                fail("WebSocket connection failed: ${error?.message ?: "Unknown error"}")
            } else {
                fail("WebSocket connection timed out or was interrupted: ${e.message}")
            }
        }

        // 2. Send a message
        val messageToSend = "hello world from stu (Kotest)"
        println("CHAT TEST CLIENT (Kotest): Sending message: $messageToSend")
        webSocket.sendText(messageToSend, true).join()

        // 3. Assert expected behavior (expect no reply by default)
        val receivedMessage = messagesQueue.poll(2, TimeUnit.SECONDS)
        receivedMessage.shouldBeNull() // Kotest assertion

        // 4. Close
        println("CHAT TEST CLIENT (Kotest): Sending close")
        webSocket.sendClose(WebSocket.NORMAL_CLOSURE, "Test complete").join()

        // 5. Wait for close confirmation
        try {
            closeLatch.get(5, TimeUnit.SECONDS)
            println("CHAT TEST CLIENT (Kotest): Connection confirmed closed.")
        } catch (e: Exception) {
            if (errorLatch.isDone) println("CHAT TEST CLIENT (Kotest): Error during/after close: ${errorLatch.getNow(null)?.message}")
            fail("WebSocket close confirmation timed out or was interrupted: ${e.message}")
        }

        // 6. Check for unexpected errors
        val unexpectedError = errorLatch.getNow(null)
        if (unexpectedError != null) {
            fail("An unexpected WebSocket error occurred during the test: ${unexpectedError.message}")
        }
        // Alternatively, check latch state:
        errorLatch.isDone.shouldBeFalse() // Assert no error occurred
    }
}
