package au21.engine.test.commands.de

import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.services.sampledb.TradingUserFixture
import au21.engine.domain.de.services.sampledb.create_sample_db_auction
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance


@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class SampleDbTest {

    val close_last_round = true
    val round_count = 10
    val trader_count = 4
    val trading_users = TradingUserFixture.create(trader_count, listOf(), listOf())

    val de = create_sample_db_auction(
        "Auction one", trading_users, round_count, close_last_round
    )


    @Test
    fun first() {

        de.apply {
            auction_name.shouldBe("Auction one")
            traders.shouldHaveSize(4)

            traders.onEachIndexed { index: Int, t: DeAuction.Trader ->
                val i = index + 1
                t.apply {
                    company.shortname.shouldBe("c-$i")
                }
            }
            rounds.shouldHaveSize(10)
            rounds.onEach {
                it.trader_infos.shouldHaveSize(4)
            }
        }
    }
}
