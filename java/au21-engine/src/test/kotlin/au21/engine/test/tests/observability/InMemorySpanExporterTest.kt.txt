package au21.framework.observability

import io.opentelemetry.sdk.trace.SdkTracerProvider
import org.junit.jupiter.api.BeforeEach
import io.opentelemetry.sdk.trace.export.SimpleSpanProcessor
import org.junit.jupiter.api.AfterEach
import io.opentelemetry.sdk.trace.data.SpanData
import au21.framework.observability.InMemorySpanExporterTest
import io.opentelemetry.api.trace.SpanKind
import io.opentelemetry.api.trace.Tracer
import io.opentelemetry.sdk.trace.data.StatusData
import org.junit.jupiter.api.Test

internal class InMemorySpanExporterTest {
    private val exporter: InMemorySpanExporter = InMemorySpanExporter.create()
    private var tracerProvider: SdkTracerProvider? = null
    private var tracer: Tracer? = null

    @BeforeEach
    fun setup() {
        tracerProvider = SdkTracerProvider.builder().addSpanProcessor(SimpleSpanProcessor.create(exporter)).build()
        tracer = tracerProvider.get("InMemorySpanExporterTest")
    }

    @AfterEach
    fun tearDown() {
        tracerProvider!!.shutdown()
    }

    @get:Test
    val finishedSpanItems: Unit
        get() {
            tracer!!.spanBuilder("one").startSpan().end()
            tracer!!.spanBuilder("two").startSpan().end()
            tracer!!.spanBuilder("three").startSpan().end()
            val spanItems: List<SpanData> = exporter.getFinishedSpanItems()
            assertThat(spanItems).isNotNull()
            assertThat(spanItems.size).isEqualTo(3)
            assertThat(spanItems[0].name).isEqualTo("one")
            assertThat(spanItems[1].name).isEqualTo("two")
            assertThat(spanItems[2].name).isEqualTo("three")
        }

    @Test
    fun reset() {
        tracer!!.spanBuilder("one").startSpan().end()
        tracer!!.spanBuilder("two").startSpan().end()
        tracer!!.spanBuilder("three").startSpan().end()
        val spanItems: List<SpanData> = exporter.getFinishedSpanItems()
        assertThat(spanItems).isNotNull()
        assertThat(spanItems.size).isEqualTo(3)
        // Reset then expect no items in memory.
        exporter.reset()
        assertThat(exporter.getFinishedSpanItems()).isEmpty()
    }

    @Test
    fun shutdown() {
        tracer!!.spanBuilder("one").startSpan().end()
        tracer!!.spanBuilder("two").startSpan().end()
        tracer!!.spanBuilder("three").startSpan().end()
        val spanItems: List<SpanData> = exporter.getFinishedSpanItems()
        assertThat(spanItems).isNotNull()
        assertThat(spanItems.size).isEqualTo(3)
        // Shutdown then expect no items in memory.
        exporter.shutdown()
        assertThat(exporter.getFinishedSpanItems()).isEmpty()
        // Cannot add new elements after the shutdown.
        tracer!!.spanBuilder("one").startSpan().end()
        assertThat(exporter.getFinishedSpanItems()).isEmpty()
    }

    @Test
    fun export_ReturnCode() {
        assertThat(exporter.export(listOf(makeBasicSpan())).isSuccess()).isTrue()
        exporter.shutdown()
        // After shutdown no more export.
        assertThat(exporter.export(listOf(makeBasicSpan())).isSuccess()).isFalse()
        exporter.reset()
        // Reset does not do anything if already shutdown.
        assertThat(exporter.export(listOf(makeBasicSpan())).isSuccess()).isFalse()
    }

    companion object {
        fun makeBasicSpan(): SpanData {
            return TestSpanData.builder()
                .setHasEnded(true)
                .setName("span")
                .setKind(SpanKind.SERVER)
                .setStartEpochNanos(100000000100L)
                .setStatus(StatusData.ok())
                .setEndEpochNanos(200000000200L)
                .setTotalRecordedLinks(0)
                .setTotalRecordedEvents(0)
                .build()
        }
    }
}
