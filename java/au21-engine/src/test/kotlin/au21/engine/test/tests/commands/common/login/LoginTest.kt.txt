package au21.engine.test.commands.common.login

import au21.engine.framework.client.ClientsManager
import com.microsoft.playwright.BrowserType
import com.microsoft.playwright.Page
import com.microsoft.playwright.Playwright
import io.quarkus.test.junit.QuarkusTest
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import java.nio.file.Paths
import javax.inject.Inject

@Disabled
@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class LoginTest {

    @Inject
    lateinit var clients_manager: ClientsManager

    val browser = Playwright.create().chromium().launch(
        BrowserType.LaunchOptions()
            .setHeadless(false)
            .setSlowMo(1_000.0)
    )

    fun login(page: Page, username: String, password: String) {
        page.navigate("http:/localhost:8080/")
        page.fill("input[type='text']", username);
        page.fill("input[type='password']", password);
        page.click("button:has-text('Sign in')");
        page.screenshot(Page.ScreenshotOptions().setPath(Paths.get("example.png")))
    }

    @Test
    fun it_should_log_in() {
        val page: Page = browser.newPage()
        login(page, "a1", "1")
        page.title().shouldBe("au21-frontend")
        clients_manager.last_stores.size.shouldBe(1)
    }
}
