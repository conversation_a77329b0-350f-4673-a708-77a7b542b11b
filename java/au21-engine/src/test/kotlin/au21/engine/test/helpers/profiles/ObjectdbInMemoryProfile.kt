package au21.engine.test.helpers.profiles

import io.quarkus.test.junit.QuarkusTestProfile

/*
 * NOTE: not needed for OBJECTDB_URL if
 * - set in build.gradle and
 * - build run from gradle command line,
 *  - or IDE configured to run via gradle (which it currently is)
 *
 * IE: I believe that if nothing specified it uses the test db in gradle.build,
 *  - else it uses the specified profile:
 * ie:  @TestProfile(MyProfile.class)
 */

// speficied as default in gradle.build
class ObjectdbInMemoryProfile : QuarkusTestProfile{

    override fun getConfigOverrides(): MutableMap<String, String> {
        return mutableMapOf("OBJECTDB_URL" to "objectdb:test.mem")
    }

    fun func(){
        println("func")
    }

}


class ObjectdbClientServerProfile : QuarkusTestProfile{

    override fun getConfigOverrides(): MutableMap<String, String> {
        return mutableMapOf("OBJECTDB_URL" to "objectdb://localhost:6136/test.odb;user=admin;password=admin")
    }

    fun func(){
        println("func")
    }

}
