package au21.engine.test.helpers.validators

import au21.engine.domain.common.model.*
import au21.engine.domain.common.model.AuUserRole.AUCTIONEER
import au21.engine.domain.common.model.AuUserRole.TRADER
import au21.engine.domain.common.viewmodel.*
import au21.engine.domain.de.model.DeAuctioneerState
import au21.engine.domain.de.model.DeCommonState
import au21.engine.domain.de.model.DeFlowControlType
import au21.engine.domain.de.viewmodel.*
import au21.engine.framework.PageName
import au21.engine.framework.client.LiveClientStore
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldNotBeBlank


class LiveStoreValidator(session_id: String, time: TimeValue) {

    var expected_store = LiveClientStore(
        auction_rows = emptyList(),
        companies = emptyList(),
        counterparty_credits = emptyList(),
        de_auction = DeAuctionValue(
            auction_id = null,
            auction_counterparty_credits = emptyList(),
            auctioneer_info = null,
            auctioneer_status = null,
            award_value = null,
            blotter = DeBlotter(
                rounds = emptyList(),
                traders = emptyList(),
                round_traders = emptyList()
            ),
            matrix_last_round = null,
            messages = emptyList(),
            notice = "",
            settings = null,
            common_status = null,
            trader_history_rows = emptyList(),
            trader_info = null,
            users_that_have_seen_auction = emptySet()
        ),
        session_user = SessionUserValue(
            company_id = "",
            company_shortname = "",
            company_longname = "",
            current_auction_id = "",
            current_page = PageName.LOGIN_PAGE,
            isAuctioneer = false,
            isOnline = true,
            role = null,
            session_id = session_id,
            socket_state = AuSession.ClientSocketState.OPENED,
            user_id = "",
            username = ""
        ),
        time = time,
        users = emptyList()
    )

    fun role(): AuUserRole = expected_store.session_user?.role!!
    fun shouldBeAuctioneer() = role().shouldBe(AUCTIONEER)
    fun shouldBeTrader() = role().shouldBe(TRADER)
    fun session_user(): SessionUserValue = expected_store.session_user!!
    fun user() = expected_store.session_user

    fun de_auction_for_auctioneer(): DeAuctionValue {
        shouldBeAuctioneer()
        val de_auction = expected_store.de_auction
        de_auction.shouldNotBeNull()
        return de_auction
    }

    fun de_auction_for_trader(): DeAuctionValue {
        shouldBeTrader()
        val de_auction = expected_store.de_auction
        de_auction.shouldNotBeNull()
        return de_auction
    }


    fun login(role: AuUserRole, u: Person) {
        u.username.shouldNotBeBlank()
        val c = u.company
        expected_store = expected_store.copy(
            session_user = expected_store.session_user!!.copy(
                company_id = c?.id_str() ?: "",
                company_shortname = c?.shortname ?: "",
                company_longname = c?.longname ?: "",
                current_page = PageName.HOME_PAGE,
                isAuctioneer = role == AUCTIONEER,
                role = role,
                user_id = u.id_str(),
                username = u.username
            )
        )
    }

    // for auctioneers:
    fun trader_logged_in(trader_session: AuSession) {
        role().shouldBe(AUCTIONEER)
        expected_store = expected_store.copy(
            users = expected_store.users.map {
                when (it.user_id == trader_session.user!!.id_str()) {
                    true -> it.copy(
                        isOnline = true,
                        role = TRADER,
                        socket_state = AuSession.ClientSocketState.OPENED
                    )
                    false -> it.copy()
                }
            }
        )
    }

//    val null_auction_value = DeAuctionValue(
//        auction_id = null,
//        auctioneer_info = null,
//        auctioneer_status = null,
//        award_value = null,
//        blotter = DeBlotter(
//            rounds = emptyList(),
//            traders = emptyList(),
//            round_traders = emptyList()
//        ),
//        matrix_last_round = null,
//        messages = emptyList(),
//        common_status = null,
//        notice = "",
//        settings = null,
//        trader_history_rows = emptyList(),
//        trader_info = null,
//        users_that_have_seen_auction = emptySet()
//    )


    fun find_counterparty_credit(seller: Company, buyer: Company): CounterpartyCreditElement? =
        expected_store.counterparty_credits.find { it.seller_id == seller.id_str() && it.buyer_id == buyer.id_str() }

    fun auction_rows_clear() {
        expected_store = expected_store.copy(
            auction_rows = emptyList()
        )
    }

    fun auction_row_set(row: AuctionRowElement) {
        row.auction_name.shouldNotBeBlank()
        row.starting_time_text.shouldNotBeBlank()
        val rows = expected_store.auction_rows
            .filterNot { it.auction_id == row.auction_id } + row
        expected_store = expected_store.copy(
            auction_rows = rows.sortedBy { it.id.toInt() }
        )
    }


    fun company_set(c: CompanyElement) {
        shouldBeAuctioneer()
        val companies = expected_store.companies
            .filterNot { it.company_id == c.company_id } + c
        expected_store = expected_store.copy(
            companies = companies.sortedBy { it.company_id.toInt() }
        )
    }

    fun company_remove(c: Company) {
        shouldBeAuctioneer()
        expected_store = expected_store.copy(
            companies = expected_store.companies.filter { it.company_id != c.id_str() }
        )
    }

    fun counterparty_set(cp: CounterpartyCreditElement) {
        val counterparties = expected_store.counterparty_credits
            .filterNot { it.seller_id == cp.seller_id && it.buyer_id == cp.buyer_id } + cp
        expected_store = expected_store.copy(
            counterparty_credits = counterparties.sortedBy { it.seller_id.toInt() }
        )
    }

    fun counterparties_remove(c: Company) {
        shouldBeAuctioneer()
        expected_store = expected_store.copy(
            counterparty_credits = expected_store.counterparty_credits.filter {
                it.buyer_id.contains("BUYER.${c.id_str()}") ||
                        it.seller_id.contains("SELLER.${c.id_str()}")
            }
        )
    }

    fun de_auction_select(a: DeAuctionValue) {
        a.auction_id.shouldNotBeNull()
        user().shouldNotBeNull()
        expected_store = expected_store.copy(
            auction_rows = emptyList(), // because no longed on the home page
            de_auction = a,
            session_user = expected_store.session_user!!.copy(
                current_auction_id = a.auction_id!!,
                current_page = when (role()) {
                    AUCTIONEER -> PageName.DE_AUCTIONEER_PAGE
                    TRADER -> PageName.DE_TRADER_PAGE
                    else -> throw Exception("role should not be null")
                }
            ),
            // TODO: Not sure what this is for, for traders, should be emptylist
            users = when (role()) {
                TRADER -> emptyList()
                AUCTIONEER -> expected_store.users.map {
                    when (it.user_id == expected_store.session_user!!.user_id) {
                        true -> it.copy(current_auction_id = a.auction_id)
                        false -> it
                    }
                }
            }
        )
    }

    fun de_auctioneer_info_set(info: DeAuctioneerInfoValue) {
        shouldBeAuctioneer()
        expected_store = expected_store.copy(
            de_auction = expected_store.de_auction!!.copy(auctioneer_info = info)
        )
    }

    fun de_auctioneer_state_set(state: DeAuctioneerState, state_text: String) {
        val de_auction: DeAuctionValue = de_auction_for_auctioneer()
        expected_store = expected_store.copy(
            de_auction = de_auction.copy(
                auctioneer_status = de_auction.auctioneer_status!!.copy(
                    auctioneer_state = state,
                    auctioneer_state_text = state_text
                )
            )
        )
    }

    fun de_auctioneer_status_set(status: DeAuctioneerStatusValue) {
        shouldBeAuctioneer()
        expected_store = expected_store.copy(
            de_auction = expected_store.de_auction!!.copy(auctioneer_status = status)
        )
    }

    fun de_award_flow_remove(c: Company) {
        val de_auction: DeAuctionValue = de_auction_for_auctioneer()
        expected_store.copy(
            de_auction = de_auction.copy(
                award_value = de_auction.award_value!!.copy(
                    round_results = de_auction.award_value!!.round_results.map {
                        it.copy(
                            trader_flows = it.trader_flows.filterNot { f -> f.company_id == c.id_str() }
                        )
                    }
                )
            )
        ).also { expected_store = it }
    }

    fun de_award_flow_set(flow: DeTraderFlowVM) {
        val de_auction: DeAuctionValue = de_auction_for_auctioneer()
        val round_results = de_auction.award_value!!.round_results.map {
            it.copy(
                trader_flows = (it.trader_flows.filter { f -> f.company_id != flow.company_id } + flow)
                    .sortedBy { f -> f.company_id.toInt() }
            )
        }
        expected_store = expected_store.copy(
            de_auction = de_auction.copy(
                award_value = de_auction.award_value!!.copy(
                    round_results = round_results
                )
            )
        )
    }

    fun de_award_match_set(round_number: Int, m: DeScenarioMatchVM) {
        val de_auction: DeAuctionValue = de_auction_for_auctioneer()
        val award: DeAwardValue = de_auction.award_value!!
        val result: DeRoundResultVM =
            award.round_results
                .find { it.round_number == round_number }
                ?: throw Exception("no round results found for round number: $round_number")
        val matches = (result.matches
            .filterNot { it.buyer_id == m.buyer_id && it.seller_id == m.seller_id } + m
                ).sortedBy { it.buyer_id }

        expected_store = expected_store.copy(
            de_auction = de_auction.copy(
                award_value = award.copy(
                    round_results = award.round_results.map {
                        when (it.round_number == round_number) {
                            true -> it.copy(
                                matches = matches
                            )
                            false -> it
                        }
                    }.sortedBy { it.round_number }

                )
            )
        )
    }

    fun de_award_matches_clear(round_number: Int) {
        val de_auction: DeAuctionValue = de_auction_for_auctioneer()
        val award: DeAwardValue = de_auction.award_value!!
        expected_store = expected_store.copy(
            de_auction = de_auction.copy(
                award_value = award.copy(
                    round_results = award.round_results.map {
                        when (it.round_number == round_number) {
                            true -> it.copy(
                                matches = emptyList()
                            )
                            false -> it
                        }
                    }.sortedBy { it.round_number }

                )
            )
        )
    }

    fun de_award_value_set(award: DeAwardValue) {
        shouldBeAuctioneer()
        expected_store = expected_store.copy(
            de_auction = expected_store.de_auction!!.copy(award_value = award)
        )
    }

    fun de_blotter_round_set(round: DeRoundElement) {
        val de_auction: DeAuctionValue = de_auction_for_auctioneer()
        val rounds =
            (de_auction
                .blotter
                ?.rounds
                ?.filter { it.round_number != round.round_number }
                ?: emptyList()) + round

        expected_store = expected_store.copy(
            de_auction = de_auction.copy(
                blotter = de_auction.blotter?.copy(
                    rounds = rounds.sortedBy { it.round_number }
                )
            )
        )
    }

    fun de_blotter_trader_set(trader: DeTraderElement) {
        val de_auction: DeAuctionValue = de_auction_for_auctioneer()
        val traders =
            (de_auction.blotter
                ?.traders
                ?.filter { it.company_id != trader.company_id }
                ?: emptyList()) + trader
        expected_store = expected_store.copy(
            de_auction = de_auction.copy(
                blotter = de_auction.blotter?.copy(
                    traders = traders.sortedBy { it.company_id.toInt() }
                )
            )
        )
    }

    fun de_blotter_remove(c: Company) {
        val de_auction: DeAuctionValue = de_auction_for_auctioneer()
        expected_store = expected_store.copy(
            de_auction = de_auction.copy(
                blotter = de_auction.blotter?.copy(
                    traders = de_auction.blotter?.traders!!.filterNot { it.company_id == c.id_str() },
                    round_traders = de_auction.blotter?.round_traders!!.filterNot { it.cid == c.id_str() }
                )
            )
        )
    }

    fun de_blotter_round_trader_set(round_trader: DeRoundTraderElement) {
//        round_trader.apply {
//            if (order_submission_type == OrderSubmissionType.DEFAULT) {
//                order_submitted_by.shouldBe("default")
//            }
//            if (order_submission_type == OrderSubmissionType.MANDATORY) {
//                order_submitted_by.shouldBe("mandatory")
//            }
//            if (order_submission_type == OrderSubmissionType.MANUAL) {
//                order_submitted_by.shouldNotBeIn("default", "mandatory")
//            }
//            if (order_volume_type == OrderVolumeType.NONE) {
//                volume_str.shouldBe("0")
//                volume_int.shouldBe(0)
//            }
//        }
        val de_auction: DeAuctionValue = de_auction_for_auctioneer()
        val round_traders = de_auction.blotter?.round_traders!!.filterNot { it.cid == round_trader.cid && it.round == round_trader.round } + round_trader
        expected_store = expected_store.copy(
            de_auction = de_auction.copy(
                blotter = de_auction.blotter?.copy(
                    round_traders = round_traders.sortedBy { it.id } // { it.round }.sortedBy { it.cid.toInt() }
                )
            )
        )
    }

    fun de_common_state_set(state: DeCommonState, state_text: String) {
        val de_auction = expected_store.de_auction
        de_auction.shouldNotBeNull()
        expected_store = expected_store.copy(
            de_auction = de_auction.copy(
                common_status = de_auction.common_status!!.copy(
                    common_state = state,
                    common_state_text = state_text
                )
            )
        )
    }

    fun de_common_status_set(status: DeCommonStatusValue) {
        expected_store = expected_store.copy(
            de_auction = expected_store.de_auction!!.copy(
                common_status = status
            )
        )
    }

    fun de_flow_controls_set(controls: Map<DeFlowControlType, Boolean>) {
        val de_auction = de_auction_for_auctioneer()
        expected_store = expected_store.copy(
            de_auction = de_auction.copy(
                auctioneer_status = de_auction.auctioneer_status!!.copy(
                    controls = controls
                )
            )
        )
    }

    fun de_matrix_company_remove(c: Company) {
        val de_auction: DeAuctionValue = de_auction_for_auctioneer()
        val matrix = de_auction.matrix_last_round
        matrix.shouldNotBeNull()
        expected_store = expected_store.copy(
            de_auction = de_auction.copy(
                matrix_last_round = matrix.copy(
                    nodes = matrix.nodes.filter { it.cid != c.id_str() },
                    edges = matrix.edges.filterNot { it.buyer_cid == c.id_str() || it.seller_cid == c.id_str() }
                )
            )
        )
    }

    fun de_matrix_node_element_set(node: DeMatrixNodeElement) {
        val de_auction = de_auction_for_auctioneer()
        val nodes = de_auction.matrix_last_round!!.nodes
            .filterNot { it.cid == node.cid } + node
        expected_store = expected_store.copy(
            de_auction = expected_store.de_auction!!.copy(
                matrix_last_round = expected_store.de_auction!!.matrix_last_round!!.copy(
                    nodes = nodes.sortedBy { it.round }.sortedBy { it.cid.toInt() }
                )
            )
        )
    }

    fun de_matrix_edge_element_set(edge: DeMatrixEdgeElement) {
        val de_auction = de_auction_for_auctioneer()
        val edges = de_auction.matrix_last_round!!.edges
            .filterNot { it.buyer_cid == edge.buyer_cid && it.seller_cid == edge.seller_cid } + edge
        expected_store = expected_store.copy(
            de_auction = expected_store.de_auction!!.copy(
                matrix_last_round = expected_store.de_auction!!.matrix_last_round!!.copy(
                    edges = edges.sortedBy { it.r }.sortedBy { it.seller_cid.toInt() }
                )
            )
        )
    }

    fun de_matrix_round_set(mr: DeMatrixRoundElement) {
        val de_auction: DeAuctionValue = de_auction_for_auctioneer()
        //mr.round_number.shouldBe(de_auction.common_status?.round_number)
        //if (mr.round_number == de_auction.common_status?.round_number) {
        expected_store = expected_store.copy(
            de_auction = de_auction.copy(
                matrix_last_round = mr
            )
        )
        //}
    }

    // TODO: this should be moved to common_status and auctineer_status
    // - ie: Shouldn't hide expected in this class !
    fun de_starting_price_set(starting_price: String, announced: Boolean) {

        val de_auction = expected_store.de_auction
        de_auction.shouldNotBeNull()

        val auctioneer_status = de_auction.auctioneer_status
        if (role() == AUCTIONEER) {
            auctioneer_status.shouldNotBeNull()
        }

        val common_status = de_auction.common_status
        common_status.shouldNotBeNull()

        val settings = de_auction.settings
        settings.shouldNotBeNull()

        expected_store = expected_store.copy(
            de_auction = de_auction.copy(
                auctioneer_status = when (role()) {
                    TRADER -> null
                    AUCTIONEER -> auctioneer_status!!.copy(
                        auctioneer_state = DeAuctioneerState.STARTING_PRICE_SET,
                        auctioneer_state_text = "Starting price set",
                        controls = mapOf(
                            DeFlowControlType.SET_STARTING_PRICE to true,
                            DeFlowControlType.ANNOUNCE_STARTING_PRICE to true,
                            DeFlowControlType.START_AUCTION to false,
                            DeFlowControlType.CLOSE_ROUND to false,
                            DeFlowControlType.REOPEN_ROUND to false,
                            DeFlowControlType.NEXT_ROUND to false,
                            DeFlowControlType.AWARD_AUCTION to false
                        ),
                        starting_price = starting_price,
                    )
                },
                award_value = when (role()) {
                    TRADER -> null
                    AUCTIONEER -> de_auction.award_value!!.copy(
                        round_results = de_auction.award_value!!.round_results.map { result: DeRoundResultVM ->
                            when (result.round_number == 1) {
                                true -> result.copy(round_price = starting_price)
                                false -> result
                            }
                        }
                    )
                },
                blotter = de_auction.blotter?.copy(
                    rounds = when (role()) {
                        TRADER -> emptyList()
                        AUCTIONEER -> de_auction.blotter?.rounds!!.map { round: DeRoundElement ->
                            when (round.round_number == 1) {
                                true -> round.copy(
                                    round_price_str = starting_price,
                                    round_price = starting_price.toDouble()
                                )
                                false -> round
                            }
                        }
                    }
                ),
                common_status = common_status.copy(
                    round_price = when {
                        announced -> starting_price
                        else -> "waiting"
                    }
                )
            )
        )
    }

    fun de_trader_history_row_excess_set(round_number: Int, excess_side: OrderType, excess_level: String) {
        val de_auction = de_auction_for_trader()
        expected_store = expected_store.copy(
            de_auction = de_auction.copy(
                trader_history_rows = de_auction.trader_history_rows.map { row ->
                    if (row.round_number == round_number.toString()) {
                        row.copy(
                            excess_side = excess_side,
                            excess_level = excess_level
                        )
                    } else {
                        row
                    }
                }
            )
        )
    }

    fun de_trader_history_row_set(row: DeTraderHistoryRowElement) {
        val de_auction = de_auction_for_trader()
        row.auction_id.shouldBe(de_auction.auction_id)
        val rows = de_auction.trader_history_rows
            .filter { it.round_number != row.round_number } + row

        expected_store = expected_store.copy(
            de_auction = de_auction.copy(
                trader_history_rows = rows.sortedBy { it.round_number }
            )
        )
    }

    fun de_trader_info_set(info: DeTraderInfoValue) {
        val de_auction = de_auction_for_trader()
        info.company_id.shouldBe(user()?.company_id)
        expected_store = expected_store.copy(
            de_auction = de_auction.copy(
                trader_info = info
            )
        )
    }

    fun message_add(message: MessageElement) {
        val de_auction = expected_store.de_auction
        de_auction.shouldNotBeNull()
        expected_store = expected_store.copy(
            de_auction = de_auction.copy(messages = de_auction.messages + message)
        )
    }

    fun messages_clear() {
        val de_auction = expected_store.de_auction
        de_auction.shouldNotBeNull()
        expected_store = expected_store.copy(
            de_auction = de_auction.copy(messages = emptyList())
        )
    }

    fun notice_set(notice: String) {
        val de_auction = expected_store.de_auction
        de_auction.shouldNotBeNull()
        expected_store = expected_store.copy(
            de_auction = de_auction.copy(notice = notice)
        )
    }

    fun page_set_and_auction_reset(page: PageName) {
        // must come before de_auction_set !!
        expected_store = expected_store.copy(
            session_user = session_user().copy(
                current_page = page,
                current_auction_id = ""
            ),
            de_auction = DeAuctionValue.value_for_null_auction
        )
        // then after this, set the auction!
    }

    fun user_has_seen_auction(s: AuSession) {
        val de_auction = de_auction_for_auctioneer()
        val cid = s.user?.company?.id_str()
        val uid = s.user?.id_str()
        uid.shouldNotBeNull()
        cid.shouldNotBeNull()

        expected_store = expected_store.copy(
            de_auction = de_auction.copy(
                blotter = de_auction.blotter?.copy(
                    traders = de_auction.blotter?.traders!!.map {
                        when (it.company_id == cid) {
                            true -> it.copy(has_seen_auction = true)
                            false -> it
                        }
                    },
                ),
                users_that_have_seen_auction = de_auction.users_that_have_seen_auction + uid // should be idempotent
            )
        )
    }

    fun user_delete(u: Person) {
        shouldBeAuctioneer()
        expected_store = expected_store.copy(
            users = expected_store.users.filter { it.id != u.id_str() }
        )
    }

    fun user_current_auction_id(u: Person, a: Auction?) {
        shouldBeAuctioneer()
        expected_store = expected_store.copy(
            users = expected_store.users.map { ue: UserElement ->
                when (ue.user_id == u.id_str()) {
                    true -> ue.copy(current_auction_id = a?.id_str())
                    false -> ue.copy()
                }
            }
        )
    }

    fun user_set(user: UserElement) {
        shouldBeAuctioneer()
        val users = expected_store.users
            .filterNot { it.user_id == user.user_id } + user
        expected_store = expected_store.copy(
            users = users.sortedBy { it.id.toInt() }
        )
    }

}
