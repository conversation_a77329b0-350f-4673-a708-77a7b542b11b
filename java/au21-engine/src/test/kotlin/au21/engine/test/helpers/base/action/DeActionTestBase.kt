package au21.engine.test.helpers.base.action

import au21.engine.domain.common.commands.ClientSocketAction
import au21.engine.domain.common.commands.DbInitAction
import au21.engine.domain.common.commands.LoginAction
import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.Company
import au21.engine.domain.common.model.Person
import au21.engine.domain.de.model.DeAuction
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.randomString
import io.kotest.matchers.nulls.shouldNotBeNull
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import javax.inject.Inject


open class DeActionTestBase :CommonActionTestBase(){


}
