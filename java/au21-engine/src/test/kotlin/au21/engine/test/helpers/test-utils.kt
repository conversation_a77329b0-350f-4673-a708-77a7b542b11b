package au21.engine.test.helpers

import kotlin.reflect.KFunction

fun call_fn(f: KFunction<Unit>) {
    try {
        println("-------------------------------------------")
        println(f.name)
        println("-------------------------------------------")
        f.call()
    } catch (e: Throwable) {
        when (e.cause) {
            null -> throw e
            else -> throw e.cause!!
        }
    }
}
