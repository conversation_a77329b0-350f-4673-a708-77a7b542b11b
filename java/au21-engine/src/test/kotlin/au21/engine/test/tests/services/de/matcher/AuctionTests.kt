package au21.engine.test.tests.services.de.matcher

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Company
import au21.engine.domain.de.model.DeAuctioneerState
import au21.engine.domain.de.model.DeCommonState
import au21.engine.domain.de.model.DeFlowControlType
import au21.engine.test.helpers.base.command.CommandTestBase
import io.kotest.matchers.nulls.beNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNot
import io.kotest.matchers.string.beEmpty
import io.quarkus.test.junit.QuarkusTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

@QuarkusTest
internal class AuctionTests : CommandTestBase() {

    lateinit var auctioneerSession: AuSession
    lateinit var companies: Map<Company, AuSession>

    @BeforeEach
    fun setUp() {
        println("Setting up all the parties: auctioneers, traders")
        db_init_command()
        auctioneerSession = create_auctioneer_session_and_login()
        companies = listOf("c1", "c2").map { create_company(auctioneerSession, it) }.associateWith {
            val username = "u${it.shortname}"
            createUser(auctioneerSession, username, it)
            val session = create_session()
            login_command(session, username, "1")
            session
        }

    }

    @Test
    fun `create an auction and verify it's state`() {
        val auction = create_auction(auctioneerSession, "Test")
        auction shouldNot beNull()
        auction.id_str() shouldNot beEmpty()
        auction.id shouldNot beNull()
        auction.starting_time_text() shouldNot beEmpty()
        auction.auction_name shouldBe "Test"
        auction.auctioneer_state shouldBe DeAuctioneerState.STARTING_PRICE_NOT_SET
        auction.auction_has_started shouldBe false
        auction.de_trading_companies shouldBe emptyList()
        auction.common_state shouldBe DeCommonState.SETUP
        auction.common_state_text shouldBe "Waiting for starting price"
        auction.companies_that_have_seen_auction shouldBe emptyList()
        auction.price_decimal_places shouldBe 3
        auction.rounds.size shouldBe 1
    }

    @Test
    fun `set an initial price in the auction and check the state`() {
        val auctionName = "Test"
        val auction = create_auction(auctioneerSession, auctionName)
        de_flow_control_command(auctioneerSession, auction, DeFlowControlType.SET_STARTING_PRICE, "100.000")
        val auctionAfterPriceSet = findAuction(auctionName)
        auctionAfterPriceSet shouldNot beNull()
        auctionAfterPriceSet?.auction_has_started() shouldBe false
        auctionAfterPriceSet?.auctioneer_state shouldBe DeAuctioneerState.STARTING_PRICE_SET
        auctionAfterPriceSet?.auctioneer_state_text shouldBe "Starting price set"
        auctionAfterPriceSet?.common_state_text shouldBe "Waiting for starting price"
        auctionAfterPriceSet?.firstround()?.price shouldBe 100.000

        de_flow_control_command(auctioneerSession, auction, DeFlowControlType.ANNOUNCE_STARTING_PRICE)
        val auctionAfterPriceAnnounced = findAuction(auctionName)
        auctionAfterPriceAnnounced?.auction_has_started() shouldBe false
        auctionAfterPriceAnnounced?.auctioneer_state shouldBe DeAuctioneerState.STARTING_PRICE_ANNOUNCED
        auctionAfterPriceAnnounced?.auctioneer_state_text shouldBe "Starting price announced"
        auctionAfterPriceSet?.common_state_text shouldBe "Starting price announced"
    }
}
