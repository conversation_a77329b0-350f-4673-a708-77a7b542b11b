package au21.engine.test.services.de.constraints

import au21.engine.domain.common.model.OrderVolumeType
import au21.engine.domain.common.model.PriceDirection
import au21.engine.domain.de.model.DeAuction.DeBidConstraints
import au21.engine.domain.de.services.constraints.calculate_constraints
import au21.engine.framework.commands.AlertException
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.FreeSpec
import io.kotest.data.row
import io.kotest.matchers.shouldBe

class DeOrderConstraintExceptionSpec : FreeSpec({

    "next round constraints with exceptions" - {
        listOf(

            /** NONE */

            // error if min buy
            row(
                "NONE: error if there is a min buy if price increases",
                DeBidConstraints(50, 10, 0, 0),
                OrderVolumeType.NONE,
                0,
                PriceDirection.UP,
                "Cannot enter a zero volume bid if min buy volume > 0."
            ),
            row(
                "NONE: error if there is a min buy if price decreases",
                DeBidConstraints(50, 10, 0, 0),
                OrderVolumeType.NONE,
                0,
                PriceDirection.DOWN,
                "Cannot enter a zero volume bid if min buy volume > 0."
            ),

            // error if min sell
            row(
                "NONE: error if there is a min sell if price increases",
                DeBidConstraints(0, 0, 10, 50),
                OrderVolumeType.NONE,
                0,
                PriceDirection.UP,
                "cannot enter a zero volume bid if min sell volume > 0."
            ),
            row(
                "NONE: error if there is a min sell if price decreases",
                DeBidConstraints(0, 0, 10, 50),
                OrderVolumeType.NONE,
                0,
                PriceDirection.DOWN,
                "cannot enter a zero volume bid if min sell volume > 0."
            ),


            /** BUY */

            // buy below min buy error:
            row(
                "BUY: error if buy vol below min buy and price increases",
                DeBidConstraints(50, 10, 0, 0),
                OrderVolumeType.BUY,
                5,
                PriceDirection.UP,
                "Cannot enter a buy order for less than the minimum buy volume of 10."
            ),
            row(
                "BUY: error if buy vol below min buy and price decreases",
                DeBidConstraints(50, 10, 0, 0),
                OrderVolumeType.BUY,
                5,
                PriceDirection.DOWN,
                "Cannot enter a buy order for less than the minimum buy volume of 10."
            ),

            // buy above max buy error:

            row(
                "BUY: error if buy vol above max buy and price increases",
                DeBidConstraints(50, 10, 0, 0),
                OrderVolumeType.BUY,
                55,
                PriceDirection.UP,
                "Cannot enter a buy order for more than the maximum buy volume of 50."
            ),
            row(
                "BUY: error if buy vol above max buy and price decreases",
                DeBidConstraints(50, 10, 0, 0),
                OrderVolumeType.BUY,
                55,
                PriceDirection.DOWN,
                "Cannot enter a buy order for more than the maximum buy volume of 50."
            ),

            /** SELL */

            // sell below min sell error:
            row(
                "SELL: error if sell vol below min sell and price increases",
                DeBidConstraints(0, 0, 10, 50),
                OrderVolumeType.SELL,
                5,
                PriceDirection.UP,
                "Cannot enter a sell order for less than the minimum sell volume of 10."
            ),
            row(
                "SELL: error if sell vol below min sell and price decreases",
                DeBidConstraints(0, 0, 10, 50),
                OrderVolumeType.SELL,
                5,
                PriceDirection.DOWN,
                "Cannot enter a sell order for less than the minimum sell volume of 10."
            ),

            // sell above max sell error:

            row(
                "SELL: error if sell vol above max sell and price increases",
                DeBidConstraints(0, 0, 10, 50),
                OrderVolumeType.SELL,
                55,
                PriceDirection.UP,
                "Cannot enter a sell order for more than the maximum sell volume of 50."
            ),
            row(
                "SELL: error if sell vol above max sell and price decreases",
                DeBidConstraints(0, 0, 10, 50),
                OrderVolumeType.SELL,
                55,
                PriceDirection.DOWN,
                "Cannot enter a sell order for more than the maximum sell volume of 50."
            ),

            /** Min buy > Max buy */
            row(
                "BUY: error if min buy > max buy, price increases",
                DeBidConstraints(50, 55, 0, 60),
                OrderVolumeType.BUY,
                50,
                PriceDirection.UP,
                "Min buy volume cannot be greater than max buy volume."
            ),
            row(
                "BUY: error if min buy > max buy, price decreases",
                DeBidConstraints(50, 55, 0, 60),
                OrderVolumeType.BUY,
                50,
                PriceDirection.DOWN,
                "Min buy volume cannot be greater than max buy volume."
            ),


            /** Min sell > Max sell */
            row(
                "SELL: error if min sell > max sell, price increases",
                DeBidConstraints(60, 0, 55, 50),
                OrderVolumeType.SELL,
                50,
                PriceDirection.UP,
                "Min sell volume cannot be greater than max sell volume."
            ),
            row(
                "SELL: error if min sell > max sell, price decreases",
                DeBidConstraints(60, 0, 55, 50),
                OrderVolumeType.SELL,
                50,
                PriceDirection.DOWN,
                "Min sell volume cannot be greater than max sell volume."
            ),

            ).map { (
                        description: String,
                        prev_constraints: DeBidConstraints,
                        order_volume_type: OrderVolumeType,
                        order_volume: Int,
                        next_round_direction: PriceDirection,
                        exception_message: String) ->

            description {
                shouldThrow<AlertException> {
                    calculate_constraints(
                        prev_constraints,
                        order_volume_type,
                        order_volume,
                        next_round_direction
                    )
                }.message.shouldBe(exception_message)
            }
        }
    }
})
