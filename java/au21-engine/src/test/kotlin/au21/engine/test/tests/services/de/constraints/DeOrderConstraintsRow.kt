package au21.engine.test.tests.services.de.constraints

import au21.engine.domain.common.model.OrderType
import au21.engine.domain.common.model.PriceDirection
import au21.engine.domain.de.model.DeBidConstraints

class DeOrderConstraintsRow(
    val description: String,
    val prev_constraints: DeBidConstraints,
    val order_quantity_type: OrderType,
    val order_quantity: Int,
    val next_direction: PriceDirection,
    val next_constraints: DeBidConstraints,
    val exception_message: String? = null
)
