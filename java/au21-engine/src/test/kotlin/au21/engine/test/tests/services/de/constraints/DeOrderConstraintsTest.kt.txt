package au21.engine.test.services.de.constraints

import au21.engine.domain.common.model.OrderVolumeType
import au21.engine.domain.common.model.PriceDirection
import au21.engine.domain.de.model.DeAuction.DeBidConstraints
import au21.engine.domain.de.services.constraints.calculate_constraints
import io.kotest.matchers.equality.shouldBeEqualToComparingFields
import org.junit.jupiter.api.Test

class DeOrderConstraintsTest {

    @Test
    fun `next round constraints without exceptions`() {
        listOf(
            /** NONE */

            // buy and sell = 0
            DeOrderConstraintsRow(
                "NONE: buy = 0 sell = 0, price increases => no buy, no sell",
                DeBidConstraints(0, 0, 0, 0),
                OrderVolumeType.NONE,
                0,
                PriceDirection.UP,
                DeBidConstraints(0, 0, 0, 0)
            ),
            DeOrderConstraintsRow(
                "NONE: buy = 0 sell = 0, price decreases => no buy, no sell",
                DeBidConstraints(0, 0, 0, 0),
                OrderVolumeType.NONE,
                0,
                PriceDirection.DOWN,
                DeBidConstraints(0, 0, 0, 0)
            ),

            // buy = 0, sell > 0
            DeOrderConstraintsRow(
                "NONE: buy = 0 sell > 0, price increases => no buy",
                DeBidConstraints(0, 0, 0, 50),
                OrderVolumeType.NONE,
                0,
                PriceDirection.UP,
                DeBidConstraints(0, 0, 0, 50)
            ),
            DeOrderConstraintsRow(
                "NONE: buy = 0 sell > 0, price decreases => no buy, no sell",
                DeBidConstraints(0, 0, 0, 50),
                OrderVolumeType.NONE,
                0,
                PriceDirection.DOWN,
                DeBidConstraints(0, 0, 0, 0)
            ),

            // buy > 0, sell = 0
            DeOrderConstraintsRow(
                "NONE: buy > 0 sell = 0, price increases => no buy, no sell",
                DeBidConstraints(50, 0, 0, 0),
                OrderVolumeType.NONE,
                0,
                PriceDirection.UP,
                DeBidConstraints(0, 0, 0, 0)
            ),
            DeOrderConstraintsRow(
                "NONE: buy > 0 sell = 0, price decreases => no sell",
                DeBidConstraints(50, 0, 0, 0),
                OrderVolumeType.NONE,
                0,
                PriceDirection.DOWN,
                DeBidConstraints(50, 0, 0, 0)
            ),

            // - buy and sell > 0
            DeOrderConstraintsRow(
                "NONE: buy and sell > 0, price increases => no buy",
                DeBidConstraints(50, 0, 0, 50),
                OrderVolumeType.NONE,
                0,
                PriceDirection.UP,
                DeBidConstraints(0, 0, 0, 50)
            ),
            DeOrderConstraintsRow(
                "NONE: buy and sell > 0, price decreases => no sell",
                DeBidConstraints(50, 0, 0, 50),
                OrderVolumeType.NONE,
                0,
                PriceDirection.DOWN,
                DeBidConstraints(50, 0, 0, 0)
            ),

            /** BUY */

            // - no min buy, vol = 0
            DeOrderConstraintsRow(
                "BUY: no min buy, buy volume = 0, price increases => max buy to zero",
                DeBidConstraints(50, 0, 0, 50),
                OrderVolumeType.BUY,
                0,
                PriceDirection.UP,
                DeBidConstraints(0, 0, 0, 50)
            ),
            DeOrderConstraintsRow(
                "BUY: no min buy, buy volume = 0, price decreases => no sell vol",
                DeBidConstraints(50, 0, 0, 50),
                OrderVolumeType.BUY,
                0,
                PriceDirection.DOWN,
                DeBidConstraints(50, 0, 0, 0)
            ),

            // - no min buy, vol > 0
            DeOrderConstraintsRow(
                "BUY: no min buy, buy volume > 0, price increases => max buy = buy volume",
                DeBidConstraints(50, 0, 0, 50),
                OrderVolumeType.BUY,
                10,
                PriceDirection.UP,
                DeBidConstraints(10, 0, 0, 50)
            ),
            DeOrderConstraintsRow(
                "BUY: no min buy, buy volume > 0, price decreases => min buy = buy volume, no sell vol",
                DeBidConstraints(50, 0, 0, 50),
                OrderVolumeType.BUY,
                10,
                PriceDirection.DOWN,
                DeBidConstraints(50, 10, 0, 0)
            ),

            // - min buy, vol = min buy, if there is a min buy, there cannot be any sell
            DeOrderConstraintsRow(
                "BUY: min buy = buy volume, price increases => max buy and min buy = order",
                DeBidConstraints(50, 10, 0, 0),
                OrderVolumeType.BUY,
                10,
                PriceDirection.UP,
                DeBidConstraints(10, 10, 0, 0)
            ),
            DeOrderConstraintsRow(
                "BUY: min buy = buy volume, price decreases => min buy = order, and no sell",
                DeBidConstraints(50, 10, 0, 0),
                OrderVolumeType.BUY,
                10,
                PriceDirection.DOWN,
                DeBidConstraints(50, 10, 0, 0)
            ),

            // - buy > min
            DeOrderConstraintsRow(
                "BUY: buy volume > min buy, when price increases -> max buy = order",
                DeBidConstraints(50, 10, 0, 0),
                OrderVolumeType.BUY,
                15,
                PriceDirection.UP,
                DeBidConstraints(15, 10, 0, 0)
            ),
            DeOrderConstraintsRow(
                "BUY: buy volume > min buy, when price decreases -> min buy = order, and no sell",
                DeBidConstraints(50, 10, 0, 0),
                OrderVolumeType.BUY,
                15,
                PriceDirection.DOWN,
                DeBidConstraints(50, 15, 0, 0)
            ),

            /** SELL */

            // - no min sell, vol = 0
            DeOrderConstraintsRow(
                "SELL: no min sell, sell volume = 0, price increases => max buy to zero",
                DeBidConstraints(50, 0, 0, 50),
                OrderVolumeType.SELL,
                0,
                PriceDirection.UP,
                DeBidConstraints(0, 0, 0, 50)
            ),
            DeOrderConstraintsRow(
                "SELL: no min sell, sell volume = 0, price decreases => no sell vol",
                DeBidConstraints(50, 0, 0, 50),
                OrderVolumeType.SELL,
                0,
                PriceDirection.DOWN,
                DeBidConstraints(50, 0, 0, 0)
            ),

            // - no min sell, vol > 0
            DeOrderConstraintsRow(
                "SELL: no min sell, sell volume > 0, price increases => min sell = sell volume, no buy vol",
                DeBidConstraints(50, 0, 0, 50),
                OrderVolumeType.SELL,
                10,
                PriceDirection.UP,
                DeBidConstraints(0, 0, 10, 50)
            ),
            DeOrderConstraintsRow(
                "SELL: no min sell, sell volume > 0, price decreases => max sell = sell volume",
                DeBidConstraints(50, 0, 0, 50),
                OrderVolumeType.SELL,
                10,
                PriceDirection.DOWN,
                DeBidConstraints(50, 0, 0, 10)
            ),

            // - min sell, vol = min sell, note: if there is a min sell, there cannot be any buy
            DeOrderConstraintsRow(
                "SELL: min sell = sell volume, price increases => no change",
                DeBidConstraints(0, 0, 10, 50),
                OrderVolumeType.SELL,
                10,
                PriceDirection.UP,
                DeBidConstraints(0, 0, 10, 50)
            ),
            DeOrderConstraintsRow(
                "SELL: min sell = sell volume, price decreases => max sell = min sell = order",
                DeBidConstraints(0, 0, 10, 50),
                OrderVolumeType.SELL,
                10,
                PriceDirection.DOWN,
                DeBidConstraints(0, 0, 10, 10)
            ),

            // - sell > min
            DeOrderConstraintsRow(
                "SELL: sell volume > min sell, when price increases => min sell = order",
                DeBidConstraints(0, 0, 10, 50),
                OrderVolumeType.SELL,
                15,
                PriceDirection.UP,
                DeBidConstraints(0, 0, 15, 50)
            ),
            DeOrderConstraintsRow(
                description = "SELL: sell volume > min sell, when price decreases => max sell = order",
                prev_constraints = DeBidConstraints(0, 0, 10, 50),
                order_volume_type = OrderVolumeType.SELL,
                order_volume = 15,
                next_direction = PriceDirection.DOWN,
                next_constraints = DeBidConstraints(0, 0, 10, 15)
            ),

            ).map { row: DeOrderConstraintsRow ->

            row.apply {

                calculate_constraints(
                    prev_constraints,
                    order_volume_type,
                    order_volume,
                    next_direction
                ).shouldBeEqualToComparingFields(row.next_constraints)
            }
        }
    }
}
