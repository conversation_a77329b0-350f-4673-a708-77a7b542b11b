package au21.engine.test.helpers.base.command

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.model.Person
import au21.engine.domain.common.services.sessions_logged_in
import au21.engine.domain.de.viewmodel.DeMatrixEdgeElement
import au21.engine.domain.de.viewmodel.DeMatrixNodeElement
import au21.engine.domain.de.viewmodel.DeRoundResultVM
import au21.engine.framework.client.LiveClientStore
import au21.engine.framework.database.AuEntity
import au21.engine.framework.utils.to_json
import au21.engine.test.helpers.validators.LiveStoreValidator
import io.kotest.assertions.json.shouldEqualJson
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.equality.shouldBeEqualToComparingFields
import io.kotest.matchers.equality.shouldBeEqualToIgnoringFields
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.string.shouldNotBeBlank

open class CommandTestValidator : CommandTestBase() {

    val expected_stores = mutableListOf<LiveStoreValidator>()

    fun create_store_validator(s: AuSession): LiveStoreValidator {
        s.session_id.shouldNotBeBlank()
        val store = LiveStoreValidator(s.session_id, time_value).also {
            expected_stores.add(it)
        }
        return store
    }

    // https://www.novatec-gmbh.de/blog/kotlin-assertion-libraries-atrium/

    fun expect_last_stores_to_be_empty() {
        client_manager.last_stores.shouldBeEmpty()
    }


    fun should_be_equal_using_json(actual: Any?, expected: Any?) =
        when (actual) {
            null -> expected.shouldBeNull()
            else -> {
                expected.shouldNotBeNull()
                actual.to_json().shouldEqualJson(expected.to_json())
            }
        }

    fun expect_stores(json: Boolean) {
        val actual_stores_sorted: List<LiveClientStore> = last_stores().map { it.store }

        // for debugging set the last store on each expected_store:
        val expected_stores: List<LiveClientStore> = actual_stores_sorted.map { actual_store ->
            val expected_store = expected_stores.find { expected ->
                actual_store.session_user!!.session_id == expected.expected_store.session_user!!.session_id
            }
            expected_store.shouldNotBeNull()
            expected_store.expected_store.let { s: LiveClientStore ->
                val results: List<DeRoundResultVM>? =
                    s.de_auction?.award_value?.round_results?.map { result: DeRoundResultVM ->
                        result.copy(
                            trader_flows = result.trader_flows.sortedBy { f -> f.company_id }
                        )
                    }

                val nodes: List<DeMatrixNodeElement>? =
                    s.de_auction?.matrix_last_round?.nodes

                val edges: List<DeMatrixEdgeElement>? =
                    s.de_auction?.matrix_last_round?.edges

                s.copy(
                    de_auction = s.de_auction?.copy(
                        award_value = s.de_auction?.award_value?.copy(
                            round_results = results ?: emptyList()
                        ),
                        matrix_last_round = s.de_auction?.matrix_last_round?.copy(
                            nodes = nodes?.sortedBy { it.id }
                                ?: emptyList(),
                            edges = edges?.sortedBy { it.id }
                                ?: emptyList()
                        )
                    )
                )
            }
        }


        when (json) {
            true -> // might be easier to debug with json
                should_be_equal_using_json(actual_stores_sorted, expected_stores)
            false ->
                actual_stores_sorted.forEachIndexed { i: Int, actual: LiveClientStore ->
                    expected_stores[i].let { expected: LiveClientStore ->
                        actual.apply {
                            auction_rows.shouldBeEqualToComparingFields(expected.auction_rows)
                            companies.shouldBeEqualToComparingFields(expected.companies)
                            counterparty_credits.shouldBeEqualToComparingFields(expected.counterparty_credits)
                            session_user?.let { }
                            when (session_user) {
                                null -> expected.session_user.shouldBeNull()
                                else -> session_user!!.shouldBeEqualToComparingFields(expected.session_user!!)
                            }
                            when (time) {
                                null -> expected.time.shouldBeNull()
                                else -> time!!.shouldBeEqualToComparingFields(expected.time!!)
                            }
                            users.shouldBeEqualToComparingFields(expected.users)
                        }
                    }
                }
        }
    }

    fun expect_auctions_empty(empty: Boolean) =
        db.findAll<Auction>().let { auctions ->
            when (empty) {
                true -> auctions.shouldBeEmpty()
                false -> auctions.shouldNotBeEmpty()
            }
        }

    fun expect_db_inited() {
        db.findAll<AuEntity>().let {
            it.shouldHaveSize(1)
            it[0].shouldBeEqualToIgnoringFields(
                Person(AuUserRole.AUCTIONEER, "a1", "1"),
                Person::id
            )
        }
    }

    fun expect_session_users(vararg usernames: String) {
        db.findAll<AuSession>()
            .map { it.user?.username ?: "" }
            .shouldContainExactlyInAnyOrder(*usernames)
    }

    fun expect_user_logged_in(username: String) {
        db.sessions_logged_in()
            .find { it.user?.username == username }
            .shouldNotBeNull()
            .is_logged_in().shouldBeTrue()
    }

    fun expect_auctions_by_name(vararg auction_names: String) {
        val auctions = db.findAll<Auction>()
        auctions.map { it.auction_name }
            .shouldContainExactlyInAnyOrder(*auction_names)
    }


}
