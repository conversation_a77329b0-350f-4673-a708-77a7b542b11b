package au21.engine.test.services.de.constraints

import au21.engine.domain.common.model.OrderVolumeType
import au21.engine.domain.common.model.PriceDirection
import au21.engine.domain.de.model.DeAuction.DeBidConstraints
import au21.engine.domain.de.services.constraints.calculate_constraints
import io.kotest.core.spec.style.FreeSpec
import io.kotest.data.row
import io.kotest.matchers.equality.shouldBeEqualToComparingFields

class DeOrderConstraintsSpec : FreeSpec({

    "next round constraints without exceptions" - {
        listOf(
            /** NONE */

            // buy and sell = 0
            row(
                "NONE: buy = 0 sell = 0, price increases => no buy, no sell",
                DeBidConstraints(0, 0, 0, 0),
                OrderVolumeType.NONE,
                0,
                PriceDirection.UP,
                DeBidConstraints(0, 0, 0, 0)
            ),
            row(
                "NONE: buy = 0 sell = 0, price decreases => no buy, no sell",
                DeBidConstraints(0, 0, 0, 0),
                OrderVolumeType.NONE,
                0,
                PriceDirection.DOWN,
                DeBidConstraints(0, 0, 0, 0)
            ),

            // buy = 0, sell > 0
            row(
                "NONE: buy = 0 sell > 0, price increases => no buy",
                DeBidConstraints(0, 0, 0, 50),
                OrderVolumeType.NONE,
                0,
                PriceDirection.UP,
                DeBidConstraints(0, 0, 0, 50)
            ),
            row(
                "NONE: buy = 0 sell > 0, price decreases => no buy, no sell",
                DeBidConstraints(0, 0, 0, 50),
                OrderVolumeType.NONE,
                0,
                PriceDirection.DOWN,
                DeBidConstraints(0, 0, 0, 0)
            ),

            // buy > 0, sell = 0
            row(
                "NONE: buy > 0 sell = 0, price increases => no buy, no sell",
                DeBidConstraints(50, 0, 0, 0),
                OrderVolumeType.NONE,
                0,
                PriceDirection.UP,
                DeBidConstraints(0, 0, 0, 0)
            ),
            row(
                "NONE: buy > 0 sell = 0, price decreases => no sell",
                DeBidConstraints(50, 0, 0, 0),
                OrderVolumeType.NONE,
                0,
                PriceDirection.DOWN,
                DeBidConstraints(50, 0, 0, 0)
            ),

            // - buy and sell > 0
            row(
                "NONE: buy and sell > 0, price increases => no buy",
                DeBidConstraints(50, 0, 0, 50),
                OrderVolumeType.NONE,
                0,
                PriceDirection.UP,
                DeBidConstraints(0, 0, 0, 50)
            ),
            row(
                "NONE: buy and sell > 0, price decreases => no sell",
                DeBidConstraints(50, 0, 0, 50),
                OrderVolumeType.NONE,
                0,
                PriceDirection.DOWN,
                DeBidConstraints(50, 0, 0, 0)
            ),

            /** BUY */

            // - no min buy, vol = 0
            row(
                "BUY: no min buy, buy volume = 0, price increases => max buy to zero",
                DeBidConstraints(50, 0, 0, 50),
                OrderVolumeType.BUY,
                0,
                PriceDirection.UP,
                DeBidConstraints(0, 0, 0, 50)
            ),
            row(
                "BUY: no min buy, buy volume = 0, price decreases => no sell vol",
                DeBidConstraints(50, 0, 0, 50),
                OrderVolumeType.BUY,
                0,
                PriceDirection.DOWN,
                DeBidConstraints(50, 0, 0, 0)
            ),

            // - no min buy, vol > 0
            row(
                "BUY: no min buy, buy volume > 0, price increases => max buy = buy volume",
                DeBidConstraints(50, 0, 0, 50),
                OrderVolumeType.BUY,
                10,
                PriceDirection.UP,
                DeBidConstraints(10, 0, 0, 50)
            ),
            row(
                "BUY: no min buy, buy volume > 0, price decreases => min buy = buy volume, no sell vol",
                DeBidConstraints(50, 0, 0, 50),
                OrderVolumeType.BUY,
                10,
                PriceDirection.DOWN,
                DeBidConstraints(50, 10, 0, 0)
            ),

            // - min buy, vol = min buy, if there is a min buy, there cannot be any sell
            row(
                "BUY: min buy = buy volume, price increases => max buy and min buy = order",
                DeBidConstraints(50, 10, 0, 0),
                OrderVolumeType.BUY,
                10,
                PriceDirection.UP,
                DeBidConstraints(10, 10, 0, 0)
            ),
            row(
                "BUY: min buy = buy volume, price decreases => min buy = order, and no sell",
                DeBidConstraints(50, 10, 0, 0),
                OrderVolumeType.BUY,
                10,
                PriceDirection.DOWN,
                DeBidConstraints(50, 10, 0, 0)
            ),

            // - buy > min
            row(
                "BUY: buy volume > min buy, when price increases -> max buy = order",
                DeBidConstraints(50, 10, 0, 0),
                OrderVolumeType.BUY,
                15,
                PriceDirection.UP,
                DeBidConstraints(15, 10, 0, 0)
            ),
            row(
                "BUY: buy volume > min buy, when price decreases -> min buy = order, and no sell",
                DeBidConstraints(50, 10, 0, 0),
                OrderVolumeType.BUY,
                15,
                PriceDirection.DOWN,
                DeBidConstraints(50, 15, 0, 0)
            ),

            /** SELL */

            // - no min sell, vol = 0
            row(
                "SELL: no min sell, sell volume = 0, price increases => max buy to zero",
                DeBidConstraints(50, 0, 0, 50),
                OrderVolumeType.SELL,
                0,
                PriceDirection.UP,
                DeBidConstraints(0, 0, 0, 50)
            ),
            row(
                "SELL: no min sell, sell volume = 0, price decreases => no sell vol",
                DeBidConstraints(50, 0, 0, 50),
                OrderVolumeType.SELL,
                0,
                PriceDirection.DOWN,
                DeBidConstraints(50, 0, 0, 0)
            ),

            // - no min sell, vol > 0
            row(
                "SELL: no min sell, sell volume > 0, price increases => min sell = sell volume, no buy vol",
                DeBidConstraints(50, 0, 0, 50),
                OrderVolumeType.SELL,
                10,
                PriceDirection.UP,
                DeBidConstraints(0, 0, 10, 50)
            ),
            row(
                "SELL: no min sell, sell volume > 0, price decreases => max sell = sell volume",
                DeBidConstraints(50, 0, 0, 50),
                OrderVolumeType.SELL,
                10,
                PriceDirection.DOWN,
                DeBidConstraints(50, 0, 0, 10)
            ),

            // - min sell, vol = min sell, note: if there is a min sell, there cannot be any buy
            row(
                "SELL: min sell = sell volume, price increases => no change",
                DeBidConstraints(0, 0, 10, 50),
                OrderVolumeType.SELL,
                10,
                PriceDirection.UP,
                DeBidConstraints(0, 0, 10, 50)
            ),
            row(
                "SELL: min sell = sell volume, price decreases => max sell = min sell = order",
                DeBidConstraints(0, 0, 10, 50),
                OrderVolumeType.SELL,
                10,
                PriceDirection.DOWN,
                DeBidConstraints(0, 0, 10, 10)
            ),

            // - sell > min
            row(
                "SELL: sell volume > min sell, when price increases => min sell = order",
                DeBidConstraints(0, 0, 10, 50),
                OrderVolumeType.SELL,
                15,
                PriceDirection.UP,
                DeBidConstraints(0, 0, 15, 50)
            ),
            row(
                "SELL: sell volume > min sell, when price decreases => max sell = order",
                DeBidConstraints(0, 0, 10, 50),
                OrderVolumeType.SELL,
                15,
                PriceDirection.DOWN,
                DeBidConstraints(0, 0, 10, 15)
            ),

        ).map { (
                    description: String,
                    prev_constraints: DeBidConstraints,
                    order_volume_type: OrderVolumeType,
                    order_volume: Int,
                    next_round_direction: PriceDirection,
                    next_constraints: DeBidConstraints) ->

            description {

                calculate_constraints(
                    prev_constraints,
                    order_volume_type,
                    order_volume,
                    next_round_direction
                ).shouldBeEqualToComparingFields(next_constraints)
            }
        }
    }
})
