package au21.engine.test.experiments

import au21.engine.framework.database.AuEntityManager
import au21.engine.test.helpers.profiles.ObjectdbClientServerProfile
import au21.engine.test.helpers.profiles.ObjectdbInMemoryProfile
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.junit.TestProfile
import jakarta.inject.Inject
import org.eclipse.microprofile.config.inject.ConfigProperty
import org.junit.jupiter.api.Test

@QuarkusTest
//@TestProfile(ObjectdbClientServerProfile::class)
@TestProfile(ObjectdbInMemoryProfile::class)
class ObjectDbExp1 {
    @Inject
    lateinit var db: AuEntityManager

    @ConfigProperty(name = "OBJECTDB_URL")
    lateinit var OBJECTDB_URL: String
    @Test
    fun `create an entity and persist it`() {
        println(OBJECTDB_URL)
        db.shouldNotBeNull()
        print("y")

        db.transact {
            val model = ModelExp1(a_ ="a_", b="b")
            db.save(model)
        }

//        val m2 = db.findFirst<ModelExp1> { it.a_ == "a_" }
//        m2.shouldNotBeNull()
//        m2.a.shouldBe("a_")
//        m2.a_.shouldBe("a_")
//        m2.arr.shouldHaveSize(3)
    }


}
