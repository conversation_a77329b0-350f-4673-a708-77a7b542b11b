package au21.engine.test.services.de.defaultorders

import au21.engine.domain.common.model.OrderSubmissionType
import au21.engine.domain.common.model.OrderVolumeType
import au21.engine.domain.de.model.DeAuction.DeBidConstraints
import au21.engine.domain.de.services.defaultorders.DeOrderInfo
import io.kotest.matchers.equality.shouldBeEqualToComparingFields
import org.junit.jupiter.api.Test


class DeDefaultOrderTest {

    class Row(
        val description: String,
        val next_constraints: DeBidConstraints,
        val default_order: DeOrderInfo
    )

    @Test
    fun `next round constraints without exceptions`() {
        listOf(
            Row(
                "max buy = 0, min buy = 0, min sell = 0, max sell = 0",
                DeBidConstraints(0, 0, 0, 0),
                DeOrderInfo(
                    OrderSubmissionType.MANDATORY,
                    OrderVolumeType.NONE,
                    0
                )
            ),
            Row(
                "max buy = 0, min buy = 0, min sell = 0, max sell = 50",
                DeBidConstraints(0, 0, 0, 50),
                DeOrderInfo(
                    OrderSubmissionType.DEFAULT,
                    OrderVolumeType.NONE,
                    0
                )
            ),
            Row(
                "max buy = 0, min buy = 0, min sell = 10, max sell = 50",
                DeBidConstraints(0, 0, 10, 50),
                DeOrderInfo(
                    OrderSubmissionType.DEFAULT,
                    OrderVolumeType.SELL,
                    10
                )
            ),
            Row(
                "max buy = 0, min buy = 0, min sell = 10, max sell = 10",
                DeBidConstraints(0, 0, 10, 10),
                DeOrderInfo(
                    OrderSubmissionType.MANDATORY,
                    OrderVolumeType.SELL,
                    10
                )
            ),
            Row(
                "max buy = 50, min buy = 0, min sell = 0, max sell = 0",
                DeBidConstraints(50, 0, 0, 0),
                DeOrderInfo(
                    OrderSubmissionType.DEFAULT,
                    OrderVolumeType.NONE,
                    0
                )
            ),
            Row(
                "max buy = 50, min buy = 10, min sell = 0, max sell = 0",
                DeBidConstraints(50, 10, 0, 0),
                DeOrderInfo(
                    OrderSubmissionType.DEFAULT,
                    OrderVolumeType.BUY,
                    10
                )
            ),
            Row(
                "max buy = 10, min buy = 10, min sell = 0, max sell = 0",
                DeBidConstraints(10, 10, 0, 0),
                DeOrderInfo(
                    OrderSubmissionType.MANDATORY,
                    OrderVolumeType.BUY,
                    10
                )
            ),
            Row(
                "max buy = 50, min buy = 0, min sell = 0, max sell = 50",
                DeBidConstraints(50, 0, 0, 50),
                DeOrderInfo(
                    OrderSubmissionType.DEFAULT,
                    OrderVolumeType.NONE,
                    0
                )
            ),
        ).map { row: Row ->
            row.apply {
                DeOrderInfo
                    .create_default_from_constraints(next_constraints)
                    .shouldBeEqualToComparingFields(default_order)
            }
        }
    }
}
