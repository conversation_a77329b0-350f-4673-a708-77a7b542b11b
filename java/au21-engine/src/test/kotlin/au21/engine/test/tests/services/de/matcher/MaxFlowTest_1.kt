package au21.engine.test.tests.services.de.matcher

import au21.engine.domain.de.services.matcher.DeFlowResult.Companion.calculate_max_flow_psjava
import au21.engine.domain.de.services.matcher.DeCapacityEdge
import au21.engine.domain.de.services.matcher.DeCapacityEdge.Companion.BUY_SINK
import au21.engine.domain.de.services.matcher.DeCapacityEdge.Companion.SELL_SOURCE
import com.github.shiguruikai.combinatoricskt.permutations
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test


class MaxFlowTest_1 {

    val b1 = "b1"
    val b2 = "b2"
    val b3 = "b3"
    val b4 = "b4"
    val traders = listOf(b1, b2, b3, b4)

    @Test
    fun no_flow_1() {
        val flows = calculate_max_flow_psjava(emptyList())
        flows.shouldBeEmpty()
    }

    @Test
    fun no_flow_2() {
        val flows = calculate_max_flow_psjava(
            listOf(
                DeCapacityEdge(from = SELL_SOURCE, to = b1, 50),
                DeCapacityEdge(from = SELL_SOURCE, to = b2, 50),
                DeCapacityEdge(from = b1, to = b2, 10)
            )
        )
        flows.shouldHaveSize(1)
        flows[0].flow.shouldBe(0)
    }

    @Test
    fun no_flow_3() {
        val flows = calculate_max_flow_psjava(
            listOf(
                DeCapacityEdge(from = SELL_SOURCE, to = b1, 10),
                DeCapacityEdge(from = SELL_SOURCE, to = b2, 10),
                DeCapacityEdge(from = b1, to = b2, 10)
            )
        )
        flows.shouldHaveSize(1)
        flows[0].flow.shouldBe(0)
    }


    @Test
    fun buy_44_sell_33_capacity_19() {
        // Scenario:
        // - b1: buy 44, b2: sell 33
        // Constraints:
        // - 50 vol all round, except that b2 -> b1 = 19
        // Objective:
        // - see why we got the 'bounce' around b3 and b4

        val capacities: MutableList<DeCapacityEdge> = mutableListOf()

        // HELPERS:
        fun sell(trader: String, quantity: Int) {
            capacities.addAll(
                listOf(
                    DeCapacityEdge(from = SELL_SOURCE, to = trader, quantity),
                    DeCapacityEdge(from = trader, to = BUY_SINK, 0),
                )
            )
        }

        fun buy(trader: String, quantity: Int) {
            capacities.addAll(
                listOf(
                    DeCapacityEdge(from = trader, to = BUY_SINK, quantity),
                    DeCapacityEdge(from = SELL_SOURCE, to = trader, 0),
                )
            )
        }

        fun none(trader: String) {
            capacities.addAll(
                listOf(
                    DeCapacityEdge(from = trader, to = BUY_SINK, 0),
                    DeCapacityEdge(from = SELL_SOURCE, to = trader, 0),
                )
            )
        }
        buy(b1, 44)
        sell(b2, 33)
        none(b3)
        none(b4)


        traders.permutations(2).forEach { (seller, buyer) ->
            // counterparty edges: (all 50 except b2 -> b1 which is 19
            capacities.add(
                DeCapacityEdge(
                    from = seller,
                    to = buyer,
                    capacity = when (seller == "b2" && buyer == "b1") {
                        true -> 19
                        false -> 0 // TODO: make sure that this is actually zero, ie: min(credit, buy, sell)
                    }
                )
            )
        }

        val flow_results = calculate_max_flow_psjava(capacities)

        // Logger.info(capacities.to_table())
        // Logger.info(flow_results.log_tables())

        flow_results.shouldNotBeEmpty()
        flow_results.sumOf { it.flow }.shouldBe(19)
        flow_results.filter { it.flow > 0 }.apply {
            shouldHaveSize(1)
            get(0).apply {
                from_seller.shouldBe(b2)
                to_buyer.shouldBe(b1)
            }
        }
    }

}
