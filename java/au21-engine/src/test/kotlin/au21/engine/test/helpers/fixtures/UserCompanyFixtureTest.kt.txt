package au21.engine.test.fixtures

import au21.engine.domain.common.model.Person
import au21.engine.domain.de.services.sampledb.TradingUserFixture
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.doubles.shouldBeGreaterThan
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test


class UserCompanyFixtureTest {

    @Test()
//  "${TradingUserFixture::class.simpleName} should create the correct number of users" {
    fun `should create the correct number of users`() {
        TradingUserFixture.create(2, listOf(), listOf()).also { users:List<Person> ->
            users.shouldHaveSize(2)
            users[0].apply {
                username.shouldBe("b1")
                isTrader().shouldBe(true)
                company?.apply {
                    shortname.shouldBe("c-1")
                    get_credit_limit(users[1].company!!)?.shouldBeGreaterThan(99_000.0)
                }
            }
            users[1].apply {
                username.shouldBe("b2")
                isTrader().shouldBe(true)
                company?.apply {
                    shortname.shouldBe("c-2")
                    get_credit_limit(users[0].company!!)?.shouldBeGreaterThan(99_000.0)
                }
            }

            TradingUserFixture.create(2, users, users.map { it.company!! }).apply {
                shouldHaveSize(2) // ie: shouldn't create new users, so total should be the same
            }
        }

    }
}
