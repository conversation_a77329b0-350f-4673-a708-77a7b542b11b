package au21.engine.test.playwright

import com.microsoft.playwright.Page
import io.quarkus.test.junit.QuarkusTest
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import java.nio.file.Paths


@QuarkusTest
@Disabled
class PlaywrightExample03 : TestFixtures() {

    @Test
    fun test1() {
        val username = "a1"
        val password = "1"
        page.navigate("http:/localhost:8081/")
        page.fill("input[type='text']", username);
        page.fill("input[type='password']", password);
        page.click("button:has-text('Sign in')");
        page.screenshot(Page.ScreenshotOptions().setPath(Paths.get("example.png")))
    }

    @Test
    fun test2() {
        val username = "a1"
        val password = "1"
        page.navigate("http:/localhost:8081/")
        page.fill("input[type='text']", username);
        page.fill("input[type='password']", password);
        page.click("button:has-text('Sign in')");
        page.screenshot(Page.ScreenshotOptions().setPath(Paths.get("example.png")))
    }
}
