package engine.test.helpers.logging

import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.common.QuarkusTestResourceLifecycleManager
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.condition.EnabledIf
import org.testcontainers.containers.GenericContainer

@QuarkusTestResource(SeqGelfResource::class)
@Disabled()
class MyTestClass {
    @Test
    fun `test something`() {

    }
}

class SeqGelfResource : QuarkusTestResourceLifecycleManager {
    private lateinit var container: GenericContainer<*>

    override fun start(): Map<String, String> {
        container = GenericContainer("seq-input-gelf-image").apply {
            withExposedPorts(12201)
            start()
        }

        return mapOf(
            "quarkus.log.handler.gelf.host" to container.host,
            "quarkus.log.handler.gelf.port" to container.getMappedPort(12201).toString()
        )
    }

    override fun stop() {
        container.stop()
    }
}
