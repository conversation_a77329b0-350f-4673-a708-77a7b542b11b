package au21.engine.test.helpers.base.command

import io.quarkus.test.junit.QuarkusTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

@QuarkusTest
class CommandTestBaseTest : CommandTestValidator() {

    @BeforeEach
    fun beforeEach(){
        db_init_command()
    }

    @Test
    fun `test db init and recreate`() {
        expect_auctions_empty(true)
        de_create_db_command()
        expect_auctions_empty(false)
    }
}
