package au21.engine.test.playwright;

import com.microsoft.playwright.*
import org.junit.jupiter.api.*

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
open class TestFixtures {
    // Shared between all tests in the class.
    val playwright = Playwright.create();
    val browser = playwright.chromium().launch(BrowserType.LaunchOptions().setHeadless(false).setSlowMo(10_000.0))
  //  lateinit var context: BrowserContext
    lateinit var page: Page;

    @BeforeAll
    fun launchBrowser() {

    }

    @BeforeEach
    fun createContextAndPage() {
        // context = browser.newContext();
        page = browser.newPage() // context.newPage();
    }

    @AfterEach
    fun closeContext() {
       // context.close();
    }

    @AfterAll
    fun closeBrowser() {
        playwright.close();
    }


}
