package au21.engine.domain.de.services.nextroundprice

import com.microsoft.playwright.BrowserType
import com.microsoft.playwright.Page
import com.microsoft.playwright.Playwright
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.quarkus.test.junit.QuarkusTest
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.TestInstance
import java.nio.file.Paths

//@QuarkusTest
//@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class LoginSpec : DescribeSpec({

    val browser = Playwright.create().chromium().launch(
        BrowserType.LaunchOptions()
            .setHeadless(false)
            .setSlowMo(1_000.0)
    )

    fun login(page: Page, username: String, password: String) {
        page.navigate("http:/localhost:8080/")
        page.fill("input[type='text']", username);
        page.fill("input[type='password']", password);
        page.click("button:has-text('Sign in')");
        page.screenshot(Page.ScreenshotOptions().setPath(Paths.get("example.png")))
    }

    describe("it logs in successfully") {
        it("should login") {
            val page: Page = browser.newPage()
            login(page, "a1", "1")
            page.title().shouldBe("au21-frontend")
        }
    }
})
