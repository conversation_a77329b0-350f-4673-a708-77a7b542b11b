package au21.engine.test.helpers.base.command

import au21.engine.domain.common.commands.*
import au21.engine.domain.common.model.*
import au21.engine.domain.common.services.company_by_shortname
import au21.engine.domain.common.services.find_auction_by_name
import au21.engine.domain.common.viewmodel.DateTimeValue
import au21.engine.domain.common.viewmodel.TimeValue
import au21.engine.domain.de.commands.*
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeFlowControlType
import au21.engine.framework.PageName
import au21.engine.framework.client.ClientsManager
import au21.engine.framework.commands.AlertException
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.EngineCommandEnvelope
import au21.engine.framework.commands.EngineCommandHandler
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.to_json
import io.kotest.matchers.nulls.beNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldNot
import io.kotest.matchers.types.shouldBeInstanceOf
import io.quarkus.logging.Log
import jakarta.inject.Inject
import org.joda.time.DateTime
import org.joda.time.DateTimeUtils
import java.util.*

open class CommandTestBase {

    @Inject
    lateinit var db: AuEntityManager

    @Inject
    lateinit var handler: EngineCommandHandler

    @Inject
    lateinit var client_manager: ClientsManager

    fun last_stores() = client_manager.last_stores

    private fun nextSid() = UUID.randomUUID().toString()

    /**
     *   engine handler helper:
     */


    /*
    // V1 - verbose logging
    fun handle(session: AuSession?, cmd: EngineCommand) {
        val commandEnvelope = EngineCommandEnvelope(
            session_id = session?.session_id ?: "",
            command = cmd
        )
        val cmd_json = commandEnvelope.to_json()

        // --- Add Logging Here ---
        val commandSimpleName = cmd::class.java.simpleName
        val testClassName = this::class.java.simpleName // Get the name of the test class
        val testMethodName = Thread.currentThread().stackTrace[2].methodName // Get current method name (might be fragile)
        Log.infof("TEST LOG (%s.%s): Handling command [%s] for session [%s]",
            testClassName, testMethodName, commandSimpleName, commandEnvelope.session_id ?: "null")
        // --- End Logging ---

        try { // Add try-catch here to log errors originating *within* the handler more clearly
            handler.handle(db, cmd_json)
            // handler.error check remains, but we catch exceptions from handler.handle too
            handler.error?.let {
                Log.errorf("TEST LOG (%s.%s): Handler error re-throwing: %s", testClassName, testMethodName, it.message)
                throw it
            }
        } catch (e: Throwable) {
            Log.errorf(e, "TEST LOG (%s.%s): Exception during handler.handle for command [%s]", testClassName, testMethodName, commandSimpleName)
            throw e // Re-throw after logging
        }
    }
     */

    /* v2 NO STACK TRACES, but double logging of errors:
        fun handle(session: AuSession?, cmd: EngineCommand): Throwable? { // <<< Changed return type to Throwable?
        val commandEnvelope = EngineCommandEnvelope(
            session_id = session?.session_id ?: "",
            command = cmd
        )
        val cmd_json = commandEnvelope.to_json()

        // --- Logging ---
        val commandSimpleName = cmd::class.java.simpleName
        // Getting test class/method name (can be fragile, adjust if needed)
        val stackTrace = Thread.currentThread().stackTrace
        val caller = stackTrace.getOrNull(2) // Index might vary slightly
        val testClassName = caller?.className?.substringAfterLast('.') ?: this::class.java.simpleName
        val testMethodName = caller?.methodName ?: "unknown_method"
        Log.infof("TEST LOG (%s.%s): Handling command [%s] for session [%s]",
            testClassName, testMethodName, commandSimpleName, commandEnvelope.session_id ?: "null")
        // --- End Logging ---

        var caughtError: Throwable? = null // Variable to store any caught error

        try {
            // Call the actual handler
            handler.handle(db, cmd_json)
            // Check if the handler itself stored an error (e.g., AlertException it handled internally)
            handler.error?.let {
                Log.warnf("TEST LOG (%s.%s): Handler stored an error: %s", testClassName, testMethodName, it.message)
                caughtError = it // Store the error from the handler
            }
        } catch (e: Throwable) {
            // Catch any exception thrown directly by handler.handle()
            Log.errorf(e, "TEST LOG (%s.%s): Exception caught during handler.handle for command [%s]", testClassName, testMethodName, commandSimpleName)
            caughtError = e // Store the caught exception
        } finally {
            // IMPORTANT: Clear the handler's error state so it doesn't affect the next call
            handler.error = null
        }

        // Return the error (if any occurred) for the test method to assert on
        return caughtError // <<< Return the caught error or null
    }
     */

    /*
    // V3: Returns true on success, throws on UNEXPECTED errors
    // https://aistudio.google.com/prompts/1ihaMOfivcrGX1DWwSaPuRNN1_uZHI3jh?model=gemini-2.5-pro-exp-03-25&utm_source=deepmind.google&utm_medium=referral&utm_campaign=gdm&utm_content=
    fun handle(session: AuSession?, cmd: EngineCommand): Boolean { // <<< Return Boolean for success/failure indication (optional)
        val commandEnvelope = EngineCommandEnvelope(
            session_id = session?.session_id ?: "",
            command = cmd
        )
        val cmd_json = commandEnvelope.to_json()

        // --- Logging ---
        val commandSimpleName = cmd::class.java.simpleName
        val stackTrace = Thread.currentThread().stackTrace
        val caller = stackTrace.getOrNull(2)
        val testClassName = caller?.className?.substringAfterLast('.') ?: this::class.java.simpleName
        val testMethodName = caller?.methodName ?: "unknown_method"
        Log.infof("TEST LOG (%s.%s): Handling command [%s] for session [%s]",
            testClassName, testMethodName, commandSimpleName, commandEnvelope.session_id ?: "null")
        // --- End Logging ---

        var success = false
        try {
            // Call the actual handler
            handler.handle(db, cmd_json)

            // Check if the handler stored an *unexpected* error AFTER processing
            // (It shouldn't if it handles AlertException internally, but check just in case)
            handler.error?.let { storedError ->
                if (storedError !is AlertException) {
                    Log.errorf(storedError, "TEST LOG (%s.%s): UNEXPECTED Handler stored error re-throwing: %s", testClassName, testMethodName, storedError.message)
                    throw storedError // Re-throw UNEXPECTED stored errors
                } else {
                    // If an AlertException was stored, log it but don't throw from here
                    Log.warnf("TEST LOG (%s.%s): Handler stored AlertException (expected?): %s", testClassName, testMethodName, storedError.message)
                }
            }
            // If we reach here without exceptions or unexpected stored errors
            success = true

        } catch (ae: AlertException) {
            // Catch AlertException thrown directly by handler.handle()
            // This is an EXPECTED validation failure path. Log it clearly but DO NOT throw.
            Log.warnf("TEST LOG (%s.%s): Caught EXPECTED AlertException for command [%s]: %s",
                testClassName, testMethodName, commandSimpleName, ae.message)
            // Do not set success = true, do not throw
            handler.error = ae // Optionally store it if the handler didn't already

        } catch (e: Throwable) {
            // Catch all OTHER (unexpected) exceptions
            Log.errorf(e, "TEST LOG (%s.%s): Caught UNEXPECTED Exception for command [%s]",
                testClassName, testMethodName, commandSimpleName)
            handler.error = e // Store the unexpected error
            throw e // Re-throw UNEXPECTED errors to fail the test clearly

        } finally {
            // We might clear handler.error here, OR rely on the handler itself to clear it at the start.
            // Let's assume the handler clears it at the beginning of its handle method.
            // handler.error = null // Optional: depends on handler's own logic
        }
        return success // Optional: Indicate if no exception occurred
    }
    */

    // V4:
    // REVISED handle method: Throws on UNEXPECTED errors, logs expected AlertExceptions
    fun handle(session: AuSession?, cmd: EngineCommand) { // <<< Returns implicit Unit
        val commandEnvelope = EngineCommandEnvelope(
            session_id = session?.session_id ?: "",
            command = cmd
        )
        val cmd_json = commandEnvelope.to_json()

        // --- Logging ---
        val commandSimpleName = cmd::class.java.simpleName
        val stackTrace = Thread.currentThread().stackTrace
        val caller = stackTrace.getOrNull(2)
        val testClassName = caller?.className?.substringAfterLast('.') ?: this::class.java.simpleName
        val testMethodName = caller?.methodName ?: "unknown_method"
        Log.infof("TEST LOG (%s.%s): Handling command [%s] for session [%s]",
            testClassName, testMethodName, commandSimpleName, commandEnvelope.session_id ?: "null")
        // --- End Logging ---

        var caughtError: Throwable? = null // Still useful to track for finally block

        try {
            // Call the actual handler
            handler.handle(db, cmd_json)

            // Check if the handler stored an error AFTER processing and re-throw it
            handler.error?.let { storedError ->
                Log.errorf(storedError, "TEST LOG (%s.%s): Handler stored error, re-throwing: %s", testClassName, testMethodName, storedError.message)
                throw storedError
            }
            // If we reach here, no exceptions occurred

        } catch (e: Throwable) {
            // Catch exceptions THROWN by handler.handle()
            caughtError = e // Store it temporarily
            logFilteredStackTrace(testClassName, testMethodName, commandSimpleName, e, "Caught Exception for command")
            throw e // <<< Re-throw ALL caught exceptions

        } finally {
            // Clear handler error state regardless of outcome
            handler.error = null
        }
    }

    // Helper function for filtered logging (Keep this as before)
    private fun logFilteredStackTrace(testClass: String, testMethod: String, commandName: String, e: Throwable, prefixMessage: String) {
        val filteredTrace = e.stackTrace
            .filter { it.className.startsWith("au21.engine.") } // Your package prefix
            .take(10) // Limit lines
            .joinToString("\n\t") { "at ${it.className}.${it.methodName}(${it.fileName}:${it.lineNumber})" }

        val cause = e.cause
        val causeMessage = if (cause != null) "\n\tCaused by: ${cause::class.simpleName}: ${cause.message}" else ""

        if (filteredTrace.isNotBlank()) {
            Log.errorf("%s [%s]: %s%s\n\tRelevant Stack Trace:\n\t%s",
                prefixMessage, commandName, e.message, causeMessage, filteredTrace)
        } else {
            Log.errorf(e, "%s [%s] (No stack trace in au21.engine package): %s%s",
                prefixMessage, commandName, e.message, causeMessage)
        }
    }

    /**
     *   Time helpers:
     */
    // if called after fix_time() above, then will return fixed_time:
    val fixed_time = DateTime("2021-12-13T08:00:00.00-08:00")
    val fixed_time_formatted = "Mon, 13 Dec 2021 at 08:00:00 PST"
    lateinit var time_value: TimeValue

    // NOTE: this must be set in beforeEach (or BeforeAll)
    fun fix_time() {
        DateTimeUtils.setCurrentMillisFixed(fixed_time.millis)
        time_value = TimeValue("Houston", DateTimeValue.create(DateTime()))
    }

    /**
     *     1) COMMON:
     */

    fun auction_row_command(
        session: AuSession,
        auction: Auction,
        instruction: AuctionInstruction
    ) = handle(
        session,
        AuctionRowCommand(
            auction.id_str(),
            instruction
        )
    )

    fun auction_select_command(
        session: AuSession,
        auction: Auction,
    ) = handle(
        session,
        AuctionSelectCommand(auction.id_str())
    )

    fun client_socket_command(
        session: AuSession?,
        state: AuSession.ClientSocketState
    ) = handle(
        session,
        ClientSocketCommand(nextSid(), state)
    )

    fun create_session(): AuSession {
        handle(null, ClientSocketCommand(nextSid(), AuSession.ClientSocketState.OPENED))
        val action = client_manager.last_action
        action.shouldBeInstanceOf<ClientSocketAction>()
        action.new_session.let {
            it.shouldNotBeNull()
            return it
        }
    }

    fun findSession(sessionId: String) = db.findFirst<AuSession> { it.session_id == sessionId }

    fun findAuction(auction_name: String) = db.find_auction_by_name<DeAuction>(auction_name)

    fun findCompany(shortname: String) = db.company_by_shortname(shortname)

    fun findUser(userName: String) = db.findFirst<Person> { it.username == userName  }

    fun company_delete_command(
        session: AuSession,
        company: Company
    ) = handle(
        session,
        CompanyDeleteCommand(company.id_str())
    )

    fun company_save_command(
        session: AuSession,
        company: Company?,
        company_shortname: String,
        company_longname: String
    ) = handle(
        session,
        CompanySaveCommand(
            company?.id_str() ?: "",
            company_shortname,
            company_longname
        )
    )

    fun counterparty_credit_set(de: DeAuction, s: AuSession, seller: Company, buyer: Company, limit: String) = handle(
        s,
        DeCreditSetCommand(
            de.id_str(),
            lender_id = seller.id_str(),
            borrower_id = buyer.id_str(),
            credit_limit = limit
        )
    )

    fun db_delete_auctions_command() =
        handle(null, DbDeleteAuctionsCommand())

    fun db_init_command() =
        handle(null, DbInitCommand())

    fun errors_send_command(
        session: AuSession,
        auction: Auction,
        trader_session: AuSession,
        error: String
    ) = handle(
        session,
        ErrorsSendCommand(
            auction.id_str(),
            trader_session.id_str(),
            error
        )
    )

    fun login_command(
        session: AuSession,
        username: String,
        password: String,
    ) = handle(
        session,
        LoginCommand(
            username,
            password
        )
    )

    fun message_send_command(
        session: AuSession,
        auction: Auction,
        message: String
    ) = handle(
        session,
        MessageSendCommand(
            auction.id_str(),
            message
        )
    )

    fun notice_save_command(
        session: AuSession,
        auction: Auction,
        notice: String
    ) = handle(
        session,
        NoticeSaveCommand(
            auction.id_str(),
            notice
        )
    )

    // TODO: not sure where this is used?
    fun page_set_command(
        session: AuSession,
        page: PageName
    ) = handle(
        session,
        PageSetCommand(page)
    )


    fun session_terminate_command(
        session: AuSession,
        reason: AuSession.SessionTerminationReason
    ) = handle(
        session,
        SessionTerminateCommand(reason)
    )

    fun user_delete_command(
        session: AuSession,
        user: Person?
    ) = handle(
        session,
        UserDeleteCommand(user?.id_str() ?: "")
    )

    fun user_save_command(
        session: AuSession,
        company: Company?,
        email: String,
        password: String,
        phone: String,
        role: AuUserRole,
        user: Person?,
        username: String
    ) = handle(
        session,
        UserSaveCommand(
            company?.id_str() ?: "",
            email,
            password,
            phone,
            role,
            user?.id_str() ?: "",
            username
        )
    )

    fun create_user(
        session: AuSession,
        role: AuUserRole,
        username: String,
        company: Company?
    ) =
        user_save_command(
            session,
            company,
            email = "",
            password = "1",
            phone = "",
            role,
            null,
            username
        )

    fun create_auctioneer_user(username: String): Person =
        db.transact {
            Person(
                AuUserRole.AUCTIONEER,
                username,
                password = "1"
            )
        }

    fun create_auctioneer_session_and_login(): AuSession {
        val auctioneerSession = create_session()
        login_command(auctioneerSession, "a1", "1")
        return auctioneerSession
    }

    // 2) DE AUCTION

    fun de_auction_award_command(
        session: AuSession,
        de: DeAuction,
        round_number: Int,
        //allocations: Map<Company, String>
    ) = handle(
        session,
        DeAuctionAwardCommand(
            de.id_str(),
            round_number.toString(),
            // allocations.mapKeys { it.key.id_str() }
        )
    )


    fun de_auction_save_command(
        session: AuSession,
        de: DeAuction?,
        auction_name: String,
        use_counterparty_credits: Boolean,

        quantity_label: String,
        quantity_minimum: String,
        quantity_step: String,

        price_change_initial: String,
        price_change_post_reversal: String,
        price_label: String,
        price_decimal_places: String,
        //   starting_price: String,
        cost_multiplier: String,

        default_buyer_max: String,
        default_seller_max: String,

        excess_level_0_label: String,
        excess_level_1_label: String,
        excess_level_2_label: String,
        excess_level_3_label: String,
        excess_level_4_label: String,

        excess_level_1_quantity: String,
        excess_level_2_quantity: String,
        excess_level_3_quantity: String,
        excess_level_4_quantity: String,

        starting_price_announcement_mins: String,

        month_is_1_based: Boolean,
        starting_year: String,
        starting_month: String,
        starting_day: String,
        starting_hour: String,
        starting_mins: String,

        round_red_secs: String,
        round_orange_secs: String,
        round_open_min_seconds: String,
        round_closed_min_secs: String

    ) = handle(
        session,
        DeAuctionSaveCommand(
            auction_id = de?.id_str() ?: "",
            auction_name = auction_name,
            use_counterparty_credits = use_counterparty_credits.toString(),

            quantity_label = quantity_label,
            quantity_minimum = quantity_minimum,
            quantity_step = quantity_step,

            price_change_initial = price_change_initial,
            price_change_post_reversal = price_change_post_reversal,
            price_label = price_label,
            price_decimal_places = price_decimal_places,
            //   starting_price,
            cost_multiplier = cost_multiplier,

            excess_level_0_label = excess_level_0_label,
            excess_level_1_label = excess_level_1_label,
            excess_level_2_label = excess_level_2_label,
            excess_level_3_label = excess_level_3_label,
            excess_level_4_label = excess_level_4_label,

            excess_level_1_quantity = excess_level_1_quantity,
            excess_level_2_quantity = excess_level_2_quantity,
            excess_level_3_quantity = excess_level_3_quantity,
            excess_level_4_quantity = excess_level_4_quantity,

            starting_price_announcement_mins = starting_price_announcement_mins,

            month_is_1_based = month_is_1_based,
            starting_year = starting_year,
            starting_month = starting_month,
            starting_day = starting_day,
            starting_hour = starting_hour,
            starting_mins = starting_mins,

            round_red_secs = round_red_secs,
            round_orange_secs = round_orange_secs,
            round_open_min_seconds = round_open_min_seconds,
            round_closed_min_secs = round_closed_min_secs
        )
    )

    fun create_default_de_auction(
        session: AuSession,
        auction_name: String
    ) {
        val d = DateTime()
        de_auction_save_command(
            session,
            de = null,
            auction_name = auction_name,
            use_counterparty_credits = true,

            quantity_label = "MMlb",
            quantity_minimum = "1",
            quantity_step = "1",

            price_change_initial = "0.5",
            price_change_post_reversal = "0.125",
            price_label = "cpp",
            price_decimal_places = "3",
            //   starting_price,
            cost_multiplier = "10000",

            default_buyer_max = "50",
            default_seller_max = "50",

            excess_level_0_label = "+",
            excess_level_1_label = "++",
            excess_level_2_label = "+++",
            excess_level_3_label = "++++",
            excess_level_4_label = "+++++",

            excess_level_1_quantity = "10",
            excess_level_2_quantity = "20",
            excess_level_3_quantity = "30",
            excess_level_4_quantity = "40",

            starting_price_announcement_mins = "5",

            month_is_1_based = true, // TODO: is this correct?
            starting_year = d.year.toString(),
            starting_month = d.monthOfYear.toString(), // this is 1 based!
            starting_day = d.dayOfMonth.toString(),
            starting_hour = d.hourOfDay.toString(),
            starting_mins = d.minuteOfHour.toString(),

            round_red_secs = "15",
            round_orange_secs = "30",
            round_open_min_seconds = "15",
            round_closed_min_secs = "5"

        )
    }


    fun de_auctions_tick_command() =
        handle(null, DeAuctionsTickCommand())

//    fun de_calculate_award_command(
//        de: DeAuction
//    ) = handle(
//        null,
//        DeCalculateAwardCommand(de.id_str())
//    )

    fun de_create_db_command(
        auction_count: Int = 1,
        auctioneer_count: Int = 2,
        close_last_round: Boolean = false,
        round_count: Int = 1,
        trader_count: Int = 4,
    ) = handle(
        null,
        DeCreateSampleDbCommand(
            auction_count,
            auctioneer_count,
            close_last_round,
            round_count,
            trader_count,
        )
    )

    fun de_credit_set_command(
        de: DeAuction,
        session: AuSession,
        seller: Company,
        buyer: Company,
        credit_limit: String
    ) = handle(
        session,
        DeCreditSetCommand(
            de.id_str(),
            seller.id_str(),
            buyer.id_str(),
            credit_limit
        )
    )

//    fun de_eligibility_set_command(
//        session: AuSession,
//        de: DeAuction,
//        company: Company,
//        max_buy: String, // need to be strings because can be entered manually
//        max_sell: String
//    ) = handle(
//        session,
//        DeEligibilitySetCommand(
//            de.id_str(),
//            company.id_str(),
//            max_buy,
//            max_sell
//        )
//    )


    fun de_flow_control_command(
        session: AuSession,
        de: DeAuction,
        control: DeFlowControlType,
        starting_price: String? = null
    ) = handle(
        session,
        DeFlowControlCommand(
            de.id_str(),
            control,
            starting_price
        )
    )

    fun de_order_submit_command(
        session: AuSession,
        de: DeAuction,
        order_type: OrderType,
        round: Int,
        quantity: String
    ) = handle(
        session,
        DeOrderSubmitCommand(
            de.id_str(),
            session.user!!.company!!.id_str(),
            order_type,
            round.toString(),
            quantity // need to be strings because can be entered manually
        )
    )

    fun de_round_history_command(
        session: AuSession,
        auction: DeAuction,
        round_number: Int
    ) = handle(
        session,
        DeRoundHistoryCommand(
            auction.id_str(),
            round_number.toString()
        )
    )

    fun de_template_delete_command(
        session: AuSession,
        template_id: String
    ) = handle(session, DeTemplateDeleteCommand(template_id))


    fun de_template_save_command(session: AuSession) = handle(session, DeTemplateSaveCommand())

    fun de_traders_add_command(
        session: AuSession,
        auction: DeAuction,
        companies: List<Company>
    ) = handle(
        session,
        DeTradersAddCommand(
            auction.id_str(),
            companies.map { it.id_str() })
    )

    fun de_trader_remove_command(
        session: AuSession,
        auction: DeAuction,
        companies: List<Company>
    ) = handle(
        session,
        DeTradersRemoveCommand(
            auction.id_str(),
            companies.map { it.id_str() })
    )

    fun create_company(auctioneer_session: AuSession, shortname: String): Company {
        val companySaveCommand = CompanySaveCommand("", shortname, "$shortname-long")
        handle(auctioneer_session, companySaveCommand)
        val company = findCompany(shortname)
        company shouldNot beNull()
        return company!!
    }

    fun createUser(auctioneer_session: AuSession, username: String, company: Company?): Person {
        create_user(auctioneer_session, AuUserRole.TRADER,username,company)
        val user = findUser(username)
        user shouldNot beNull()
        return user!!
    }

    fun create_auction(auctioneer_session: AuSession, auctionName: String): DeAuction {
        create_default_de_auction(auctioneer_session, auctionName)
        val auction = findAuction(auctionName)
        auction shouldNot beNull()
        return auction!!
    }
}
