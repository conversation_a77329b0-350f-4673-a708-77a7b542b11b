package au21.engine.domain.de.services.prices;

import au21.engine.domain.common.model.Company
import au21.engine.domain.common.model.OrderVolumeType.BUY
import au21.engine.domain.common.model.OrderVolumeType.SELL
import au21.engine.domain.common.model.Person
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeAuction.Round
import au21.engine.domain.de.model.calculate_and_set_matches
import au21.engine.domain.de.model.lastround
import au21.engine.domain.de.model.set_counterparty_volume_limits
import com.github.shiguruikai.combinatoricskt.permutationsWithRepetition
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test


@Disabled
class PriceChangesTest {

    val trader_names = listOf("b1", "b2", "b3")

    val up_buy = 0
    val up_sell = 1
    val down_buy = 2
    val down_sell = 3

    val buys = listOf(up_buy, up_sell)
    val sells = listOf(down_buy, down_sell)

    val permutations = listOf(up_buy, up_sell, down_buy, down_sell)
        .permutationsWithRepetition(3)
        .filter {
            when {
                it[0] in buys && it[1] in sells && it[2] in buys -> false
                it[0] in sells && it[1] in buys && it[2] in sells -> false
                else -> true
            }
        }


    val oid: Long = 100;

    @Test
    fun upBuy() {
        // remove B->S->B and S -> B -> S

        permutations.forEach { list ->
            val y = list.map {
                when (it) {
//                    0 -> "up_buy"
//                    1 -> "up_sell"
//                    2 -> "down_buy"
//                    3 -> "down_sell"
                    0, 1 -> "UP"
                    2, 3 -> "DOWN"
                    else -> throw Error("")
                }
            }
            println(y)
        }
        println()
    }

    @Test
    fun upSell() {

    }

    @Test
    fun downBuy() {

    }

    @Test
    fun downSell() {

    }


    @Test
    fun should_match_1() {
        val (de: DeAuction, companies: List<Company>, people: List<Person>) = de_basic(trader_names, 100.0)
        val n: Round = de.lastround() ?: throw Error("auction has no rounds")

        assertThat(n.price).isEqualTo(100.0)
        assertThat(de.rounds).hasSize(1)
        assertThat(de.traders).hasSize(trader_names.size)

        val order = de.create_manual_order(
            r = n,
            t = de.traders[0],
            u = people[0],
            order_volume_type = BUY,
            order_volume = 10
        )

        assertThat(order.volume_type == BUY).isTrue
        assertThat(order.volume_type == SELL).isFalse
      //  assertThat(order.withdrawal_reason).isNull()

        // NB: MUST be done before calcualting actuan_maxflow !!
        de.set_counterparty_volume_limits(companies)
        de.calculate_and_set_matches()

        assertThat(n.match_vol()).isEqualTo(0)

    }

    @Test
    fun should_not_match_due_to_lack_of_credits() {
        val (de, companies, people) = de_basic(trader_names, 100.0)
        val n: Round = de.lastround() ?: throw Error("auction has no rounds")

        de.create_manual_order(
            r = n,
            t = de.traders[0],
            u = people[0],
            order_volume_type = BUY,
            order_volume = 10
        )

        de.create_manual_order(
            r = n,
            t = de.traders[1],
            u = people[1],
            order_volume_type = SELL,
            order_volume = 20
        )
        // NB: MUST be done before calcualting actuan_maxflow !!
        de.set_counterparty_volume_limits(companies)
        de.calculate_and_set_matches()

        assertThat(n.match_vol()).isEqualTo(0)
    }

    @Test
    fun should_match_2() {
        val scenario = de_basic(trader_names, 100.0)
        val de: DeAuction = scenario.first
        val n: Round = de.lastround() ?: throw Error("auction has no rounds")

        val companies: List<Company> = scenario.second
        companies.forEach { seller ->
            companies.forEach { buyer ->
                if (seller != buyer)
                    seller.set_credit_limit(buyer, 5_000_000.0)
            }
        }
        de.create_manual_order(
            r = n,
            t = de.traders[0],
            u = scenario.third[0],
            order_volume_type = BUY,
            order_volume = 10
        )

        de.create_manual_order(
            r = n,
            t = de.traders[1],
            u = scenario.third[1],
            order_volume_type = SELL,
            order_volume = 20
        )
        // NB: MUST be done before calcualting actuan_maxflow !!
        de.set_counterparty_volume_limits(scenario.second)
        de.calculate_and_set_matches()

        assertThat(n.match_vol()).isEqualTo(5)
    }


}
