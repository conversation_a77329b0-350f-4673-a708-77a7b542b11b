package au21.engine.test.tests.websockets // Use your actual package

import io.kotest.assertions.fail
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.nulls.shouldBeNull
import io.quarkus.test.common.http.TestHTTPResource
import io.quarkus.test.junit.QuarkusTest
import org.junit.jupiter.api.BeforeEach // Keep JUnit @BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test     // Keep JUnit @Test
import org.junit.jupiter.api.Timeout // Keep JUnit @Timeout
import java.net.URI
import java.net.http.HttpClient
import java.net.http.WebSocket
import java.net.http.WebSocketHandshakeException
import java.nio.ByteBuffer
import java.util.concurrent.*

@QuarkusTest
class SocketSessionTest {

    @TestHTTPResource("/socket/test-session-1?browser_name=TestClient&browser_version=1.0&browser_os=TestOS")
    lateinit var serverUri: URI

    // --- Test State Variables ---
    private lateinit var client: HttpClient
    private lateinit var webSocket: WebSocket
    private lateinit var messagesQueue: LinkedBlockingDeque<String>       // Re-init in setup
    private lateinit var binaryMessagesQueue: LinkedBlockingDeque<ByteBuffer> // Re-init in setup
    private lateinit var openLatch: CompletableFuture<WebSocket>        // Re-init in setup
    private lateinit var errorLatch: CompletableFuture<Throwable>       // Re-init in setup
    private lateinit var closeLatch: CompletableFuture<WebSocket>       // Re-init in setup


    // Listener implementation (No changes needed here)
    inner class ClientWebSocketListener : WebSocket.Listener {
        override fun onOpen(webSocket: WebSocket) {
            println("SOCKET SESSION TEST CLIENT: WebSocket Opened")
            // Use lateinit property directly
            <EMAIL> = webSocket
            openLatch.complete(webSocket)
            webSocket.request(1)
        }
        override fun onText(webSocket: WebSocket, data: CharSequence, last: Boolean): CompletionStage<*>? {
            println("SOCKET SESSION TEST CLIENT: Received Text: $data")
            messagesQueue.add(data.toString())
            webSocket.request(1)
            return null
        }
        override fun onBinary(webSocket: WebSocket, data: ByteBuffer, last: Boolean): CompletionStage<*>? {
            println("SOCKET SESSION TEST CLIENT: Received Binary: ${data.remaining()} bytes")
            val copy = ByteBuffer.allocate(data.remaining()).put(data).flip()
            binaryMessagesQueue.add(copy)
            webSocket.request(1)
            return null
        }
        override fun onError(webSocket: WebSocket, error: Throwable) {
            println("SOCKET SESSION TEST CLIENT: Error: ${error.message}")
            if (!openLatch.isDone) openLatch.completeExceptionally(error)
            errorLatch.complete(error)
        }
        override fun onClose(webSocket: WebSocket, statusCode: Int, reason: String): CompletionStage<*>? {
            println("SOCKET SESSION TEST CLIENT: Closed with status $statusCode, reason: $reason")
            closeLatch.complete(webSocket)
            return null
        }
    }

    @BeforeEach
    fun setup() {
        client = HttpClient.newHttpClient()
        // FIX: Re-initialize queues and latches for each test
        messagesQueue = LinkedBlockingDeque<String>()
        binaryMessagesQueue = LinkedBlockingDeque<ByteBuffer>()
        openLatch = CompletableFuture<WebSocket>()
        errorLatch = CompletableFuture<Throwable>()
        closeLatch = CompletableFuture<WebSocket>()
    }

    @Test
    @Timeout(10, unit = TimeUnit.SECONDS)
    @Throws(Exception::class)
    fun testConnectionAndMessaging() {
        val wsUri = URI.create(serverUri.toString().replaceFirst("http", "ws"))
        println("SOCKET SESSION TEST CLIENT: Connecting to $wsUri")

        // Connect
        try {
            client.newWebSocketBuilder()
                .buildAsync(wsUri, ClientWebSocketListener())
                .join()
        } catch (e: CompletionException) {
            val cause = e.cause
            if (cause is WebSocketHandshakeException) fail("WebSocket handshake failed: ${cause.message}")
            else if (cause != null) fail("Failed connection (cause): ${cause.message}")
            else fail("Failed connection: ${e.message}")
        } catch(e: Exception) {
            fail("Failed build/connect: ${e.message}")
        }

        // 1. Wait for connection
        try {
            webSocket = openLatch.get(5, TimeUnit.SECONDS) // This should no longer throw CancellationException
            println("SOCKET SESSION TEST CLIENT: Connection confirmed open.")
            ::webSocket.isInitialized.shouldBeTrue() // Kotest assertion
        } catch (e: Exception) {
            if (errorLatch.isDone) fail("WebSocket connection failed: ${errorLatch.getNow(null)?.message ?: "Unknown error"}")
            else fail("WebSocket connection timed out/interrupted: ${e.message}")
        }

        // 2. Send a message FROM the client TO the server
        val messageToSend = """{"command":"test-from-client-kotest"}"""
        println("SOCKET SESSION TEST CLIENT: Sending message: $messageToSend")
        webSocket.sendText(messageToSend, true).join()

        // 3. Assert expected behavior (NO reply expected by default from SocketHandler)
        val receivedMessage = messagesQueue.poll(2, TimeUnit.SECONDS)
        receivedMessage.shouldBeNull() // Use Kotest assertion

        // 4. Close connection
        println("SOCKET SESSION TEST CLIENT: Sending close")
        webSocket.sendClose(WebSocket.NORMAL_CLOSURE, "Test complete").join()

        // 5. Wait for close confirmation
        try {
            closeLatch.get(5, TimeUnit.SECONDS)
            println("SOCKET SESSION TEST CLIENT: Connection confirmed closed.")
        } catch (e: Exception) {
            if (errorLatch.isDone) println("SOCKET SESSION TEST CLIENT: Error during/after close: ${errorLatch.getNow(null)?.message}")
            fail("WebSocket close confirmation timed out/interrupted: ${e.message}")
        }

        // 6. Check for unexpected errors
        errorLatch.isDone.shouldBeFalse() // Use Kotest assertion
    }
}
