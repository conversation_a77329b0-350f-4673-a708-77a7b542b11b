package au21.engine.test.tests.commands.de

import io.restassured.RestAssured.given
import jakarta.inject.Inject
import org.eclipse.microprofile.config.inject.ConfigProperty
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import javax.persistence.EntityManagerFactory


@Disabled
class DeFlowControlActionTest {

    @ConfigProperty(name = "ENGINE_INPUT_CHANNEL")
    lateinit var ENGINE_INPUT_CHANNEL: String

    @Inject
    lateinit var emf: EntityManagerFactory

    @Test
    fun `empty command message should throw class not found exception`() {
        // for some reason the
//        Given {
        // neither of the
//            param("message", "{}")
//            queryParam("message", "{}")
//        }
//        When {
//            get(ENGINE_INPUT_CHANNEL)
//        } Then {
//            statusCode(200)
//        }

        assertThrows<Throwable> {
            given()
                .param("message", "{}")
                .`when`().get("/ENGINE_INPUT_CHANNEL")
                .then() // .statusCode(200)
        }

      //  assertEquals(e.message, "")
    }

}
