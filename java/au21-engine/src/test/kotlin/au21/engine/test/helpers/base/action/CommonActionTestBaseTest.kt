package au21.engine.test.helpers.base.action

import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.nulls.shouldNotBeNull
import io.quarkus.test.junit.QuarkusTest
import org.junit.jupiter.api.Test

@QuarkusTest
class CommonActionTestBaseTest : CommonActionTestBase() {

    @Test
    fun test1() {
        sa1.shouldNotBeNull()
        a1.shouldNotBeNull()
        sa1.is_logged_in().shouldBeFalse()
        login(sa1, a1)
        sa1.is_logged_in().shouldBeTrue()
    }
}
