
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.core.spec.style.StringSpec
import io.kotest.data.forAll
import io.kotest.data.headers
import io.kotest.data.row
import io.kotest.data.table
import io.kotest.matchers.shouldBe

// NOTE: infers the argument names,
// - whereas in table example below need to provide the argument names
class Exp1 : StringSpec({
    "maximum of two numbers" {
        forAll(
            row(3, 5, 5),
            row(1, 0, 1),
            row(0, 0, 0)
        ) { a, b, max ->
            Math.max(a, b) shouldBe max
        }
    }
})

class Exp2 : StringSpec({
    "maximum of two numbers" {
        table(
            headers("a", "b", "max"),
            row(3, 3, 3),
            row(0, 0, 0),
            row(4, -1, 4),
            row(-2, -1, -1)
        ).forAll { a, b, max ->
            Math.max(a, b) shouldBe max
        }
    }
})

class Exp3 : BehaviorSpec({

    Given("list maximum of two numbers") {
        Then("should calculate max") {
            forAll(
                row(3, 5, 5),
                row(1, 0, 1),
                row(0, 0, 0)
            ) { a, b, max ->
                Math.max(a, b) shouldBe max
            }
        }
    }
})
