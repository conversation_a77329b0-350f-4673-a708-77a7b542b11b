package au21.engine.test.helpers.base.action

import au21.engine.domain.common.commands.ClientSocketAction
import au21.engine.domain.common.commands.DbInitAction
import au21.engine.domain.common.commands.LoginAction
import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.Company
import au21.engine.domain.common.model.Person
import au21.engine.domain.de.model.DeAuction
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.randomString
import io.kotest.matchers.nulls.shouldNotBeNull
import io.mockk.mockk
import jakarta.inject.Inject
import org.junit.jupiter.api.BeforeEach


open class CommonActionTestBase {

    lateinit var a1: Person
    lateinit var sa1: AuSession

    lateinit var c1: Company
    lateinit var c2: Company
    lateinit var c3: Company
    lateinit var c4: Company

    lateinit var b1: Person
    lateinit var b2: Person
    lateinit var b3: Person
    lateinit var b4: Person

    lateinit var sb1: AuSession
    lateinit var sb2: AuSession
    lateinit var sb3: AuSession
    lateinit var sb4: AuSession

    lateinit var de: DeAuction

    @Inject
    lateinit var db: AuEntityManager

    @BeforeEach
    fun beforeEach() {

        db_init_action()

        a1 = create_user("a1")
        sa1 = create_session()

        c1 = create_company("c1")
        c2 = create_company("c2")
        c3 = create_company("c3")
        c4 = create_company("c4")

        b1 = create_user("b1", c1)
        b2 = create_user("b2", c2)
        b3 = create_user("b3", c3)
        b4 = create_user("b4", c4)

        sb1 = create_session()
        sb2 = create_session()
        sb3 = create_session()
        sb4 = create_session()

//        create_default_de_auction(sa1, auction_name)
//        db.find_auction_by_name<DeAuction>(auction_name).let {
//            it.shouldNotBeNull()
//            de = it
//        }
//
//        page_set_command(sa1, PageName.USER_PAGE)
//
//        listOf(c1, c2, c3 /* c4 */)
//            .permutations(2)
//            .forEach { (a, b) ->
//                counterparty_credit_set(
//                    null,
//                    sa1,
//                    seller = a,
//                    buyer =
//                    b, "\$100,000,000")
//            }
//
    }

    fun db_init_action() {
        db.transact {
            DbInitAction(mockk(), db, null).mutate()
        }
    }

    fun create_company(shortname: String): Company =
        db.transact {
            Company(
                longname = "$shortname-long",
                shortname = shortname
            )
        }

    fun create_user(username: String, c: Company? = null): Person =
        db.transact {
            when (c) {
                null -> Person(
                    AuUserRole.AUCTIONEER,
                    username,
                    password = "1"
                )
                else -> Person(
                    AuUserRole.TRADER,
                    username,
                    password = "1",
                    company = c
                )
            }
        }

    fun create_session(): AuSession {
        return db.transact {
            ClientSocketAction(
                command = mockk(),
                db,
                session = null,
                sid = randomString(4),
                state = AuSession.ClientSocketState.OPENED,
            ).apply {
                mutate()
            }.let {
                it.new_session.shouldNotBeNull()
                it.new_session!!
            }
        }
    }

    fun login(s: AuSession, u: Person) {
        db.transact {
            LoginAction(mockk(), db, s, u).mutate()
        }
    }

    fun findSession(sessionId: String) = db.findFirst<AuSession> { it.session_id == sessionId }
}
