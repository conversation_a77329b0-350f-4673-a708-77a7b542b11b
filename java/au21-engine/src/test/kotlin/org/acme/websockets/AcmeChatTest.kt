package org.acme.websockets // Keep the package consistent with the endpoint

import io.kotest.assertions.fail
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.string.shouldContain
import io.kotest.matchers.shouldBe // <<< Import for shouldBe
import io.quarkus.test.common.http.TestHTTPResource
import io.quarkus.test.junit.QuarkusTest
import kotlinx.serialization.encodeToString // <<< Import for encodeToString
import kotlinx.serialization.json.Json     // <<< Import for Json object
import org.acme.AcmeWebSocket
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Timeout
import java.net.URI
import java.net.http.HttpClient
import java.net.http.WebSocket
import java.net.http.WebSocketHandshakeException
import java.nio.ByteBuffer
import java.util.concurrent.*

@QuarkusTest
@Disabled
class AcmeChatTest {

    @TestHTTPResource("/socket/stu")
    lateinit var serverUri: URI

    // --- Test State Variables ---
    private lateinit var client: HttpClient
    private lateinit var webSocket: WebSocket
    private val messagesQueue = LinkedBlockingDeque<String>()
    private val openLatch = CompletableFuture<WebSocket>()
    private val errorLatch = CompletableFuture<Throwable>()
    private val closeLatch = CompletableFuture<WebSocket>()

    // --- Listener Implementation ---
    inner class ClientWebSocketListener : WebSocket.Listener {
        override fun onOpen(webSocket: WebSocket) {
            println("AUCTION TEST CLIENT: WebSocket Opened")
            <EMAIL> = webSocket
            openLatch.complete(webSocket)
            webSocket.request(1)
        }
        override fun onText(webSocket: WebSocket, data: CharSequence, last: Boolean): CompletionStage<*>? {
            val message = data.toString()
            println("AUCTION TEST CLIENT: Received Text: $message")
            messagesQueue.add(message)
            webSocket.request(1)
            return null
        }
        override fun onBinary(webSocket: WebSocket, data: ByteBuffer, last: Boolean): CompletionStage<*>? { webSocket.request(1); return null }
        override fun onError(webSocket: WebSocket, error: Throwable) {
            println("AUCTION TEST CLIENT: Error: ${error.message}")
            if (!openLatch.isDone) openLatch.completeExceptionally(error)
            errorLatch.complete(error)
        }
        override fun onClose(webSocket: WebSocket, statusCode: Int, reason: String): CompletionStage<*>? {
            println("AUCTION TEST CLIENT: Closed with status $statusCode, reason: $reason")
            closeLatch.complete(webSocket)
            return null
        }
    }

    @BeforeEach
    fun setup() {
        client = HttpClient.newHttpClient()
        messagesQueue.clear()
        openLatch.cancel(false)
        errorLatch.cancel(false)
        closeLatch.cancel(false)
    }

    @Test
    @Timeout(10, unit = TimeUnit.SECONDS)
    @Throws(Exception::class)
    fun testAuctionSocketFlow() {
        val wsUri = URI.create(serverUri.toString().replaceFirst("http", "ws"))
        println("AUCTION TEST CLIENT: Connecting to $wsUri")

        // Connect (error handling uses Kotest fail)
        try {
            client.newWebSocketBuilder()
                .buildAsync(wsUri, ClientWebSocketListener())
                .join()
        } catch (e: CompletionException) {
            val cause = e.cause
            if (cause is WebSocketHandshakeException) fail("WebSocket handshake failed: ${cause.message}")
            else if (cause != null) fail("Failed connection (cause): ${cause.message}")
            else fail("Failed connection: ${e.message}")
        } catch(e: Exception) {
            fail("Failed build/connect: ${e.message}")
        }

        // 1. Wait for connection & expect JOIN message broadcast
        try {
            webSocket = openLatch.get(5, TimeUnit.SECONDS)
            println("AUCTION TEST CLIENT: Connection confirmed open.")
            val joinMessage = messagesQueue.poll(5, TimeUnit.SECONDS)
            joinMessage.shouldNotBeNull()
            joinMessage shouldContain """"type":"JOIN""""
            joinMessage shouldContain """"from":"stu""""
        } catch (e: Exception) {
            if (errorLatch.isDone) fail("WebSocket connection/join failed: ${errorLatch.getNow(null)?.message ?: "Unknown error"}")
            else fail("WebSocket connection/join timed out/interrupted: ${e.message}")
        }

        // 2. Send a valid Event message using kotlinx.serialization
        val eventToSend = AcmeWebSocket.Event(AcmeWebSocket.Type.MESSAGE, "stu", "hello from test")
        // Use the default Json configuration
        val jsonMessageToSend = Json.encodeToString(eventToSend)
        println("AUCTION TEST CLIENT: Sending message: $jsonMessageToSend")
        webSocket.sendText(jsonMessageToSend, true).join()

        // 3. Expect the same message back due to broadcast
        val receivedMessage = messagesQueue.poll(5, TimeUnit.SECONDS)
        receivedMessage.shouldNotBeNull()
        receivedMessage shouldBe jsonMessageToSend // Assert JSON strings match

        // 4. Close connection
        println("AUCTION TEST CLIENT: Sending close")
        webSocket.sendClose(WebSocket.NORMAL_CLOSURE, "Test complete").join()

        // 5. Wait for close confirmation
        try {
            closeLatch.get(5, TimeUnit.SECONDS)
            println("AUCTION TEST CLIENT: Connection confirmed closed.")
        } catch (e: Exception) {
            if (errorLatch.isDone) println("AUCTION TEST CLIENT: Error during/after close: ${errorLatch.getNow(null)?.message}")
            fail("WebSocket close confirmation timed out/interrupted: ${e.message}")
        }

        // 6. Check for unexpected errors
        errorLatch.isDone.shouldBeFalse()
    }
}
