
plugins {
    id 'org.jetbrains.kotlin.jvm' version '1.9.22'
    id 'org.jetbrains.kotlin.plugin.allopen' version '1.9.22'
    id 'io.quarkus'
    id 'com.github.ben-manes.versions' version '0.51.0'
    id 'jacoco'
}
repositories {
    mavenCentral()
    mavenLocal()
    maven {
        url 'https://m2.objectdb.com'
    }
    maven {
        url 'https://jitpack.io'
    }
}
ext {
    quarkusPlatformGroupId = project.findProperty('quarkusPlatformGroupId')
    quarkusPlatformArtifactId = project.findProperty('quarkusPlatformArtifactId')
    quarkusPlatformVersion = project.findProperty('quarkusPlatformVersion')
}
dependencies {
    implementation enforcedPlatform("${quarkusPlatformGroupId}:${quarkusPlatformArtifactId}:${quarkusPlatformVersion}")
    implementation 'io.quarkus:quarkus-arc'
    implementation 'io.quarkus:quarkus-container-image-jib'
    implementation 'io.quarkus:quarkus-elytron-security-properties-file'
    implementation 'io.quarkus:quarkus-kotlin'
    implementation 'io.quarkus:quarkus-opentelemetry'
    implementation 'io.quarkus:quarkus-resteasy-reactive'
    implementation 'io.quarkus:quarkus-resteasy-reactive-jackson'
    implementation 'io.quarkus:quarkus-smallrye-graphql'
    implementation 'io.quarkus:quarkus-websockets'
    implementation 'org.jetbrains.kotlin:kotlin-stdlib-jdk8'
    implementation "org.tinylog:tinylog-api-kotlin:$tinylog"
    implementation "org.tinylog:tinylog-impl:$tinylog"

    implementation "org.eclipse.persistence:javax.persistence:$javax_persistence"
    implementation "javax.transaction:jta:$jta"
    implementation "com.objectdb:objectdb:$object_db"

    implementation "ch.ifocusit:plantuml-builder:$plantuml_builder"
    implementation "com.github.ntrrgc:ts-generator:$ts_generator"
    implementation "com.konghq:unirest-java:$uri_test"
    implementation "com.opencsv:opencsv:$opencsv"
    implementation "de.vandermeer:asciitable:$asciitable"
    implementation "gg.jte:jte:$jte"
    implementation "io.github.classgraph:classgraph:$classgraph"
    implementation "io.micrometer:micrometer-core:$micrometer"
    implementation "joda-time:joda-time:$joda_time"
    implementation "net.pearx.kasechange:kasechange-jvm:$kasechange_jvm"
    implementation "org.psjava:psjava:$ps_java"

    implementation "com.fasterxml.jackson.module:jackson-module-kotlin:$jackson_module_kotlin"
    implementation "com.google.code.gson:gson:$gson"
    implementation "com.jsoniter:jsoniter:$jsoniter"

    testImplementation 'io.quarkus:quarkus-junit5'
    testImplementation 'io.rest-assured:rest-assured'
    testImplementation "io.kotest:kotest-runner-junit5:$kotest"
    testImplementation "io.kotest:kotest-assertions-core:$kotest"
    testImplementation "io.kotest:kotest-assertions-json-jvm:$kotest"
    testImplementation "io.mockk:mockk:$mockk"
    testImplementation "com.github.shiguruikai:combinatoricskt:$combinatorics"
}
group = 'au24.engine'
version = '1.0.0-SNAPSHOT'
java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}
test {
    systemProperty 'java.util.logging.manager', 'org.jboss.logmanager.LogManager'
}
allOpen {
    annotation 'jakarta.ws.rs.Path'
    annotation 'jakarta.enterprise.context.ApplicationScoped'
    annotation 'jakarta.persistence.Entity'
    annotation 'io.quarkus.test.junit.QuarkusTest'
}
compileKotlin {
    finalizedBy 'enhance'
    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17.toString()
        javaParameters = true
    }
}
jacocoTestReport {
    reports {
        xml.required = true
    }
}
test {
    useJUnitPlatform()
    failFast = true
    systemProperty 'OBJECTDB_URL', 'objectdb:test-set-in-build-gradle.mem'
    afterEvaluate {
        classDirectories.setFrom(files(classDirectories.files.collect {
            fileTree(it) {
                exclude 'simulator/*'
                exclude 'exp/*'
                exclude 'optimizationExps/*'
            }
        }))
    }
}
task enhance(type: JavaExec) {
    description = 'Enhances Object Db model classes'
    mainClass = 'com.objectdb.Enhancer'
    args '-cp', 'src/main/kotlin',
            'au21.engine.domain.common.model.',
            'au21.engine.domain.de.model.',
            'au21.engine.framework.database.AuEntity',
            'au21.engine.framework.commands.CommandJson'
    classpath = sourceSets.main.runtimeClasspath
    onlyIf {
        def env = System.getenv('SKIP_ENHANCE')
        println "enhance task, SKIP_ENHANCE=$env"
        env?.toLowerCase() == 'true'
    }
}
task generateTypescript(type: JavaExec) {
    mainClass = 'au21.engine.generators.typescript.Ts_generatorKt'
    classpath = sourceSets.main.runtimeClasspath
}

