# Project Architecture

## Overview

The au21-engine project follows a layered architecture with command pattern implementation for handling business operations. It's built on the Quarkus framework with Ko<PERSON>in as the primary language.

## Key Components

### 1. Application Entry Point

`Main.kt` serves as the entry point, which initializes the Quarkus application and sets up the environment.

### 2. Framework Layer

Located in `au21.engine.framework`, this layer provides core infrastructure:

- **Commands**: Implementation of the command pattern (`EngineCommand`, `EngineCommandHandler`, etc.)
- **Database**: Abstraction over ObjectDB (`AuEntity`, `AuEntityManager`, etc.)
- **Client**: WebSocket handlers and client management
- **Utils**: Utility functions for operations like JSON handling, logging, etc.
- **GraphQL**: API definitions and endpoint handlers
- **Observability**: Tracing and metrics configuration

### 3. Domain Layer

Located in `au21.engine.domain`, this layer contains the business logic:

- **Common**: Shared domain models and commands (`Person`, `Company`, `Auction`, etc.)
- **DE (Double Exchange)**: Specialized models and logic for double exchange auctions

Each domain area is further divided into:
- **Model**: Entity definitions
- **Commands**: Command implementations
- **Services**: Business logic
- **Viewmodel**: Data transfer objects for client communication

### 4. Generators

Located in `au21.engine.generators`, this component generates code for:
- **TypeScript**: Client-side type definitions
- **PlantUML**: Visualization of domain models
- **Mermaid**: Flowcharts and diagrams

## Data Flow

1. **Client Request** → Received via GraphQL or WebSocket
2. **Command Creation** → Request mapped to a command
3. **Command Handling** → Command processed by the appropriate handler
4. **Domain Logic** → Business rules applied
5. **Database Operations** → Entity persistence
6. **Response Generation** → Results returned to the client

## Command Pattern Implementation

1. Commands are defined as data classes
2. Commands are wrapped in `EngineCommandEnvelope` with metadata
3. `EngineCommandHandler` dispatches commands to appropriate handlers
4. Handlers implement business logic and return results

## Database Model

- Object-oriented database using ObjectDB
- Entity classes enhanced at build time
- Entities inherit from `AuEntity` base class

## API Interfaces

### GraphQL API
- Defined in `GraphqlApi.kt` 
- Provides queries and mutations
- Endpoint: http://localhost:4040/graphql

### WebSocket API
- Handles real-time communication
- Maintains client sessions

## Event Flow for Auctions

1. Auction creation via command
2. Traders added to auction
3. Auction flow controlled by auctioneer
4. Orders submitted by traders
5. Matching algorithm executed
6. Results communicated to clients