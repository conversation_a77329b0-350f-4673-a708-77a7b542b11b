# Task Completion Workflow

When completing a task in the au21-engine project, follow these steps to ensure quality and consistency:

## 1. Code Quality Checks

### Formatting
Kotlin code should be formatted according to the project conventions. While there's no explicit formatter command in the project, standard Kotlin formatting rules apply.

### Linting
No explicit linting tools are mentioned in the project, but follow the code style guidelines documented in the code_style_and_conventions.md file.

### Testing
Run the tests to ensure your changes don't break existing functionality:
```sh
./gradlew test
```

Generate code coverage report:
```sh
./gradlew jacocoTestReport
```

## 2. Building the Application

Build the application to ensure it compiles correctly:
```sh
./gradlew build
```

For a production build:
```sh
./gradlew build -Dquarkus.package.jar.type=uber-jar
```

## 3. Running the Application

Test your changes by running the application in development mode:
```sh
./gradlew quarkusDev
# or
just dev
```

Access the application at:
- GraphQL UI: http://localhost:4040/q/graphql-ui/
- Developer UI: http://localhost:4040/q/dev/

## 4. Database Enhancement

If you've made changes to entity models, run the enhance task:
```sh
./gradlew enhance
```

## 5. TypeScript Generation

If you've made changes to models that are exposed to the frontend, generate TypeScript definitions:
```sh
./gradlew generateTypescript
```

## 6. Git Workflow

1. Create a branch for your task
2. Make your changes
3. Commit your changes with a descriptive message
4. Push your branch
5. Create a merge request

## 7. Documentation

If your changes impact the API or functionality:
1. Update docstrings in the code
2. Update or create diagrams if necessary (PlantUML or Mermaid)
3. Update the README.md if needed

## 8. Observability Checks

For significant changes, verify that:
1. Appropriate logging is in place
2. Tracing spans are added for important operations
3. Metrics are updated if relevant

## 9. Final Review

Before considering a task complete:
1. Review your code against the project's code style guidelines
2. Ensure all tests pass
3. Verify the application builds and runs correctly
4. Check that any necessary documentation is updated