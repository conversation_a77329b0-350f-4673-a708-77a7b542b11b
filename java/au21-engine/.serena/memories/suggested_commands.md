# Suggested Commands for au21-engine

## Development Commands

### Running the Application
```sh
# Run in development mode with live coding
./gradlew quarkusDev
# or
just dev
```

### Building the Application
```sh
# Build the application
./gradlew build

# Build with an uber-jar
./gradlew build -Dquarkus.package.jar.type=uber-jar

# Build a native executable
./gradlew build -Dquarkus.native.enabled=true

# Build a native executable in a container
./gradlew build -Dquarkus.native.enabled=true -Dquarkus.native.container-build=true
```

### Testing
```sh
# Run tests
./gradlew test

# Generate JaCoCo test report
./gradlew jacocoTestReport
```

### Upgrading Dependencies
```sh
# Check for dependency updates
./gradlew dependencyUpdates
# or
just check_for_dependency_updates

# Upgrade Gradle
./gradlew wrapper --gradle-version=8.10
# or
just upgrade_gradle
```

### TypeScript Generation
```sh
# Generate TypeScript definitions
./gradlew generateTypescript
```

## Quarkus Developer UI & GraphQL
```sh
# Open Quarkus Dev UI
open http://localhost:4040/q/dev
# or
just open_quarkus_dev

# Open GraphQL UI
open http://localhost:4040/q/graphql-ui/?query=query%20%7B%0A%20%20users%20%7B%0A%20%20%20%20id%0A%20%20%20%20username%0A%20%20%20%20password%0A%20%20%7D%0A%7D
# or
just open_quarkus_graphql-ui

# View GraphQL Schema
open http://localhost:4040/graphql/schema.graphql
# or
just open_quarkus_graphql-schema
```

## Utility Commands

### Docker & Services
```sh
# Run Jaeger (OpenTelemetry UI)
just run-jaeger-with-docker
just stop-jaeger-with-docker
just start-jaeger-with-docker

# Run SEQ (Log visualization)
just seq_run
just seq_input_gelf_up
just seq_input_gelf_down
```

### Port Management
```sh
# Kill processes on specific ports
just kill-port-4040
just kill-port-8080
```

### Node Modules Cleanup
```sh
# Clean up node_modules and lock files
just remove_node_modules_and_lock_files
```

### Frontend Integration
```sh
# Copy frontend dist to engine resources
just copy-frontend-dist-to-engine
```

## Enhancing ObjectDB Models
```sh
# Enhance ObjectDB model classes
./gradlew enhance
```

## Git Commands
```sh
# Standard Git commands
git status
git add .
git commit -m "commit message"
git push

# Clone the repository
git clone <repository-url>
```

## System Commands
```sh
# List directories and files
ls -la

# Change directory
cd <directory>

# Find files
find . -name "*.kt"

# Grep for text in files
grep -r "TextToFind" .
```