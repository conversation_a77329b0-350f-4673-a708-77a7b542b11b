# au21-engine Project Onboarding Summary

## Project Overview
The au21-engine project is a Quarkus-based Java/Kotlin application that serves as an auction engine, with particular focus on Double Exchange (DE) auctions. It provides backend services for auction management, user management, order submission, and auction flow control.

## Technology Stack
- **Language**: Kotlin 2.0.10 with Java 21
- **Framework**: Quarkus 3.14.x 
- **Build System**: Gradle (Kotlin DSL)
- **Database**: ObjectDB (Object-Oriented Database)
- **API**: GraphQL and REST endpoints
- **Communication**: WebSockets
- **Observability**: OpenTelemetry, GELF logging, Jaeger
- **Testing**: JUnit 5, Kotest, Mockk

## Key Features
- Command-based architecture for handling auction operations
- GraphQL API for querying and manipulating auction data
- WebSocket-based real-time communication
- Double Exchange auction engine with matching algorithm
- User and company management
- Session management
- Observability integration

## Project Structure
- `src/main/kotlin/au21/engine/`: Main application code
  - `framework/`: Core framework functionality
  - `domain/`: Domain models and business logic
    - `common/`: Shared models
    - `de/`: Double Exchange auction specific code
  - `generators/`: Code generators
- `src/main/resources/`: Configuration and static resources
- `src/test/`: Test code
- `devops/`: DevOps related files

## Development Workflow
1. Run the application in dev mode: `./gradlew quarkusDev` or `just dev`
2. Access the application at: http://localhost:4040
3. Access GraphQL UI at: http://localhost:4040/q/graphql-ui/
4. Run tests with: `./gradlew test`
5. Generate test reports: `./gradlew jacocoTestReport`

## Important Commands
- **Run in dev mode**: `./gradlew quarkusDev` or `just dev`
- **Build application**: `./gradlew build`
- **Run tests**: `./gradlew test`
- **Generate test reports**: `./gradlew jacocoTestReport`
- **Check for dependency updates**: `./gradlew dependencyUpdates`
- **Upgrade Gradle**: `just upgrade_gradle`
- **Enhance DB models**: `./gradlew enhance`
- **Generate TypeScript definitions**: `./gradlew generateTypescript`

## Architectural Patterns
- Command pattern for handling operations
- Domain-driven design with clear separation of concerns
- Entity-based persistence with ObjectDB
- Real-time communication via WebSockets
- GraphQL API for flexible data queries

## Code Style and Conventions
- Kotlin naming conventions (camelCase, PascalCase)
- Command pattern implementation with consistent naming
- Entity enhancement for ObjectDB
- KDoc format for documentation
- Error handling with specific exception types

## Additional Resources
- Memory files:
  - `project_overview.md`: Detailed project overview
  - `suggested_commands.md`: Complete list of useful commands
  - `task_completion_workflow.md`: Step-by-step task completion process
  - `entrypoints_and_running.md`: Application entrypoints and running instructions
  - `project_architecture.md`: Detailed architectural information
  - `code_style_and_conventions.md`: Coding standards and conventions

## How to Get Started
1. Activate the project: `activate_project` with "au21-engine"
2. Review memory files: `list_memories` and `read_memory`
3. Run the application: `./gradlew quarkusDev`
4. Explore the GraphQL API: http://localhost:4040/q/graphql-ui/
5. Initialize test data with the `reCreateDummyDb` mutation