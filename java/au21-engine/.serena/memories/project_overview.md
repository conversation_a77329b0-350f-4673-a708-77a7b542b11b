# Project Overview: au21-engine

## Purpose
The au21-engine project is a Quarkus-based Java/Kotlin application that serves as an auction engine. It appears to be focused on providing backend services for auction systems, with specific features for Double Exchange (DE) auctions. The project includes functionality for auction management, user and company management, order submission, and flow control.

## Tech Stack
- **Language**: Kotlin 2.0.10 with Java 21
- **Framework**: Quarkus 3.14.x
- **Build System**: Gradle (Kotlin DSL)
- **Database**: ObjectDB (Object-Oriented Database)
- **API**: GraphQL and REST endpoints
- **Communication**: WebSockets
- **Observability**: OpenTelemetry, GELF logging, Jaeger
- **Visualization**: Generates PlantUML and Mermaid diagrams
- **Testing**: JUnit 5, Kotest, Mockk
- **Template Engine**: JTE (Java Template Engine)

## Project Structure
- `src/main/kotlin/au21/engine/`: Main application code
  - `framework/`: Core framework functionality including commands, database, utils
  - `domain/`: Domain models and business logic
    - `common/`: Shared models (users, companies, etc.)
    - `de/`: Double Exchange auction specific code
  - `generators/`: Code generators for TypeScript, PlantUML, Mermaid
- `src/main/resources/`: Configuration and static resources
- `src/test/`: Test code
- `devops/`: DevOps related files (Docker, OpenTelemetry)

## Main Features
- GraphQL API for querying and manipulating auction data
- WebSocket-based communication for real-time updates
- Command-based architecture for handling auction operations
- Double Exchange auction engine with sophisticated matching logic
- User and company management
- Session management
- Flow control functionality for auctions
- Observability with OpenTelemetry and logging

The project appears to be part of a larger system, with references to a frontend component and various microservices.