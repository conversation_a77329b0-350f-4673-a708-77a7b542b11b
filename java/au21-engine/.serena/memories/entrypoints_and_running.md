# Entrypoints and Running the Application

## Main Entrypoints

### 1. Application Entry Point
The main entry point is defined in `au21.engine.Main.kt` which initializes the Quarkus application.

### 2. GraphQL API
The GraphQL API is the primary interface for external systems and is defined in `au21.engine.framework.graphql.GraphqlApi.kt`.

Endpoints:
- GraphQL API: http://localhost:4040/graphql
- GraphQL UI: http://localhost:4040/q/graphql-ui/
- GraphQL Schema: http://localhost:4040/graphql/schema.graphql

### 3. WebSocket Interface
The WebSocket interface handles real-time communication with clients and is implemented in `au21.engine.framework.client.SocketHandler.kt`.

## Running the Application

### Development Mode
```sh
# Run in development mode with live coding
./gradlew quarkusDev
# or
just dev
```

This starts the application in development mode with hot reloading enabled. You can access:
- Application: http://localhost:4040
- Dev UI: http://localhost:4040/q/dev
- GraphQL UI: http://localhost:4040/q/graphql-ui/

### Production Mode
```sh
# Build the application
./gradlew build

# Run the application
java -jar build/quarkus-app/quarkus-run.jar
```

### Native Executable
```sh
# Build a native executable
./gradlew build -Dquarkus.native.enabled=true

# Run the native executable
./build/au24.engine-1.0.0-SNAPSHOT-runner
```

## Docker Container
The application can be run in a Docker container:

```sh
docker login registry.gitlab.com
docker pull registry.gitlab.com/auctionologies/2024/2024-02-19-code-with-quarkus:COMMIT_3f99f61b
docker run -p 8080:8080 registry.gitlab.com/auctionologies/2024/2024-02-19-code-with-quarkus:COMMIT_3f99f61b 
```

## Supporting Services

### Jaeger (for OpenTelemetry)
```sh
# Start Jaeger container
just run-jaeger-with-docker
```

Access Jaeger UI at: http://localhost:16686

### SEQ (for Logging)
```sh
# Start SEQ logging
just seq_input_gelf_up
```

Access SEQ UI at: http://dev1.auctionologies.com:5340

## Initializing the Database

The database is initialized on first access. You can also initialize it explicitly through the GraphQL API:

```graphql
mutation {
  initDb
}
```

For a sample database with test data:

```graphql
mutation {
  reCreateDummyDb(
    auction_count: 1,
    auctioneer_count: 2,
    trader_count: 4,
    round_count: 1
  )
}
```

## Testing API with GraphQL

Example queries:

```graphql
# Get all auctions
query {
  auctions {
    id
    name
  }
}

# Get all users
query {
  users {
    id
    username
  }
}
```

Example mutations:

```graphql
# Create a new auction
mutation {
  createAuction(name: "Test Auction")
}

# Create a new company
mutation {
  create_company(shortName: "TEST")
}
```