import java.time.Duration
// import org.gradle.caching.http.HttpBuildCache // Uncomment if enabling remote HTTP cache

// settings.gradle.kts

// --- Plugin Management ---
pluginManagement {
    val quarkusPluginVersion: String by settings
    val quarkusPluginId: String by settings
    repositories {
        mavenCentral()
        gradlePluginPortal()
    }
    plugins {
        id(quarkusPluginId) version quarkusPluginVersion
        id("com.gradle.develocity") version "3.18.1" // Use a recent version
        id("org.gradle.toolchains.foojay-resolver-convention") version "0.10.0"
    }
}

// --- Apply Plugins to Settings ---
plugins {
    id("com.gradle.develocity")
    id("org.gradle.toolchains.foojay-resolver-convention")
}


// --- Configure Develocity (formerly Gradle Enterprise) ---
develocity {
    buildScan {
        termsOfUseUrl = "https://gradle.com/terms-of-service"
        termsOfUseAgree = "yes"

        // Configure publishing rules inside this nested block
        publishing {
            // *** Use onlyIf to implement the publish-on-failure condition ***
            onlyIf {
                // 'it' is the PublishingContext providing access to build results etc.
                it.buildResult.failures.isNotEmpty() // Publish only if there were failures
            }
            // Alternatively, to always publish on CI (your second example):
            // if (!System.getenv("CI").isNullOrEmpty()) {
            //     onlyIf { true } // Always publish if CI env var is set
            // }
            // Or simply use publishAlways() if you always want scans:
            // publishAlways()
        }

        // You can still add tags conditionally, outside the publishing block if needed
        // if (!System.getenv("CI").isNullOrEmpty()) {
        //     tag("CI")
        // }
    }
}

// --- Configure Build Cache ---
buildCache {
    local {
        directory = File(rootDir, "build-cache")
        removeUnusedEntriesAfterDays = 7 // Using integer property
    }
    // Optional: Configure Remote Cache
    /*
    remote(HttpBuildCache::class) {
        url = uri("https://your-remote-cache.example.com/cache/")
        isPush = System.getenv("CI") == "true"
    }
    */
}

// --- Project Name ---
rootProject.name = "au21-engine"
