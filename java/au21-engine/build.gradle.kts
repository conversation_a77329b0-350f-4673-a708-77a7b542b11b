// build.gradle.kts
import com.github.benmanes.gradle.versions.updates.DependencyUpdatesTask
import org.jetbrains.kotlin.gradle.dsl.JvmTarget


// versions:
val axion_release = "1.15.0"
val asciitable = "0.3.2"
val classgraph = "4.8.179"
val combinatorics = "1.6.0"
val gson = "2.13.0"
val jackson_module_kotlin = "2.18.3"
val javax_persistence = "2.2.1"
val joda_time = "2.14.0" // Consider migrating to java.time if possible
val jsoniter = "0.9.23"
val jta = "1.1"
val jte = "3.2.0"
val kaml = "0.77.1"
val kasechange_jvm = "1.4.1"
val kotest = "5.9.1"
val kotlinVersion = "2.1.20"
val kotlinx_serialization_json = "1.8.1" // Use a version compatible with Kotlin 2.1.20 (e.g., 1.7.0+)
val mockk = "1.14.0"
val object_db = "2.9.0"
val opencsv = "5.10"
val plantuml_builder = "2.8"
val ps_java = "0.1.19"
val testcontainers = "1.19.8"
val ts_generator = "1.1.2"
val uri_rest: String = "3.14.5"

plugins {
    // --- Use kotlinVersion variable consistently ---
    kotlin("jvm") version "2.1.20"
    kotlin("plugin.allopen") version "2.1.20"
    kotlin("plugin.serialization") version "2.1.20"
    // ---
    id("io.quarkus")
    id("com.github.ben-manes.versions") version "0.52.0" // Ensure this version is current
    jacoco
}

repositories {
    mavenCentral()
    mavenLocal()
    maven { url = uri("https://m2.objectdb.com") }
    maven { url = uri("https://jitpack.io") }
}

val quarkusPlatformGroupId: String by project
val quarkusPlatformArtifactId: String by project
val quarkusPlatformVersion: String by project

dependencies {
    implementation(enforcedPlatform("$quarkusPlatformGroupId:$quarkusPlatformArtifactId:$quarkusPlatformVersion"))
    implementation("io.quarkus:quarkus-arc")
    implementation("io.quarkus:quarkus-container-image-jib")
    implementation("io.quarkus:quarkus-elytron-security-properties-file")
    implementation("io.quarkus:quarkus-kotlin")
    implementation("io.quarkus:quarkus-logging-json")
    implementation("io.quarkus:quarkus-micrometer-opentelemetry")
    implementation("io.quarkus:quarkus-opentelemetry")
    implementation("io.quarkus:quarkus-rest")
    implementation("io.quarkus:quarkus-rest-jackson")
    implementation("io.quarkus:quarkus-smallrye-graphql")
    implementation("io.quarkus:quarkus-websockets")

    // needed for OTEL:
    implementation("com.squareup.okhttp3:okhttp-jvm:5.0.0-alpha.12")

    // database
    implementation("org.eclipse.persistence:javax.persistence:$javax_persistence") // Note: This is the old `javax` namespace
    implementation("javax.transaction:jta:$jta") // Note: Old `javax` namespace
    implementation("com.objectdb:objectdb:$object_db")

    // Miscellaneous
    implementation("ch.ifocusit:plantuml-builder:$plantuml_builder")
    implementation("com.github.ntrrgc:ts-generator:$ts_generator")
    implementation("com.konghq:unirest-java:$uri_rest") // Consider renaming 'uri_test' variable
    implementation("com.opencsv:opencsv:$opencsv")
    implementation("de.vandermeer:asciitable:$asciitable")
    implementation("gg.jte:jte:$jte")
    implementation("io.github.classgraph:classgraph:$classgraph")
    implementation("joda-time:joda-time:$joda_time")
    implementation("net.pearx.kasechange:kasechange-jvm:$kasechange_jvm")
    implementation("org.psjava:psjava:$ps_java")

    // json
    // Ensure this version is compatible with Kotlin 2.1.20
    implementation("org.jetbrains.kotlinx:kotlinx-serialization-json:$kotlinx_serialization_json")
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:$jackson_module_kotlin")
    implementation("com.google.code.gson:gson:$gson")
    implementation("com.jsoniter:jsoniter:$jsoniter")

    // yaml
    // implementation("com.charleskorn.kaml:kaml:${kaml}")
    implementation("com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.16.1")
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.16.1")


    // testing
    testImplementation("io.quarkus:quarkus-junit5")
    testImplementation("io.quarkus:quarkus-jacoco")
    testImplementation("io.rest-assured:rest-assured")
    testImplementation("io.kotest:kotest-runner-junit5:$kotest")
    testImplementation("io.kotest:kotest-assertions-core:$kotest")
    testImplementation("io.kotest:kotest-assertions-json-jvm:$kotest")
    testImplementation("io.mockk:mockk:$mockk")
    testImplementation("com.github.shiguruikai:combinatoricskt:$combinatorics")
    testImplementation("org.testcontainers:testcontainers:$testcontainers")
}

group = "au24.engine" // Consider if this group name is still accurate (au24 vs au21)
version = "1.0.0-SNAPSHOT"

// Ensure all Kotlin dependencies use the defined kotlinVersion (Keep existing)
configurations.all {
    resolutionStrategy.eachDependency {
        if (requested.group == "org.jetbrains.kotlin" && requested.name.startsWith("kotlin-")) {
            useVersion(kotlinVersion)
        }
    }
}

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

allOpen {
    annotation("jakarta.ws.rs.Path")
    annotation("jakarta.enterprise.context.ApplicationScoped")
    annotation("jakarta.persistence.Entity") // Using jakarta.persistence is more modern than javax.persistence
    annotation("io.quarkus.test.junit.QuarkusTest")
}

tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile>().configureEach {
    finalizedBy(enhance) // Use the task provider directly
    compilerOptions {
        jvmTarget.set(JvmTarget.JVM_21)
        javaParameters = true
        // Removed '-Xuse-k2' (default in 2.x) and '-Xbackend-threads=0' (usually default)
        // freeCompilerArgs.addAll(listOf(/* Add specific flags if needed */))
    }
    // Removed risky 'outputs.cacheIf { true }'
}

kotlin { jvmToolchain(21) }

// Updated Test Task Configuration
tasks.withType<Test>().configureEach {
    // Keep existing settings
    systemProperty("java.util.logging.manager", "org.jboss.logmanager.LogManager")
    useJUnitPlatform()
    failFast = true // Keep existing preference
    systemProperty("OBJECTDB_URL", "objectdb:test-set-in-build-gradle.mem") // Keep existing

    // Disable Kotest autoscan to avoid startup cost and memory issues
    systemProperty("kotest.framework.classpath.scanning.autoscan.disable", "true")

    // --- Added Balanced Parallel Test Execution ---
    val maxCpuCores = Runtime.getRuntime().availableProcessors()
    // Use half the cores, or at least 1, up to a reasonable maximum (e.g., 4 or 8)
    maxParallelForks = (maxCpuCores / 2).coerceAtLeast(1).coerceAtMost(4)
    println("Test maxParallelForks set to $maxParallelForks")
    // ---

    // Increase memory for tests to handle classpath scanning
    jvmArgs("-Xmx1g", "-XX:MaxMetaspaceSize=256m")

    // Keep existing test logging configuration
    testLogging {
        events("started", "passed", "skipped", "failed")
        exceptionFormat = org.gradle.api.tasks.testing.logging.TestExceptionFormat.SHORT
        showStandardStreams = true // Keep existing preference
        showStackTraces = true
        showCauses = true
    }
    // Removed harmful 'outputs.upToDateWhen { false }'
}

// Keep existing Jacoco configuration
tasks.withType<JacocoReport> {
    afterEvaluate {
        classDirectories.setFrom(
            files(
                classDirectories.files.map {
                    fileTree(it) {
                        exclude("simulator/*", "exp/*", "optimizationExps/*")
                    }
                }
            )
        )
    }
}
tasks.jacocoTestReport { reports { xml.required.set(true) } }


// Updated 'enhance' Task - REMOVED incorrect output declaration
// Updated 'enhance' Task - Added kotlin-stdlib to the enhancer's classpath
val enhance by tasks.registering(JavaExec::class) {
    description = "Enhances Object Db model classes"
    mainClass.set("com.objectdb.Enhancer")

    // --- Declare Inputs for Caching ---
    inputs.files(sourceSets.main.get().output.classesDirs)
        .withPropertyName("classesToEnhance")
        .withPathSensitivity(PathSensitivity.RELATIVE)

    val enhancerArgs = listOf(
        "-cp", tasks.compileKotlin.get().destinationDirectory.get().asFile.absolutePath, // Target classes for enhancement
        "au21.engine.domain.common.model.*",
        "au21.engine.domain.de.model.*",
        "au21.engine.framework.database.AuEntity",
        "au21.engine.framework.commands.CommandJson"
    )
    inputs.property("enhancerArgs", enhancerArgs)

    // --- Updated Enhancer Classpath Configuration ---
    val objectdbEnhancerConfig = configurations.create("objectdbEnhancer") {
        isCanBeResolved = true
        isCanBeConsumed = false
        // Add ObjectDB dependency
        dependencies.add(project.dependencies.create("com.objectdb:objectdb:$object_db"))
        // *** ADD KOTLIN STDLIB DEPENDENCY ***
        dependencies.add(project.dependencies.create("org.jetbrains.kotlin:kotlin-stdlib:$kotlinVersion"))
    }
    // Input: The classpath needed to RUN the enhancer tool itself (now includes kotlin-stdlib)
    inputs.files(objectdbEnhancerConfig)
        .withPropertyName("enhancerClasspath")
        .withPathSensitivity(PathSensitivity.NONE)

    // --- Task Configuration ---
    // Use the configuration that now includes objectdb AND kotlin-stdlib
    classpath = objectdbEnhancerConfig
    args(enhancerArgs) // Use the argument list defined above

    // Keep existing conditional execution logic
    onlyIf {
        val skipEnhance = System.getenv("SKIP_ENHANCE")?.lowercase() == "true"
        if (skipEnhance) {
            println("Skipping the 'enhance' task because SKIP_ENHANCE is set to 'true'.")
        } else {
            println("Running the 'enhance' task because SKIP_ENHANCE is not set to 'true'.")
        }
        !skipEnhance
    }
}


// Updated 'generateTypescript' Task with Input/Output Declarations for Caching
// Assuming it was previously registered like this. If using tasks.named, adapt accordingly.
tasks.register("generateTypescript", JavaExec::class) { // Or tasks.named("generateTypescript", JavaExec::class) { ... }
    description = "Generates TypeScript definition files from Kotlin code" // Added description
    mainClass.set("au21.engine.generators.typescript.Ts_generatorKt")
    classpath = sourceSets.main.get().runtimeClasspath // May need refinement if generator has specific needs

    // --- Declare Inputs for Caching ---
    // Input: Source files the generator reads
    inputs.files(sourceSets.main.get().allSource)
        .withPropertyName("sourceFilesForTsGen")
        .withPathSensitivity(PathSensitivity.RELATIVE) // Content + relative path matter

    // Input: Classpath needed for reflection/analysis by the generator
    inputs.files(sourceSets.main.get().runtimeClasspath)
        .withPropertyName("runtimeClasspathForTsGen")
        .withPathSensitivity(PathSensitivity.RELATIVE) // Classpath contents matter

    // --- Declare Outputs (Adjust path as needed) ---
    // Output: The directory where TypeScript files are generated
    // Make sure this path is accurate for your generator
    outputs.dir("${project.buildDir}/generated/ts")
        .withPropertyName("typescriptOutputDir")

    // Optional: If generation is unstable or inputs aren't fully trackable
    // outputs.upToDateWhen { false } // Use ONLY if absolutely necessary

    // Removed risky 'outputs.cacheIf { true }'
}


// Keep existing Dependency Updates configuration
tasks.named<DependencyUpdatesTask>("dependencyUpdates") {
    outputFormatter = "plain,json,html"
    outputDir = "build/dependencyUpdates"
    rejectVersionIf {
        // Keep existing rejection logic for Quarkus versions
        if (candidate.group.startsWith("io.quarkus")) {
            // Example: Allow only 3.20.x or potentially newer stable versions within Quarkus 3
            // Adjust this regex based on your upgrade strategy
            !candidate.version.matches("^3\\.20\\.[0-9]+$".toRegex()) && !candidate.version.matches("^3\\.[2-9][0-9]+\\..*".toRegex())
        } else {
            // Keep existing stability check for other dependencies
            isNonStable(candidate.version) && !isNonStable(currentVersion)
        }
    }
}
// Keep existing isNonStable function
fun isNonStable(version: String): Boolean {
    val stableKeyword = listOf("RELEASE", "FINAL", "GA").any { version.uppercase().contains(it) }
    val regex = "^[0-9,.v-]+(-r)?$".toRegex()
    return !(stableKeyword || regex.matches(version))
}


// Keep existing diagnostic task (whyKotlinStdlib)
tasks.register("whyKotlinStdlib") {
    doLast {
        configurations.compileClasspath.get().resolvedConfiguration.lenientConfiguration.allModuleDependencies
            .filter { it.moduleName == "kotlin-stdlib-jdk8" || it.moduleName == "kotlin-stdlib" } // Check both variants
            .forEach { dep ->
                println("${dep.moduleGroup}:${dep.moduleName}:${dep.moduleVersion} is brought in by:")
                dep.parents.forEach { parent -> println(" - ${parent.name}") }
            }
    }
}

// --- Added Dependency Locking ---
dependencyLocking {
    lockAllConfigurations() // Lock all configurations for reproducible builds
    // To generate/update lockfiles, run: ./gradlew dependencies --write-locks
}
