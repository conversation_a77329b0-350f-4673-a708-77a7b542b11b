<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="au21-engine.main-redhat" type="QuarkusRunConfigurationType" factoryName="Quarkus">
    <module name="au21-engine.main" />
    <QsGradleRunConfiguration>
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value="quarkusDev" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>false</RunAsTest>
      <profile>dev</profile>
    </QsGradleRunConfiguration>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>