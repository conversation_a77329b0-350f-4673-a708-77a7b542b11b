
double-sided auction design:

Initial Round Price: The auction starts with a round price set by the auctioneer, deliberately distant from the last known market price. This favors one side, either being much lower or much higher than the market price.

Bidding and Price Adjustment: In each round, participants enter the number of items they're willing to buy or sell at the announced price. If total demand exceeds total supply, the price increases. If total supply exceeds total demand, the price decreases. The price moves in large steps initially.

One-Time Price Reversal: The large price steps continue until the price overshoots - i.e., the demand-supply ratio inverts (total demand is less than total supply, or vice versa). At this point, the price reverses direction and starts moving in smaller steps. This price reversal only happens once.

Demand/Supply Constraints: If the price is increasing, buyers cannot increase their demand and sellers cannot decrease their supply. If the price is decreasing, sellers cannot increase their supply and buyers cannot decrease their demand.

Switching Roles: Participants can switch roles (from buyer to seller or vice versa) only once, and only if the switch is rational given the price movement. Once switched, they cannot switch back. For example, a buyer cannot switch to a seller at a lower price than they were willing to buy, and a seller cannot switch to a buyer at a higher price than they were willing to sell.

End of Auction: The auction ends when total demand equals total supply at the current round price. If the auction ends without reaching this equilibrium, the items are prorated based on the demand or supply in the last round.


---

Buying Rules:

As price increases, buyers cannot increase their quantity.
As price decreases, buyers cannot decrease their quantity.
Selling Rules:

As price increases, sellers cannot decrease their quantity.
As price decreases, sellers cannot increase their quantity.
Switching Rules:

A seller cannot switch to a buyer if the price goes up.
A buyer cannot switch to a seller if the price goes down.
A zero bid is equivalent to a 0 sell and a 0 buy, so simply apply the buyer, seller, and switching rules.

---

Auction Structure:

The auction proceeds in multiple rounds.
The auction aims to maximize the volume of product traded between sellers and buyers at a single price.
Price Adjustment:

Ascending Price Auction: If demand exceeds supply in a round, the price increases in subsequent rounds.
Descending Price Auction: If supply exceeds demand in a round, the price decreases in subsequent rounds.
The auction ends when supply equals demand or becomes less than demand.
Bids and Asks:

Traders submit their bids (willingness to buy) and asks (willingness to sell) in each round based on the current price.
No transactions occur until the auction ends; bids and asks are indications of willingness.
Monotonic Behavior:

Buyers: Cannot demand more volume at a higher price or less volume at a lower price.
Sellers: Cannot supply more volume at a lower price or less volume at a higher price.
Role Switching:

Traders can switch roles from buyer to seller or vice versa, but:
In an Ascending Price Auction: Sellers cannot switch to buyers.
In a Descending Price Auction: Buyers cannot switch to sellers.
A trader can only switch roles if it doesn't violate their previous willingness to trade (i.e., they don’t buy at a higher price than they were willing to sell or sell at a lower price than they were willing to buy).
Credit Limits:

Each trader has counterparty credit limits with other traders, constraining the maximum volume that can be traded between any two traders.
Flow Maximization:

The goal is to maximize the volume of the product that can be traded given the price, bids, asks, and credit limits.
This involves solving a maximum flow problem where the network is adjusted according to trader roles and credit limits.

---

this is a multi-round clock auction in which the auction proceeds in a series of Rounds. At the start of each round the auctioneer announces a round price, and the traders indicate the quantity of product they want to buy or sell in this auction. These are 'indicative' bids, nothing is actually transacted until the end of the auction, see below.
* At the end of the round to total buy and sell quantities are added up, and the buyers and sellers are matched. This is 'provisional' match and not revealed to traders
* if the buy volume is greater than the sell volume in the first round this this will be and ascending price auction, and if the opposite then a descending price auction.
* The auction ends when the provisional match volume decreases, or the total buy vol = total sell vol with nothing unmatched (which i think is the same thing).
* there are rules to prevent 'irrational bidding.
* Buy rule: traders cannot indicate a willingness to buy a greater volume at a higher price or a lower volume at a lower price, and the opposite is the case for sellers.
* Switching: traders can switch from buyers to sellers and vice versa as long as they don't indicate a willingness to buy at a higher price than they were willing to sell, and the opposite.
* If the price 'overshoots' ie: the ratio of total buy vol to sell vol inverts, then that round is called the Post Overshoot round, and the prior round the Pre Overshoot round, and what happens then is that the price reverses but in smaller increments until just before it reaches the PreOvershoot price, or the total buy=total sell and is fully matched or the match volume decreases (this is the ending rule) ie: the price might initially increase in 1cent increments, and overshoot at 51cents then it might decrease in 0.25 cent decrements until it reaches 51.25 (or the ending rule is met).
* Additionally the system should allow for traders to have counterparty credit limits with each other and those will affect the matching. Note: the intention is to handle counterparty risks outside of the auction, eg letters of credit, because there are case where the matching may not increase monotonically.


1) traders can switch from buyer to seller or vice versa subject to my switching rule above - pay attention, please!
2) price changes vary, each auction has a pre-overshoot price change, and a post overshoot (reversal) price change.
3) the matching is a max-flow algorithm, ie: maximizing the flow from seller to buyer, and we should allow for configuration ie: max number of matching partners (pro-rata, or breadth first), and minimum number of matching partners (depth first), or first-come-first-served (this is determined by timestamp of first bid - an incentive for the Dutch side (price improving for them each round) of the auction to place a bid, the English side (price getting worse for them each round) has eligibility constraints, if you think through my rules carefully) - this is a good question btw,
4) No irrational bids are allowed, traders get max and min buy and sell limits each round, how that is presented to traders will be explained later.
5) auction starting price is set by the auctioneer. The comment about reserve prices demonstrates some lack of understanding of the rules, go back and think clearly what it means to not demonstrate a willingness to buy more at a higher price etc
6) Time limits is a good question: we could implement a clock that ticks down and in addition have a rule that the rounds end once all the bids are in (and we will add a randon time so that bidders don't know that they were the last one).
7) we use max flow right, so each edge in the graph is a constraint, ie: volume based on current round price and credit limit.
8) awarded price is the same for everyone, and is the round which results in the max flow.
9) good question: once the next round starts, we show traders the total activity in terms of a range: High, medium, low. High means that the delta between buy and sell volume is in a high range, eg: 30+ (configurable) Medium might be 21-30, and low is 0-20. So traders get some info but not enough to game the system.
10) no max rounds, but the price is configurable: there is a pre-overshoot (large) price and a post overshoot (low)price



