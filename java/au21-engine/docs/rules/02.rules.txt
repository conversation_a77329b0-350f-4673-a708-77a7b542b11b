# Price reversing clock auction rules


## Auction Structure
- Multi-round clock auction
- Aims to maximize trading volume at a single price
- Consists of buyers and sellers submitting the quantity they are willing to buy or sell at each round

## Price Mechanism
- Initial price set by auctioneer, deliberately distant from market price
- Price adjusts based on supply and demand:
  - Ascending if demand > supply
  - Descending if supply > demand
- Large price steps initially, smaller steps after price reversal
- One-time price reversal occurs when demand-supply ratio inverts

## Bidding Process
- Traders submit indicative bids/asks each round
- No transactions until auction ends
- Provisional matching done after each round (not revealed to traders)

## Trader Behavior Rules
- Monotonic and rational behavior enforced:

### Buying Rules:

As price increases, buyers cannot increase their quantity.
As price decreases, buyers cannot decrease their quantity.

### Selling Rules:

As price increases, sellers cannot decrease their quantity.
As price decreases, sellers cannot increase their quantity.

### Switching Rules:

A seller cannot switch to a buyer if the price goes up.
A buyer cannot switch to a seller if the price goes down.
A zero bid is equivalent to a 0 sell and a 0 buy, so simply apply the buyer, seller, and switching rules.

## Auction End Conditions
- Supply equals demand
- Provisional match volume decreases
- Reaches just before pre-overshoot price in reversal phase

## Additional Features
- Counterparty credit limits affect matching
- Max-flow algorithm used for matching
- Configurable matching strategies (e.g., pro-rata, depth-first, first-come-first-served)
- Activity level feedback provided to traders (High, Medium, Low)
  - for example: ranges based on the difference between buy and sell volumes.
- Time limits and random ending to prevent gaming

## Final Price and Allocation
- Single price for all participants
- ie: price at round with maximum matched volume
- Prorating volume (at a single price) if perfect equilibrium not reached