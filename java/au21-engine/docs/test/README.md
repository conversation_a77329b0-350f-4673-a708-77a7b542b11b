# AU21 Engine Testing Framework

This document provides comprehensive guidance for testing the AU21 Engine auction system.

## Overview

The AU21 Engine testing framework implements a multi-layered testing strategy:

- **Unit Tests**: Test individual commands and components in isolation
- **Integration Tests**: Test complete workflows and component interactions
- **End-to-End Tests**: Test the full system through the GraphQL UI
- **Performance Tests**: Measure system performance under load
- **Security Tests**: Validate security measures and dependency safety

## Test Structure

```
src/test/kotlin/au21/engine/
├── domain/
│   ├── common/
│   │   └── LoginCommandTest.kt
│   └── de/
│       ├── DeAuctionSaveCommandTest.kt
│       ├── DeCreditSetCommandTest.kt
│       ├── DeOrderSubmitCommandTest.kt
│       └── ...
├── test/
│   ├── e2e/
│   │   └── GraphQLUIE2ETest.js
│   ├── integration/
│   │   └── AuctionWorkflowIntegrationTest.kt
│   └── performance/
│       └── AuctionPerformanceTest.kt
└── CommandTestBase.kt
```

## Running Tests

### Prerequisites

1. **Java 17+** - Required for Kotlin compilation
2. **Node.js 16+** - Required for E2E tests
3. **PostgreSQL** - Required for integration tests
4. **Docker** (optional) - For containerized testing

### Setup

```bash
# Install E2E test dependencies
npm install

# Create test result directories
npm run setup:test-dirs

# Start PostgreSQL (if not using Docker)
# Configure connection in application-test.properties
```

### Unit Tests

```bash
# Run all unit tests
./gradlew test

# Run specific test class
./gradlew test --tests "DeAuctionSaveCommandTest"

# Run tests with coverage
./gradlew test jacocoTestReport

# Run tests for specific package
./gradlew test --tests "au21.engine.domain.de.*"
```

### Integration Tests

```bash
# Run all integration tests
./gradlew test --tests "*IntegrationTest"

# Run specific integration test
./gradlew test --tests "AuctionWorkflowIntegrationTest"
```

### Performance Tests

```bash
# Run performance tests
./gradlew test --tests "*PerformanceTest"

# Run with performance profiling
./gradlew test --tests "*PerformanceTest" -Dquarkus.profile=performance
```

### End-to-End Tests

```bash
# Start the application first
./gradlew quarkusDev

# In another terminal, run E2E tests
npm run test:e2e

# Run E2E tests in headless mode (for CI)
npm run test:e2e:headless

# Run E2E tests with CI reporting
npm run test:e2e:ci
```

### All Tests

```bash
# Run complete test suite
./gradlew clean test
npm run test:e2e:headless
```

## Test Configuration

### Application Properties

Test-specific configuration in `src/test/resources/application-test.properties`:

```properties
# Database configuration for tests
quarkus.datasource.db-kind=postgresql
quarkus.datasource.jdbc.url=******************************************
quarkus.datasource.username=testuser
quarkus.datasource.password=testpassword

# Test-specific settings
quarkus.hibernate-orm.database.generation=drop-and-create
quarkus.log.level=INFO
quarkus.log.category."au21.engine".level=DEBUG

# Performance test settings
quarkus.test.profile=test
```

### E2E Test Configuration

Puppeteer configuration in E2E tests:

```javascript
const browser = await puppeteer.launch({
    headless: process.env.HEADLESS !== 'false',
    slowMo: process.env.SLOW_MO || 100,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
});
```

## Writing Tests

### Command Tests

All command tests should extend `CommandTestBase`:

```kotlin
@QuarkusTest
class MyCommandTest : DescribeSpec(), CommandTestBase {
    init {
        describe("MyCommand") {
            beforeEach {
                // Setup test data
                val initCommand = DbInitCommand()
                commandHandler.handle(initCommand.toJson())
            }

            it("should validate input correctly") {
                val command = MyCommand(/* parameters */)
                val result = command.validate()
                result.success shouldBe true
            }

            it("should execute successfully") {
                val command = MyCommand(/* parameters */)
                val result = commandHandler.handle(command.toJson())
                result.success shouldBe true
            }
        }
    }
}
```

### Integration Tests

Integration tests should test complete workflows:

```kotlin
@QuarkusTest
@Tag("integration")
class MyWorkflowIntegrationTest : DescribeSpec(), CommandTestBase {
    init {
        describe("Complete Workflow") {
            it("should complete end-to-end process") {
                // Step 1: Setup
                val setupCommand = SetupCommand()
                commandHandler.handle(setupCommand.toJson())

                // Step 2: Execute main workflow
                val workflowCommand = WorkflowCommand()
                val result = commandHandler.handle(workflowCommand.toJson())
                result.success shouldBe true

                // Step 3: Verify results
                // Add verification logic
            }
        }
    }
}
```

### E2E Tests

E2E tests should use the GraphQL UI:

```javascript
describe('My Feature E2E', () => {
    it('should complete user workflow', async () => {
        // Navigate to GraphQL UI
        await page.goto('http://localhost:8080/q/graphql-ui');
        
        // Execute GraphQL mutation
        const mutation = `
            mutation {
                executeCommand(input: {
                    classname: "au21.engine.domain.MyCommand"
                    json: "{\"param\": \"value\"}"
                }) {
                    success
                    message
                }
            }
        `;
        
        const response = await executeGraphQLMutation(page, mutation);
        expect(response).to.include('success');
    });
});
```

## Test Data Management

### Database Initialization

Each test should start with a clean database:

```kotlin
beforeEach {
    val initCommand = DbInitCommand()
    val result = commandHandler.handle(initCommand.toJson())
    result.success shouldBe true
}
```

### Test Data Builders

Use builder patterns for complex test data:

```kotlin
class AuctionTestDataBuilder {
    private var auctionId = "test-auction"
    private var auctionName = "Test Auction"
    
    fun withId(id: String) = apply { this.auctionId = id }
    fun withName(name: String) = apply { this.auctionName = name }
    
    fun build() = DeAuctionSaveCommand(
        auction_id = auctionId,
        auction_name = auctionName,
        // ... other required fields
    )
}
```

## Continuous Integration

### GitHub Actions

The CI pipeline runs automatically on:
- Push to `main` or `develop` branches
- Pull requests to `main` or `develop`
- Daily scheduled runs (for performance tests)

### Pipeline Stages

1. **Unit Tests** - Fast feedback on code changes
2. **Integration Tests** - Verify component interactions
3. **Security Scan** - Check for vulnerabilities
4. **E2E Tests** - Validate complete user workflows
5. **Performance Tests** - Monitor system performance (scheduled)
6. **Build & Package** - Create deployment artifacts

### Quality Gates

- **Code Coverage**: Minimum 80% line coverage
- **Test Success Rate**: 100% for unit and integration tests
- **Performance Benchmarks**: Response times within acceptable limits
- **Security**: No high-severity vulnerabilities

## Test Maintenance

### Adding New Command Tests

1. Create test file: `src/test/kotlin/au21/engine/domain/.../MyCommandTest.kt`
2. Extend `CommandTestBase`
3. Test validation logic
4. Test successful execution
5. Test error conditions
6. Add integration test if needed

### Updating E2E Tests

1. Update GraphQL mutations/queries as needed
2. Update selectors if UI changes
3. Add new test scenarios for new features
4. Update screenshots and assertions

### Performance Test Maintenance

1. Update performance benchmarks as system evolves
2. Add new performance tests for new features
3. Monitor and adjust test timeouts
4. Review and update load test scenarios

## Troubleshooting

### Common Issues

**Database Connection Errors**
```bash
# Check PostgreSQL is running
pg_isready -h localhost -p 5432

# Verify test database exists
psql -h localhost -U testuser -d au21_test -c "SELECT 1;"
```

**E2E Test Failures**
```bash
# Check application is running
curl http://localhost:8080/q/health

# Run E2E tests with visible browser for debugging
HEADLESS=false npm run test:e2e
```

**Performance Test Issues**
```bash
# Run with increased memory
export GRADLE_OPTS="-Xmx4g"
./gradlew test --tests "*PerformanceTest"
```

### Debug Mode

```bash
# Run tests with debug logging
./gradlew test -Dquarkus.log.level=DEBUG

# Run specific test with debug
./gradlew test --tests "MyTest" --debug-jvm
```

### Test Reports

Test reports are generated in:
- `build/reports/tests/test/` - HTML test reports
- `build/reports/jacoco/test/` - Coverage reports
- `test-results/` - E2E test results and screenshots

## Best Practices

### Test Organization

- Group related tests using `describe` blocks
- Use descriptive test names that explain the scenario
- Keep tests independent and isolated
- Use `beforeEach` for common setup

### Test Data

- Use meaningful test data that reflects real scenarios
- Avoid hardcoded values; use constants or builders
- Clean up test data after each test
- Use factories for complex object creation

### Assertions

- Use specific assertions that clearly indicate what failed
- Test both positive and negative scenarios
- Verify not just success/failure but also side effects
- Include meaningful error messages

### Performance

- Keep unit tests fast (< 100ms each)
- Use `@Tag` to separate slow tests
- Mock external dependencies in unit tests
- Use real dependencies in integration tests

### Maintenance

- Review and update tests when requirements change
- Remove obsolete tests
- Refactor common test code into utilities
- Keep test documentation up to date

## Resources

- [Kotest Documentation](https://kotest.io/)
- [Quarkus Testing Guide](https://quarkus.io/guides/getting-started-testing)
- [Puppeteer Documentation](https://pptr.dev/)
- [Mocha Testing Framework](https://mochajs.org/)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)