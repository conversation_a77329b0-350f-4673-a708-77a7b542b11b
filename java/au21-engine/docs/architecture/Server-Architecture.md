# Server Architecture Definition

The server implements a **Real-Time Serialized Command Processing System** with these key characteristics:

## Core Server Architecture

1.  **Serialized Command Execution**
    *   Commands are processed sequentially, likely in a single-threaded manner (e.g., via `@Synchronized` or a dedicated dispatcher).
    *   This deliberate serialization prevents race conditions in concurrent operations.
    *   Critical for maintaining temporal consistency and fairness in a real-time auction environment.
    *   Ensures all operations have a defined, auditable order.

2.  **Command-Action Pattern**
    *   Commands represent intent and handle validation (`AuctionRowCommand`, `LoginCommand`, etc.).
    *   Two-phase processing: validation (`validate`) followed by execution (`mutate`).
    *   Actions (`AuctionRowAction`, `LoginAction`) implement the actual state changes.
    *   Clear separation between validation logic and mutation logic.

3.  **Domain Entity Management with ObjectDB**
    *   `AuEntityManager` provides controlled access to domain objects persisted via ObjectDB.
    *   Leverages ObjectDB's transparent persistence with transaction-like semantics, minimizing mapping complexity.
    *   Domain objects (`Auction`, `Person`, `Company`) encapsulate business logic and are accessed efficiently, often akin to in-memory operations for the target data scale.

4.  **Full View State Replication via Server Push** *(Edited Section)*
    *   After successful command processing and state mutation, the **complete, client-specific view state** (e.g., `LiveClientStore`) is regenerated for each relevant connected client.
    *   This **entire view state snapshot** is then pushed to the client via WebSocket. *(Replaced "Events contain patches or complete state updates for relevant entities")*
    *   This approach guarantees client data consistency and simplifies the system by avoiding complex Change Data Capture or event diffing logic.
    *   Enables real-time monitoring of auction state by all participants, ensuring they always have the latest consistent picture.

## Design Rationale

This architecture is specifically designed for a system where:

*   **Command ordering** is critical for fairness and business rule enforcement.
*   **Absolute client data consistency** is required, achieved via immediate propagation of the full state.
*   Strict validation and rule enforcement are necessary.
*   Auditability of operations is essential.
*   **Development simplicity** and maintainability (e.g., for a single developer) are prioritized, leveraging ObjectDB's features and avoiding complex CDC mechanisms.

The client architecture (e.g., Valtio store with reactive UI) is separate and complementary to this server design, serving as the receiver and renderer of the server-pushed full state updates.

