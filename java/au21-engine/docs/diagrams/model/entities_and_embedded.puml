@startuml

class "Activity" {
  heading : String
  body : Map<String, String>
  entities : Map<String, AuEntity>
  timestamp : Date
}

class "AuSession" {
  session_id : String
  created : Date
  browser_name : String
  browser_version : String
  browser_os : String
  socket_state_label : String
  socket_last_closed : Date
  auction : Auction
  user : Person
  page_label : String
  last_ping : Date
  termination_time : Date
  termination_reason_label : String
  hasConnectionProblem() : boolean
  inRole(AuUserRole[]) : boolean
  login(Person)
  ping()
  set_page(PageName, Auction)
  terminate(SessionTerminationReason)
  termination_reason() : SessionTerminationReason
}

abstract class "Auction" {
  auction_name : String
  closed : boolean
  hidden : boolean
  notice : String
  starting_time : Date
  auction_has_started : boolean
  common_state_text : String
  auctioneer_state_text : String
  trading_companies : List<CompanyProxy>
  messages : List<AuctionMessage>
  users_that_have_seen_auction : Set<PersonProxy>
  companies_that_have_seen_auction : Set<CompanyProxy>
  counterparty_credit_limits : List<CounterpartyCreditLimit>
  add_trader_that_has_seen_auction(AuSession)
  get_counterparty_credit_limit(CompanyProxy, CompanyProxy) : CounterpartyCreditLimit
  has_trader(Company) : boolean
  set_counterparty_credit_limit(CompanyProxy, CompanyProxy, Double)
  show_auction(AuSession) : boolean
  starting_time_text() : String
}

class "AuctionMessage" {
  message_type : AuMessageType
  message : String
  from_user : PersonProxy
  from_company : CompanyProxy
  to_company : CompanyProxy
  body : String
  message_type_label : String
  from_label : String
  to_label : String
  timestamp : Date
  timestamp_label : String
  messageType() : AuMessageType
}

class "CommandJson" {
  command_json : String
  timestamp : Date
}

class "Company" {
  longname : String
  shortname : String
}

abstract class "CompanyProxy" {
  company_id : long
  shortname_at_auction_time : String
  longname_at_auction_time : String
}

class "CounterpartyCreditLimit" {
  lender : CompanyProxy
  borrower : CompanyProxy
  credit_limit : Double
  credit_limit_str : String
}

class "DeAuction" {
  settings : DeAuctionSettings
  auctioneer_state_str : String
  common_state_str : String
  autopilot_label : String
  price_decimal_places : int
  awarded_round : DeRound
  revised_orders : List<DeOrder>
  rounds : List<DeRound>
  de_trading_companies : List<DeTradingCompany>
  announce_time() : Date
  auction_has_started() : boolean
  create_manual_order(DeRound, DeTradingCompany, PersonProxy, OrderDirectionType, int) : DeOrder
  create_trader(Company) : DeTradingCompany
  firstround() : DeRound
  last_round_has_started() : boolean
  lastround() : DeRound
  remove_trader(Company)
  set_credit()
  starting_price_announced() : boolean
  starting_time_text() : String
}

class "DeAuctionSettings" {
  use_counterparty_credits : boolean
  starting_price_announcement_mins : int
  round_open_min_secs : int
  round_closed_min_secs : int
  round_orange_secs : int
  round_red_secs : int
  cost_multiplier : double
  quantity_units : String
  quantity_minimum : int
  quantity_step : int
  price_units : String
  price_decimal_places : int
  price_rule : DePriceRule
}

class "DeAuctionTemplate" {
  template_name : String
  settings : DeAuctionSettings
}

class "DeBidConstraints" {
  max_buy_quantity : int
  min_buy_quantity : int
  min_sell_quantity : int
  max_sell_quantity : int
}

class "DeInitialLimits" {
  initial_buying_cost_limit : double
  initial_selling_quantity_limit : int
  initial_buying_cost_limit_str : String
  initial_selling_quantity_limit_str : String
}

class "DeMatch" {
  sell_order : DeOrder
  buy_order : DeOrder
  buyer_shortname : String
  seller_shortname : String
  selling_quantity_limit : int
  credit : Double
  credit_str : String
  buy_limit : int
  sell_limit : int
  capacity : int
  match : int
  value : double
  value_str : String
}

class "DeOrder" {
  user : PersonProxy
  trading_company : DeTradingCompany
  timestamp : Date
  price : double
  round_number : int
  submission_type_label : String
  submission_type : OrderSubmissionType
  order_type_label : String
  type : OrderDirectionType
  quantity : int
  prev_order : DeOrder
  cost : double
  cost_str : String
  buyVol() : int
  sellVol() : int
}

class "DePriceRule" {
  price_change_initial : double
  price_change_post_reversal : double
  excess_level_1_quantity : int
  excess_level_2_quantity : int
  excess_level_3_quantity : int
  excess_level_4_quantity : int
  excess_level_0_label : String
  excess_level_1_label : String
  excess_level_2_label : String
  excess_level_3_label : String
  excess_level_4_label : String
  get_excess_level(int, AuUserRole) : String
}

class "DeRound" {
  number : int
  price : Double
  has_reversed : boolean
  price_direction_label : String
  open_time : Date
  closed_time : Date
  time_started : Date
  trader_infos : List<DeRoundTraderInfo>
  matches : List<DeMatch>
  max_potential_flow : int
  all_orders_are_non_default() : boolean
  buy_orders() : List<DeOrder>
  buyers() : List<DeTradingCompany>
  close()
  getMatch(DeTradingCompany) : DeMatch
  match_vol(DeTradingCompany) : int
  match_vol() : int
  open()
  round_open_seconds() : int
  sell_orders() : List<DeOrder>
  sellers() : List<DeTradingCompany>
  started()
}

class "DeRoundTraderInfo" {
  default_order : DeOrder
  current_matched_vol : int
  fully_opposed_match_vol : int
  de_trading_company : DeTradingCompany
  constraints : DeBidConstraints
  order : DeOrder
  bid_while_closed : boolean
  has_non_default_bid() : boolean
  has_non_zero_bid() : boolean
}

class "DeTradingCompany" {
  initial_limits : DeInitialLimits
  next_round_order_will_be_mandatory : boolean
  awarded_quantity : int
  awarded_order_type : OrderDirectionType
  blinded : boolean
  rank : Integer
  award(OrderDirectionType, int)
}

class "Person" {
  username : String
  password : String
  company : Company
  email : String
  isObserver : boolean
  isTester : boolean
  phone : String
  role_label : String
  inRole(AuUserRole[]) : boolean
}

class "PersonProxy" {
  person_id : long
  username_at_auction_time : String
  role_at_auction_item : AuUserRole
}

"AuSession" <-> "Auction" : auction
"AuSession" --> "Person" : user
"Auction" --> "*" "AuctionMessage" : messages
"Auction" --> "Company" : use
"Auction" "*" --> "*" "CompanyProxy" : trading_companies/companies_that_have_seen_auction
"Auction" --> "*" "CounterpartyCreditLimit" : counterparty_credit_limits
"Auction" <|-- "DeAuction"
"AuctionMessage" --> "CompanyProxy" : from_company/to_company
"AuctionMessage" --> "PersonProxy" : from_user
"Auction" --> "*" "PersonProxy" : users_that_have_seen_auction
"CompanyProxy" <|-- "DeTradingCompany"
"CounterpartyCreditLimit" --> "CompanyProxy" : lender/borrower
"DeAuction" --> "Company" : use
"DeAuction" --> "DeAuctionSettings" : settings
"DeAuction" --> "*" "DeOrder" : revised_orders
"DeAuction" "*" --> "DeRound" : awarded_round/rounds
"DeAuction" --> "*" "DeTradingCompany" : de_trading_companies
"DeAuction" --> "PersonProxy" : use
"DeAuctionSettings" --> "DePriceRule" : price_rule
"DeAuctionTemplate" --> "DeAuctionSettings" : settings
"DeMatch" --> "DeOrder" : sell_order/buy_order
"DeOrder" --> "DeTradingCompany" : trading_company
"DeOrder" --> "PersonProxy" : user
"DeRound" --> "*" "DeMatch" : matches
"DeRound" --> "*" "DeOrder" : use
"DeRound" --> "*" "DeRoundTraderInfo" : trader_infos
"DeRound" --> "*" "DeTradingCompany" : use
"DeRoundTraderInfo" --> "DeBidConstraints" : constraints
"DeRoundTraderInfo" --> "DeOrder" : default_order/order
"DeRoundTraderInfo" --> "DeTradingCompany" : de_trading_company
"DeTradingCompany" --> "DeInitialLimits" : initial_limits
"Person" --> "Company" : company

@enduml