@startuml

class "Activity" {
  heading : String
  body : Map<String, String>
  entities : Map<String, AuEntity>
  timestamp : Date
}

class "AuSession" {
  session_id : String
  created : Date
  browser_name : String
  browser_version : String
  browser_os : String
  socket_state_label : String
  socket_last_closed : Date
  auction : Auction
  user : Person
  page_label : String
  last_ping : Date
  termination_time : Date
  termination_reason_label : String
  hasConnectionProblem() : boolean
  inRole(AuUserRole[]) : boolean
  login(Person)
  ping()
  set_page(PageName, Auction)
  terminate(SessionTerminationReason)
  termination_reason() : SessionTerminationReason
}

abstract class "Auction" {
  auction_name : String
  closed : boolean
  hidden : boolean
  notice : String
  starting_time : Date
  auction_has_started : boolean
  common_state_text : String
  auctioneer_state_text : String
  trading_companies : List<CompanyProxy>
  messages : List<AuctionMessage>
  users_that_have_seen_auction : Set<PersonProxy>
  companies_that_have_seen_auction : Set<CompanyProxy>
  counterparty_credit_limits : List<CounterpartyCreditLimit>
  add_trader_that_has_seen_auction(AuSession)
  get_counterparty_credit_limit(CompanyProxy, CompanyProxy) : CounterpartyCreditLimit
  has_trader(Company) : boolean
  set_counterparty_credit_limit(CompanyProxy, CompanyProxy, Double)
  show_auction(AuSession) : boolean
  starting_time_text() : String
}

class "CommandJson" {
  command_json : String
  timestamp : Date
}

class "Company" {
  longname : String
  shortname : String
}

class "DeAuction" {
  settings : DeAuctionSettings
  auctioneer_state_str : String
  common_state_str : String
  autopilot_label : String
  price_decimal_places : int
  awarded_round : DeRound
  revised_orders : List<DeOrder>
  rounds : List<DeRound>
  de_trading_companies : List<DeTradingCompany>
  announce_time() : Date
  auction_has_started() : boolean
  create_manual_order(DeRound, DeTradingCompany, PersonProxy, OrderDirectionType, int) : DeOrder
  create_trader(Company) : DeTradingCompany
  firstround() : DeRound
  last_round_has_started() : boolean
  lastround() : DeRound
  remove_trader(Company)
  set_credit()
  starting_price_announced() : boolean
  starting_time_text() : String
}

class "DeAuctionTemplate" {
  template_name : String
  settings : DeAuctionSettings
}

class "Person" {
  username : String
  password : String
  company : Company
  email : String
  isObserver : boolean
  isTester : boolean
  phone : String
  role_label : String
  inRole(AuUserRole[]) : boolean
}

"AuSession" <-> "Auction" : auction
"AuSession" --> "Person" : user
"Auction" --> "Company" : use
"Auction" <|-- "DeAuction"
"DeAuction" --> "Company" : use
"Person" --> "Company" : company

@enduml