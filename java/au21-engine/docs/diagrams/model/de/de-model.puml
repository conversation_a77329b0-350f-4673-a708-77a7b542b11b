@startuml

class "DeAuction" {
  settings : DeAuctionSettings
  auctioneer_state_str : String
  common_state_str : String
  autopilot_label : String
  price_decimal_places : int
  awarded_round : Round
  revised_orders : List<Order>
  rounds : List<Round>
  traders : List<Trader>
  add_trader(Company) : Trader
  announce_time() : Date
  auction_has_started() : boolean
  create_manual_order(Round, Trader, Person, OrderVolumeType, int) : Order
  firstround() : Round
  last_round_has_started() : boolean
  lastround() : Round
  remove_trader(Company)
  starting_price_announced() : boolean
  starting_time_text() : String
}

class "DeBidConstraints" {
  max_buy_volume : int
  min_buy_volume : int
  min_sell_volume : int
  max_sell_volume : int
}

class "Match" {
  sell_order : Order
  buy_order : Order
  buyer_shortname : String
  seller_shortname : String
  credit_volume_limit : int
  credit : Double
  credit_str : String
  buy_limit : int
  sell_limit : int
  capacity : int
  match : int
  value : double
  value_str : String
}

class "Order" {
  user : Person
  trader : Trader
  username : String
  timestamp : Date
  price : Double
  round_number : int
  submission_type_label : String
  submission_type : OrderSubmissionType
  volume_type_label : String
  volume_type : OrderVolumeType
  volume : int
  prev_order : Order
  value : double
  value_str : String
  buyVol() : int
  sellVol() : int
}

class "Round" {
  number : int
  price : Double
  has_reversed : boolean
  price_direction_label : String
  open_time : Date
  closed_time : Date
  time_started : Date
  trader_infos : List<RoundTraderInfo>
  matches : List<Match>
  max_potential_flow : int
  all_orders_are_non_default() : boolean
  buy_orders() : List<Order>
  buyers() : List<Trader>
  close()
  getMatch(Trader) : Match
  match_vol(Trader) : int
  match_vol() : int
  open()
  round_open_seconds() : int
  sell_orders() : List<Order>
  sellers() : List<Trader>
  started()
}

class "RoundTraderInfo" {
  default_order : Order
  current_matched_vol : int
  fully_opposed_match_vol : int
  trader : Trader
  constraints : DeBidConstraints
  order : Order
  bid_while_closed : boolean
  has_non_default_bid() : boolean
  has_non_zero_bid() : boolean
}

class "Trader" {
  buyer_credit_limit : double
  seller_quantity_limit : int
  company : Company
  buyer_credit_limits : List<BuyerCreditLimit>
  shortname_at_auction_time : String
  awarded_volume : int
  awarded_volume_type : OrderVolumeType
  blinded : boolean
  rank : Integer
  award(OrderVolumeType, int)
  get_credit_limit(Trader) : BuyerCreditLimit
  get_credit_limit_volume(Trader, double) : Integer
  get_credit_limit_volume_str(Trader, double) : String
}

"DeAuction" --> "*" "Order" : revised_orders
"DeAuction" "*" --> "Round" : awarded_round/rounds
"DeAuction" --> "*" "Trader" : traders
"Match" --> "Order" : sell_order/buy_order
"Order" --> "Trader" : trader
"Round" --> "*" "Match" : matches
"Round" --> "*" "Order" : use
"Round" --> "*" "RoundTraderInfo" : trader_infos
"Round" --> "*" "Trader" : use
"RoundTraderInfo" --> "DeBidConstraints" : constraints
"RoundTraderInfo" --> "Order" : default_order/order
"RoundTraderInfo" --> "Trader" : trader

@enduml
