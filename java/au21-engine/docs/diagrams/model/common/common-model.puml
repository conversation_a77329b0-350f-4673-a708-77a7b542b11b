@startuml

class "Activity" {
  heading : String
  body : Map<String, String>
  entities : Map<String, AuEntity>
  timestamp : Date
}

class "AuSession" {
  session_id : String
  created : Date
  browser_name : String
  browser_version : String
  browser_os : String
  socket_state_label : String
  socket_last_closed : Date
  auction : Auction
  user : Person
  page_label : String
  last_ping : Date
  termination_time : Date
  termination_reason_label : String
  hasConnectionProblem() : boolean
  inRole(AuUserRole[]) : boolean
  login(Person)
  ping()
  set_page(PageName, Auction)
  terminate(SessionTerminationReason)
  termination_reason() : SessionTerminationReason
}

abstract class "Auction" {
  auction_name : String
  closed : boolean
  hidden : boolean
  notice : String
  starting_time : Date
  auction_has_started : boolean
  common_state_text : String
  auctioneer_state_text : String
  trading_companies : List<Company>
  messages : List<AuctionMessage>
  users_that_have_seen_auction : Set<Person>
  companies_that_have_seen_auction : Set<Company>
  add_user_that_has_seen_auction(AuSession)
  has_trader(Company) : boolean
  show_auction(AuSession) : boolean
  starting_time_text() : String
}

class "AuctionMessage" {
  message_type : AuMessageType
  message : String
  from_user : Person
  to_company : Company
  body : String
  message_type_label : String
  from_username : String
  from_company : Company
  from_company_shortname : String
  to_company_shortname : String
  from_label : String
  timestamp : Date
  timestamp_label : String
  messageType() : AuMessageType
}

class "Company" {
  longname : String
  shortname : String
  buyer_credit_limits : List<BuyerCreditLimit>
  get_credit_limit(Company) : BuyerCreditLimit
  get_credit_limit_volume(Company, double) : Integer
  set_credit_limit(Company, Double)
}

class "Person" {
  username : String
  password : String
  company : Company
  email : String
  isObserver : boolean
  isTester : boolean
  phone : String
  role_label : String
  inRole(AuUserRole[]) : boolean
}

"AuSession" <-> "Auction" : auction
"AuSession" --> "Person" : user
"Auction" --> "*" "AuctionMessage" : messages
"Auction" "*" --> "*" "Company" : trading_companies/companies_that_have_seen_auction
"AuctionMessage" --> "Company" : to_company/from_company
"AuctionMessage" --> "Person" : from_user
"Auction" --> "*" "Person" : users_that_have_seen_auction
"Person" --> "Company" : company

@enduml