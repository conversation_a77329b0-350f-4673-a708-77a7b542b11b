@startuml

enum "ActivityRule" {
  ABSOLUTE
  RATIO
}

enum "AuMessageType" {
  AUCTIONEER_BROADCAST
  AUCTIONEER_TO_TRADER
  TRADER_TO_AUCTIONEER
  SYSTEM_BROADCAST
  SYSTEM_TO_TRADER
  SYSTEM_TO_AUCTIONEER
}

enum "AuUserRole" {
  AUCTIONEER
  TRADER
}

enum "AuctionInstruction" {
  HIDE
  UNHIDE
  DELETE
}

enum "AutopilotMode" {
  DISENGAGED
  ENGAGED
}

enum "BrowserMessageIcon" {
  SUCCESS
  INFO
  WARNING
  AUCTIONEER_MESSAGE
  TRADER_MESSAGE
  SYSTEM_MESSAGE
  ORDER_CONFIRMATION
}

enum "BrowserMessageKind" {
  ALERT
  NOTIFICATION
}

enum "ClientSocketState" {
  OPENED
  CLOSED
}

enum "Crud" {
  CREATE
  READ
  UPDATE
  DELETE
  ADD
  REMOVE
  CLEAR
}

enum "DeAuctioneerInfoLevel" {
  NORMAL
  WARNING
  ERROR
}

enum "DeAuctioneerState" {
  STARTING_PRICE_NOT_SET
  STARTING_PRICE_SET
  STARTING_PRICE_ANNOUNCED
  ROUND_OPEN_ALL_ORDERS_NOT_IN
  ROUND_OPEN_ALL_ORDERS_IN
  ROUND_CLOSED_NOT_AWARDABLE
  ROUND_CLOSED_AWARDABLE
  AUCTION_CLOSED
  oneOf(IAuctionState[]) : boolean
}

enum "DeCommonState" {
  SETUP
  STARTING_PRICE_ANNOUNCED
  ROUND_OPEN
  ROUND_CLOSED
  AUCTION_CLOSED
}

enum "DeCreditSetMode" {
  MANUAL
  MINIMUM
}

enum "DeFlowControlType" {
  HEARTBEAT
  SET_STARTING_PRICE
  ANNOUNCE_STARTING_PRICE
  START_AUCTION
  CLOSE_ROUND
  REOPEN_ROUND
  NEXT_ROUND
  AWARD_AUCTION
}

enum "DeRoundOpenState" {
  GREEN
  ORANGE
  RED
}

enum "DeRoundState" {
  NOT_OPEN
  GREEN
  ORANGE
  RED
}

enum "DeTimeState" {
  BEFORE_ANNOUNCE_TIME
  BEFORE_START_TIME
  AUCTION_HAS_STARTED
}

enum "Operator" {
  label
  GT
  GE
  check(int, int) : boolean
  check(double, double) : boolean
}

enum "OrderDirectionType" {
  BUY
  SELL
  NONE
}

enum "OrderSubmissionType" {
  MANUAL
  DEFAULT
  MANDATORY
}

enum "PageName" {
  CREDITOR_AUCTIONEER_PAGE
  CREDITOR_TRADER_PAGE
  HOME_PAGE
  LOGIN_PAGE
  SESSION_PAGE
  USER_PAGE
  BH_AUCTIONEER_PAGE
  BH_SETUP_PAGE
  BH_TRADER_PAGE
  DE_AUCTIONEER_PAGE
  DE_SETUP_PAGE
  DE_TRADER_PAGE
  MR_AUCTIONEER_PAGE
  MR_SETUP_PAGE
  MR_TRADER_PAGE
  TE_AUCTIONEER_PAGE
  TE_SETUP_PAGE
  TE_TRADER_PAGE
  TO_AUCTIONEER_PAGE
  TO_SETUP_PAGE
  TO_TRADER_PAGE
}

enum "PriceDirection" {
  UP
  DOWN
}

enum "ResultType" {
  ALERT
  OBJECT
  ARRAY_ITEM
}

enum "SampleOrderMove" {
  INCREASE
  DECREASE
}

enum "SessionTerminationReason" {
  BROWSER_UNLOADED
  COMPANY_DELETED
  COMPANY_NAME_EDITED
  FORCED_OFF
  LOGIN_FROM_ANOTHER_BROWSER
  SERVER_REBOOT
  SERVER_SWEPT_STALE_SESSION
  SIGNED_OFF
  USER_EDITED
  USER_DELETED
}

enum "StopMode" {
  LT
  LE
  NONE
  check(int, int) : boolean
}

enum "Visibility" {
  ALL
  FIRST_ROUND
  ELIGIBILITY
}


@enduml