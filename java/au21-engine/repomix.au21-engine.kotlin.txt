This file is a merged representation of a subset of the codebase, containing specifically included files and files not matching ignore patterns, combined into a single document by Repomix.
The content has been processed where comments have been removed, content has been compressed (code blocks are separated by ⋮---- delimiter).

================================================================
Directory Structure
================================================================
main/
  kotlin/
    au21/
      engine/
        domain/
          common/
            commands/
              auction-row.kt
              auction-select.kt
              client-socket.kt
              company-delete.kt
              company-save.kt
              db-delete-auctions.kt
              db-init.kt
              errors-send.kt
              login.kt
              message-send.kt
              notice-save.kt
              page-set.kt
              session-terminate.kt
              user-delete.kt
              user-save.kt
            model/
              Activity.kt
              Auction.kt
              AuctionMessage.kt
              AuSession.kt
              Company.kt
              CounterpartyCreditLimit.kt
              enums.kt
              extensions.kt
              Person.kt
            services/
              queries.kt
            viewmodel/
              AuctionRowElement.kt
              CompanyElement.kt
              CounterpartyCreditElement.kt
              DateTimeValue.kt
              MessageElement.kt
              SessionUserValue.kt
              TimeValue.kt
              UserElement.kt
          de/
            commands/
              de-auction-award.kt
              de-auction-save.kt
              de-auction-tick.kt
              de-create-db.kt
              de-credit-set.kt
              de-flow-control.kt
              de-order-submit.kt
              de-round-history.kt
              de-template-delete.kt
              de-template-save.kt
              de-trader-limits.kt
              de-traders-add.kt
              de-traders-remove.kt
            exp/
              AlertResult.kt
              Result.kt
            model/
              DeAuction.kt
              DeAuctionSettings.kt
              DeAuctionTemplate.kt
              DeBidConstraints.kt
              DeInitialLimits.kt
              DeMatch.kt
              DeOrder.kt
              DePriceRule.kt
              DeRound.kt
              DeRoundTraderInfo.kt
              DeTradingCompany.kt
              enums.kt
              extensions.kt
              starting-time-labels.kt
              state-labels.kt
            services/
              constraints/
                constraint-calculator.kt
              defaultorders/
                DeDefaultOrder.kt
              matcher/
                de-potential.kt
                DeCapacityEdge.kt
                DeFlowResult.kt
                DeMatcher.kt
                RoundCounterpartyLimits.kt
              nextroundprice/
                de-price-calculator.kt
              rounds/
                de-round-creator.kt
              sampledb/
                de-sample-db-helper.kt
                TradingUserFixture.kt
              state/
                auctioneeer_info_level.kt
                control_state_viewmodel.kt
                DeControlValidator.kt
                DeMutator.kt
                test.kt
              de-queries.kt
            validations/
              de_order_validations.kt
            viewmodel/
              de-blotter.kt
              de-matrix.kt
              DeAuctioneerInfoValue.kt
              DeAuctioneerStatusValue.kt
              DeAuctionValue.kt
              DeAwardValue.kt
              DeCommonStatusValue.kt
              DeSettingsValue.kt
              DeTraderHistoryRowElement.kt
              DeTraderInfoValue.kt
        framework/
          client/
            client-command-types.kt
            ClientsManager.kt
            CommandBuffer.kt
            CommandsHelper.kt
            LiveClientStore.kt
            SocketHandler.kt
          commands/
            interfaces/
              IAuctionMessage.kt
              ISessionsTerminated.kt
            AuSessionAction.kt
            command-validators.kt
            controller.kt
            deserializer.kt
            dispatcher.kt
            EngineAction.kt
            EngineCommand.kt
            EngineCommandEnvelope.kt
            exceptions.kt.kt
            handler.kt
            HeartbeatAction.kt
          database/
            AuEntity.kt
            AuEntityManager.kt
            AuEntityManagerFactory.kt
            IAuctionState.kt
          features/
            AuFeatures.kt
          graphql/
            GraphqlApi.kt
          observability/
            GelfLoggingResource.kt
            metrics.kt
            tracing.kt
          utils/
            au-format-utils.kt
            compression.kt
            config.kt
            enum-utils.kt
            json-utils.kt
            logging.kt
            misc-utils.kt
            string-utils.kt
            table-formatter.kt
            TimeFormatter.kt
          enums.kt
        generators/
          mermaid/
            mermaid-writer.kt
          plantuml/
            plantuml-writer.kt
          typescript/
            exp/
              ts-generator-jte.kt
            ts-generator-2-gpt4o.kt
            TsGeneratorVersionInfo.kt
          GitHelper.kt
          helpers.kt
          template_helpers.kt
        Main.kt
    exp/
      otel/
        TracedResource.kt
        tracing.kt
      template/
        TemplateExp.kt
      opentelemetry-exp.kt
      TracedResource.kt
    optimizationExps/
      NetworkResourceExp1.kt
    org/
      acme/
        AcmeWebSocket.kt
    simulator/
      CalculateMaxFlow.kt
      Runner.kt
native-test/
  kotlin/
    org/
      acme/
        GreetingResourceIT.kt

================================================================
Files
================================================================

================
File: main/kotlin/au21/engine/domain/common/commands/auction-row.kt
================
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.model.AuctionInstruction
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.common.services.sessions_logged_in
import au21.engine.domain.common.services.sessions_logged_in_traders
import au21.engine.framework.PageName
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail_if
import au21.engine.framework.commands.fail_if_not_auctioneer
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.database.is_same_entity

class AuctionRowCommand(
    val auction_id: String,
    val instruction: AuctionInstruction
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        val session = db.session_non_terminated_or_alert(session_id)
        val a: Auction = db.auction_or_alert(auction_id)

        fail_if_not_auctioneer(session)

        when (instruction) {
            AuctionInstruction.HIDE -> fail_if(a.hidden, "Auction is already hidden")
            AuctionInstruction.UNHIDE -> fail_if(!a.hidden, "Auction is not hidden")
            AuctionInstruction.DELETE -> {
            }
        }

        return AuctionRowAction(this, db, session, a)
    }

}

class AuctionRowAction(
    override val command: AuctionRowCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val a: Auction
) : EngineAction {








    override fun mutate() {

        fun bounce(s: AuSession) {
            s.set_page(PageName.HOME_PAGE)

            db.save(s)
        }

        when (command.instruction) {
            AuctionInstruction.HIDE -> {
                a.hidden = true
                db.sessions_logged_in_traders()
                    .filter { is_same_entity(it.auction, a) }
                    .forEach { bounce(it) }
            }
            AuctionInstruction.UNHIDE -> {
                a.hidden = false
            }
            AuctionInstruction.DELETE -> {
                a.deleted = true
                db.sessions_logged_in()
                    .filter { is_same_entity(it.auction, a) }
                    .forEach { bounce(it) }
            }
        }
        db.save(a)
    }

}

================
File: main/kotlin/au21/engine/domain/common/commands/auction-select.kt
================
package au21.engine.domain.common.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.framework.PageName
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail_if
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.database.is_same_entity
import io.quarkus.logging.Log

class AuctionSelectCommand(
    val auction_id: String
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        val a: Auction = db.auction_or_alert(auction_id)

        fail_if(
            !a.show_auction(session),
            "Unable to display auction, because either it was deleted or you are not a trader in this auction."
        )

        return AuctionSelectAction(this, db, session, a)
    }

}


class AuctionSelectAction(
    override val command: AuctionSelectCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val a: Auction
) : EngineAction {

    override fun mutate() {

        if (is_same_entity(session.auction, a)) {
            Log.warn("already on auction, not resending")
            return
        }












        val page: PageName = when (a) {
            is DeAuction ->
                if (session.is_auctioneer())
                    PageName.DE_AUCTIONEER_PAGE
                else if (session.is_trader())
                    PageName.DE_TRADER_PAGE
                else throw Error("No page for role: " + session.user?.role)
            else -> throw Error("Auction design not handled: " + a::class.java.simpleName)
        }

        if (session.inRole(AuUserRole.TRADER)) {
            a.add_trader_that_has_seen_auction(session)
        }

        session.set_page(page, a)
        db.save(session)
        db.save(a)


















    }

}

================
File: main/kotlin/au21/engine/domain/common/commands/client-socket.kt
================
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuSession.ClientSocketState.CLOSED
import au21.engine.domain.common.model.AuSession.ClientSocketState.OPENED
import au21.engine.domain.common.services.session_by_sid
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail
import au21.engine.framework.commands.interfaces.ISessionsTerminated
import au21.engine.framework.commands.interfaces.SessionTermination
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.is_blank
import io.quarkus.logging.Log







class ClientSocketCommand(
    val sid: String,
    val state: AuSession.ClientSocketState,
    val browser_name: String? = null,
    val browser_version: String? = null,
    val browser_os: String? = null
) : EngineCommand() {


    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {





        if (sid.is_blank())
            fail("No session id.")

        if (sid == "0")
            fail("Session id cannot be zero")

        val existing_session: AuSession? = db.session_by_sid(sid)






        return ClientSocketAction(
            this, db, existing_session,
            sid,
            state,
            browser_name,
            browser_version,
            browser_os
        )
    }
}


class ClientSocketAction(
    override val command: ClientSocketCommand,
    override val db: AuEntityManager,
    override val session: AuSession?,
    val sid: String,
    val state: AuSession.ClientSocketState,
    val browser_name: String? = null,
    val browser_version: String? = null,
    val browser_os: String? = null
) : EngineAction, ISessionsTerminated {

    override val sessions_terminated = mutableListOf<SessionTermination>()










    var new_session: AuSession? = null

    override fun mutate() {

        when (state) {
            OPENED ->
                when (session) {
                    null ->



                        new_session = AuSession(
                            session_id = sid,
                            browser_name = browser_name,
                            browser_version = browser_version,
                            browser_os = browser_os
                        ).also {
                            db.save(it)
                            Log.info("session created: $sid")

                        }
                    else -> {
                        session.socket_state = OPENED

                    }
                }
            CLOSED -> {
                when (session) {
                    null -> Log.error("sid $sid closed, but has no AuSession!!")
                    else -> {
                        session.socket_state = CLOSED

                    }
                }
            }
        }


        new_session?.let { db.save(it) }
        session?.let { db.save(it) }









    }

}

================
File: main/kotlin/au21/engine/domain/common/commands/company-delete.kt
================
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.model.Company
import au21.engine.domain.common.model.Person
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.framework.commands.*
import au21.engine.framework.commands.interfaces.ISessionsTerminated
import au21.engine.framework.commands.interfaces.SessionTermination
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.database.is_same_entity

class CompanyDeleteCommand(
    val company_id: String
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)

        val company: Company? = db.byId(company_id)
        if (company == null) {
            fail("No company found with id: $company_id")
        } else {



            fail_if(db.findAll<Auction>().any { a: Auction ->
                a.companies_that_have_seen_auction.any { it.company_id == company.id}
            }, "Cannot remove a company that has bid in, or seen, any auction.")

            fail_if(
                db.findAll<Person>().any { is_same_entity(it.company, company) },
                "Cannot delete company: ${company.shortname} because it has traders."
            )





            return CompanyDeleteAction(this, db, session, company)
        }
    }
}


class CompanyDeleteAction(
    override val command: CompanyDeleteCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val company: Company
) : EngineAction, ISessionsTerminated {

    override val sessions_terminated = mutableListOf<SessionTermination>()

    override fun mutate() {








        db.delete(company)
    }

}

================
File: main/kotlin/au21/engine/domain/common/commands/company-save.kt
================
package au21.engine.domain.common.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.model.Company
import au21.engine.domain.common.model.Person
import au21.engine.domain.common.services.*
import au21.engine.framework.commands.*
import au21.engine.framework.commands.interfaces.ISessionsTerminated
import au21.engine.framework.commands.interfaces.SessionTermination
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.is_blank

class CompanySaveCommand(
    val company_id: String,
    val company_shortname: String,
    val company_longname: String
) : EngineCommand() {


    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        val is_create: Boolean = company_id.is_blank()

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)

        err_if(company_shortname.is_blank(), "Company short name cannot be blank.")
        err_if(company_longname.is_blank(), "Company long name cannot be blank.")

        val shortname_trimmed = company_shortname.trim()
        val longname_trimmed = company_shortname.trim()

        err_if(shortname_trimmed.length > 10, "Company short name cannot be longer than 10 characters.")
        err_if(longname_trimmed.length > 20, "Company long name cannot be longer than 20 characters.")

        err_if(shortname_trimmed.contains("mandory"), "Short name cannot be 'mandatory'")
        err_if(shortname_trimmed.contains("default"), "Short name cannot be 'default'")

        err_if(longname_trimmed.contains("mandory"), "Long name cannot be 'mandatory'")
        err_if(longname_trimmed.contains("default"), "Long name cannot be 'default'")

        fail_if_errors()

        if (is_create) {

            err_if(db.company_by_shortname(shortname_trimmed) != null, "Company short name taken.")
            err_if(db.company_by_longname(longname_trimmed) != null, "Company long name taken.")
            fail_if_errors()

            return CompanySaveAction(
                this, db, session,
                Company(
                    longname = longname_trimmed,
                    shortname = shortname_trimmed
                )
            )

        } else {



            val company: Company? = db.byId(company_id)

            if (company == null) {

                fail("No company found with id: $company_id")

            } else {

                fail_if(
                    db.open_auctions().any { a: Auction -> a.has_trader(company) },
                    "Company is a trader in an open auction, first remove them from that auction then delete."
                )

                db.company_by_shortname(company_shortname)?.let { c ->

                    err_if(c != company, "Another company has that short name.")
                }
                db.company_by_longname(company_longname)?.let { c ->

                    err_if(c != company, "Another company has that long name.")
                }
                fail_if_errors()

                company.longname = company_longname
                company.shortname = company_shortname

                return CompanySaveAction(this, db, session, company)
            }
        }

    }
}


class CompanySaveAction(
    override val command: CompanySaveCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val company: Company
) : EngineAction, ISessionsTerminated {

    override val sessions_terminated = mutableListOf<SessionTermination>()


    override fun mutate() {
        db.company_users(company).forEach { u: Person ->
            db.logged_in_session_for_user(u)?.let { s: AuSession ->
                terminate_session(db, s, AuSession.SessionTerminationReason.COMPANY_DELETED)
            }
        }

        db.save(company)
    }

}

================
File: main/kotlin/au21/engine/domain/common/commands/db-delete-auctions.kt
================
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Auction
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.database.AuEntityManager





class DbDeleteAuctionsCommand : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        return DbDeleteAuctionsAction(this, db, null)
    }
}

class DbDeleteAuctionsAction(
    override val command: DbDeleteAuctionsCommand,
    override val db: AuEntityManager,
    override val session: AuSession? = null,
) : EngineAction {

    override fun mutate() {
        db.deleteAllIncDeleted<Auction>()
    }
}

================
File: main/kotlin/au21/engine/domain/common/commands/db-init.kt
================
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.Person
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.database.AuEntity
import au21.engine.framework.database.AuEntityManager





class DbInitCommand : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        return DbInitAction(this, db, null)
    }
}

class DbInitAction(
    override val command: DbInitCommand,
    override val db: AuEntityManager,
    override val session: AuSession? = null,
) : EngineAction {

    override fun mutate() {
        db.deleteAllIncDeleted<AuEntity>()
        db.save(
            Person(
                username = "a1",
                password = "1",
                role = AuUserRole.AUCTIONEER
            )
        )
    }
}

================
File: main/kotlin/au21/engine/domain/common/commands/errors-send.kt
================
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.*
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_by_sid
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail_if
import au21.engine.framework.commands.interfaces.IAuctionMessage
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.is_blank







class ErrorsSendCommand(
    val auction_id: String,
    val trader_session_id: String,
    val error: String
) : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {




        val a: Auction = db.auction_or_alert(auction_id)

        fail_if(error.is_blank(), "Error is blank")

        val preamble: String = "Order entry error " + try {
            val trader_session: AuSession? = db.session_by_sid(trader_session_id)
            val trader_user: Person? = trader_session?.user
            val trader_company: Company? = trader_session?.user?.company

            if (trader_user != null && trader_company != null) {
                "from ${trader_user.username} (${trader_company.shortname})"
            } else {
                "from unknown trader"
            }
        } catch (t: Throwable) {
            t.printStackTrace()
            "from unknown trader"
        }

        val m = AuctionMessage(
            message_type_ = AuMessageType.SYSTEM_TO_AUCTIONEER,
            message_ = "$preamble: $error",
            from_user_ = null,
            to_company_ = null
        )
        return ErrorsSendAction(this, db, null, a, m)

    }
}


class ErrorsSendAction(
    override val command: ErrorsSendCommand,
    override val db: AuEntityManager,
    override val session: AuSession?,
    override val auction: Auction,
    override val message: AuctionMessage,
) : EngineAction, IAuctionMessage {
    override fun mutate() {
        auction.messages.add(message)
        db.save(auction)
    }
}

================
File: main/kotlin/au21/engine/domain/common/commands/login.kt
================
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Person
import au21.engine.domain.common.services.logged_in_session_for_user
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.common.services.user_by_username
import au21.engine.framework.commands.*
import au21.engine.framework.commands.interfaces.ISessionsTerminated
import au21.engine.framework.commands.interfaces.SessionTermination
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.jsonToPrettyFormat
import au21.engine.framework.utils.objToPrettyFormat
import au21.engine.framework.utils.to_json
import io.quarkus.logging.Log

class LoginCommand(
    val username: String,
    val password: String,
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): LoginAction {

        val session = db.session_non_terminated_or_alert(session_id)
        err_if_blank(::username)
        err_if_blank(::password)
        fail_if_errors()

        if(username == "admin")
            fail("admin not implemented")

        val u: Person = db.user_by_username(
            username,
            case_insensitive = true,
            include_deleted = false
        ) ?: fail("No active user found with username: $username")

        fail_if(u.deleted, "Account not active.")
        fail_if(u.password.uppercase() != u.password.uppercase(), "Password incorrect")

        return LoginAction(this, db, session, u)
    }

}

class LoginAction(
    override val command: LoginCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val user: Person
) : EngineAction, ISessionsTerminated {

    override val sessions_terminated = mutableListOf<SessionTermination>()

    override fun mutate() {




        db.logged_in_session_for_user(user)?.let { existing ->
            terminate_session(db, existing, AuSession.SessionTerminationReason.LOGIN_FROM_ANOTHER_BROWSER)
        }

        session.login(user)
        db.save(session)
        Log.info(objToPrettyFormat(session))
    }
}

================
File: main/kotlin/au21/engine/domain/common/commands/message-send.kt
================
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.*
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.common.services.user_by_username
import au21.engine.framework.commands.*
import au21.engine.framework.commands.interfaces.IAuctionMessage
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.is_blank

class MessageSendCommand(
    val auction_id: String,
    val message: String
) : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        val session = db.session_non_terminated_or_alert(session_id)
        val a:Auction = db.auction_or_alert(auction_id)
        fail_if(message.is_blank(), "Message is blank")

        val u: Person = db.user_by_username(session.user?.username)
            ?: fail("Session has no user.")

        when (u.role) {
            AuUserRole.AUCTIONEER -> {
            }
            AuUserRole.TRADER ->
                fail_if_not(
                    a.has_trader(u.company),
                    "You are not a trader in this auction"
                )

        }


        val m = when (u.role) {
            AuUserRole.AUCTIONEER ->
                AuctionMessage(
                    message_type_ = AuMessageType.AUCTIONEER_BROADCAST,
                    message_ = message,
                    from_user_ = PersonProxy(u),
                    to_company_ = null
                )
            AuUserRole.TRADER ->
                AuctionMessage(
                    message_type_ = AuMessageType.TRADER_TO_AUCTIONEER,
                    message_ = message,
                    from_user_ = PersonProxy(u),
                    to_company_ = null
                )

        }

        return MessageSendAction(this, db, session, a, m)
    }
}


class MessageSendAction(
    override val command: MessageSendCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    override val auction: Auction,
    override val message: AuctionMessage
) : EngineAction, IAuctionMessage {

    override fun mutate() {
        auction.messages.add(message)
        db.save(auction)
    }
}

================
File: main/kotlin/au21/engine/domain/common/commands/notice-save.kt
================
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail_if_not_auctioneer
import au21.engine.framework.database.AuEntityManager

class NoticeSaveCommand(
    val auction_id:String,
    val notice:String
) : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)

        val a: Auction = db.auction_or_alert(auction_id)

        return NoticeSaveAction(this, db, session, a, notice)
    }
}


class NoticeSaveAction(
    override val command: NoticeSaveCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val a:Auction,
    val notice:String
) : EngineAction {
    override fun mutate() {
        a.notice = notice
        db.save(a)
    }

}

================
File: main/kotlin/au21/engine/domain/common/commands/page-set.kt
================
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.framework.PageName
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail
import au21.engine.framework.commands.fail_if
import au21.engine.framework.database.AuEntityManager



class PageSetCommand(
    val page:PageName
) : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        val session = db.session_non_terminated_or_alert(session_id)
        fun throw_if_not_auctioneer(){
            if (!session.inRole(AuUserRole.AUCTIONEER))
                fail("Only auctioneers can access page: $page")
        }
        when(page){
            PageName.CREDITOR_AUCTIONEER_PAGE ->
                throw_if_not_auctioneer()
            PageName.CREDITOR_TRADER_PAGE -> {


            }
            PageName.HOME_PAGE -> {
                fail_if(!session.is_logged_in(), "Please login first.")
            }
            PageName.LOGIN_PAGE -> {}
            PageName.SESSION_PAGE ->
                throw_if_not_auctioneer()
            PageName.USER_PAGE ->
                throw_if_not_auctioneer()
            PageName.DE_AUCTIONEER_PAGE -> {
                throw_if_not_auctioneer()
            }
            PageName.DE_SETUP_PAGE ->
                throw_if_not_auctioneer()
            PageName.DE_TRADER_PAGE ->
                fail_if(!session.is_trader(), "Only traders can see the trader page")

            else -> fail("page not recognized: $page")
        }
        return PageSetAction(this, db, session, page)
    }
}


class PageSetAction(
    override val command: PageSetCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val page:PageName
) : EngineAction {
    override fun mutate() {
        session.set_page(page)
        db.save(session)
    }

}

================
File: main/kotlin/au21/engine/domain/common/commands/session-terminate.kt
================
package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail
import au21.engine.framework.commands.interfaces.ISessionsTerminated
import au21.engine.framework.commands.interfaces.SessionTermination
import au21.engine.framework.database.AuEntityManager


class SessionTerminateCommand(
    val reason: AuSession.SessionTerminationReason
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {


        val s = db.session_non_terminated_or_alert(session_id)


        if (s.termination_reason() != null) {
            fail("Session already terminated: $session_id, reason:${s.termination_reason()}")
        }





        return SessionTerminateAction(this, db, s, reason)
    }

}

class SessionTerminateAction(
    override val command: SessionTerminateCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val reason: AuSession.SessionTerminationReason
) : EngineAction, ISessionsTerminated {

    override val sessions_terminated = mutableListOf<SessionTermination>()

    override fun mutate() {
        terminate_session(db, session, reason)
    }

}

================
File: main/kotlin/au21/engine/domain/common/commands/user-delete.kt
================
package au21.engine.domain.common.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Person
import au21.engine.domain.common.services.logged_in_session_for_user
import au21.engine.domain.common.services.open_auctions
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.framework.commands.*
import au21.engine.framework.commands.interfaces.ISessionsTerminated
import au21.engine.framework.commands.interfaces.SessionTermination
import au21.engine.framework.database.AuEntityManager

class UserDeleteCommand(
    val user_id:String
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)

        val p: Person = db.byId(user_id, include_deleted = true) ?: fail("no user found with id: $user_id")

        fail_if(p.deleted, "User has already been deleted.")


        fail_if(db.open_auctions().any { it.users_that_have_seen_auction.any { pp -> pp.person_id == p.id } } ,
            "User is a trader in an open auction, first remove them from that auction then delete.")



        return UserDeleteAction(this, db, session, p)
    }
}



class UserDeleteAction(
    override val command: UserDeleteCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val person:Person
) : ISessionsTerminated, EngineAction {

    override val sessions_terminated = mutableListOf<SessionTermination>()

    override fun mutate() {
        person.deleted = true
        db.save(person)
        db.logged_in_session_for_user(person)?.let { s ->
            terminate_session(db, s, AuSession.SessionTerminationReason.USER_DELETED)
        }
    }

}

================
File: main/kotlin/au21/engine/domain/common/commands/user-save.kt
================
package au21.engine.domain.common.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.Company
import au21.engine.domain.common.model.Person
import au21.engine.domain.common.services.logged_in_session_for_user
import au21.engine.domain.common.services.open_auctions
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.common.services.user_by_username
import au21.engine.framework.commands.*
import au21.engine.framework.commands.interfaces.ISessionsTerminated
import au21.engine.framework.commands.interfaces.SessionTermination
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.database.is_same_entity
import au21.engine.framework.utils.is_blank

class UserSaveCommand(
    val company_id: String,
    val email: String,



    val password: String,
    val phone: String,
    val role: AuUserRole,
    val user_id: String,
    val username: String
) : EngineCommand() {


    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        val is_create: Boolean = user_id.is_blank()

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)


        err_if(username.is_blank(), "Username cannot be blank.")
        err_if(password.is_blank(), "Password cannot be blank.")

        if (username.lowercase() == "admin")
            fail("admin role not supported.")

        val username_trimmed = username.trim()
        val password_trimmed = password.trim()

        err_if(username_trimmed.length > 10, "Username cannot be greater than 10 characters.")
        err_if(password_trimmed.length > 20, "Password cannot be greater than 20 characters.")

        fail_if(username == "mandory", "Username cannot be 'mandatory'")
        fail_if(username == "default", "Username cannot be 'default'")

        val c: Company? = db.byId(company_id)

        if (role == AuUserRole.TRADER) {
            if (company_id.is_blank()) {
                err_if(true, "Traders must have a company.")
            } else if (c == null) {
                err_if(true, "Traders must have a company, and none found with id: $company_id.")
            }
        }
        fail_if_errors()


        if (is_create) {

            fail_if(db.user_by_username(username_trimmed) != null, "Username taken already.")

            return UserSaveAction(
                this, db, session, Person(
                    company = c,
                    email = email,
                    password = password_trimmed,
                    phone = phone,
                    role = role,
                    username = username_trimmed
                )
            )

        } else {

            val existing: Person? = db.byId(user_id)

            fail_if(db.open_auctions().any { it.users_that_have_seen_auction.any { p -> p.person_id == existing?.id } } ,
                "User is a trader in an open auction, first remove them from that auction then delete.")

            if (existing == null) {
                fail("No user found with id: $user_id")
            } else {
                db.user_by_username(username_trimmed)?.let { u ->

                    fail_if(
                        is_same_entity(u, existing),
                        "Another user has this username."
                    )
                }

                existing.also {
                    it.company = c
                    it.email = email
                    it.password = password_trimmed
                    it.phone = phone
                    it.role = role
                    it.username = username_trimmed
                }

                return UserSaveAction(this, db, session, existing)
            }

        }

    }
}


class UserSaveAction(
    override val command: UserSaveCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val person: Person
) : EngineAction, ISessionsTerminated {

    override val sessions_terminated = mutableListOf<SessionTermination>()

    override fun mutate() {
        db.save(person)
        db.logged_in_session_for_user(person)?.let { s ->
            terminate_session(db, s, AuSession.SessionTerminationReason.USER_DELETED)
        }
    }

}

================
File: main/kotlin/au21/engine/domain/common/model/Activity.kt
================
package au21.engine.domain.common.model

import au21.engine.framework.database.AuEntity
import org.joda.time.DateTime
import java.util.*
import javax.jdo.annotations.Index
import javax.persistence.Entity

@Entity
class Activity(
    var heading: String,
    var body: MutableMap<String, String> = mutableMapOf(),
    var entities: MutableMap<String, AuEntity> = mutableMapOf()
) : AuEntity() {

    @Index
    var timestamp: Date = DateTime().toDate()
        private set
}

================
File: main/kotlin/au21/engine/domain/common/model/Auction.kt
================
package au21.engine.domain.common.model

import au21.engine.framework.database.AuEntity
import java.util.*
import javax.jdo.annotations.Index
import javax.persistence.Entity


@Entity
abstract class Auction(
    var auction_name: String,
) : AuEntity() {

    @Index
    var closed: Boolean = false
        protected set

    @Index
    var hidden: Boolean = false

    var notice: String = ""

    var starting_time: Date? = null

    var auction_has_started: Boolean = false

    var common_state_text: String = ""
    var auctioneer_state_text: String = ""

    abstract fun starting_time_text(): String

    protected var trading_companies: MutableList<CompanyProxy> = mutableListOf()
        private set

    fun has_trader(c: Company?): Boolean =
        when (c) {
            null -> false
            else -> trading_companies.any { it.company_id == c.id }
        }

    fun show_auction(s: AuSession?): Boolean =
        when {
            s == null -> false
            s.user?.role == AuUserRole.AUCTIONEER -> true
            this.hidden -> false
            this.has_trader(s.user?.company) -> true
            else -> false
        }

    var messages: MutableList<AuctionMessage> = mutableListOf()
        private set

    var users_that_have_seen_auction: MutableSet<PersonProxy> = mutableSetOf()
        private set


    var companies_that_have_seen_auction: MutableSet<CompanyProxy> = mutableSetOf()
        private set

    fun add_trader_that_has_seen_auction(s: AuSession) {
        s.user?.let { u: Person ->
            users_that_have_seen_auction.add(PersonProxy(u))
            u.company?.let { c: Company ->
                trading_companies.find { it.company_id == c.id }
                    ?.let {
                        companies_that_have_seen_auction.add(it)
                    }
            }
        }
    }

    // Counterparty credit:
    var counterparty_credit_limits: MutableList<CounterpartyCreditLimit> = mutableListOf()
        private set

    fun get_counterparty_credit_limit(lender: CompanyProxy, borrower: CompanyProxy): CounterpartyCreditLimit? =
        counterparty_credit_limits.find {
            it.lender == lender && it.borrower == borrower
        }

    fun set_counterparty_credit_limit(lender: CompanyProxy, borrower: CompanyProxy, limit: Double?) {
        (get_counterparty_credit_limit(lender, borrower)
            ?: run {
                CounterpartyCreditLimit(lender, borrower).also {
                    counterparty_credit_limits.add(it)
                }
            }).set_credit_limit(limit)
    }

    //     var state : IAuctionState
//        get() = enumValueOf(state_label) // enumValueOrNull<TeAuctionState>(state_string)!!
//        set(value) {
//            state_label = value.toString()
//        }

    // Another way of doing enums in general
//    var state_label: String = DeState.AUCTION_INIT.toString()
//        private set
//
//    var state: DeState
//        get() = DeState.valueOf(state_label)
//        set(it) {
//            this.state_label = it.toString()
//        }


    // 2) state label (ie: state could be open_for_bidding, and label could be "open for 20:00:00" etc



}

================
File: main/kotlin/au21/engine/domain/common/model/AuctionMessage.kt
================
package au21.engine.domain.common.model

import au21.engine.domain.common.model.AuMessageType.*
import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.AuFormatter
import org.joda.time.DateTime
import java.util.*
import javax.persistence.Embeddable

@Embeddable
class AuctionMessage(
    message_type_: AuMessageType,
    message_: String,
    from_user_: PersonProxy? = null,
    from_company_:CompanyProxy? = null,
    to_company_: CompanyProxy? = null
) {

    var message_type: AuMessageType = message_type_
        private set

    var message: String = message_
        private set

    var from_user: PersonProxy? = from_user_
        private set

    var from_company: CompanyProxy? = from_company_
        private set

    var to_company: CompanyProxy? = to_company_
        private set

    init {

        fun check_from_auctioneer() {
            if (from_user == null || from_user!!.role_at_auction_item != AuUserRole.AUCTIONEER)
                throw AlertException("Message type $message_type can only be sent by auctioneers")
        }

        fun check_from_trader() {
            if (from_user == null || from_user!!.role_at_auction_item != AuUserRole.TRADER)
                throw AlertException("Message type $message_type can only be sent by a trader.")
        }

        fun check_to_trader() {
            if (to_company == null)
                throw AlertException("Message type $message_type can only be sent to traders.")
        }

        fun check_fromUser_null() {
            if (from_user != null)
                throw AlertException("Message type $message_type should have blank 'from'.")
        }

        fun check_toCompany_null() {
            if (from_user != null && to_company != null)
                throw AlertException("Message type $message_type should have blank 'to' company.")
        }

        when (message_type) {
            AUCTIONEER_BROADCAST -> {
                check_from_auctioneer()
                check_toCompany_null()
            }
            AUCTIONEER_TO_TRADER -> {
                check_from_auctioneer()
                check_to_trader()
            }
            TRADER_TO_AUCTIONEER -> {
                check_from_trader()
                check_toCompany_null()
            }
            SYSTEM_BROADCAST -> {
                check_fromUser_null()
                check_toCompany_null()
            }
            SYSTEM_TO_TRADER -> {
                check_fromUser_null()
                check_to_trader()
            }
            SYSTEM_TO_AUCTIONEER -> {
                check_fromUser_null()
                check_toCompany_null()
            }
        }
    }

    var body: String = message
        private set

    var message_type_label: String = message_type.toString()
        private set


    fun messageType() = valueOf(message_type_label)



    var from_label: String =
        when (messageType()) {
            AUCTIONEER_BROADCAST -> "Auctioneer"
            AUCTIONEER_TO_TRADER -> "Auctioneer"

            TRADER_TO_AUCTIONEER -> "${from_user!!.username_at_auction_time} (${from_company!!.shortname_at_auction_time})"
            SYSTEM_BROADCAST -> "System"
            SYSTEM_TO_TRADER -> "System"
            SYSTEM_TO_AUCTIONEER -> "System"
        }
        private set

    var to_label: String =
        when (messageType()) {
            AUCTIONEER_BROADCAST -> "all"
            AUCTIONEER_TO_TRADER -> to_company!!.shortname_at_auction_time
            TRADER_TO_AUCTIONEER -> "auctioneer"
            SYSTEM_BROADCAST -> "all"
            SYSTEM_TO_TRADER -> to_company!!.shortname_at_auction_time
            SYSTEM_TO_AUCTIONEER -> "auctioneer"
        }
        private set

    var timestamp: Date = DateTime().toDate()
        private set
    var timestamp_label: String = AuFormatter.date_time_format(timestamp)
        private set




}

================
File: main/kotlin/au21/engine/domain/common/model/AuSession.kt
================
package au21.engine.domain.common.model

import au21.engine.domain.common.model.AuSession.ClientSocketState.CLOSED
import au21.engine.domain.common.model.AuSession.ClientSocketState.OPENED
import au21.engine.framework.PageName
import au21.engine.framework.database.AuEntity
import au21.engine.framework.utils.toEnumOrError
import org.joda.time.DateTime
import java.util.*
import javax.persistence.Entity

@Entity
class AuSession(
    var session_id: String,
    var created: Date = DateTime().toDate(),
    var browser_name: String? = null,
    var browser_version: String? = null,
    var browser_os: String? = null
) : AuEntity() {

    enum class ClientSocketState {
        OPENED, CLOSED
    }

    enum class SessionTerminationReason {
        BROWSER_UNLOADED,
        COMPANY_DELETED,
        COMPANY_NAME_EDITED,
        FORCED_OFF,
        LOGIN_FROM_ANOTHER_BROWSER,
        SERVER_REBOOT,
        SERVER_SWEPT_STALE_SESSION,
        SIGNED_OFF,
        USER_EDITED,
        USER_DELETED,
    }





















    var socket_state_label: String = OPENED.toString()
        private set

    var socket_state: ClientSocketState
        get() = socket_state_label.let { ClientSocketState.valueOf(it) }
        set(state) {
            this.socket_state_label = state.toString()
            when (state) {
                OPENED -> this.socket_last_closed = null
                CLOSED -> if (this.socket_last_closed == null)
                    this.socket_last_closed = DateTime().toDate()
            }
        }

    var socket_last_closed: Date? = null
        private set

    fun hasConnectionProblem(): Boolean =
        this.socket_state == CLOSED && !this.isTerminated()

    var auction: Auction? = null
        private set
















    var user: Person? = null
        private set








    fun is_trader(): Boolean = user?.isTrader() ?: false
    fun is_auctioneer(): Boolean = user?.isAuctioneer() ?: false


    fun inRole(vararg roles: AuUserRole): Boolean = roles.any { it == user?.role }


    var page_label: String = PageName.LOGIN_PAGE.toString()
        private set

    var page: PageName
        get () = toEnumOrError<PageName>(page_label)

        set(value) {
            this.page_label = value.toString()
        }

    fun set_page(p: PageName, a: Auction? = null) {

        page = p
        auction = a
    }

    fun login(p: Person) {
        set_page(PageName.HOME_PAGE)
        user = p


    }













    fun is_logged_in(): Boolean = !isTerminated() && user != null









    var last_ping: Date = DateTime().toDate()
        private set

    var termination_time: Date? = null
        private set

    var termination_reason_label: String? = null
        private set

    fun terminate(tr: SessionTerminationReason) {
        auction = null
        termination_time = DateTime().toDate()
        termination_reason_label = tr.toString()

        socket_state = CLOSED
        socket_last_closed = DateTime().toDate()
    }


    fun termination_reason(): SessionTerminationReason? =
        termination_reason_label?.let { SessionTerminationReason.valueOf(it) }

    fun isTerminated(): Boolean = termination_reason() != null

    fun ping() {
        last_ping = DateTime().toDate()
    }


}

================
File: main/kotlin/au21/engine/domain/common/model/Company.kt
================
package au21.engine.domain.common.model

import au21.engine.framework.commands.AlertException
import au21.engine.framework.database.AuEntity
import javax.persistence.Embeddable
import javax.persistence.Entity




@Embeddable
abstract class CompanyProxy(c:Company){
    var company_id:Long = c.id
        private set
    var shortname_at_auction_time:String = c.shortname
    var longname_at_auction_time:String = c.longname
}


@Entity
class Company(
    var longname: String,
    var shortname: String
) : AuEntity() {

    init {

        if (shortname.trim() == "")
            throw AlertException("Company short name cannot be blank.")

        if (shortname.trim().length > 10) {
            throw AlertException("Company short name cannot be greater than 10 characters: $shortname")
        }
    }


}

================
File: main/kotlin/au21/engine/domain/common/model/CounterpartyCreditLimit.kt
================
package au21.engine.domain.common.model

import au21.engine.framework.utils.AuFormatter
import javax.persistence.Embeddable

@Embeddable
class CounterpartyCreditLimit(
    lender_: CompanyProxy,
    borrower_: CompanyProxy) {

    companion object {
        const val no_limit = "no limit"
        const val none = "none"
    }

    var lender: CompanyProxy = lender_
        private set

    var borrower: CompanyProxy = borrower_
        private set

    var credit_limit: Double? = null
        private set

    var credit_limit_str: String? = null
        private set

    fun set_credit_limit(limit: Double?) {
        this.credit_limit = limit
        this.credit_limit_str = when (limit) {
            null -> no_limit
            else -> AuFormatter.format_currency(limit)
        }
    }
}

================
File: main/kotlin/au21/engine/domain/common/model/enums.kt
================
package au21.engine.domain.common.model































inline fun <reified T> String.enumValueOfWithTrace():T
        where
        T : Enum<T> {
    try {
        return enumValueOf(this)
    }
    catch (e:Throwable){
        println("Unable to get ${T::class.simpleName} from $this")
        throw e
    }
}

enum class AutopilotMode {
    DISENGAGED,
    ENGAGED
}

enum class Crud {
    CREATE,
    READ,
    UPDATE,
    DELETE,
    ADD,
    REMOVE,
    CLEAR
}

enum class AuMessageType {
    AUCTIONEER_BROADCAST,
    AUCTIONEER_TO_TRADER,
    TRADER_TO_AUCTIONEER,
    SYSTEM_BROADCAST,
    SYSTEM_TO_TRADER,
    SYSTEM_TO_AUCTIONEER
}

enum class AuctionInstruction {
    HIDE, UNHIDE, DELETE
}

enum class AuUserRole {

    AUCTIONEER,
    TRADER
}

enum class Operator(var label: String) {
    GT("greater than"),
    GE("greater than or equal to");

    fun check(x: Double, limit: Double): Boolean =
        when (this) {
            GT -> x > limit
            GE -> x >= limit
        }

    fun check(x: Int, limit: Int): Boolean =
        when (this) {
            GT -> x > limit
            GE -> x >= limit
        }
}

enum class OrderType { BUY, SELL, NONE }

enum class OrderSubmissionType { MANUAL, DEFAULT, MANDATORY }

enum class PriceDirection { UP, DOWN }








enum class ActivityRule { ABSOLUTE, RATIO }


enum class StopMode {
    LT, LE, NONE;

    fun check(x: Int, limit: Int): Boolean =
        when (this) {
            StopMode.LT -> x < limit
            StopMode.LE -> x <= limit
            NONE -> false
        }
}

enum class Visibility { ALL, FIRST_ROUND, ELIGIBILITY }

================
File: main/kotlin/au21/engine/domain/common/model/extensions.kt
================
package au21.engine.domain.common.model

inline fun <reified T : Auction> AuSession.get_auction(): T = this as T






fun Company?.fullname(): String =
    when (this) {
        null -> ""
        else -> "$longname + ($shortname)"
    }


//fun AuSession?.has_username(username: String) =
//        this?.person?.username.equals(username) ?: false

/*
     USER extensions
 */

fun Person.label(): String =
    this.username + this.company?.let { c -> " (${c.shortname})" }

================
File: main/kotlin/au21/engine/domain/common/model/Person.kt
================
package au21.engine.domain.common.model


import au21.engine.framework.database.AuEntity
import au21.engine.framework.utils.toEnumOrError
import javax.persistence.Embeddable
import javax.persistence.Entity

@Embeddable
class PersonProxy(p: Person) {
    var person_id: Long = p.id
        private set
    var username_at_auction_time: String = p.username
    var role_at_auction_item:AuUserRole = p.role
}

@Entity
class Person(
    role: AuUserRole,
    var username: String,
    var password: String,
    var company: Company? = null,
    var email: String = "",
    var isObserver: Boolean = false,
    var isTester: Boolean = false,
    var phone: String = ""
) : AuEntity() {

    var role_label: String = role.toString()
        private set

    var role: AuUserRole // mustn't be null
        get() = toEnumOrError(role_label)
        set(r) {
            role_label = r.toString()
//            if(r == AuUserRole.AUCTIONEER && company == null)
//                throw AlertException("Traders cannot have a null company")
        }



    fun inRole(vararg roles: AuUserRole): Boolean =
        roles.contains(role)

    fun isTrader(): Boolean = inRole(AuUserRole.TRADER)
    fun isAuctioneer(): Boolean = inRole(AuUserRole.AUCTIONEER)


}

================
File: main/kotlin/au21/engine/domain/common/services/queries.kt
================
package au21.engine.domain.common.services

import au21.engine.domain.common.model.*
import au21.engine.framework.commands.AlertException
import au21.engine.framework.database.AuEntityManager
import io.quarkus.logging.Log






fun AuEntityManager.open_auctions(): List<Auction> =
    try {
        val query = "SELECT a FROM ${Auction::class.java.name} a WHERE a.deleted=false AND a.closed=false"
        em.createQuery(query, Auction::class.java).resultList
    } catch (e: Exception) {
        Log.error(e.message)
        listOf()
    }

inline fun <reified A : Auction> AuEntityManager.auction_or_alert(auction_id: String?): A =
    auction_id
        ?.let { this.byId(auction_id) }
        ?: throw  AlertException("no auction found with id: $auction_id")








private fun AuEntityManager.non_terminated_sessions(
    logged_in_only: Boolean,
    role: AuUserRole? = null
): List<AuSession> {

    val non_terminated_sessions: List<AuSession> =
        query("select s from AuSession s WHERE s.deleted = false AND s.termination_reason_label == null")

    val first_filter =
        if (logged_in_only)
            non_terminated_sessions.filter { it.user != null }
        else
            non_terminated_sessions

    val second_filter = role?.let {
        first_filter.filter { it.inRole(role) }
    } ?: first_filter

    return second_filter
}

























fun AuEntityManager.sessions_non_terminated(): List<AuSession> =
    non_terminated_sessions(logged_in_only = false)

fun AuEntityManager.sessions_logged_in(): List<AuSession> =
    non_terminated_sessions(logged_in_only = true)

fun AuEntityManager.sessions_logged_in_auctioneers(): List<AuSession> =
    non_terminated_sessions(logged_in_only = true, AuUserRole.AUCTIONEER)

fun AuEntityManager.sessions_logged_in_traders(): List<AuSession> =
    non_terminated_sessions(logged_in_only = true, AuUserRole.TRADER)

fun AuEntityManager.session_by_sid(sid: String?): AuSession? =
    findFirst {

        it.session_id == sid
    }

fun AuEntityManager.logged_in_session_for_user(u: Person?): AuSession? =
    u?.let {
        this.non_terminated_sessions(logged_in_only = true)
            .find { s: AuSession -> s.user == u }
    }

fun AuEntityManager.is_logged_in(p: Person?): Boolean =
    this.logged_in_session_for_user(p) != null


fun AuEntityManager.session_non_terminated_or_alert(session_id: String?): AuSession =
    when (val s: AuSession? = session_by_sid(session_id)) {
        null -> throw AlertException("no session found with id: $session_id")
        else ->
            if (s.isTerminated())
                throw AlertException("Session already terminated.")
            else
                s
    }

fun AuEntityManager.auctioneer_session_or_alert(session_id: String?): AuSession =
    session_non_terminated_or_alert(session_id)
        .takeIf { it.is_auctioneer() }
        ?: throw AlertException("Only auctioneers can perform this action.")






fun AuEntityManager.user_by_username(
    username: String?,
    case_insensitive: Boolean = true,
    include_deleted: Boolean = false
): Person? =
    username?.let {
        findFirst(include_deleted) {
            if (case_insensitive)
                it.username.equals(username, ignoreCase = true)
            else
                it.username == username
        }
    }


fun AuEntityManager.traders(): List<Person> =
    findAll<Person>().filter { it.isTrader() }

fun AuEntityManager.company_by_shortname(
    shortname: String?,
    case_insensitive: Boolean = true,
    include_deleted: Boolean = false
): Company? =
    shortname?.let {
        findFirst(include_deleted) {
            if (case_insensitive)
                it.shortname.equals(shortname, ignoreCase = true)
            else it.shortname == shortname
        }
    }

fun AuEntityManager.company_by_longname(
    longname: String?,
    case_insensitive: Boolean = true,
    include_deleted: Boolean = false
): Company? =
    longname?.let {
        findFirst(include_deleted) {
            if (case_insensitive)
                it.longname.equals(longname, ignoreCase = true)
            else it.longname == longname
        }
    }

fun AuEntityManager.company_users(c: Company): List<Person> =
    findAll<Person>().filter { it.company == c }


inline fun <reified T : Auction> AuEntityManager.find_auction_by_name(
    auction_name: String?,
    case_insensitive: Boolean = true,
    include_deleted: Boolean = false
): T? =
    auction_name?.let {
        findFirst(include_deleted) {
            if (case_insensitive)
                it.auction_name.equals(auction_name, ignoreCase = true)
            else
                it.auction_name == auction_name
        }
    }

================
File: main/kotlin/au21/engine/domain/common/viewmodel/AuctionRowElement.kt
================
package au21.engine.domain.common.viewmodel

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Auction
import au21.engine.framework.client.StoreElement

class AuctionRowTraders(val element: AuctionRowElement)

data class AuctionRowElement(
    override val id: String,
    val auction_design: String,
    val auction_id: String,
    val auction_name: String,
    val isClosed: Boolean,
    val common_state_text: String,
    val isHidden: Boolean,
    val starting_time_text: String,



) : StoreElement {


    companion object {
        fun create(a: Auction): AuctionRowElement =
            AuctionRowElement(
                id = a.id_str(),
                auction_design = a::class.java.simpleName,
                auction_id = a.id_str(),
                auction_name = a.auction_name,
                isClosed = a.closed,
                isHidden = a.hidden,
                starting_time_text = a.starting_time_text(),
                common_state_text = a.common_state_text,
            )










        fun elements_for_session(
            auctions: List<Auction>,
            s: AuSession?
        ): List<AuctionRowElement> =
            auctions
                .filter { a -> a.show_auction(s) }
                .map { create(it) }

    }
}

================
File: main/kotlin/au21/engine/domain/common/viewmodel/CompanyElement.kt
================
package au21.engine.domain.common.viewmodel


import au21.engine.domain.common.model.Company
import au21.engine.framework.client.StoreElement
import au21.engine.framework.database.AuEntityManager

data class CompanyElement(
    override val id: String,
    val company_id: String,
    val company_longname: String,
    val company_shortname: String,


) : StoreElement {


    companion object {

        fun create(c: Company) = CompanyElement(
            id = c.id_str(),
            company_id = c.id_str(),
            company_longname = c.longname,
            company_shortname = c.shortname
        )

        fun all(db: AuEntityManager): List<CompanyElement> =
            db.findAll<Company>().map { create(it) }

    }
}

================
File: main/kotlin/au21/engine/domain/common/viewmodel/CounterpartyCreditElement.kt
================
package au21.engine.domain.common.viewmodel

import au21.engine.domain.common.model.Company
import au21.engine.framework.client.StoreElement
import au21.engine.framework.database.is_same_entity

data class CounterpartyCreditElement(
    override val id: String,
    val seller_id: String,
    val seller_longname: String,
    val seller_shortname: String,
    val buyer_id: String,
    val buyer_longname: String,
    val buyer_shortname: String,

    val limit_str: String
) : StoreElement {






    companion object {

        fun create(

            seller: Company,
            buyer: Company
        ): CounterpartyCreditElement {



            return CounterpartyCreditElement(
                id = id_str(seller, buyer),
                seller_id = seller.id_str(),
                seller_longname = seller.longname,
                seller_shortname = seller.shortname,
                buyer_id = buyer.id_str(),
                buyer_longname = buyer.longname,
                buyer_shortname = buyer.shortname,

                limit_str = "TODO"
            )
        }

        fun id_str(creditor: Company, debtor: Company) =
            "SELLER.${creditor.id_str()}.BUYER.${debtor.id_str()}"




        fun all(companies: List<Company>): List<CounterpartyCreditElement> =
            companies.map { seller: Company ->

                companies.filterNot { is_same_entity(it, seller) }
                    .map { buyer: Company ->
                        create(seller, buyer)
                    }
            }.flatten()

    }

}

================
File: main/kotlin/au21/engine/domain/common/viewmodel/DateTimeValue.kt
================
package au21.engine.domain.common.viewmodel

import au21.engine.framework.client.StoreValue
import org.joda.time.DateTime
import java.util.*

data class DateTimeValue(
    val year: Int,
    val month: Int,
    val day_of_week: Int,
    val day_of_month: Int,
    val hour: Int,
    val minutes: Int,
    val seconds: Int,
) : StoreValue {

    fun toDate(): Date = DateTime(
        year,
        month + 1,
        day_of_month,
        hour,
        minutes,
        seconds
    ).toDate()

    companion object {
        fun create(dt: DateTime): DateTimeValue =
            DateTimeValue(
                year = dt.year,
                month = dt.monthOfYear - 1,
                day_of_week = dt.dayOfWeek,
                day_of_month = dt.dayOfMonth,
                hour = dt.hourOfDay,
                minutes = dt.minuteOfHour,
                seconds = dt.secondOfMinute
            )
    }
}

================
File: main/kotlin/au21/engine/domain/common/viewmodel/MessageElement.kt
================
package au21.engine.domain.common.viewmodel

import au21.engine.domain.common.model.*
import au21.engine.domain.common.model.AuMessageType.*
import au21.engine.framework.client.StoreElement
import au21.engine.framework.database.is_same_entity


data class MessageElement(
    override val id: String,
    val from: String,
    val message: String,
    val message_type: AuMessageType,
    val message_type_label: String,
    val timestamp: Long,
    val timestamp_label: String,
    val to: String
) : StoreElement {

    companion object {

        fun create(m: AuctionMessage) =
            MessageElement(
                id = "AuctionMessage." + m.timestamp.time,
                from = m.from_label,
                message = m.body,
                message_type = m.messageType(),
                message_type_label = m.messageType().toString(),
                timestamp = m.timestamp.time,
                timestamp_label = m.timestamp_label,
                to = m.to_label
            )







        fun is_for(auction: Auction, m: AuctionMessage, s: AuSession?): Boolean =
            s?.auction?.let { a ->
                when {
                    !is_same_entity(a, auction) -> false
                    s.user == null -> false
                    s.inRole(AuUserRole.AUCTIONEER) -> true
                    else ->

                        when (m.messageType()) {
                            SYSTEM_TO_AUCTIONEER ->
                                false
                            AUCTIONEER_BROADCAST ->
                                true
                            SYSTEM_BROADCAST ->
                                true
                            TRADER_TO_AUCTIONEER ->
                                s.user?.company?.let { it.id == m.from_company?.company_id}?:false
                            SYSTEM_TO_TRADER ->
                                s.user?.company?.let { it.id == m.to_company?.company_id}?:false
                            AUCTIONEER_TO_TRADER ->
                                s.user?.company?.let { it.id == m.to_company?.company_id}?:false
                        }
                }
            } ?: false

        fun recipient_sids_for_message(
            sessions: List<AuSession>,
            a: Auction,
            m: AuctionMessage
        ): List<String> = sessions
            .filter { s -> is_for(a, m, s) }
            .map { it.session_id }


















        fun message_elements_for_session(a: Auction, s: AuSession?): List<MessageElement> =
            a.messages
                .filter { m -> is_for(a, m, s) }
                .map { m -> create(m) }

    }
}

================
File: main/kotlin/au21/engine/domain/common/viewmodel/SessionUserValue.kt
================
package au21.engine.domain.common.viewmodel

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.framework.PageName
import au21.engine.framework.client.StoreValue


data class SessionUserValue(
    val company_id: String,
    val company_shortname: String,
    val company_longname: String,
    val current_auction_id: String,
    val current_page: PageName,
    val isAuctioneer: Boolean,
    val isOnline: Boolean,
    val role: AuUserRole?,
    val session_id: String,
    val socket_state: AuSession.ClientSocketState,
    val user_id: String,
    val username: String,
) : StoreValue {

    companion object {
        fun create(s: AuSession): SessionUserValue =
            SessionUserValue(
                company_id = s.user?.company?.id_str() ?: "",
                company_shortname = s.user?.company?.shortname ?: "",
                company_longname = s.user?.company?.longname ?: "",
                current_auction_id = s.auction?.id_str() ?: "",
                current_page = s.page,
                isAuctioneer = s.inRole(AuUserRole.AUCTIONEER),
                isOnline = !s.isTerminated(),
                role = s.user?.role,
                session_id = s.session_id,
                socket_state = s.socket_state,
                user_id = s.user?.id_str() ?: "",
                username = s.user?.username ?: ""
            )
    }
}

================
File: main/kotlin/au21/engine/domain/common/viewmodel/TimeValue.kt
================
package au21.engine.domain.common.viewmodel

import au21.engine.framework.client.StoreValue
import org.joda.time.DateTime

data class TimeValue(
    val city: String,
    val date_time: DateTimeValue
) : StoreValue {
    companion object {

        fun now(city: String): TimeValue =
            TimeValue(city, DateTimeValue.create(DateTime()))
    }
}

================
File: main/kotlin/au21/engine/domain/common/viewmodel/UserElement.kt
================
package au21.engine.domain.common.viewmodel

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.Person
import au21.engine.framework.client.StoreElement
import au21.engine.framework.database.AuEntityManager
import java.util.*


data class UserElement(
    override val id: String,


    val company_id: String,
    val company_longname: String,
    val company_shortname: String,

    val email: String,
    val isAuctioneer: Boolean,
    val isObserver: Boolean,
    val isTester: Boolean,
    val password: String,
    val phone: String,
    val role: AuUserRole,
    val username: String,
    val user_id: String,


    val isOnline: Boolean,
    val current_auction_id: String?,
    val termination_reason: AuSession.SessionTerminationReason?,
    val socket_state: AuSession.ClientSocketState?,
    val socket_state_last_closed: Date?,
    val has_connection_problem: Boolean

) : StoreElement {


    companion object {

        fun create(u: Person, online_session: AuSession?): UserElement {

            return UserElement(
                id = u.id_str(),


                company_id = u.company?.id_str() ?: "",
                company_longname = u.company?.longname ?: "",
                company_shortname = u.company?.shortname ?: "",

                email = u.email,
                isAuctioneer = u.isAuctioneer(),
                isObserver = u.isObserver,
                isTester = u.isTester,
                password = u.password,
                phone = u.phone,
                role = u.role,
                username = u.username,
                user_id = u.id_str(),


                isOnline = online_session?.let { !it.isTerminated() } ?: false,
                current_auction_id = online_session?.auction?.id_str(),
                termination_reason = online_session?.termination_reason(),
                socket_state = online_session?.socket_state,
                socket_state_last_closed = online_session?.socket_last_closed,
                has_connection_problem = online_session?.hasConnectionProblem() ?: false
            )

        }

        fun user_elements(db: AuEntityManager, logged_in_sessions: List<AuSession>) =
            db.findAll<Person>().map { u: Person ->
                create(u, logged_in_sessions.find { it.user == u })
            }
    }
}

================
File: main/kotlin/au21/engine/domain/de/commands/de-auction-award.kt
================
package au21.engine.domain.de.commands


import au21.engine.domain.common.model.*
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeRound
import au21.engine.domain.de.model.DeTradingCompany
import au21.engine.domain.de.model.format_round_price
import au21.engine.framework.commands.*
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.thousands

class DeAuctionAwardCommand(
    val auction_id: String,
    val round_number: String,

) : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)


        val de: DeAuction = db.auction_or_alert(auction_id)

        val deRound: DeRound = round_number.toIntOrNull()?.let { num ->
            de.rounds.find { it.number == num }
        } ?: fail("Unable to find round with number: $round_number")



























        fail_if_errors()

        return DeAuctionAwardAction(this, db, session, de, deRound)
    }
}


class DeAuctionAwardAction(
    override val command: DeAuctionAwardCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de: DeAuction,
    val deRound: DeRound,

) : EngineAction {

    val messages: MutableList<AuctionMessage> = mutableListOf()

    override fun mutate() {

        val award_price = de.format_round_price(AuUserRole.AUCTIONEER, deRound)

















        deRound.buyers().forEach { b: DeTradingCompany ->
            val award_vol = deRound.match_vol(b)
            b.award(OrderType.BUY, award_vol)
            if(de.companies_that_have_seen_auction.any{it.company_id == b.company_id}){
                AuctionMessage(
                    message_type_ = AuMessageType.SYSTEM_TO_TRADER,
                    message_ = "${b.shortname_at_auction_time} bought ${award_vol.thousands()} @ $award_price",
                    from_user_ = null,
                    to_company_ = b
                )

            }
        }

        deRound.sellers().forEach { s: DeTradingCompany ->
            val award_vol = deRound.match_vol(s)
            s.award(OrderType.SELL, award_vol)
            if(de.companies_that_have_seen_auction.contains(s)){
                AuctionMessage(
                    message_type_ = AuMessageType.SYSTEM_TO_TRADER,
                    message_ = "${s.shortname_at_auction_time} sold ${award_vol.thousands()} @ $award_price",
                    from_user_ = null,
                    to_company_ = s
                )
            }
        }

        db.save(de)
    }

}

================
File: main/kotlin/au21/engine/domain/de/commands/de-auction-save.kt
================
package au21.engine.domain.de.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeAuctionSettings
import au21.engine.domain.de.model.DePriceRule
import au21.engine.domain.de.services.matcher.DeMatcher
import au21.engine.framework.commands.*
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.is_blank
import org.joda.time.DateTime
import java.util.*

class DeAuctionSaveCommand(
    val auction_id: String,
    val auction_name: String,
    val use_counterparty_credits:String,







    val quantity_label: String,
    val quantity_minimum: String,
    val quantity_step: String,

    val price_change_initial: String,
    val price_change_post_reversal: String,
    val price_label: String,
    val price_decimal_places: String,

    val cost_multiplier: String,

    val excess_level_0_label: String,
    val excess_level_1_label: String,
    val excess_level_2_label: String,
    val excess_level_3_label: String,
    val excess_level_4_label: String,

    val excess_level_1_quantity: String,
    val excess_level_2_quantity: String,
    val excess_level_3_quantity: String,
    val excess_level_4_quantity: String,


    val starting_price_announcement_mins: String,

    val month_is_1_based: Boolean,
    val starting_year: String,
    val starting_month: String,
    val starting_day: String,
    val starting_hour: String,
    val starting_mins: String,

    val round_red_secs: String,
    val round_orange_secs: String,
    val round_open_min_seconds: String,
    val round_closed_min_secs: String

) : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)

        val is_create: Boolean = auction_id.is_blank()

        err_if_blank(::auction_name)
        err_if_blank(::quantity_label)
        err_if_blank(::price_label)
        err_if_blank(::use_counterparty_credits)

        val use_counterparty_credits_b:Boolean? =
            when(use_counterparty_credits){
                "true" -> true
                "false" -> false
                else -> null
            }

        err_if_null(use_counterparty_credits_b, "Use credit limits cannot be blank.")





        val quantity_minimum_i: Int? = err_unless_Int_GT_zero(::quantity_minimum)
        val quantity_step_i: Int? = err_unless_Int_GT_zero(::quantity_step)

        val price_decimal_places_i: Int? = err_unless_Int_GE_zero(::price_decimal_places)



        val cost_multiplier_d: Double? =
            if (cost_multiplier.is_blank())
                10000.0
            else
                err_unless_Double_GT_zero(::cost_multiplier)


        fun to_error(s: String) = "$s must be a number greater than zero."

        val price_change_initial_double: Double? =
            toDoubleOrError(::price_change_initial, ::is_GT_zero) { to_error("Initial price change") }
        val price_change_post_reversal_double: Double? =
            toDoubleOrError(::price_change_post_reversal, ::is_GT_zero) { to_error("Post reversal price change") }

        price_change_initial_double?.let { inital_price ->
            price_change_post_reversal_double?.let { post_reversal_price ->
                err_if(
                    post_reversal_price >= inital_price,
                    "Price change post reversal must be less than inital price."
                )
            }
        }

        err_if_blank(::excess_level_0_label, "Excess level 0 label")
        err_if_blank(::excess_level_1_label, "Excess level 1 label")
        err_if_blank(::excess_level_2_label, "Excess level 2 label")
        err_if_blank(::excess_level_3_label, "Excess level 3 label")
        err_if_blank(::excess_level_4_label, "Excess level 4 label")

        val excess_level_1_quantity_int: Int? =
            toIntOrError(::excess_level_1_quantity, ::is_GT_zero) { to_error("Excess level 1") }
        val excess_level_2_quantity_int: Int? =
            toIntOrError(::excess_level_2_quantity, ::is_GT_zero) { to_error("Excess level 2") }
        val excess_level_3_quantity_int: Int? =
            toIntOrError(::excess_level_3_quantity, ::is_GT_zero) { to_error("Excess level 3") }
        val excess_level_4_quantity_int: Int? =
            toIntOrError(::excess_level_4_quantity, ::is_GT_zero) { to_error("Excess level 4") }

        fail_if_errors()

        err_if(
            excess_level_4_quantity_int!! < excess_level_3_quantity_int!!,
            "Excess level 4 cannot be less than excess level 3"
        )
        err_if(
            excess_level_3_quantity_int < excess_level_2_quantity_int!!,
            "Excess level 3 cannot be less than excess level 2"
        )
        err_if(
            excess_level_2_quantity_int < excess_level_1_quantity_int!!,
            "Excess level 2 cannot be less than excess level 1"
        )

        fail_if_errors()









        val starting_price_announcement_mins_int: Int = starting_price_announcement_mins.toIntOrNull() ?: 0









        val starting_day_int: Int? = toIntOrError(::starting_day) { to_error("starting day") }
        val starting_hour_int: Int? = toIntOrError(::starting_hour) { to_error("starting hour") }
        val starting_mins_int: Int? = toIntOrError(::starting_mins) { to_error("starting minutes") }
        val starting_month_int_raw: Int? = toIntOrError(::starting_month) { to_error("starting month") }
        val starting_year_int: Int? = toIntOrError(::starting_year) { to_error("starting year") }

        val round_red_secs_int: Int? =
            toIntOrError(::round_red_secs, ::is_GT_zero) { to_error("Round red seconds") }

        val round_orange_secs_int: Int? =
            toIntOrError(::round_orange_secs, ::is_GT_zero) { to_error("Round orange seconds") }

        val round_open_min_seconds_int: Int? =
            toIntOrError(::round_open_min_seconds, ::is_GT_zero) { to_error("Round open minimum seconds") }

        val round_closed_min_secs_int: Int? =
            toIntOrError(::round_closed_min_secs, ::is_GT_zero) { to_error("Round closed seconds") }

        err_if(month_is_1_based && starting_month_int_raw == 0, "Starting month cannot be zero")

        fail_if_errors()

        val starting_month_int: Int = when (month_is_1_based) {
            true -> starting_month_int_raw!!
            false -> starting_month_int_raw!! + 1
        }

        val starting_date_time: Date = DateTime(
            starting_year_int!!,
            starting_month_int,
            starting_day_int!!,
            starting_hour_int!!,
            starting_mins_int!!
        ).toDate()




        fail_if_errors()

        val settings = DeAuctionSettings(
            use_counterparty_credits = use_counterparty_credits_b!!,

            round_open_min_secs = round_open_min_seconds_int!!,
            round_closed_min_secs = round_closed_min_secs_int!!,
            round_orange_secs = round_orange_secs_int!!,
            round_red_secs = round_red_secs_int!!,
            starting_price_announcement_mins = starting_price_announcement_mins_int,

            quantity_units = quantity_label,
            quantity_minimum = quantity_minimum_i!!,
            quantity_step = quantity_step_i!!,
            price_units = price_label,
            price_decimal_places = price_decimal_places_i!!,
            cost_multiplier = cost_multiplier_d!!,
            price_rule = DePriceRule(
                price_change_initial = price_change_initial_double!!,
                price_change_post_reversal = price_change_post_reversal_double!!,
                excess_level_1_quantity = excess_level_1_quantity_int,
                excess_level_2_quantity = excess_level_2_quantity_int,
                excess_level_3_quantity = excess_level_3_quantity_int,
                excess_level_4_quantity = excess_level_4_quantity_int,
                excess_level_0_label = excess_level_0_label,
                excess_level_1_label = excess_level_1_label,
                excess_level_2_label = excess_level_2_label,
                excess_level_3_label = excess_level_3_label,
                excess_level_4_label = excess_level_4_label
            ),
        )

        val de: DeAuction = when {

            is_create -> DeAuction(
                auction_name = auction_name,
                settings = settings
            )
            else -> db.auction_or_alert<DeAuction>(auction_id).also {








                it.settings = settings
            }
        }

        de.starting_time = starting_date_time

        return DeAuctionSaveAction(this, db, session, de, is_create)
    }

}


class DeAuctionSaveAction(
    override val command: DeAuctionSaveCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de: DeAuction,
    val is_create: Boolean
) : EngineAction {
    override fun mutate() {
        db.save(de)

        DeMatcher.calculate_and_set_matches(de, this)
        db.save(session)
    }


}

================
File: main/kotlin/au21/engine/domain/de/commands/de-auction-tick.kt
================
package au21.engine.domain.de.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.database.AuEntityManager

class DeAuctionsTickCommand : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)




        return DeAuctionsTickAction(this, db, session)
    }
}


class DeAuctionsTickAction(
    override val command: DeAuctionsTickCommand,
    override val db: AuEntityManager,
    override val session: AuSession
) : EngineAction {
    override fun mutate() {
        TODO("Not yet implemented")
    }

}

================
File: main/kotlin/au21/engine/domain/de/commands/de-create-db.kt
================
package au21.engine.domain.de.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.Person
import au21.engine.domain.common.services.find_auction_by_name
import au21.engine.domain.common.services.user_by_username
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.services.sampledb.TradingUserFixture
import au21.engine.domain.de.services.sampledb.create_sample_db_auction
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.database.AuEntityManager
import kotlin.random.Random


class DeCreateSampleDbCommand(
    val auction_count: Int = 1,
    val auctioneer_count: Int = 2,
    val close_last_round: Boolean = false,
    val round_count: Int = 30,
    val trader_count: Int = 20,
    val use_counterparty_credits: Boolean = false,
) : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        return DeCreateSampleDbAction(
            this,
            db,
            null,
            trader_count,
            round_count,
            auction_count,
            auctioneer_count,
            close_last_round,
            use_counterparty_credits
        )
    }
}

class DeCreateSampleDbAction(
    override val command: DeCreateSampleDbCommand,
    override val db: AuEntityManager,
    override val session: AuSession? = null,
    val trader_count: Int,
    val round_count: Int,
    val auction_count: Int,
    val auctioneer_count: Int,
    val close_last_round: Boolean,
    val use_counterparty_credits: Boolean,
) : EngineAction {

    override fun mutate() {

        (1..auctioneer_count).forEach { i ->
            db.user_by_username("a$i") ?: db.save(
                Person(
                    username = "a$i",
                    password = "1",
                    role = AuUserRole.AUCTIONEER
                )
            )
        }

        val trading_users: List<Person> = TradingUserFixture.create(
            db,
            trader_count,
            use_counterparty_credits
        )

        (1..auction_count).forEach { num ->
            Random.nextInt(10000).toString().let { count: String ->
                val auction_name = "Auction $num - $count"
                db.find_auction_by_name<DeAuction>(auction_name) ?: run {
                    val de = create_sample_db_auction(
                        auction_name = auction_name,
                        trading_users = trading_users,
                        round_count = round_count,
                        close_last_round = close_last_round,
                        use_counterparty_credits = use_counterparty_credits
                    )
                    db.save(de)
                }
            }

        }
    }
}

================
File: main/kotlin/au21/engine/domain/de/commands/de-credit-set.kt
================
package au21.engine.domain.de.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeTradingCompany
import au21.engine.framework.commands.*
import au21.engine.framework.database.AuEntityManager

class DeCreditSetCommand(
    val auction_id: String,
    val lender_id: String,
    val borrower_id: String,
    val credit_limit: String,
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        val session = db.session_non_terminated_or_alert(session_id)



        val credit_limit_absolute: Double = credit_limit.trim_commas_and_underscores().replace("$", "").toDoubleOrNull()
            ?: throw AlertException("Credit limit is not a valid number: $credit_limit")

        val de: DeAuction =
            db.byId<DeAuction>(auction_id).also {
                when (it) {
                    null -> err_if(true, "Cannot find auction without an id.")
                    else -> {
                        err_if(
                            it.de_trading_companies.none { t -> t.company_id == borrower_id.toLong() },
                            "borrower not in auction"
                        )
                        err_if(
                            it.de_trading_companies.none { t -> t.company_id == lender_id.toLong() },
                            "lender not in auction"
                        )
                    }
                }
            } ?: throw AlertException("Cannot find auction without an id: $auction_id")

        val lender = de.de_trading_companies.firstOrNull { it.company_id == lender_id.toLong() }
            ?: throw AlertException("lender not in auction")
        val borrower = de.de_trading_companies.firstOrNull { it.company_id == borrower_id.toLong() }
            ?: throw AlertException("borrower not in auction")

        val current_absolute_limit: Double? = de.get_counterparty_credit_limit(lender, borrower)?.credit_limit

        err_if(
            current_absolute_limit != null
                    && current_absolute_limit < credit_limit_absolute,
            "Cannot reduce ${borrower.shortname_at_auction_time}'s (buyer) credit limit with ${lender.shortname_at_auction_time} (seller) because the buyer has a bid in an open auction."
        )

        return DeCreditSetAction(
            command = this,
            db = db,
            session = session,
            de = de,
            lender = lender,
            borrower = borrower,
            credit_limit_absolute = credit_limit_absolute
        )
    }
}


class DeCreditSetAction(
    override val command: DeCreditSetCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de: DeAuction,
    val lender: DeTradingCompany,
    val borrower: DeTradingCompany,
    val credit_limit_absolute: Double,
) : EngineAction {
    override fun mutate() {
        de.set_counterparty_credit_limit(lender, borrower, credit_limit_absolute)
        db.save(de)
    }

}

================
File: main/kotlin/au21/engine/domain/de/commands/de-flow-control.kt
================
package au21.engine.domain.de.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeFlowControlType
import au21.engine.domain.de.model.DeFlowControlType.SET_STARTING_PRICE
import au21.engine.domain.de.services.state.DeControlValidator
import au21.engine.domain.de.services.state.DeControlValidator.MUTATE
import au21.engine.domain.de.services.state.DeMutator
import au21.engine.framework.commands.*
import au21.engine.framework.database.AuEntityManager

class DeFlowControlCommand(
    val auction_id: String,
    val control: DeFlowControlType,
    val starting_price: String?
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val s = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(s)

        val de: DeAuction = db.auction_or_alert(auction_id)


        val starting_price_double: Double? = starting_price?.toDoubleOrNull()






        return when (val result: String? = DeControlValidator.validate(de, control)) {
            null -> DeFlowControlAction(this, db, s, de, false, starting_price_double)
            MUTATE -> {
                fail_if(
                    control == SET_STARTING_PRICE && starting_price_double == null,
                    "Round price not valid: $starting_price"
                )




                DeFlowControlAction(this, db, s, de, true, starting_price_double)
            }
            else -> fail(result)
        }
    }
}


class DeFlowControlAction(
    override val command: DeFlowControlCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de: DeAuction,
    val is_mutatable: Boolean,
    var starting_price: Double?
) : EngineAction {
    override fun mutate() {

        if (is_mutatable) {

            DeMutator.mutate(de, command.control, starting_price)




            db.save(de)
        }
    }

}

================
File: main/kotlin/au21/engine/domain/de/commands/de-order-submit.kt
================
package au21.engine.domain.de.commands


import au21.engine.domain.common.model.*
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.*
import au21.engine.domain.de.services.matcher.DeMatcher
import au21.engine.domain.de.validations.validate_de_order_against_constraints_and_throw_if_fails
import au21.engine.framework.PageName
import au21.engine.framework.commands.*
import au21.engine.framework.commands.interfaces.IAuctionMessage
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.thousands

class DeOrderSubmitCommand(
    val auction_id: String,
    val company_id: String,
    val order_type: OrderType,
    val round: String,
    val quantity: String
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if(
            company_id != session.user?.company?.id_str(),
            "Company id does not match session id."
        )

        fail_if(session.page != PageName.DE_TRADER_PAGE, "You can only submit orders from the auction page.")

        val de: DeAuction = db.auction_or_alert(auction_id)


        val c: Company = db.byId(company_id) ?: fail("no company found")


        val user_proxy = de.users_that_have_seen_auction.find { it.person_id == session.user?.id }
            ?: fail("You are not a trader in this auction.")

        val t: DeTradingCompany = de.de_trading_companies.firstOrNull{it.company_id == company_id.toLong()}
            ?: fail("You are not a trader in this auction.")

        fail_if(
            de.common_state
                    != DeCommonState.ROUND_OPEN, "Auction not open for bidding."
        )

        val n: DeRound = de.lastround()
        val round_int: Int = err_unless_Int_GT_zero(::round)
            ?: fail("Round should be greater than zero")

        fail_if(round_int != n.number, "Order isn't for round ${n.number}")

        val rbi = n.get_rti(t)
            ?: fail("No round bidder info")

        val quantity_i: Int = err_unless_Int_GE_zero(::quantity)
            ?: fail("quantity must be a whole number")

        val order_type_correct: OrderType = if (quantity_i == 0) OrderType.NONE else order_type



        val constraints: DeBidConstraints = rbi.constraints


        val order_quantity_type_corrected: OrderType =
            when (quantity_i) {
                0 -> OrderType.NONE
                else -> order_type_correct
            }

        validate_de_order_against_constraints_and_throw_if_fails(
            constraints,
            order_type = order_quantity_type_corrected,
            order_quantity = quantity_i,
            quantity_units = de.settings.quantity_units,
        )

        val message = AuctionMessage(
            AuMessageType.SYSTEM_TO_TRADER,
            "${user_proxy.username_at_auction_time} submitted " + when (order_type) {
                OrderType.BUY -> "buy order for ${quantity_i.thousands()} ${de.settings.quantity_units}"
                OrderType.SELL -> "sell order for ${quantity_i.thousands()} ${de.settings.quantity_units}"
                OrderType.NONE -> "order for 0 ${de.settings.quantity_units}"
            },
            null,
            to_company_ = t,
        )

        return DeOrderSubmitAction(
            this,
            db,
            session,
            de,
            message,
            n,
            t,
            user_proxy,
            c,
            rbi,
            order_quantity_type_corrected,
            quantity_i
        )

    }

}


class DeOrderSubmitAction(
    override val command: DeOrderSubmitCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    override val auction: DeAuction,
    override val message: AuctionMessage,
    val n: DeRound,
    val trading_company: DeTradingCompany,
    val user_proxy: PersonProxy,
    val company: Company,
    val rbi: DeRoundTraderInfo,
    val order_type: OrderType,
    val quantity: Int
) : EngineAction, IAuctionMessage {

    lateinit var order: DeOrder

    override fun mutate() {

        order = auction.create_manual_order(
            r = n,
            t = trading_company,
            u = user_proxy,
            order_type = order_type,
            order_quantity = quantity
        )





        DeMatcher.calculate_and_set_matches(auction, this)



        if (n.all_orders_are_non_default()) {
            auction.setState(DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN)
        }

        auction.messages.add(message)
        db.save(auction)

    }

}

================
File: main/kotlin/au21/engine/domain/de/commands/de-round-history.kt
================
package au21.engine.domain.de.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeRound
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail
import au21.engine.framework.commands.fail_if_not_auctioneer
import au21.engine.framework.database.AuEntityManager




class DeRoundHistoryCommand(
    val auction_id:String,
    val round_number:String
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)

        val de: DeAuction = db.auction_or_alert(auction_id)

        val round_num:Int = round_number.toIntOrNull() ?: fail("round_number invalid: $round_number")
        val deRound: DeRound = de.rounds.find { it.number == round_num }
            ?: fail("no round found with number: $round_num")

        return DeRoundControllerAction(this, db, session, de, deRound)
    }
}


class DeRoundControllerAction(
    override val command: DeRoundHistoryCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de:DeAuction,
    val deRound:DeRound
) : EngineAction {

    override fun mutate() {

    }

}

================
File: main/kotlin/au21/engine/domain/de/commands/de-template-delete.kt
================
package au21.engine.domain.de.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail_if_not_auctioneer
import au21.engine.framework.database.AuEntityManager

class DeTemplateDeleteCommand(
    val template_id:String
): EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)




        return DeTemplateDeleteAction(this, db, session)
    }
}

class DeTemplateDeleteAction(
    override val command: DeTemplateDeleteCommand,
    override val db:AuEntityManager,
    override val session:AuSession
) : EngineAction {
    override fun mutate() {
        TODO("Not yet implemented")
    }

}

================
File: main/kotlin/au21/engine/domain/de/commands/de-template-save.kt
================
package au21.engine.domain.de.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail_if_not_auctioneer
import au21.engine.framework.database.AuEntityManager

class DeTemplateSaveCommand(
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)


        return DeTemplateSaveAction(this, db, session)
    }
}


class DeTemplateSaveAction(
    override val command: DeTemplateSaveCommand,
    override val db: AuEntityManager,
    override val session: AuSession
) : EngineAction {
    override fun mutate() {
        TODO("Not yet implemented")
    }

}

================
File: main/kotlin/au21/engine/domain/de/commands/de-trader-limits.kt
================
package au21.engine.domain.de.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeRoundTraderInfo
import au21.engine.domain.de.model.DeTradingCompany
import au21.engine.domain.de.model.get_rti
import au21.engine.framework.commands.*
import au21.engine.framework.database.AuEntityManager


class DeTraderLimitsCommand(
    val auction_id: String,
    val company_id: String,
    val selling_quantity_limit: String,


    val buying_cost_limit: String
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        val session = db.session_non_terminated_or_alert(session_id)
        val de: DeAuction =
            db.byId<DeAuction>(auction_id) ?: throw AlertException("Cannot find auction without an id: $auction_id")


        fail_if(de.auction_has_started, "Cannot change trader limits after auction has started")




        val company_id_long:Long = err_unless_Long_GT_zero(::company_id)
            ?: throw AlertException("Invalid company id: $company_id")

        val trader = de.de_trading_companies.firstOrNull { it.company_id == company_id_long }
            ?: throw AlertException("Trader not in auction")

        val rti:DeRoundTraderInfo = de.firstround().get_rti(trader)
            ?: throw AlertException("Round trader info not found for trader: ${trader.shortname_at_auction_time}")


        val selling_quantity_limit_i: Int = err_unless_Int_GE_zero(::selling_quantity_limit) ?: 0

        val buying_cost_limit_d: Double = err_unless_Double_GE_zero(::buying_cost_limit) ?: 0.0


        fail_if_errors()

        return DeSetTraderLimitsAction(
            command = this,
            db = db,
            session = session,
            de = de,
            trader = trader,
            rti = rti,
            selling_quantity_limit = selling_quantity_limit_i,


            buying_cost_limit = buying_cost_limit_d
        )
    }
}

class DeSetTraderLimitsAction(
    override val command: DeTraderLimitsCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de: DeAuction,
    val trader: DeTradingCompany,
    val rti: DeRoundTraderInfo,
    val selling_quantity_limit: Int,


    val buying_cost_limit: Double
) : EngineAction {
    override fun mutate() {
        trader.initial_limits.also {
            it.initial_selling_quantity_limit = selling_quantity_limit


            it.initial_buying_cost_limit = buying_cost_limit
        }
        de.set_credit()
        db.save(de)
    }

}

================
File: main/kotlin/au21/engine/domain/de/commands/de-traders-add.kt
================
package au21.engine.domain.de.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Company
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.is_before_end_of_first_round
import au21.engine.domain.de.services.matcher.DeMatcher
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail
import au21.engine.framework.commands.fail_if_not_auctioneer
import au21.engine.framework.database.AuEntityManager









class DeTradersAddCommand(
    val auction_id: String,
    val company_ids: List<String>
) : EngineCommand() {







    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)

        val de: DeAuction = db.auction_or_alert(auction_id)

        if (!de.is_before_end_of_first_round()) {
            fail("Traders can only be added before the end of round one")
        }


        val company_id_longs:List<Long> = company_ids.map { it.toLong() }
        val existing_company_ids: List<Long> = de.de_trading_companies.map { it.company_id }

        val companies_to_add: List<Company> = company_id_longs
            .filter { !existing_company_ids.contains(it) }
            .mapNotNull { db.byId<Company>(it) }

        return DeTradersAddAction(this, db, session, de, companies_to_add)
    }
}


class DeTradersAddAction(
    override val command: DeTradersAddCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de: DeAuction,
    val companies_to_add: List<Company>
) : EngineAction {
    override fun mutate() {

        companies_to_add.forEach { c ->
            de.create_trader(c)
        }

        DeMatcher.calculate_and_set_matches(de, this)
        db.save(de)
    }

}

================
File: main/kotlin/au21/engine/domain/de/commands/de-traders-remove.kt
================
package au21.engine.domain.de.commands


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Company
import au21.engine.domain.common.services.auction_or_alert
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.common.services.sessions_logged_in_traders
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.has_non_zero_bid
import au21.engine.domain.de.services.matcher.DeMatcher
import au21.engine.framework.PageName
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.fail
import au21.engine.framework.commands.fail_if_not_auctioneer
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.database.is_same_entity









class DeTradersRemoveCommand(
    val auction_id: String,
    val company_ids: List<String>
) : EngineCommand() {







    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {

        val session = db.session_non_terminated_or_alert(session_id)
        fail_if_not_auctioneer(session)

        val de: DeAuction = db.auction_or_alert(auction_id)

        val existing_company_ids: List<String> = de.de_trading_companies.map { it.company_id.toString() }

        val companies_to_remove: List<Company> = company_ids
            .filter { existing_company_ids.contains(it) }
            .mapNotNull { db.byId(it) }


        val companies_to_remove_that_have_bid: List<Company> = companies_to_remove
            .filter { c -> de.has_non_zero_bid(c) }

        if (companies_to_remove_that_have_bid.isNotEmpty()) {
            fail(
                "Unable to remove these bidders as they have already bid in this auction: "
                        + companies_to_remove_that_have_bid.joinToString(",") { it.shortname }
            )
        }

        val companies_that_have_seen_auction: List<Company> = companies_to_remove
            .filter {c ->
                de.companies_that_have_seen_auction
                    .any { it.company_id == c.id }
            }

        if (companies_that_have_seen_auction.isNotEmpty()) {
            fail(
                "Unable to remove these bidders as they have already seen this auction: "
                        + companies_that_have_seen_auction.joinToString(",") { it.shortname }
            )
        }

        return DeTradersRemoveAction(this, db, session, de, companies_to_remove)
    }
}


class DeTradersRemoveAction(
    override val command: DeTradersRemoveCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de: DeAuction,
    val companies_removed: List<Company>,

) : EngineAction {









    override fun mutate() {

        companies_removed.forEach { removed_company ->

            de.remove_trader(removed_company)





            db.sessions_logged_in_traders()
                .filter { is_same_entity(it.user?.company, removed_company) }
                .forEach { s: AuSession ->

                    s.set_page(PageName.HOME_PAGE)

                    db.save(s)
                }
        }








        DeMatcher.calculate_and_set_matches(de, this)

        db.save(de)
    }

}

================
File: main/kotlin/au21/engine/domain/de/exp/AlertResult.kt
================
package au21.engine.domain.de.exp

class AlertResult : Result {
    override val type = ResultType.ALERT
}

================
File: main/kotlin/au21/engine/domain/de/exp/Result.kt
================
package au21.engine.domain.de.exp

interface Result {
    val type: ResultType
}




enum class ResultType {
    ALERT, OBJECT, ARRAY_ITEM
}

class ResultsEnvelope(
    val results: List<Result> = listOf()
)

class UserResult() : Result {
    override val type = ResultType.OBJECT

    lateinit var username: String

    constructor(
        username: String
    ) : this() {
        this.username = username
    }

}

================
File: main/kotlin/au21/engine/domain/de/model/DeAuction.kt
================
package au21.engine.domain.de.model

import au21.engine.domain.common.model.*
import au21.engine.domain.de.services.constraints.calculate_first_round_constraints
import au21.engine.domain.de.services.defaultorders.DeOrderInfo
import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.AuFormatter
import au21.engine.framework.utils.TimeFormatter
import org.joda.time.DateTime
import java.util.*
import javax.persistence.Entity

@Entity
class DeAuction(
    auction_name: String,
    var settings: DeAuctionSettings
) : Auction(auction_name) {








    private var auctioneer_state_str: String = DeAuctioneerState.STARTING_PRICE_NOT_SET.toString()
    var auctioneer_state: DeAuctioneerState
        get() = DeAuctioneerState.valueOf(auctioneer_state_str)
        private set(s) {
            auctioneer_state_str = s.toString()
        }

    private var common_state_str: String = DeCommonState.SETUP.toString()
    var common_state: DeCommonState
        get() = common_state_str.enumValueOfWithTrace()
        private set(v) {
            common_state_str = v.toString()
        }

    override fun starting_time_text(): String =
        when (common_state) {
            DeCommonState.SETUP,
            DeCommonState.STARTING_PRICE_ANNOUNCED ->
                starting_time?.let { d: Date ->
                    val starting_time_text = "${AuFormatter.auction_row_date_formatter.format(d)} at " +
                            "${AuFormatter.auction_row_time_formatter.format(d)} "

                    val time_to =
                        TimeFormatter.formatDuration(d).let {

                            when (it) {
                                "now" -> "(starting now)"
                                else -> "($it from now)"
                            }
                        }

                    return "$starting_time_text $time_to"
                } ?: ""

            DeCommonState.ROUND_OPEN, DeCommonState.ROUND_CLOSED -> "Auction started"
            DeCommonState.AUCTION_CLOSED -> "Auction closed"
        }










    fun lastround(): DeRound = rounds.lastOrNull() ?: throw AlertException("Auction must have at least on round.")
    fun firstround(): DeRound = rounds.firstOrNull() ?: throw AlertException("Auction must have at least on round.")



    fun announce_time(): Date? = starting_time?.let { d: Date ->
        DateTime(d)
            .minusMinutes(settings.starting_price_announcement_mins)
            .toDate()
    }

    fun setState(state: DeAuctioneerState) {


        val n = lastround()
        closed = state == DeAuctioneerState.AUCTION_CLOSED
        auctioneer_state = state
        auctioneer_state_text = when (state) {
            DeAuctioneerState.STARTING_PRICE_NOT_SET -> "Starting price not set"
            DeAuctioneerState.STARTING_PRICE_SET -> "Starting price set"
            DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> "Starting price announced"
            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN ->
                "Round ${n.number} open, all orders not in." + if (price_has_overshot()) " Price has overshoot" else ""

            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN ->
                "Round ${n.number} open, all orders in." + if (price_has_overshot()) " Price has overshoot" else ""

            DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE ->
                "Round ${n.number} closed, auction not awardable." + if (price_has_overshot()) " Price has overshoot" else ""

            DeAuctioneerState.ROUND_CLOSED_AWARDABLE ->
                "Round ${n.number} closed, auction awardable."

            DeAuctioneerState.AUCTION_CLOSED -> "Auction closed"
        }

        common_state = when (state) {
            DeAuctioneerState.STARTING_PRICE_NOT_SET -> DeCommonState.SETUP
            DeAuctioneerState.STARTING_PRICE_SET -> DeCommonState.SETUP
            DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> DeCommonState.STARTING_PRICE_ANNOUNCED
            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> DeCommonState.ROUND_OPEN
            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> DeCommonState.ROUND_OPEN
            DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> DeCommonState.ROUND_CLOSED
            DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> DeCommonState.ROUND_CLOSED
            DeAuctioneerState.AUCTION_CLOSED -> DeCommonState.AUCTION_CLOSED
        }

        common_state_text = when (common_state) {
            DeCommonState.SETUP -> "Waiting for starting price"
            DeCommonState.STARTING_PRICE_ANNOUNCED -> "Starting price announced"
            DeCommonState.ROUND_OPEN -> "Round ${n.number} open for orders!"
            DeCommonState.ROUND_CLOSED -> "Round ${n.number} closed"
            DeCommonState.AUCTION_CLOSED -> "Auction closed"
        }
    }

    fun starting_price_announced(): Boolean = !auctioneer_state.oneOf(
        DeAuctioneerState.STARTING_PRICE_NOT_SET,
        DeAuctioneerState.STARTING_PRICE_SET
    )

    var autopilot_label: String = AutopilotMode.DISENGAGED.toString()
        private set
    var autopilot: AutopilotMode
        get() = AutopilotMode.valueOf(autopilot_label)
        set(value) {
            this.autopilot_label = value.toString()
        }



    fun auction_has_started(): Boolean =
        last_round_has_started() || this.rounds.size > 1


    fun last_round_has_started(): Boolean =
        this.auctioneer_state.oneOf(
            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN,
            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN,
            DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE,
            DeAuctioneerState.ROUND_CLOSED_AWARDABLE,
            DeAuctioneerState.AUCTION_CLOSED
        )

    fun create_trader(c: Company): DeTradingCompany {



        if (!is_before_end_of_first_round())
            throw AlertException("Cannot add traders after the first round is closed.")

        if (trading_companies.any { it.company_id == c.id })
            throw Error("Trader already exists with name: ${c.shortname}")

        val t = DeTradingCompany(c).also {
            trading_companies.add(it)
        }

        create_first_round_rti(t)

        return t
    }


    fun remove_trader(c: Company) {
        de_trading_companies.removeIf { it.company_id == c.id }
        rounds.forEach { r: DeRound ->
            r.trader_infos.removeIf { it.de_trading_company.company_id == c.id }
        }
        trading_companies.removeIf { it.company_id == c.id }
    }








    private fun create_first_round_rti(t: DeTradingCompany) {




        val n = lastround()

        if (n.number != 1)
            throw AlertException("Cannot create first round infos after the first round.")



        n.apply {
            get_rti(t)?.let {
                trader_infos.remove(it)
            }
        }



        val constraints = calculate_first_round_constraints(t,  settings, n)
        val default_order = DeOrderInfo.create_default_from_constraints(constraints)

        val rti = DeRoundTraderInfo(
            trading_company_ = t,
            constraints_ = constraints,
            default_order = DeOrder(
                round = n,
                trading_company_ = t,
                u = null,
                order_info = default_order,
                cost_multiplier = settings.cost_multiplier,
                prev_order = null,
            ),
            current_matched_vol = 0,
            fully_opposed_match_vol = 0
        )

        n.trader_infos.add(rti)
    }

    fun set_starting_price(price: Double) {
        if (lastround().number != 1)
            throw AlertException("Cannot create first round infos after the first round.")

        firstround().price = price


        de_trading_companies.forEach { create_first_round_rti(it) }
    }















    var price_decimal_places: Int = 3
    var awarded_round: DeRound? = null

    var revised_orders: MutableList<DeOrder> = mutableListOf()
        private set

    var rounds: MutableList<DeRound> = mutableListOf()
        private set


    @Suppress("UNCHECKED_CAST")
    var de_trading_companies: MutableList<DeTradingCompany> = mutableListOf()
        get() = trading_companies as MutableList<DeTradingCompany>
        private set












    fun set_rank(t: DeTradingCompany) {

        if (t.rank == null) {

            t.rank = 1 + de_trading_companies.filter { it.rank != null }.size
        }
    }


    fun create_manual_order(
        r: DeRound,
        t: DeTradingCompany,
        u: PersonProxy,
        order_type: OrderType,
        order_quantity: Int
    ): DeOrder {
        val rti = r.get_rti(t)
            ?: throw AlertException("expected rti for trader: ${t.shortname_at_auction_time}")



        set_rank(t)

        return DeOrder(
            round = r,
            trading_company_ = t,
            u = u,
            order_info = DeOrderInfo(
                OrderSubmissionType.MANUAL,
                order_type,
                order_quantity
            ),
            cost_multiplier = settings.cost_multiplier,
            prev_order = rti.order
        ).also {
            rti.order = it
        }
    }


    fun set_credit() {









    }

    init {
        rounds.add(DeRound(number = 1, price = null, has_reversed_ = false, direction = null))
        setState(DeAuctioneerState.STARTING_PRICE_NOT_SET)
    }

}

================
File: main/kotlin/au21/engine/domain/de/model/DeAuctionSettings.kt
================
package au21.engine.domain.de.model

import javax.persistence.Embeddable

@Embeddable
class DeAuctionSettings(

    var use_counterparty_credits: Boolean,



    var starting_price_announcement_mins: Int = 0,

    var round_open_min_secs: Int,
    var round_closed_min_secs: Int,
    var round_orange_secs: Int,
    var round_red_secs: Int,





    var cost_multiplier: Double = 10_000.0,


    var quantity_units: String = "MMlb",
    var quantity_minimum: Int = 1,
    var quantity_step: Int = 1,


    var price_units: String = "cpp",
    var price_decimal_places: Int = 3,


    var price_rule: DePriceRule
)

================
File: main/kotlin/au21/engine/domain/de/model/DeAuctionTemplate.kt
================
package au21.engine.domain.de.model

import au21.engine.framework.database.AuEntity
import javax.persistence.Entity

@Entity
class DeAuctionTemplate(
        var template_name: String,
        var settings: DeAuctionSettings
) : AuEntity()

================
File: main/kotlin/au21/engine/domain/de/model/DeBidConstraints.kt
================
package au21.engine.domain.de.model

import javax.persistence.Embeddable


@Embeddable
class DeBidConstraints(
    var max_buy_quantity: Int,
    var min_buy_quantity: Int,
    var min_sell_quantity: Int,
    var max_sell_quantity: Int
)

================
File: main/kotlin/au21/engine/domain/de/model/DeInitialLimits.kt
================
package au21.engine.domain.de.model

import au21.engine.framework.utils.AuFormatter
import au21.engine.framework.utils.thousands
import javax.persistence.Embeddable





@Embeddable
class DeInitialLimits {

    var initial_buying_cost_limit = 50_000_000.0
        set(value) {
            field = value
            initial_buying_cost_limit_str = AuFormatter.format_currency(field)
        }














    var initial_selling_quantity_limit = 50
        set(value) {
            field = value
            initial_selling_quantity_limit_str = field.thousands()
        }


    var initial_buying_cost_limit_str:String = ""
   // var initial_selling_cost_limit_str:String = ""
   // var initial_buying_quantity_limit_str:String = ""
    var initial_selling_quantity_limit_str:String = ""

}

================
File: main/kotlin/au21/engine/domain/de/model/DeMatch.kt
================
package au21.engine.domain.de.model

import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.AuFormatter
import javax.persistence.Embeddable


@Embeddable
class DeMatch(

    sell_order_: DeOrder,
    buy_order_: DeOrder,
    match_: Int,
    capacity_: Int,
    round_price_multiplier: Double,
) {

    init {

        if (sell_order_.trading_company.company_id == buy_order_.trading_company.company_id)
            throw AlertException(
                "Buyer and seller cannot be the same: " +
                        sell_order_.trading_company.shortname_at_auction_time
            )
    }

    var sell_order: DeOrder = sell_order_
        private set

    var buy_order: DeOrder = buy_order_
        private set

    var buyer_shortname = buy_order.trading_company.shortname_at_auction_time
        private set

    var seller_shortname = sell_order.trading_company.shortname_at_auction_time
        private set


    var selling_quantity_limit: Int =
        sell_order.trading_company.initial_limits.initial_selling_quantity_limit
        private set

    var credit:Double? = sell_order.trading_company.initial_limits.initial_buying_cost_limit
        private set

    var credit_str:String = sell_order.trading_company.initial_limits.initial_buying_cost_limit_str
        private set

    var buy_limit: Int = buy_order.quantity
        private set

    var sell_limit: Int = sell_order.quantity


    var capacity: Int = capacity_
        private set

    var match: Int = match_
        private set

    var value: Double = match * round_price_multiplier
        private set

    var value_str: String = AuFormatter.format_currency(value)
        private set
}

================
File: main/kotlin/au21/engine/domain/de/model/DeOrder.kt
================
package au21.engine.domain.de.model

import au21.engine.domain.common.model.OrderSubmissionType
import au21.engine.domain.common.model.OrderType
import au21.engine.domain.common.model.PersonProxy
import au21.engine.domain.de.services.defaultorders.DeOrderInfo
import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.AuFormatter
import org.joda.time.DateTime
import java.util.*
import javax.persistence.Embeddable

@Embeddable
class DeOrder(
    round: DeRound,
    trading_company_: DeTradingCompany,
    u: PersonProxy?,
    order_info: DeOrderInfo,
    cost_multiplier: Double,
    prev_order: DeOrder?
) {

    init {

        order_info.apply {
            if (order_type != OrderType.NONE && quantity == 0) {
                throw AlertException("Order type cannot be $order_type if quantity is zero")
            }
            if (order_type == OrderType.NONE && quantity != 0) {
                throw AlertException("Order type cannot be NONE if quantity is not zero")
            }
        }
        if (order_info.quantity < 0) {
            throw AlertException("Order quantity cannot be negative")
        }
    }

    var user: PersonProxy? = u
        private set

    var trading_company: DeTradingCompany = trading_company_
        private set

    var timestamp: Date = DateTime().toDate()
        private set

    var price: Double = round.price ?: 0.0
        private set

    var round_number: Int = round.number
        private set





    var submission_type_label: String = order_info.submission_type.toString()

    var submission_type: OrderSubmissionType = order_info.submission_type
        get() = OrderSubmissionType.valueOf(submission_type_label)
        private set

    var order_type_label: String = order_info.order_type.toString()
        private set

    var type: OrderType = order_info.order_type
        get() = OrderType.valueOf(order_type_label)
        private set

    var quantity: Int = order_info.quantity
        private set

    fun sellVol() = if (type == OrderType.SELL) quantity else 0
    fun buyVol() = if (type == OrderType.BUY) quantity else 0

    var prev_order: DeOrder? = prev_order
        private set

    var cost:Double = price * cost_multiplier * quantity
        private set

    var cost_str: String = AuFormatter.format_currency(cost)
        private set
}

================
File: main/kotlin/au21/engine/domain/de/model/DePriceRule.kt
================
package au21.engine.domain.de.model

import au21.engine.domain.common.model.AuUserRole
import javax.persistence.Embeddable


@Embeddable
class DePriceRule(
    var price_change_initial: Double = 0.5,
    var price_change_post_reversal: Double = 0.125,
    var excess_level_1_quantity: Int = 10,
    var excess_level_2_quantity: Int = 20,
    var excess_level_3_quantity: Int = 30,
    var excess_level_4_quantity: Int = 40,
    var excess_level_0_label: String = "1+",
    var excess_level_1_label: String = "2+",
    var excess_level_2_label: String = "3+",
    var excess_level_3_label: String = "4+",
    var excess_level_4_label: String = "5+",
) {



    fun get_excess_level(excess: Int, role: AuUserRole): String =
        when {
            excess > excess_level_4_quantity -> excess_level_4_label
            excess > excess_level_3_quantity -> excess_level_3_label
            excess > excess_level_2_quantity -> excess_level_2_label
            excess > excess_level_1_quantity -> excess_level_1_label
            excess > 0 -> excess_level_0_label
            excess == 0 ->
                if (role == AuUserRole.AUCTIONEER) {
                    "0"
                } else {
                    excess_level_0_label
                }
            else ->
                if (role == AuUserRole.AUCTIONEER) {
                    "-"
                } else {
                    excess_level_0_label
                }
        }


















}

================
File: main/kotlin/au21/engine/domain/de/model/DeRound.kt
================
package au21.engine.domain.de.model

import au21.engine.domain.common.model.OrderType
import au21.engine.domain.common.model.PriceDirection
import au21.engine.framework.commands.AlertException
import org.joda.time.DateTime
import java.util.*
import javax.persistence.Embeddable


@Embeddable
class DeRound(
    var number: Int,
    var price: Double?,
    has_reversed_: Boolean,
    direction: PriceDirection?
) {

    init {
        if (number == 0)
            throw AlertException("Rounds must always have a number > 0")
        if (number > 1 && price == null)
            throw AlertException("After round 1, rounds must be created with a non-null price.")
    }

    var has_reversed: Boolean = has_reversed_
        private set

    var price_direction_label: String? = direction?.toString()
        private set
    var price_direction: PriceDirection?
        get() = price_direction_label?.let { PriceDirection.valueOf(it) }
        set(varue) {
            this.price_direction_label = varue.toString()
        }

    var open_time: Date? = null
        private set

    fun open() {
        if (this.open_time != null)
            throw AlertException("open_time should be null")
        open_time = DateTime().toDate()
    }

    var closed_time: Date? = null
        private set

    fun close() {
        if (this.closed_time != null)
            throw AlertException("closed time should be null")
        closed_time = DateTime().toDate()
    }

    var time_started: Date? = null
        private set

    fun started() {
        time_started = DateTime().toDate()
    }

    var trader_infos: MutableList<DeRoundTraderInfo> = mutableListOf()
        private set





    fun buy_orders(): List<DeOrder> =
        trader_infos
            .map { it.order }
            .filter { it.type == OrderType.BUY }

    fun sell_orders(): List<DeOrder> =
        trader_infos
            .map { it.order }
            .filter { it.type == OrderType.SELL }

    fun buyers(): List<DeTradingCompany> = buy_orders().map { it.trading_company }
    fun sellers(): List<DeTradingCompany> = sell_orders().map { it.trading_company }

    var matches: MutableList<DeMatch> = mutableListOf()
        private set

    fun setMatches(m: List<DeMatch>) {
        matches.clear()
        m.forEach { matches.add(it) }
    }

    fun getMatch(t: DeTradingCompany): DeMatch? =
        matches.find {
            it.buyer_shortname == t.shortname_at_auction_time ||
                    it.seller_shortname == t.shortname_at_auction_time
        }

    fun match_vol(): Int = matches.sumOf { it.match }
    fun match_vol(t: DeTradingCompany): Int = matches

        .filter {
            it.buy_order.trading_company.company_id == t.company_id
                    || it.sell_order.trading_company.company_id == t.company_id
        }
        .sumOf { it.match }

    fun all_orders_are_non_default(): Boolean =
        trader_infos.all { it.has_non_default_bid() }

    fun round_open_seconds(): Int = 0

    var max_potential_flow: Int = 0
        private set















}

================
File: main/kotlin/au21/engine/domain/de/model/DeRoundTraderInfo.kt
================
package au21.engine.domain.de.model

import au21.engine.domain.common.model.OrderSubmissionType
import au21.engine.domain.common.model.OrderType
import javax.persistence.Embeddable

@Embeddable
class DeRoundTraderInfo(
    constraints_: DeBidConstraints,
    trading_company_: DeTradingCompany,
    var default_order: DeOrder,
    var current_matched_vol: Int,
    var fully_opposed_match_vol: Int
) {

    var de_trading_company: DeTradingCompany = trading_company_
        private set




    var constraints: DeBidConstraints = constraints_
        private set

    var order: DeOrder = default_order






    var bid_while_closed: Boolean = false

    fun has_non_default_bid(): Boolean =
        when (order.submission_type) {
            OrderSubmissionType.DEFAULT -> false
            OrderSubmissionType.MANUAL -> true
            OrderSubmissionType.MANDATORY -> true
        }

    fun has_non_zero_bid(): Boolean =
        order.type != OrderType.NONE ||
                order.quantity != 0

}

================
File: main/kotlin/au21/engine/domain/de/model/DeTradingCompany.kt
================
package au21.engine.domain.de.model

import au21.engine.domain.common.model.Company
import au21.engine.domain.common.model.CompanyProxy
import au21.engine.domain.common.model.OrderType
import javax.persistence.Embeddable

@Embeddable
class DeTradingCompany(c: Company) : CompanyProxy(c){

    var initial_limits:DeInitialLimits = DeInitialLimits()
        private set


    var next_round_order_will_be_mandatory:Boolean = false


    var awarded_quantity: Int = 0
        private set

    var awarded_order_type: OrderType = OrderType.NONE
        private set

    fun award(order_type: OrderType, quantity: Int) {
        awarded_order_type = order_type
        awarded_quantity = quantity
    }

    var blinded: Boolean = false

    var rank: Int? = null
}

================
File: main/kotlin/au21/engine/domain/de/model/enums.kt
================
package au21.engine.domain.de.model

import au21.engine.framework.database.IAuctionState

enum class DeRoundOpenState {
    GREEN, ORANGE, RED
}

enum class DeTimeState {
    BEFORE_ANNOUNCE_TIME,
    BEFORE_START_TIME,
    AUCTION_HAS_STARTED
}

enum class DeAuctioneerInfoLevel {
    NORMAL,
    WARNING,
    ERROR
}

enum class DeAuctioneerState : IAuctionState {
    STARTING_PRICE_NOT_SET,
    STARTING_PRICE_SET,
    STARTING_PRICE_ANNOUNCED,
    ROUND_OPEN_ALL_ORDERS_NOT_IN,
    ROUND_OPEN_ALL_ORDERS_IN,
    ROUND_CLOSED_NOT_AWARDABLE,
    ROUND_CLOSED_AWARDABLE,
    AUCTION_CLOSED;
    override fun oneOf(vararg states: IAuctionState): Boolean {
        return states.contains(this)
    }
}

enum class DeCommonState {
    SETUP,
    STARTING_PRICE_ANNOUNCED,
    ROUND_OPEN,
    ROUND_CLOSED,
    AUCTION_CLOSED
}





fun DeAuctioneerState.common_state():DeCommonState =
    when (this) {
        DeAuctioneerState.STARTING_PRICE_NOT_SET -> DeCommonState.SETUP
        DeAuctioneerState.STARTING_PRICE_SET -> DeCommonState.SETUP
        DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> DeCommonState.STARTING_PRICE_ANNOUNCED
        DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> DeCommonState.ROUND_OPEN
        DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> DeCommonState.ROUND_OPEN
        DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> DeCommonState.ROUND_CLOSED
        DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> DeCommonState.ROUND_CLOSED
        DeAuctioneerState.AUCTION_CLOSED -> DeCommonState.AUCTION_CLOSED
    }


enum class DeRoundState {
    NOT_OPEN, GREEN, ORANGE, RED
}

enum class DeFlowControlType {


    HEARTBEAT,
    SET_STARTING_PRICE,
    ANNOUNCE_STARTING_PRICE,
    START_AUCTION,
    CLOSE_ROUND,
    REOPEN_ROUND,
    NEXT_ROUND,
    AWARD_AUCTION
}

enum class DeCreditSetMode {
    MANUAL,
    MINIMUM
}

================
File: main/kotlin/au21/engine/domain/de/model/extensions.kt
================
package au21.engine.domain.de.model

import au21.engine.domain.common.model.*
import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.AuFormatter
import au21.engine.framework.utils.thousands
import org.joda.time.DateTime
import kotlin.math.abs








fun DeAuction.price_has_overshot(): Boolean =
    lastround().let { n ->
        when (n.price_direction) {
            null -> false
            PriceDirection.UP -> excess_supply(n) < 0
            PriceDirection.DOWN -> excess_demand(n) < 0
        }
    }

fun DeAuction.current_price_direction(): PriceDirection? = lastround().let { n: DeRound ->
    if (n.number == 1)
        return null
    penultimate()?.let { pen: DeRound ->

        if (n.price!! > pen.price!!) PriceDirection.UP
        else if (pen.price!! > n.price!!) PriceDirection.DOWN
        else throw Error("subsequent rounds can't have the same price")
    }
}

fun DeAuction.time_state(): DeTimeState? = DateTime().toDate().let { now ->
    announce_time()?.let {
        if (now.before(it))
            return DeTimeState.BEFORE_ANNOUNCE_TIME
    }
    starting_time?.let {
        return if (now.before(it))
            DeTimeState.BEFORE_START_TIME
        else
            DeTimeState.AUCTION_HAS_STARTED
    }
    return null
}


fun DeAuction.is_awardable(): Boolean = lastround().let { n ->
    val total_demand = total_buy(n)
    val total_supply = total_sell(n)
    when {

        de_trading_companies.all { it.next_round_order_will_be_mandatory } -> true

        n.price == null -> false


        total_demand > total_supply ->

            when (n.has_reversed) {
                false -> false
                true -> {
                    val potential_next_price = n.price!! - settings.price_rule.price_change_post_reversal
                    rounds.any { it.price != null && it.price!! <= potential_next_price }
                }
            }

        total_supply > total_demand ->

            when (n.has_reversed) {
                (n.price == null) -> false
                false -> false
                true -> {
                    val potential_next_price = n.price!! + settings.price_rule.price_change_post_reversal
                    rounds.any { it.price != null && it.price!! >= potential_next_price }
                }
            }

        else -> true
    }
}






































fun DeAuction.getTrader(c: Company?): DeTradingCompany? =
    when (c) {
        null -> null
        else -> de_trading_companies.find { it.company_id == c.id }
    }

fun DeAuction.hasTrader(c: Company?): Boolean =
    when (c) {
        null -> false
        else -> de_trading_companies.any { t -> t.company_id == c.id }
    }

fun DeAuction.hasTrader(u: Person?): Boolean =
    when (u) {
        null -> false
        else -> hasTrader(u.company)
    }
















fun DeAuction.has_non_zero_bid(company: Company?) =
    when (company) {
        null -> false
        else -> rounds.any { r ->
            r.trader_infos
                .find { it.de_trading_company.company_id == company.id }
                ?.let { rti: DeRoundTraderInfo -> rti.has_non_zero_bid() }
                ?: false
        }
    }


fun DeAuction.is_before_end_of_first_round(): Boolean = rounds.size > 1 || !listOf(
    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE,
    DeAuctioneerState.ROUND_CLOSED_AWARDABLE,
    DeAuctioneerState.AUCTION_CLOSED
).contains(auctioneer_state)
























































fun DeAuction.message_auction_state(): String =


    when (auctioneer_state) {







        else -> throw Exception()
    }

fun DeAuction.excess_side(r: DeRound): OrderType =
    when {
        total_buy(r) > total_sell(r) -> OrderType.BUY
        total_sell(r) > total_buy(r) -> OrderType.SELL
        else -> OrderType.NONE
    }

fun DeAuction.excess_quantity(r: DeRound): Int =
    abs(total_buy(r) - total_sell(r))

fun DeAuction.excess_level(r: DeRound, role: AuUserRole): String =
    settings.price_rule.get_excess_level(excess_quantity(r), role)






fun DeAuction.show_trader_excess(r: DeRound): Boolean = r != lastround() || listOf(
    DeCommonState.AUCTION_CLOSED
).contains(common_state)

fun DeAuction.format_round_price(role: AuUserRole, r: DeRound): String =
    when (role) {

        AuUserRole.AUCTIONEER -> r.price?.let {
            AuFormatter.format_to_places(it, this.price_decimal_places)
        } ?: "---"

        AuUserRole.TRADER -> when {
            r.price == null || !starting_price_announced() -> "waiting"
            else -> AuFormatter.format_to_places(r.price!!, this.price_decimal_places)
        }
    }









fun DeAuction.trader_by_company_id(company_id: Long?): DeTradingCompany? =
    when (company_id) {
        null -> null
        else -> de_trading_companies.find { it.company_id == company_id }
    }

fun DeAuction.trader_by_company_id_str(company_id_str: String?): DeTradingCompany? =
    when (company_id_str) {
        null -> null
        else -> de_trading_companies.find { it.company_id.toString() == company_id_str }
    }








fun DeAuction.get_trader(s: AuSession?): DeTradingCompany? =
    when (val c = s?.user?.company) {
        null -> null
        else -> getTrader(c)
    }

fun DeAuction.round_sellers(r: DeRound): List<DeTradingCompany> =
    de_trading_companies.filter {
        r.get_order(it).type == OrderType.SELL
    }

fun DeAuction.round_buyers(r: DeRound): List<DeTradingCompany> =
    de_trading_companies.filter {
        r.get_order(it).type == OrderType.BUY
    }

fun DeAuction.seller_count(r: DeRound): Int = round_sellers(r).size

fun DeAuction.buyer_count(r: DeRound): Int = round_buyers(r).size









fun DeAuction.get_orders(t: DeTradingCompany): List<DeOrder> =
    rounds.map { it.trader_infos }
        .flatten()
        .filter { it.de_trading_company.company_id == t.company_id }
        .map { it.order }



fun DeRound.get_order(t: DeTradingCompany): DeOrder =
    trader_infos
        .find { it.de_trading_company.company_id == t.company_id }
        ?.order
        ?: throw AlertException("We should always have a round info and an order for a trader")













fun DeOrder?.format(): String = when (this?.type) {
    null -> "---"
    OrderType.BUY -> "Buy: " + buyVol().thousands()
    OrderType.SELL -> "Sell: " + sellVol().thousands()
    OrderType.NONE -> "Zero"
}


fun DeAuction.order_value_str(o: DeOrder?): String = o?.run {


    AuFormatter.format_currency(o.price * o.quantity * settings.cost_multiplier)
} ?: "---"










fun DeAuction.penultimate(): DeRound? = rounds.takeIf { it.size > 1 }?.let { it[it.size - 2] }

fun DeAuction.total_sell(r: DeRound? = lastround()): Int = r?.sell_orders()?.sumOf { it.quantity } ?: 0

fun DeAuction.total_buy(r: DeRound? = lastround()): Int = r?.buy_orders()?.sumOf { it.quantity } ?: 0

fun DeAuction.excess_demand(r: DeRound? = lastround()): Int = total_buy(r) - total_sell(r)

fun DeAuction.excess_supply(r: DeRound? = lastround()): Int = total_sell(r) - total_buy(r)






fun DeRound?.get_rti(t: DeTradingCompany?): DeRoundTraderInfo? =

    if (t == null || this == null) {
        null
    } else {
        this.trader_infos.find { it.de_trading_company.company_id == t.company_id }
    }














fun DeRound?.get_rti_by_company(company: Company?): DeRoundTraderInfo? =
    when {
    this == null -> null
    company == null -> null
    else -> trader_infos.find { it.de_trading_company.company_id == company.id }
}



fun DeRound.sell_quantity(t: DeTradingCompany): Int =
    get_order(t).takeIf { it.type == OrderType.SELL }?.quantity ?: 0


fun DeRound.buy_quantity(t: DeTradingCompany): Int =
    get_order(t).takeIf { it.type == OrderType.BUY }?.quantity ?: 0

================
File: main/kotlin/au21/engine/domain/de/model/starting-time-labels.kt
================
package au21.engine.domain.de.model

================
File: main/kotlin/au21/engine/domain/de/model/state-labels.kt
================
package au21.engine.domain.de.model

================
File: main/kotlin/au21/engine/domain/de/services/constraints/constraint-calculator.kt
================
package au21.engine.domain.de.services.constraints

import au21.engine.domain.common.model.OrderType
import au21.engine.domain.common.model.PriceDirection
import au21.engine.domain.de.model.DeAuctionSettings
import au21.engine.domain.de.model.DeBidConstraints
import au21.engine.domain.de.model.DeRound
import au21.engine.domain.de.model.DeTradingCompany
import kotlin.math.floor





fun calculate_first_round_constraints(
    t:DeTradingCompany,
    s:DeAuctionSettings,
    n:DeRound) = DeBidConstraints(

        max_buy_quantity = max_quantity_from_round_price_and_cost_limit(
            t.initial_limits.initial_buying_cost_limit,
            s.cost_multiplier,
            n.price ?: 0.0
        ),
        min_buy_quantity = 0,
        min_sell_quantity = 0,
        max_sell_quantity = t.initial_limits.initial_selling_quantity_limit





    )

 fun max_quantity_from_round_price_and_cost_limit(

    cost_limit: Double,
    round_price: Double,
    cost_multiplier: Double,
): Int =
    when {
        round_price == 0.0 -> 0
        cost_limit == 0.0 -> 0
        cost_multiplier == 0.0 -> 0
        else -> floor(cost_limit / (round_price * cost_multiplier)).toInt()
    }

fun calculate_subsequent_round_constraints(
    prev_round_constraints: DeBidConstraints,
    prev_round_order_type: OrderType,
    prev_round_order_quantity: Int,
    next_round_direction: PriceDirection
): DeBidConstraints {







    return when (prev_round_order_type) {

        OrderType.NONE ->

            when (next_round_direction) {
                PriceDirection.UP ->
                    DeBidConstraints(
                        min_buy_quantity = 0,
                        max_buy_quantity = 0,
                        min_sell_quantity = 0,
                        max_sell_quantity = prev_round_constraints.max_sell_quantity
                    )
                PriceDirection.DOWN ->
                    DeBidConstraints(
                        min_buy_quantity = 0,
                        max_buy_quantity = prev_round_constraints.max_buy_quantity,
                        min_sell_quantity = 0,
                        max_sell_quantity = 0
                    )
            }
        OrderType.BUY ->
            when (next_round_direction) {
                PriceDirection.UP ->
                    DeBidConstraints(
                        min_buy_quantity = prev_round_constraints.min_buy_quantity,
                        max_buy_quantity = prev_round_order_quantity,
                        min_sell_quantity = prev_round_constraints.min_sell_quantity,
                        max_sell_quantity = prev_round_constraints.max_sell_quantity
                    )
                PriceDirection.DOWN ->
                    DeBidConstraints(
                        min_buy_quantity = prev_round_order_quantity,
                        max_buy_quantity = prev_round_constraints.max_buy_quantity,
                        min_sell_quantity = 0,
                        max_sell_quantity = 0
                    )
            }
        OrderType.SELL ->
            when (next_round_direction) {
                PriceDirection.UP ->
                    DeBidConstraints(
                        min_buy_quantity = 0,
                        max_buy_quantity = 0,
                        min_sell_quantity = prev_round_order_quantity,
                        max_sell_quantity = prev_round_constraints.max_sell_quantity
                    )
                PriceDirection.DOWN ->
                    DeBidConstraints(
                        min_buy_quantity = prev_round_constraints.min_buy_quantity,
                        max_buy_quantity = prev_round_constraints.max_buy_quantity,
                        min_sell_quantity = prev_round_constraints.min_sell_quantity,
                        max_sell_quantity = prev_round_order_quantity
                    )
            }
    }
}

================
File: main/kotlin/au21/engine/domain/de/services/defaultorders/DeDefaultOrder.kt
================
package au21.engine.domain.de.services.defaultorders

import au21.engine.domain.common.model.OrderSubmissionType
import au21.engine.domain.common.model.OrderType
import au21.engine.domain.de.model.DeBidConstraints
import au21.engine.framework.commands.AlertException

class DeOrderInfo(
    val submission_type: OrderSubmissionType,
    val order_type: OrderType,
    val quantity: Int
) {
    companion object {






        fun create_default_from_constraints(
            constraints: DeBidConstraints
        ): DeOrderInfo {

            constraints.apply {
                if (max_buy_quantity == 0 && max_sell_quantity == 0) {
                    return DeOrderInfo(
                        OrderSubmissionType.MANDATORY,
                        OrderType.NONE,
                        0
                    )
                } else if (min_buy_quantity == 0 && min_sell_quantity == 0) {
                    return DeOrderInfo(
                        OrderSubmissionType.DEFAULT,
                        OrderType.NONE,
                        0
                    )
                } else if (min_buy_quantity > 0) {
                    return if (max_buy_quantity == min_buy_quantity) {
                        DeOrderInfo(
                            OrderSubmissionType.MANDATORY,
                            OrderType.BUY,
                            min_buy_quantity
                        )
                    } else {
                        DeOrderInfo(
                            OrderSubmissionType.DEFAULT,
                            OrderType.BUY,
                            min_buy_quantity
                        )
                    }
                }
                else if (min_sell_quantity > 0) {
                    return if (max_sell_quantity == min_sell_quantity) {
                        DeOrderInfo(
                            OrderSubmissionType.MANDATORY,
                            OrderType.SELL,
                            min_sell_quantity
                        )
                    } else {
                        DeOrderInfo(
                            OrderSubmissionType.DEFAULT,
                            OrderType.SELL,
                            min_sell_quantity
                        )
                    }
                }
                else {

                    throw AlertException("Unable to calculate default order!")
                }
            }

        }
    }
}

================
File: main/kotlin/au21/engine/domain/de/services/matcher/de-potential.kt
================
package au21.engine.domain.de.services.matcher


fun potential_maxflow(){
    TODO("not implemented")
}

================
File: main/kotlin/au21/engine/domain/de/services/matcher/DeCapacityEdge.kt
================
package au21.engine.domain.de.services.matcher

import au21.engine.domain.common.model.OrderType
import au21.engine.domain.de.model.DeOrder
import au21.engine.domain.de.model.DeRoundTraderInfo
import au21.engine.framework.utils.format_table
import java.lang.Integer.min

data class DeCapacityEdge(
    val from: String,
    val to: String,

    val capacity: Int,
) {

    companion object {

        const val SELL_SOURCE = "SELL_SOURCE"
        const val BUY_SINK = "BUY_SINK"

        fun List<DeCapacityEdge>.to_table(): String =
            format_table(this.sortedBy { it.to }.sortedBy { it.from }, listOf("from_seller", "to_buyer", "capacity")
            )










        fun to_edges(r: List<DeRoundTraderInfo>): List<DeCapacityEdge> {



            val edges = mutableListOf<DeCapacityEdge>()



            r.forEach { rti: DeRoundTraderInfo ->
                rti.order.let { o: DeOrder ->
                    val shortname: String = o.trading_company.shortname_at_auction_time
                    when (o.type) {


                        OrderType.SELL -> {

                            edges += DeCapacityEdge(
                                from = SELL_SOURCE, to = shortname, capacity = o.quantity
                            )

                            edges += DeCapacityEdge(
                                from = shortname, to = BUY_SINK, capacity = 0
                            )

                            r.filter { it.order.type == OrderType.BUY }.forEach {
                                edges += DeCapacityEdge(
                                    from = shortname,
                                    to = it.de_trading_company.shortname_at_auction_time,
                                    capacity = min(rti.order.quantity, it.order.quantity)
                                )
                            }
                        }


                        OrderType.BUY -> {

                            edges += DeCapacityEdge(
                                from = SELL_SOURCE, to = shortname, capacity = 0
                            )

                            edges += DeCapacityEdge(
                                from = shortname, to = BUY_SINK, capacity = o.quantity
                            )

                            r.filter { it.order.type == OrderType.SELL }.forEach { it ->
                                edges += DeCapacityEdge(
                                    from = it.de_trading_company.shortname_at_auction_time,
                                    to = shortname,
                                    capacity = min(rti.order.quantity, it.order.quantity)
                                )
                            }
                        }

                        else -> {}
                    }
                }
            }
            return edges
        }
    }
}

================
File: main/kotlin/au21/engine/domain/de/services/matcher/DeFlowResult.kt
================
package au21.engine.domain.de.services.matcher

import au21.engine.framework.utils.format_table
import io.quarkus.logging.Log
import org.psjava.algo.graph.flownetwork.FordFulkersonAlgorithm
import org.psjava.algo.graph.flownetwork.MaximumFlowAlgorithmResult
import org.psjava.algo.graph.pathfinder.BFSPathFinder
import org.psjava.algo.graph.pathfinder.DFSPathFinder
import org.psjava.ds.graph.CapacityEdge
import org.psjava.ds.graph.MutableCapacityGraph
import org.psjava.ds.math.Function
import org.psjava.ds.numbersystrem.IntegerNumberSystem





data class DeFlowResult(
    val from_seller: String,
    val to_buyer: String,
    val flow: Int
) {

    companion object {

        fun List<DeFlowResult>.log_tables(): String =
            format_table(
                this,
                listOf("from_seller", "to_buyer", "flow")
            )


        fun log(g: MutableCapacityGraph<String, Int>, max_flow_function: Function<CapacityEdge<String, Int>, Int>) {
            Log.info("Capacities:")
            val edges = g.vertices.sorted().flatMap { node: String ->
                g.getEdges(node).map { edge: CapacityEdge<String, Int> ->
                    object {
                        val from = edge.from()
                        val to = edge.to()
                        val capacity = edge.capacity()
                        val flow = max_flow_function.get(edge)
                    }
                }
            }
                .sortedBy { it.from }
                .sortedBy { it.to }

            Log.info(
                format_table(
                    edges,
                    listOf("from", "to", "flow", "capacity")
                )
            )
        }



        fun calculate_max_flow_psjava(edges: List<DeCapacityEdge>, isDFS: Boolean = true): List<DeFlowResult> {

            val nodes: Set<String> = mutableSetOf<String>().apply {

                add(DeCapacityEdge.SELL_SOURCE)
                add(DeCapacityEdge.BUY_SINK)
                edges.forEach { c: DeCapacityEdge ->
                    add(c.from)
                    add(c.to)
                }
            }.toSet()

            when {
                !nodes.contains(DeCapacityEdge.SELL_SOURCE) -> throw Error("SOURCE node not found.")
                !nodes.contains(DeCapacityEdge.BUY_SINK) -> throw Error("SINK node not found.")
            }



            val capacityGraph = MutableCapacityGraph.create<String, Int>().apply {
                nodes.forEach { n -> insertVertex(n) }
                edges.forEach { e -> addEdge(e.from, e.to, e.capacity) }
            }

            val result: MaximumFlowAlgorithmResult<Int, CapacityEdge<String, Int>> = FordFulkersonAlgorithm
                .getInstance(if (isDFS) DFSPathFinder.getInstance() else BFSPathFinder.getInstance())
                .calc(
                    capacityGraph,
                    DeCapacityEdge.SELL_SOURCE,
                    DeCapacityEdge.BUY_SINK,
                    IntegerNumberSystem.getInstance()
                )

            val maxflow: Int = result.calcTotalFlow()
            Log.info("max flow: $maxflow")


            val max_flow_function: Function<CapacityEdge<String, Int>, Int> = result.calcFlowFunction()

            log(capacityGraph, max_flow_function)

            val flow_results: List<DeFlowResult> = nodes
                .filter { it != DeCapacityEdge.SELL_SOURCE && it != DeCapacityEdge.BUY_SINK }
                .flatMap { trader ->
                    capacityGraph.getEdges(trader)
                        .filter { it.to() != DeCapacityEdge.BUY_SINK }
                        .map { e: CapacityEdge<String, Int> ->
                            DeFlowResult(
                                from_seller = e.from(),
                                to_buyer = e.to(),
                                flow = max_flow_function.get(e)
                            )
                        }
                }
            return flow_results
        }
    }
}

================
File: main/kotlin/au21/engine/domain/de/services/matcher/DeMatcher.kt
================
package au21.engine.domain.de.services.matcher


import au21.engine.domain.de.commands.DeOrderSubmitAction
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeMatch
import au21.engine.domain.de.model.DeRound
import au21.engine.domain.de.model.DeTradingCompany
import au21.engine.domain.de.services.matcher.DeCapacityEdge.Companion.to_table
import au21.engine.domain.de.services.matcher.DeFlowResult.Companion.calculate_max_flow_psjava
import au21.engine.domain.de.services.matcher.DeFlowResult.Companion.log_tables
import au21.engine.framework.commands.AlertException
import au21.engine.framework.commands.EngineAction
import io.quarkus.logging.Log



















object DeMatcher {


    fun calculate_and_set_matches(
        de: DeAuction,
        caller: EngineAction?,
        debug: Boolean = false
    ) {

        calculate_and_set_matches(
            de.lastround(),
            de.settings.cost_multiplier,
            caller,
            debug
        )
    }


    fun calculate_and_set_matches(
        last_De_round: DeRound,
        cost_multiplier: Double,
        caller: EngineAction?,
        debug: Boolean = false
    ) {

        val price_cost_multiplier = (last_De_round.price ?: 0.0) * cost_multiplier


        val counterparty_limits: List<RoundCounterpartyLimits> =
            RoundCounterpartyLimits.calculate_counterparty_capacities(
                round_trader_infos = last_De_round.trader_infos,
                price_cost_multiplier = price_cost_multiplier
            )


        val capacity_edges: List<DeCapacityEdge> = DeCapacityEdge.to_edges(last_De_round.trader_infos)












        val max_flow_results: List<DeFlowResult> =
            calculate_max_flow_psjava(capacity_edges)


        val matches: List<DeMatch> =
            create_matches_from_flow_results(counterparty_limits, max_flow_results, price_cost_multiplier)

        last_De_round.setMatches(matches)


        if (debug || caller is DeOrderSubmitAction) {

            Log.info(capacity_edges.to_table())
            Log.info(max_flow_results.log_tables())
        }
    }

    fun create_matches_from_flow_results(
        counterparty_capacities: List<RoundCounterpartyLimits>,
        max_flow_results: List<DeFlowResult>,
        round_price_multiplier: Double
    ): List<DeMatch> {

        val tradingCompanies: Set<DeTradingCompany> = counterparty_capacities.flatMap {
            listOf(it.buyer_rti.de_trading_company, it.seller_rti.de_trading_company)
        }.toSet()

        return max_flow_results
            .filter { flow: DeFlowResult ->
                listOf(DeCapacityEdge.SELL_SOURCE, DeCapacityEdge.BUY_SINK)
                    .none { it == flow.from_seller || it == flow.to_buyer }
            }
            .mapNotNull { f: DeFlowResult ->

                val seller: DeTradingCompany =
                    tradingCompanies.find { it.shortname_at_auction_time == f.from_seller }
                        ?: throw Error("No buyer found with shortname: ${f.to_buyer}")

                val buyer: DeTradingCompany =
                    tradingCompanies.find { it.shortname_at_auction_time == f.to_buyer }
                        ?: throw Error("No buyer found with shortname: ${f.to_buyer}")

                if (buyer.company_id != seller.company_id) {
                    val rcl: RoundCounterpartyLimits = counterparty_capacities
                        .find {
                            it.buyer_rti.de_trading_company.company_id == buyer.company_id
                                    && it.seller_rti.de_trading_company.company_id == seller.company_id
                        }
                        ?: throw AlertException("unable to find a quantity limit for buyer=${buyer.shortname_at_auction_time}, seller=${seller.shortname_at_auction_time}")

                    DeMatch(
                        sell_order_ = rcl.seller_rti.order,
                        buy_order_ = rcl.buyer_rti.order,
                        match_ = f.flow,
                        capacity_ = rcl.capacity,
                        round_price_multiplier = round_price_multiplier
                    )

                } else {
                    null
                }
            }
    }
}

================
File: main/kotlin/au21/engine/domain/de/services/matcher/RoundCounterpartyLimits.kt
================
package au21.engine.domain.de.services.matcher

import au21.engine.domain.de.model.DeRoundTraderInfo
import au21.engine.framework.commands.AlertException



class RoundCounterpartyLimits(
    val seller_rti: DeRoundTraderInfo,
    val buyer_rti: DeRoundTraderInfo,
    val price_cost_multiplier: Double
) {
    init {
        if (seller_rti.de_trading_company.company_id == buyer_rti.de_trading_company.company_id)
            throw AlertException("Quantity limit between same buyer and seller not allowed.")
    }

    val buy_max: Int = buyer_rti.constraints.max_buy_quantity
    val buy_limit: Int = buyer_rti.order.quantity
    val sell_max: Int = seller_rti.constraints.max_sell_quantity
    val sell_limit: Int = seller_rti.order.quantity






    val capacity = minOf(buy_limit, sell_limit)









    companion object {


        fun calculate_counterparty_capacities(
            round_trader_infos:List<DeRoundTraderInfo>,
            price_cost_multiplier: Double
        ): List<RoundCounterpartyLimits> {

            return round_trader_infos.flatMap { buyer_rti: DeRoundTraderInfo ->
                round_trader_infos.mapNotNull { seller_rti: DeRoundTraderInfo ->
                    when (buyer_rti.de_trading_company.company_id == seller_rti.de_trading_company.company_id) {
                        true -> null
                        false -> RoundCounterpartyLimits(
                            seller_rti = seller_rti,
                            buyer_rti = buyer_rti,
                            price_cost_multiplier = price_cost_multiplier
                        )
                    }
                }
            }
        }

    }
}

================
File: main/kotlin/au21/engine/domain/de/services/nextroundprice/de-price-calculator.kt
================
package au21.engine.domain.de.services.nextroundprice

import au21.engine.domain.common.model.PriceDirection
import au21.engine.framework.commands.AlertException





class DeNextRoundPriceInfo(
    val price: Double,
    val direction: PriceDirection,
    val is_post_price_reversal: Boolean
) {
    companion object {



        fun create(
            prev_round_number: Int,
            prev_round_price: Double,
            prev_round_direction: PriceDirection?,
            prev_round_is_post_reversal: Boolean,
            prev_round_total_buy: Int,
            prev_round_total_sell: Int,
            price_change_initial: Double,
            price_change_post_reversal: Double
        ): DeNextRoundPriceInfo {

            val next_round_direction: PriceDirection = when {
                prev_round_total_buy > prev_round_total_sell -> PriceDirection.UP
                prev_round_total_sell > prev_round_total_buy -> PriceDirection.DOWN
                else -> throw AlertException("Cannot create subsequent round if prior round supply and demand are equal!")
            }





            if (prev_round_number == 1) {
                return DeNextRoundPriceInfo(
                    direction = next_round_direction,
                    is_post_price_reversal = false,
                    price = when (next_round_direction) {
                        PriceDirection.UP ->
                            prev_round_price + price_change_initial
                        PriceDirection.DOWN ->
                            prev_round_price - price_change_initial
                    }
                )
            }





            if(prev_round_direction == null)
                throw AlertException("After first round, previous round direction cannot be null.")

            val price_has_reversed = when {
                    prev_round_is_post_reversal -> true
                    else -> prev_round_direction != next_round_direction
                }

            val next_round_price: Double = when (next_round_direction) {
                    PriceDirection.UP ->
                        when (price_has_reversed) {
                            false -> prev_round_price + price_change_initial
                            true -> prev_round_price + price_change_post_reversal
                        }
                    PriceDirection.DOWN ->
                        when (price_has_reversed) {
                            false -> prev_round_price - price_change_initial
                            true -> prev_round_price - price_change_post_reversal
                        }
                }

            return DeNextRoundPriceInfo(
                price = next_round_price,
                direction = next_round_direction,
                is_post_price_reversal = price_has_reversed
            )
        }
    }
}

================
File: main/kotlin/au21/engine/domain/de/services/rounds/de-round-creator.kt
================
package au21.engine.domain.de.services.rounds

import au21.engine.domain.de.model.*
import au21.engine.domain.de.services.constraints.calculate_subsequent_round_constraints
import au21.engine.domain.de.services.defaultorders.DeOrderInfo
import au21.engine.domain.de.services.matcher.DeMatcher
import au21.engine.domain.de.services.nextroundprice.DeNextRoundPriceInfo
import au21.engine.framework.commands.AlertException











fun DeAuction.create_subsequent_round(): DeRound {






    val prev_de_round: DeRound = lastround()

    val next_price_info: DeNextRoundPriceInfo = DeNextRoundPriceInfo.create(
        prev_round_number = prev_de_round.number,
        prev_round_price = prev_de_round.price
            ?: throw AlertException("Previous round has no price, therefore cannot create subsequent round"),
        prev_round_direction = prev_de_round.price_direction,
        prev_round_is_post_reversal = prev_de_round.has_reversed,
        prev_round_total_buy = total_buy(prev_de_round),
        prev_round_total_sell = total_sell(prev_de_round),
        price_change_initial = settings.price_rule.price_change_initial,
        price_change_post_reversal = settings.price_rule.price_change_post_reversal,
    )


    val next_de_round = DeRound(
        number = rounds.size + 1,
        price = next_price_info.price,
        has_reversed_ = next_price_info.is_post_price_reversal,
        direction = next_price_info.direction
    )

    if (next_de_round.number < 2) throw AlertException("Expected round number > 1")

    rounds.add(next_de_round)

    de_trading_companies.forEach { t: DeTradingCompany ->


        next_de_round.apply { get_rti(t)?.let { trader_infos.remove(it) } }

        val prev_order: DeOrder = prev_de_round.get_order(t)

        val prev_rti: DeRoundTraderInfo = prev_de_round.get_rti(t)
            ?: throw AlertException("Exepected round trader info for trader: ${t.shortname_at_auction_time} in round ${prev_de_round.number}.")

        val prev_constraints = prev_rti.constraints


        val next_round_constraints:DeBidConstraints = calculate_subsequent_round_constraints(
            prev_constraints, prev_order.type, prev_order.quantity, next_price_info.direction
        )


        val order_info:DeOrderInfo =  DeOrderInfo.create_default_from_constraints(next_round_constraints)

        val default_order: DeOrder = DeOrder(
            round = next_de_round,
            trading_company_ = t,
            u = null,
            order_info = order_info,
            cost_multiplier = settings.cost_multiplier,
            prev_order = null,
        )

        next_de_round.trader_infos.add(

            DeRoundTraderInfo(
                trading_company_ = t,
                constraints_ = next_round_constraints,
                default_order = default_order,
                current_matched_vol = 0,
                fully_opposed_match_vol = 0
            )
        )

        DeMatcher.calculate_and_set_matches(this, null)
    }
    return next_de_round
}

================
File: main/kotlin/au21/engine/domain/de/services/sampledb/de-sample-db-helper.kt
================
package au21.engine.domain.de.services.sampledb

import au21.engine.domain.common.model.OrderType
import au21.engine.domain.common.model.Person
import au21.engine.domain.common.model.PersonProxy
import au21.engine.domain.common.model.PriceDirection
import au21.engine.domain.de.model.*
import au21.engine.domain.de.services.matcher.DeMatcher
import au21.engine.domain.de.services.rounds.create_subsequent_round
import au21.engine.domain.de.services.sampledb.SampleOrderMove.DECREASE
import au21.engine.domain.de.services.sampledb.SampleOrderMove.INCREASE
import io.quarkus.logging.Log
import org.joda.time.DateTime
import kotlin.random.Random







enum class SampleOrderMove { INCREASE, DECREASE }


fun random_bid(de: DeAuction, t: DeTradingCompany, u: Person) {

    val n: DeRound = de.lastround()
    val n_rti: DeRoundTraderInfo =
        n.get_rti(t) ?: throw Error("expected rti for trader: ${t.shortname_at_auction_time}")

    val n_constraints: DeBidConstraints =
        n_rti.constraints

    fun simple_frac(
        min: Int, max: Int,
        divisor_min: Int, divisor_max: Int,
        move: SampleOrderMove,
    ): Int {
        try {
            if (min == max)
                return min
            val random_range: Int = Random.nextInt(min, max)
            val random_change: Int = random_range / Random.nextInt(divisor_min, divisor_max)
            return when (move) {
                INCREASE -> min + random_change
                DECREASE -> max - random_change
            }
        } catch (t: Throwable) {
            throw t
        }
    }

    fun simple_frac_5(min: Int, max: Int, move: SampleOrderMove): Int = simple_frac(min, max, 3, 5, move)

    fun submit(order_type: OrderType, quantity: Int) {


        val vt: OrderType = when (quantity) {
            0 -> OrderType.NONE
            else -> order_type
        }


        val vol: Int = when (order_type) {
            OrderType.NONE -> 0
            else -> quantity
        }

        de.create_manual_order(
            r = n,
            t = t,
            order_quantity = when (vt) {
                OrderType.NONE -> 0
                OrderType.BUY -> Integer.max(
                    Integer.min(vol, n_constraints.max_buy_quantity),
                    n_constraints.min_buy_quantity
                )
                OrderType.SELL -> Integer.max(
                    Integer.min(vol, n_constraints.max_sell_quantity),
                    n_constraints.min_sell_quantity
                )
            },
            order_type = vt,
            u = PersonProxy(u)
        )

        DeMatcher.calculate_and_set_matches(de, null)
    }

    fun submit(rti: DeRoundTraderInfo, side: OrderType, move: SampleOrderMove) {
        rti.constraints.apply {
            when (side) {
                OrderType.NONE -> submit(OrderType.NONE, 0)
                OrderType.BUY -> submit(
                    OrderType.BUY,
                    simple_frac_5(min_buy_quantity, max_buy_quantity, move)
                )
                OrderType.SELL -> submit(
                    OrderType.SELL,
                    simple_frac_5(min_sell_quantity, max_sell_quantity, move)
                )
            }
        }
    }

    fun submit(rti: DeRoundTraderInfo, side: OrderType) {
        val constraints = rti.constraints

        val range: Int = constraints.max_buy_quantity + constraints.max_sell_quantity
        val fraction = Random.nextInt(range / 5)
        when (side) {
            OrderType.NONE -> submit(OrderType.NONE, 0)
            OrderType.BUY -> when {
                fraction <= constraints.max_buy_quantity ->
                    submit(OrderType.BUY, fraction)
                else ->
                    submit(OrderType.SELL, fraction - constraints.max_buy_quantity)
            }
            OrderType.SELL -> when {
                fraction <= constraints.max_sell_quantity ->
                    submit(OrderType.SELL, fraction)
                else ->
                    submit(
                        OrderType.BUY,
                        fraction - constraints.max_sell_quantity
                    )
            }
        }
    }

    when (n.number) {
        1 ->
            if (Random.nextInt(100) > 80) {
                submit(n_rti, OrderType.SELL, INCREASE)
            } else {
                submit(n_rti, OrderType.BUY, DECREASE)
            }
        else -> {
            val n_direction: PriceDirection = n.price_direction!!
            val pen = de.penultimate()!!
            val pen_order: DeOrder = pen.get_order(t)
            val pen_type: OrderType = pen_order.type
            when (n.number) {
                2 -> when {
                    n_direction == PriceDirection.UP && pen_type == OrderType.BUY ->
                        submit(
                            OrderType.BUY,
                            simple_frac_5(n_constraints.min_buy_quantity, n_constraints.max_buy_quantity, DECREASE)
                        )
                    n_direction == PriceDirection.UP && pen_type == OrderType.SELL ->
                        submit(
                            OrderType.SELL,
                            simple_frac_5(n_constraints.min_sell_quantity, n_constraints.max_sell_quantity, INCREASE)
                        )
                    n_direction == PriceDirection.DOWN && pen_type == OrderType.BUY ->
                        submit(
                            OrderType.BUY,
                            simple_frac_5(n_constraints.min_buy_quantity, n_constraints.max_buy_quantity, INCREASE)
                        )
                    n_direction == PriceDirection.DOWN && pen_type == OrderType.SELL ->
                        submit(
                            OrderType.SELL,
                            simple_frac_5(n_constraints.min_sell_quantity, n_constraints.max_sell_quantity, DECREASE)
                        )
                }
                else -> {
                    val has_reversed = n.has_reversed
                    val pen_pen: DeRound = de.rounds.first { it.number == n.number - 2 }
                    val pen_pen_order: DeOrder = pen_pen.get_order(t)
                    val pen_pen_type: OrderType = pen_pen_order.type
                    when (n_direction) {
                        PriceDirection.UP -> when {
                            pen_pen_type == OrderType.BUY && pen_type == OrderType.BUY ->
                                when (has_reversed) {
                                    false -> submit(n_rti, OrderType.BUY, DECREASE)
                                    true -> submit(n_rti, OrderType.BUY)
                                }
                            pen_pen_type == OrderType.BUY && pen_type == OrderType.SELL ->
                                when (has_reversed) {
                                    false -> submit(n_rti, OrderType.SELL, INCREASE)
                                    true -> submit(n_rti, OrderType.SELL)
                                }
                            pen_pen_type == OrderType.SELL && pen_type == OrderType.SELL ->
                                when (has_reversed) {
                                    false -> submit(n_rti, OrderType.SELL, INCREASE)
                                    true -> submit(n_rti, OrderType.SELL, DECREASE)
                                }
                        }
                        PriceDirection.DOWN -> when {
                            pen_pen_type == OrderType.SELL && pen_type == OrderType.SELL ->
                                when (has_reversed) {
                                    false -> submit(n_rti, OrderType.SELL, DECREASE)
                                    true -> submit(n_rti, OrderType.SELL)
                                }
                            pen_pen_type == OrderType.SELL && pen_type == OrderType.BUY ->
                                when (has_reversed) {
                                    false -> submit(n_rti, OrderType.BUY, INCREASE)
                                    true -> submit(n_rti, OrderType.BUY)
                                }
                            pen_pen_type == OrderType.BUY && pen_type == OrderType.BUY ->
                                when (has_reversed) {
                                    false -> submit(n_rti, OrderType.BUY, INCREASE)
                                    true -> submit(n_rti, OrderType.BUY, DECREASE)
                                }
                        }
                    }
                }

            }
        }
    }
}


fun create_sample_db_auction(
    auction_name: String,
    trading_users: List<Person>,
    round_count: Int,
    close_last_round: Boolean,
    use_counterparty_credits:Boolean
): DeAuction {

    Log.info("creating auction: $auction_name")

    if (trading_users.any { it.company == null })
        throw Error("Traders need to have a non-null companies.")



    val de = DeAuction(
        auction_name = auction_name,
        settings = DeAuctionSettings(

            use_counterparty_credits = use_counterparty_credits,

            starting_price_announcement_mins = 1,

            round_red_secs = 30,
            round_orange_secs = 15,
            round_open_min_secs = 5,
            round_closed_min_secs = 5,




            cost_multiplier = 10_000.0,

            quantity_units = "MMlb",
            quantity_minimum = 1,
            quantity_step = 1,


            price_units = "cpp",
            price_decimal_places = 3,
            price_rule = DePriceRule()
        )
    )

    de.starting_time = DateTime().plusSeconds(4).toDate()


    val traders: List<Pair<DeTradingCompany, Person>> =
        trading_users.map {
            val t: DeTradingCompany = de.create_trader(it.company!!)
            Pair(t, it)
        }

    de.firstround().price = 100.0
    de.setState(DeAuctioneerState.STARTING_PRICE_SET)
    de.setState(DeAuctioneerState.STARTING_PRICE_ANNOUNCED)
    de.setState(DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN)

    traders.forEach {
        random_bid(de, it.first, it.second)
    }

    if (round_count > 1 || close_last_round) {
        de.setState(
            if (de.is_awardable())
                DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE
            else
                DeAuctioneerState.ROUND_CLOSED_AWARDABLE
        )
    }

    Log.debug("round count: $round_count")
    if (round_count > 1) {

        (2..round_count).forEach { round_num ->
            if (!de.is_awardable()) {
                de.create_subsequent_round()


                traders.forEach {
                    random_bid(de, it.first, it.second)
                }

                Log.debug("round number = $round_num")

                if (round_num < round_count || close_last_round) {
                    de.setState(
                        if (de.is_awardable())
                            DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE
                        else
                            DeAuctioneerState.ROUND_CLOSED_AWARDABLE
                    )
                }
            }

        }
    }

    return de

}

================
File: main/kotlin/au21/engine/domain/de/services/sampledb/TradingUserFixture.kt
================
package au21.engine.domain.de.services.sampledb

import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.Company
import au21.engine.domain.common.model.Person
import au21.engine.framework.database.AuEntityManager
import io.quarkus.logging.Log

class TradingUserFixture {

    companion object {

        fun create(db: AuEntityManager, trader_count: Int, use_counterparty_credits: Boolean): List<Person> {
            create(trader_count, db.findAll<Person>().filter { it.isTrader() }, db.findAll()).map { u: Person ->
                u.also {
                    db.save(it)
                    it.company?.let { c: Company -> db.save(c) }
                }
            }
            return db.findAll<Person>().filter { it.isTrader() }
        }


        fun create(
            trader_count: Int,
            existing_trader_users: List<Person>,
            existing_companies: List<Company>,
            unlimited_credit: Boolean = false,
        ): List<Person> {

            fun uname(i: Int) = "b$i"

            fun shortname(u: Person): String =
                u.company?.shortname ?: u.username.replace("b", "").toInt().let {
                    "c-$it"
                }

            fun longname(u: Person): String =
                u.company?.shortname ?: u.username.replace("b", "").toInt().let {
                    "c-$it long name"
                }

            val new_users: List<Person> = (1..trader_count).mapNotNull { i ->
                val username = uname(i)
                if (existing_trader_users.none { it.username == username }) {
                    Person(
                        username = username,
                        password = "1",
                        role = AuUserRole.TRADER,
                        company = null
                    ).also { u: Person ->
                        Log.debug("created user: " + u.username)
                        u.company = existing_companies.find { it.shortname == shortname(u) }
                            ?: run {
                                Company(shortname = shortname(u), longname = longname(u)).also {
                                    Log.debug("created company: " + it.shortname)
                                }
                            }


                    }
                } else null
            }

            val companies: List<Company> = new_users.mapNotNull { it.company } + existing_companies




















            return new_users + existing_trader_users
        }

    }

}

================
File: main/kotlin/au21/engine/domain/de/services/state/auctioneeer_info_level.kt
================
package au21.engine.domain.de.services.state

================
File: main/kotlin/au21/engine/domain/de/services/state/control_state_viewmodel.kt
================
package au21.engine.domain.de.services.state

import au21.engine.domain.de.model.*
import au21.engine.domain.de.model.DeAuctioneerState.*
import au21.engine.domain.de.model.DeFlowControlType.*


fun DeAuction.de_controls(): Map<DeFlowControlType, Boolean> =
    enumValues<DeFlowControlType>()
        .filter { it != HEARTBEAT }
        .associateWith {
            when (it) {













                SET_STARTING_PRICE -> when (auctioneer_state) {
                    STARTING_PRICE_NOT_SET,
                    STARTING_PRICE_SET,
                    STARTING_PRICE_ANNOUNCED -> true
                    else -> false
                }
                ANNOUNCE_STARTING_PRICE -> auctioneer_state == STARTING_PRICE_SET
                START_AUCTION -> auctioneer_state == STARTING_PRICE_ANNOUNCED
                CLOSE_ROUND -> auctioneer_state.common_state() == DeCommonState.ROUND_OPEN
                REOPEN_ROUND,
                NEXT_ROUND -> auctioneer_state == DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE
                AWARD_AUCTION -> auctioneer_state == ROUND_CLOSED_AWARDABLE
                HEARTBEAT -> throw Error("HEARTBEAT should have been filtered out.")
            }
        }

================
File: main/kotlin/au21/engine/domain/de/services/state/DeControlValidator.kt
================
package au21.engine.domain.de.services.state

import au21.engine.domain.common.model.AutopilotMode
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeAuctioneerState
import au21.engine.domain.de.model.DeFlowControlType
import au21.engine.domain.de.model.is_awardable
import org.joda.time.DateTime

object DeControlValidator {

    const val MUTATE = "MUTATE"








    fun validate(de: DeAuction, command: DeFlowControlType): String? {




        val now = DateTime().toDate()

        return when (command) {



















            DeFlowControlType.HEARTBEAT ->
                when (de.autopilot) {
                    AutopilotMode.DISENGAGED -> null
                    AutopilotMode.ENGAGED ->
                        when (de.auctioneer_state) {
                            DeAuctioneerState.STARTING_PRICE_NOT_SET -> null
                            DeAuctioneerState.STARTING_PRICE_SET ->
                                when (now.after(de.announce_time()) && de.firstround().price != null) {
                                    true -> MUTATE
                                    else -> null
                                }
                            DeAuctioneerState.STARTING_PRICE_ANNOUNCED ->
                                when (now.after(de.starting_time) && de.firstround().price != null) {
                                    true -> MUTATE
                                    else -> null
                                }
                            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> null
                            DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> null





                            DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE ->
                                TODO()





                            DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> null


                            DeAuctioneerState.AUCTION_CLOSED -> null
                        }
                }
            DeFlowControlType.SET_STARTING_PRICE ->
                when (de.auctioneer_state) {
                    DeAuctioneerState.STARTING_PRICE_NOT_SET -> MUTATE
                    DeAuctioneerState.STARTING_PRICE_SET -> MUTATE
                    DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> MUTATE
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> "Cannot set price after auction started."
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> "Cannot set price after auction started."
                    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> "Cannot set price after auction started."
                    DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> "Cannot set price after auction started."
                    DeAuctioneerState.AUCTION_CLOSED -> "Auction closed."
                }
            DeFlowControlType.ANNOUNCE_STARTING_PRICE ->
                when (de.auctioneer_state) {
                    DeAuctioneerState.STARTING_PRICE_NOT_SET -> "Starting price must be set before you can announce it."
                    DeAuctioneerState.STARTING_PRICE_SET -> MUTATE
                    DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> null
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> "Auction already started."
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> "Auction already started."
                    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> "Auction already started."
                    DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> "Auction already started."
                    DeAuctioneerState.AUCTION_CLOSED -> "Auction closed."
                }
            DeFlowControlType.START_AUCTION ->
                when (de.auctioneer_state) {
                    DeAuctioneerState.STARTING_PRICE_NOT_SET -> "Cannot start auction before starting price announced."
                    DeAuctioneerState.STARTING_PRICE_SET -> "Cannot start auction buferu starting price announced."
                    DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> MUTATE
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> null
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> null
                    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> "Auction already started."
                    DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> "Auction already started."
                    DeAuctioneerState.AUCTION_CLOSED -> "Auction closed."
                }
            DeFlowControlType.CLOSE_ROUND ->
                when (de.auctioneer_state) {
                    DeAuctioneerState.STARTING_PRICE_NOT_SET -> "Auction not started."
                    DeAuctioneerState.STARTING_PRICE_SET -> "Auction not started."
                    DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> "Auction not started."
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> MUTATE
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> MUTATE
                    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> null
                    DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> null
                    DeAuctioneerState.AUCTION_CLOSED -> "Auction closed."
                }
            DeFlowControlType.REOPEN_ROUND -> {
                val msg = "Reset only possible when round is closed."
                return when (de.auctioneer_state) {
                    DeAuctioneerState.STARTING_PRICE_NOT_SET,
                    DeAuctioneerState.STARTING_PRICE_SET,
                    DeAuctioneerState.STARTING_PRICE_ANNOUNCED,
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN,
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> msg
                    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> MUTATE
                    DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> MUTATE
                    DeAuctioneerState.AUCTION_CLOSED -> "Auction closed."
                }
            }
            DeFlowControlType.NEXT_ROUND ->
                if (de.is_awardable())
                    "Auction awardable, cannot continue"
                else when (de.auctioneer_state) {
                    DeAuctioneerState.STARTING_PRICE_NOT_SET -> "Auction not started."
                    DeAuctioneerState.STARTING_PRICE_SET -> "Auction not started."
                    DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> "Auction not started"
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> "Current round still open"
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> "Current round still open"
                    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> MUTATE
                    DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> MUTATE
                    DeAuctioneerState.AUCTION_CLOSED -> "Auction Closed."
                }
            DeFlowControlType.AWARD_AUCTION ->
                when (de.auctioneer_state) {
                    DeAuctioneerState.STARTING_PRICE_NOT_SET -> "Auction not started."
                    DeAuctioneerState.STARTING_PRICE_SET -> "Auction not started."
                    DeAuctioneerState.STARTING_PRICE_ANNOUNCED -> "Auction not started."
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> "Round still open."
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> "Round still open."
                    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> "Auction not awardable"
                    DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> MUTATE
                    DeAuctioneerState.AUCTION_CLOSED -> "Auction closed."
                }
        }
    }

}

================
File: main/kotlin/au21/engine/domain/de/services/state/DeMutator.kt
================
package au21.engine.domain.de.services.state

import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeAuctioneerState
import au21.engine.domain.de.model.DeFlowControlType
import au21.engine.domain.de.model.DeFlowControlType.*
import au21.engine.domain.de.model.is_awardable
import au21.engine.domain.de.services.rounds.create_subsequent_round
import io.quarkus.logging.Log
import org.joda.time.DateTime

object DeMutator {











    fun mutate(de: DeAuction, command: DeFlowControlType, starting_price: Double?): String? {
        Log.debug("Mutate $command")















        fun set_starting_price(): String {
            if (starting_price == null)
                throw Error("Cannot set null starting price")
            de.set_starting_price(starting_price)
            if (de.auctioneer_state != DeAuctioneerState.STARTING_PRICE_ANNOUNCED)
                de.setState(DeAuctioneerState.STARTING_PRICE_SET)
            return "starting price set at: $starting_price"
        }

        fun announce_starting_price(): String {
            de.setState(DeAuctioneerState.STARTING_PRICE_ANNOUNCED)
            return "starting price announced"
        }

        fun start_auction(): String {
            de.setState(DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN)

            return "Auction started, round open"
        }

        fun close_round(): String {
            if (de.is_awardable()) {
                de.setState(DeAuctioneerState.ROUND_CLOSED_AWARDABLE)
            } else {
                de.setState(DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE)
            }


            return "round closed, current state = ${de.auctioneer_state}"
        }

        fun reopen_round(): String = run {

            TODO("reset round not implemented")



        }

        fun next_round(): String = run {
            de.create_subsequent_round()
            de.setState(DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN)
            "going to next round"
        }

        fun award_auction(): String {
            de.setState(DeAuctioneerState.AUCTION_CLOSED)
            return "auction closed"
        }

        val now = DateTime().toDate()

        return when (command) {


            HEARTBEAT ->



                when (de.auctioneer_state) {
                    DeAuctioneerState.STARTING_PRICE_NOT_SET -> null
                    DeAuctioneerState.STARTING_PRICE_SET ->
                        when (now.after(de.announce_time()) && de.firstround().price != null) {
                            true -> announce_starting_price()
                            false -> null
                        }
                    DeAuctioneerState.STARTING_PRICE_ANNOUNCED ->
                        when (now.after(de.starting_time) && de.firstround().price != null) {
                            true -> start_auction()
                            false -> null
                        }
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN -> null
                    DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN -> close_round()
                    DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE -> next_round()
                    DeAuctioneerState.ROUND_CLOSED_AWARDABLE -> null
                    DeAuctioneerState.AUCTION_CLOSED -> null
                }
            SET_STARTING_PRICE -> set_starting_price()
            ANNOUNCE_STARTING_PRICE -> announce_starting_price()
            START_AUCTION -> start_auction()
            CLOSE_ROUND -> close_round()
            REOPEN_ROUND -> reopen_round()
            NEXT_ROUND -> next_round()
            AWARD_AUCTION -> award_auction()
        }

    }

}

================
File: main/kotlin/au21/engine/domain/de/services/state/test.kt
================
package au21.engine.domain.de.services.state

import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeAuctionSettings
import au21.engine.domain.de.model.DeFlowControlType
import au21.engine.domain.de.model.DePriceRule
import au21.engine.domain.de.services.state.DeControlValidator.MUTATE
import org.joda.time.DateTime
import kotlin.concurrent.thread

const val TICK_SECS = 3

fun main() {

    val de = DeAuction(
        auction_name = "auction 1",
        settings = DeAuctionSettings(
            use_counterparty_credits = false,
            round_open_min_secs = 5,
            round_closed_min_secs = 5,
            round_orange_secs = 15,
            round_red_secs = 30,
            starting_price_announcement_mins = 1,
            price_rule = DePriceRule()
        )
    ).apply {
        starting_time = DateTime().plusMinutes(1).toDate()
    }

    fun validate_and_mutate(command: DeFlowControlType) {
        when (val result: String? = DeControlValidator.validate(de, command)) {
            null -> {
            }
            MUTATE -> DeMutator.mutate(de, command, null)
            else -> println(result)
        }
    }

    thread {
        while (true) {
            try {
                validate_and_mutate(DeFlowControlType.HEARTBEAT)
                Thread.sleep(TICK_SECS * 1_000L)
            } catch (e: Throwable) {
                println("error: " + e.message)
            }
        }
    }




    while (true) {
        readLine()?.let { x ->
            try {
                println("pre: ${de.auctioneer_state}")
                when (x.uppercase()) {


                    "BEAT" -> validate_and_mutate(DeFlowControlType.HEARTBEAT)
                    "PRICE" -> validate_and_mutate(DeFlowControlType.SET_STARTING_PRICE)
                    "ANNOUNCE" -> validate_and_mutate(DeFlowControlType.ANNOUNCE_STARTING_PRICE)
                    "START" -> validate_and_mutate(DeFlowControlType.START_AUCTION)
                    "CLOSE" -> validate_and_mutate(DeFlowControlType.CLOSE_ROUND)
                    "RESET" -> validate_and_mutate(DeFlowControlType.REOPEN_ROUND)
                    "NEXT" -> validate_and_mutate(DeFlowControlType.NEXT_ROUND)
                    "AWARD" -> validate_and_mutate(DeFlowControlType.AWARD_AUCTION)
                    else -> println("command not understood: $x")
                }
                println("post: ${de.auctioneer_state}")
                println(de.de_controls())
                println("------------------------------------------------------\n")
            } catch (e: NotImplementedError) {
                println("$x not implemented")
            } catch (e: Throwable) {

            }
        }


    }
}

================
File: main/kotlin/au21/engine/domain/de/services/de-queries.kt
================
package au21.engine.domain.de.services

import au21.engine.domain.de.model.DeAuction
import au21.engine.framework.database.AuEntityManager
import io.quarkus.logging.Log

fun AuEntityManager.open_de_auctions(): List<DeAuction> =
    try {
        val query = "SELECT de FROM ${DeAuction::class.java.name} de where de.closed=false"
        em.createQuery(query, DeAuction::class.java).resultList
    } catch (e: Exception) {
        Log.info(e.message)


        listOf()
    }

================
File: main/kotlin/au21/engine/domain/de/validations/de_order_validations.kt
================
package au21.engine.domain.de.validations

import au21.engine.domain.common.model.OrderType
import au21.engine.domain.de.model.DeBidConstraints
import au21.engine.framework.commands.fail_if
import au21.engine.framework.utils.thousands









fun validate_de_order_against_constraints_and_throw_if_fails(
    constraints: DeBidConstraints,
    order_type: OrderType,
    order_quantity: Int,
    quantity_units: String,
) {




    fun with_units(vol: Int): String = "${vol.thousands()} $quantity_units"

    return when (order_type) {
        OrderType.NONE -> {
            fail_if(order_quantity > 0, "Order quantity must be zero if OrderType is None")
            fail_if(
                constraints.min_buy_quantity > 0,
                "You cannot submit a zero quantity bid. It is less than your min buy quantity of ${
                    with_units(constraints.min_buy_quantity)
                }"
            )
            fail_if(
                constraints.min_sell_quantity > 0,
                "You cannot submit a zero quantity bid. It is less than your min sell quantity of ${
                    with_units(constraints.min_sell_quantity)
                }"
            )
        }
        OrderType.BUY -> {
            fail_if(
                constraints.min_sell_quantity > 0,
                "You cannot submit a buy order when you have a minimum sell quantity constraint of (${
                    with_units(constraints.min_sell_quantity)
                })"
            )
            fail_if(
                order_quantity > constraints.max_buy_quantity,
                "You cannot buy more than your maximum buy quantity of ${
                    with_units(constraints.max_buy_quantity)
                }"
            )

            fail_if(
                order_quantity < constraints.min_buy_quantity,
                "You cannot buy less than your minimum buy quantity of ${
                    with_units(constraints.min_buy_quantity)
                }"
            )
        }
        OrderType.SELL -> {
            fail_if(
                constraints.min_buy_quantity > 0,
                "You cannot submit a sell order when you have a minimum buy quantity constraint of (${
                    with_units(constraints.min_buy_quantity)
                })"
            )
            fail_if(
                order_quantity > constraints.max_sell_quantity,
                "You cannot sell more than your maximum sell quantity of ${with_units(constraints.max_sell_quantity)}"
            )

            fail_if(
                order_quantity < constraints.min_sell_quantity,
                "You cannot sell less than your minimum sell quantity of ${with_units(constraints.min_sell_quantity)}"
            )

        }
    }

}

================
File: main/kotlin/au21/engine/domain/de/viewmodel/de-blotter.kt
================
package au21.engine.domain.de.viewmodel


import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.OrderSubmissionType
import au21.engine.domain.common.model.OrderType
import au21.engine.domain.common.model.PriceDirection
import au21.engine.domain.de.model.*
import au21.engine.framework.client.StoreElement
import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.AuFormatter
import au21.engine.framework.utils.thousands


data class DeBlotter(
    val rounds: List<DeRoundElement>,
    val traders: List<DeTraderElement>,
    val round_traders: List<DeRoundTraderElement>,
) {
    companion object {
        fun create(de: DeAuction?) = DeBlotter(
            rounds = when (de) {
                null -> emptyList()
                else -> de.rounds.map { r: DeRound -> DeRoundElement.create(de, r) }
            },
            traders = when (de) {
                null -> emptyList()
                else -> de.de_trading_companies
                    .map { t: DeTradingCompany -> DeTraderElement.create(de, t) }
                    .sortedBy { it.company_id }
            },
            round_traders = when (de) {
                null -> emptyList()
                else -> de.rounds.flatMap { r: DeRound ->
                    r.trader_infos.map { rti: DeRoundTraderInfo ->
                        DeRoundTraderElement.create(r, rti)
                    }.sortedBy { it.id }
                }
            }
        )
    }
}

data class DeRoundElement(
    override val id: String,
    val all_orders_in_next_round_will_be_mandatory:Boolean,
    val buy_quantity: Int,
    val buyer_count: Int,
    val excess_side: OrderType,
    val excess_indicator: String,
    val excess_quantity: Int,
    val has_reversed: Boolean,
    val match_quantity_changed: Int,
    val matched: Int,
    val potential: Int,
    val potential_changed: Int,
    val raw_matched: Int,
    val round_direction: PriceDirection?,
    val round_duration: String,
    val round_number: Int,
    val round_price: Double?,
    val round_price_str: String,
    val sell_quantity_change: Int,
    val sell_quantity: Int,
    val seller_count: Int

) : StoreElement {


    companion object {

        fun create(
            a: DeAuction,
            r: DeRound,
            prev: DeRound? = a.penultimate(),

            n_matched: Int = r.match_vol(),
            prev_matched: Int = prev?.match_vol() ?: 0
        ) = DeRoundElement(
            id = "ROUND.${r.number}",
            all_orders_in_next_round_will_be_mandatory = a.de_trading_companies.all { it.next_round_order_will_be_mandatory },
            buy_quantity = a.total_buy(r),
            buyer_count = a.buyer_count(r),
            excess_side = a.excess_side(r),
            excess_indicator = a.excess_level(r, AuUserRole.AUCTIONEER),
            excess_quantity = a.excess_quantity(r),
            has_reversed = r.has_reversed,
            match_quantity_changed = (n_matched - prev_matched),
            matched = n_matched,
            potential = r.max_potential_flow,
            potential_changed = (r.max_potential_flow - (prev?.max_potential_flow ?: 0)),
            raw_matched = n_matched,
            round_direction = r.price_direction,
            round_duration = "not implemented",
            round_number = r.number,
            round_price = r.price,
            round_price_str = a.format_round_price(AuUserRole.AUCTIONEER, r),
            sell_quantity_change = a.total_sell(r) - (prev?.let { a.total_sell(it) } ?: 0),
            sell_quantity = a.total_sell(r),
            seller_count = a.seller_count(r),
        )
    }
}

data class DeTraderElement(
    override val id: String,
    val has_seen_auction: Boolean,
    val company_id: String,
    val shortname: String,
    val rank: Int?,






) : StoreElement {






















    companion object {

        fun create(a: DeAuction, t: DeTradingCompany): DeTraderElement {






            return DeTraderElement(
                id = "COMPANY.${t.company_id}",
                has_seen_auction = a.companies_that_have_seen_auction.contains(t),
                company_id = t.company_id.toString(),
                shortname = t.shortname_at_auction_time,
                rank = t.rank,







            )
        }
    }
}


data class DeRoundTraderElement(
    override val id: String,

    val bid_while_closed: Boolean,
    val buyer_credit_limit:Double,
    val buyer_credit_limit_str: String,


    val changed: Boolean,
    val cid: String,
    val company_shortname:String,
    val constraints: DeBidConstraints,
    val match: Int,
    val order_submitted_by: String,
    val order_submission_type: OrderSubmissionType,
    val order_type: OrderType,
    val round: Int,
    val timestamp_formatted: String,
    val quantity_int: Int,
    val quantity_str: String,
) : StoreElement {

    companion object {

        fun create(

            r: DeRound,
            rti: DeRoundTraderInfo,
        ): DeRoundTraderElement {

            val o: DeOrder = rti.order
            val t: DeTradingCompany = rti.de_trading_company

            return DeRoundTraderElement(

                id = "ROUND.${r.number}.COMPANY.${t.company_id}",
                bid_while_closed = rti.bid_while_closed,
                buyer_credit_limit = t.initial_limits.initial_buying_cost_limit,
                buyer_credit_limit_str = t.initial_limits.initial_buying_cost_limit_str,

                changed = o.prev_order?.submission_type == OrderSubmissionType.MANUAL,
                cid = t.company_id.toString(),
                company_shortname = t.shortname_at_auction_time,
                constraints = rti.constraints,
                match = r.getMatch(t)?.match ?: 0,
                order_submitted_by = when (o.submission_type) {
                    OrderSubmissionType.MANUAL -> o.user?.username_at_auction_time
                        ?: throw AlertException("If order is manual, then must have a username")
                    OrderSubmissionType.DEFAULT -> "default"
                    OrderSubmissionType.MANDATORY -> "mandatory"
                },
                order_submission_type = o.submission_type,
                order_type = o.type,
                quantity_int = o.quantity,
                quantity_str = o.quantity.thousands(),
                round = r.number,
                timestamp_formatted = AuFormatter.format_order_time(o.timestamp),
            )
        }
    }
}

================
File: main/kotlin/au21/engine/domain/de/viewmodel/de-matrix.kt
================
package au21.engine.domain.de.viewmodel

import au21.engine.domain.common.model.OrderType
import au21.engine.domain.de.model.*
import au21.engine.framework.client.ClientCommand.StoreCommand.AddElements
import au21.engine.framework.client.StoreElement

data class DeMatrixRoundElement(
    override val id: String,
    val round_number: Int,
    val nodes: List<DeMatrixNodeElement>,
    val edges: List<DeMatrixEdgeElement>,
) : StoreElement {

    companion object {

        fun create(r: DeRound, cost_multiplier: Double) = DeMatrixRoundElement(
            id = r.let { "Round.${r.number}" },
            round_number = r.number,
            nodes = DeMatrixNodeElement.create(r),
            edges = DeMatrixEdgeElement.create(r, cost_multiplier)
        )

        fun clear_rounds(): AddElements =
            AddElements(
                DeMatrixRoundElement::class.java,
                null
            )

        fun add_rounds(de: DeAuction, deRounds: List<DeRound>): AddElements =
            AddElements(
                DeMatrixRoundElement::class.java,
                deRounds.map { create(it, de.settings.cost_multiplier) })

    }
}

data class DeMatrixNodeElement(
    override val id: String,
    val round: Int,

    val cid: String,
    val shortname: String,

    val buy_match: Int,
    val buy_max: Int,
    val buy_min: Int,
    val buy_quantity: Int,

    val sell_match: Int,
    val sell_max: Int,
    val sell_min: Int,
    val sell_quantity: Int,


    val side: OrderType,
    val cost: Double,
    val cost_str: String,

    ) : StoreElement {










    companion object {
        fun create(
            r: DeRound
        ): List<DeMatrixNodeElement> =

            r.trader_infos.map { rbi: DeRoundTraderInfo ->
                val constraints: DeBidConstraints = rbi.constraints
                val round_num: Int = r.number
                val cid: String = rbi.de_trading_company.company_id.toString()
                val shortname: String = rbi.de_trading_company.shortname_at_auction_time

                DeMatrixNodeElement(
                    id = "R_${round_num}_T_${cid}",


                    round = round_num,
                    cid,
                    shortname,

                    buy_match = r.matches
                        .filter { rbi.de_trading_company.company_id == it.buy_order.trading_company.company_id }
                        .sumOf { it.match },
                    buy_max = constraints.max_buy_quantity,
                    buy_min = constraints.min_buy_quantity,
                    buy_quantity = r.buy_quantity(rbi.de_trading_company),

                    sell_match = r.matches
                        .filter { rbi.de_trading_company.company_id == it.sell_order.trading_company.company_id }
                        .sumOf { it.match },
                    sell_max = constraints.max_sell_quantity,
                    sell_min = constraints.min_sell_quantity,
                    sell_quantity = r.sell_quantity(rbi.de_trading_company),

                    side = rbi.order.type,
                    cost = rbi.order.cost,
                    cost_str = rbi.order.cost_str
                )
            }.sortedBy { it.id }
    }
}


data class DeMatrixEdgeElement(
    override val id: String,
    val r: Int,

    val buyer_shortname: String,
    val buyer_cid: String,

    val seller_shortname: String,
    val seller_cid: String,



    val credit_quantity_limit: Int,
    val buy_quantity_limit: Int,
    val selling_quantity_limit: Int,
    val capacity: Int,
    val match: Int,


    val value: Double,
    val value_str: String,

    val credit_str: String,

) : StoreElement {

    companion object {


        fun to_id(
            round_num: Int,
            b_cid: String,
            s_cid: String,
        ): String =
            "R_${round_num}_B_${b_cid}_S_${s_cid}"

        fun create(deRound: DeRound, cost_multiplier: Double): List<DeMatrixEdgeElement> =
            deRound.matches.map { m: DeMatch ->
                val round_number = deRound.number
                val b_cid = m.buy_order.trading_company.company_id.toString()
                val s_cid = m.sell_order.trading_company.company_id.toString()

                DeMatrixEdgeElement(
                    id = to_id(round_number, b_cid = b_cid, s_cid = s_cid),
                    r = round_number,

                    buyer_shortname = m.buyer_shortname,
                    buyer_cid = b_cid,

                    seller_shortname = m.seller_shortname,
                    seller_cid = s_cid,

                    credit_quantity_limit = m.selling_quantity_limit,
                    buy_quantity_limit = m.buy_limit,
                    selling_quantity_limit = m.sell_limit,
                    capacity = m.capacity,
                    match = m.match,

                    credit_str = m.credit_str,
                    value = m.value,
                    value_str = m.value_str






                )
            }
    }

}

================
File: main/kotlin/au21/engine/domain/de/viewmodel/DeAuctioneerInfoValue.kt
================
package au21.engine.domain.de.viewmodel

import au21.engine.domain.common.model.PriceDirection
import au21.engine.domain.de.model.*
import au21.engine.framework.client.StoreValue
import au21.engine.framework.utils.thousands
import kotlin.math.abs

data class DeAuctioneerInfoValue(
    val allow_credit_editing: Boolean,
    val pen_round: String,
    val last_round: Int,
    val pen_buyers: String,
    val last_buyers: String,
    val pen_sellers: String,
    val last_sellers: String,
    val pen_total_buy: String,
    val last_total_buy: String,
    val pen_total_sell: String,
    val last_total_sell: String,
    val pen_sell_dec: String,
    val last_sell_dec: String,
    val pen_match: String,
    val last_match: String,
    val pen_excess: String,
    val last_excess: String,
    val potential: String
) : StoreValue {

    companion object {

        fun create(de: DeAuction): DeAuctioneerInfoValue {







            val n: DeRound? = de.lastround()
            val pen: DeRound? = de.penultimate()


            return DeAuctioneerInfoValue(

                allow_credit_editing = false,

                pen_round = pen?.number?.toString() ?: "---",
                last_round = n?.number ?: 0,

                pen_buyers = pen?.let { de.buyer_count(it).toString() } ?: "---",
                last_buyers = n?.let { de.buyer_count(it) }?.toString() ?: "---",

                pen_sellers = pen?.let { de.seller_count(it).toString() } ?: "---",
                last_sellers = n?.let { de.seller_count(it).toString() } ?: "---",

                pen_total_buy = pen?.let { de.total_buy(pen).thousands() } ?: "---",
                last_total_buy = n?.let { de.total_buy(it).thousands() } ?: "---",

                pen_total_sell = pen?.let { de.total_sell(pen).thousands() } ?: "---",
                last_total_sell = n?.let { de.total_sell(n) }.thousands(),

                pen_sell_dec = "", //pen ? format_thousands ( total_decrement ( a, pen ) ) : '---'
                last_sell_dec = "", // n.round_number == 1 ? '---' : format_thousands ( total_decrement ( a, n ) )

                pen_match = pen?.match_vol()?.thousands() ?: "---",
                last_match = n?.match_vol()?.thousands() ?: "---",

                pen_excess = pen?.let { r ->
                    val demand = de.total_buy(r)
                    val supply = de.total_sell(r)
                    when (r.price_direction) {
                        PriceDirection.UP -> demand - supply
                        PriceDirection.DOWN -> supply - demand
                        null -> abs(demand - supply) // FIRST ROUND WON'T HAVE a price direction!
                    }.thousands()
                } ?: "---",

                last_excess = n?.let { r ->
                    val demand = de.total_buy(r)
                    val supply = de.total_sell(r)
                    when (r.price_direction) {
                        PriceDirection.UP -> demand - supply
                        PriceDirection.DOWN -> supply - demand
                        null -> abs(demand - supply) // FIRST ROUND WON'T HAVE a price direction!
                    }.thousands()
                } ?: "---",

                potential = n?.let { it.max_potential_flow.thousands() } ?: "---"

            )
        }
    }
}

================
File: main/kotlin/au21/engine/domain/de/viewmodel/DeAuctioneerStatusValue.kt
================
package au21.engine.domain.de.viewmodel

import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.AutopilotMode
import au21.engine.domain.common.model.OrderType
import au21.engine.domain.de.model.*
import au21.engine.domain.de.services.state.de_controls
import au21.engine.framework.client.StoreValue
import org.joda.time.DateTime
import org.joda.time.Period


data class DeAuctioneerStatusValue(
    val announced: Boolean,
    val auctioneer_state: DeAuctioneerState,
    val auctioneer_state_text: String,
    val autopilot: AutopilotMode,
    val awardable: Boolean = false,
    val controls: Map<DeFlowControlType, Boolean>,
    val excess_side: OrderType,
    val excess_level: String,

    val price_has_overshot: Boolean,
    val round_open_min_secs: Int?,

    val starting_price: String,
    val time_state: DeTimeState?,
) : StoreValue {




    companion object {
        fun create(de: DeAuction): DeAuctioneerStatusValue {

            val n: DeRound = de.lastround()

            return DeAuctioneerStatusValue(
                announced = de.starting_price_announced(),
                auctioneer_state = de.auctioneer_state,
                auctioneer_state_text = de.auctioneer_state_text,
                autopilot = de.autopilot,
                awardable = false,
                controls = de.de_controls(),
                excess_side = de.excess_side(n),
                excess_level = de.excess_level(n, AuUserRole.AUCTIONEER),

                price_has_overshot = de.price_has_overshot(),
                round_open_min_secs = n.open_time?.let {
                    Period(DateTime(), DateTime(it)).seconds
                },

                starting_price = de.format_round_price(AuUserRole.AUCTIONEER, de.firstround()),
                time_state = de.time_state()
            )
        }
    }


}

================
File: main/kotlin/au21/engine/domain/de/viewmodel/DeAuctionValue.kt
================
package au21.engine.domain.de.viewmodel

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.viewmodel.CounterpartyCreditElement
import au21.engine.domain.common.viewmodel.MessageElement
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeTradingCompany
import au21.engine.framework.client.StoreValue


data class DeAuctionValue(
    val auction_id: String?,
    val auction_counterparty_credits: List<CounterpartyCreditElement>,
    val auctioneer_info: DeAuctioneerInfoValue?,
    val auctioneer_status: DeAuctioneerStatusValue?,
    val award_value: DeAwardValue?,
    val blotter: DeBlotter?,
    val common_status: DeCommonStatusValue?,
    val matrix_last_round: DeMatrixRoundElement?,
    val messages: List<MessageElement>,
    val notice: String,
    val settings: DeSettingsValue?,
    val trader_history_rows: List<DeTraderHistoryRowElement>,
    val trader_info: DeTraderInfoValue?,
    val users_that_have_seen_auction: Set<String>

) : StoreValue {

    companion object {

        val value_for_null_auction =
            DeAuctionValue(
                auction_id = null,
                auction_counterparty_credits = emptyList(),
                auctioneer_info = null,
                auctioneer_status = null,
                award_value = null,
                blotter = DeBlotter.create(null),
                matrix_last_round = null,
                messages = emptyList(),
                notice = "",
                settings = null,
                common_status = null,
                trader_history_rows = emptyList(),
                trader_info = null,
                users_that_have_seen_auction = emptySet()
            )



        fun value_for_auctioneer(
            de: DeAuction,
            s: AuSession,
            auction_counterparty_credits: List<CounterpartyCreditElement>
        ) =
            DeAuctionValue(
                auction_id = de.id_str(),
                auction_counterparty_credits = auction_counterparty_credits,
                auctioneer_info = DeAuctioneerInfoValue.create(de),
                auctioneer_status = DeAuctioneerStatusValue.create(de),
                award_value = DeAwardValue.create(de),
                blotter = DeBlotter.create(de),
                matrix_last_round = DeMatrixRoundElement.create(de.lastround(), de.settings.cost_multiplier),
                messages = MessageElement.message_elements_for_session(de, s),
                notice = de.notice,
                settings = DeSettingsValue.create(de),
                common_status = DeCommonStatusValue.create(de),
                trader_history_rows = emptyList(),
                trader_info = null,
                users_that_have_seen_auction = de.users_that_have_seen_auction.map { it.person_id.toString() }.toSet()
            )


        fun value_for_trader(de: DeAuction, s: AuSession, t: DeTradingCompany) =
            DeAuctionValue(
                auction_id = de.id_str(),
                auction_counterparty_credits = emptyList(),
                auctioneer_info = null,
                auctioneer_status = null,
                award_value = null,
                blotter = DeBlotter.create(null),
                matrix_last_round = null,
                messages = MessageElement.message_elements_for_session(de, s),
                notice = de.notice,
                settings = DeSettingsValue.create(de),
                common_status = DeCommonStatusValue.create(de),
                trader_history_rows = de.rounds.map { r -> DeTraderHistoryRowElement.create(de, r, t) },
                trader_info = DeTraderInfoValue.create(de, t),
                users_that_have_seen_auction = emptySet()
            )

    }
}

================
File: main/kotlin/au21/engine/domain/de/viewmodel/DeAwardValue.kt
================
package au21.engine.domain.de.viewmodel

import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.OrderType
import au21.engine.domain.de.model.*
import au21.engine.framework.client.StoreValue
import au21.engine.framework.utils.thousands
import kotlin.math.min

data class DeAwardValue(val round_results: List<DeRoundResultVM>) : StoreValue {




    companion object {
        fun create(de: DeAuction) = DeAwardValue(
            round_results = de.rounds
                .takeLast(min(de.rounds.size, 3))
                .reversed()
                .map { DeRoundResultVM.create(de, it) }
        )
    }
}

data class DeRoundResultVM(
    val round_number: Int,
    val round_price: String,
    val buy_total: String,
    val sell_total: String,
    val match_total: String,
    val trader_flows: List<DeTraderFlowVM>,
    val matches: List<DeScenarioMatchVM>
) {

    companion object {
        fun create(de: DeAuction, r: DeRound) =
            DeRoundResultVM(
                round_number = r.number,
                round_price = de.format_round_price(AuUserRole.AUCTIONEER, r),
                buy_total = r.buy_orders().sumOf { it.quantity }.thousands(),
                sell_total = r.sell_orders().sumOf { it.quantity }.thousands(),
                match_total = r.match_vol().thousands(),
                trader_flows = de.de_trading_companies.map { t: DeTradingCompany ->
                    DeTraderFlowVM(
                        company_shortname = t.shortname_at_auction_time,
                        company_id = t.company_id.toString(),
                        order_type = r.get_order(t).type,
                        quantity = r.match_vol(t).thousands()
                    )
                }.sortedBy { it.company_id.toInt() },
                matches = r.matches.map { m: DeMatch ->
                    DeScenarioMatchVM.create(r, m)
                }.sortedBy { it.buyer_id }
            )
    }
}

data class DeTraderFlowVM(
    val company_shortname: String,
    val company_id: String,
    val order_type: OrderType,
    val quantity: String
)

data class DeScenarioMatchVM(
    val round_number: Int,
    val buyer_id: String,
    val buyer_shortname: String,
    val seller_id: String,
    val seller_shortname: String,
    val actual_match: Int,
    val actual_match_str: String
) {

    companion object {
        fun create(r: DeRound, m: DeMatch) = DeScenarioMatchVM(
            round_number = r.number,
            buyer_id = m.buy_order.trading_company.company_id.toString(),
            buyer_shortname = m.buyer_shortname,
            seller_id = m.sell_order.trading_company.company_id.toString(),
            seller_shortname = m.seller_shortname,
            actual_match = m.match,
            actual_match_str = m.match.thousands()
        )
    }
}

================
File: main/kotlin/au21/engine/domain/de/viewmodel/DeCommonStatusValue.kt
================
package au21.engine.domain.de.viewmodel

import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.PriceDirection
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeCommonState
import au21.engine.domain.de.model.DeRound
import au21.engine.domain.de.model.format_round_price
import au21.engine.framework.client.StoreValue

data class DeCommonStatusValue(



    val common_state: DeCommonState,
    val common_state_text: String,
    val isClosed: Boolean,
    val price_direction: PriceDirection?,
    val price_has_reversed: Boolean,
    val round_number: Int,
    val round_price: String,
    val round_seconds: Int,
    val starting_price_announced: Boolean,
    val starting_time_text: String,
) : StoreValue {






    companion object {
        fun create(de: DeAuction): DeCommonStatusValue {




            val n: DeRound = de.lastround()

            return DeCommonStatusValue(
                isClosed = de.closed,
                price_direction = n.price_direction,
                price_has_reversed = n.has_reversed,
                round_number = n.number,
                round_seconds = n.round_open_seconds(),
                round_price = when (de.starting_price_announced()) {
                    false -> "waiting"
                    true -> de.format_round_price(AuUserRole.TRADER, n)
                },
                starting_price_announced = de.starting_price_announced(),
                starting_time_text = de.starting_time_text(),
                common_state = de.common_state,
                common_state_text = de.common_state_text
            )
        }
    }
}

================
File: main/kotlin/au21/engine/domain/de/viewmodel/DeSettingsValue.kt
================
package au21.engine.domain.de.viewmodel

import au21.engine.domain.common.viewmodel.DateTimeValue
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeAuctionSettings
import au21.engine.domain.de.model.DePriceRule
import au21.engine.framework.client.StoreValue
import au21.engine.framework.utils.AuFormatter
import au21.engine.framework.utils.thousands
import org.joda.time.DateTime

data class DeSettingsValue(

    val auction_name: String,
    val use_counterparty_credits: Boolean,




    val price_change_initial: String,
    val price_change_post_reversal: String,
    val price_decimal_places: Int,
    val price_label: String,

    val excess_level_0_label: String,
    val excess_level_1_label: String,
    val excess_level_2_label: String,
    val excess_level_3_label: String,
    val excess_level_4_label: String,

    val excess_level_1_quantity: String,
    val excess_level_2_quantity: String,
    val excess_level_3_quantity: String,
    val excess_level_4_quantity: String,

    val cost_multiplier: String,
    val quantity_label: String,
    val quantity_minimum: String,
    val quantity_step: String,





    val starting_price_announcement_mins: Int,


    val starting_time: DateTimeValue?,

    val round_red_secs: Int,
    val round_orange_secs: Int,
    val round_open_min_secs: Int,
    val round_closed_min_secs: Int,

    ) : StoreValue {















    companion object {

        fun create(de: DeAuction): DeSettingsValue {

            val settings: DeAuctionSettings = de.settings
            val pr: DePriceRule = settings.price_rule
            val places: Int = settings.price_decimal_places
            val starting_date_time: DateTime? = de.starting_time?.let { DateTime(it) }

            return DeSettingsValue(


                auction_name = de.auction_name,
                use_counterparty_credits = settings.use_counterparty_credits,




                price_change_initial = AuFormatter.format_to_places(pr.price_change_initial, places),
                price_change_post_reversal = AuFormatter.format_to_places(pr.price_change_post_reversal, places),
                price_decimal_places = settings.price_decimal_places,
                price_label = settings.price_units,

                excess_level_0_label = pr.excess_level_0_label,
                excess_level_1_label = pr.excess_level_1_label,
                excess_level_2_label = pr.excess_level_2_label,
                excess_level_3_label = pr.excess_level_3_label,
                excess_level_4_label = pr.excess_level_4_label,

                excess_level_1_quantity = pr.excess_level_1_quantity.thousands(),
                excess_level_2_quantity = pr.excess_level_2_quantity.thousands(),
                excess_level_3_quantity = pr.excess_level_3_quantity.thousands(),
                excess_level_4_quantity = pr.excess_level_4_quantity.thousands(),

                quantity_label = settings.quantity_units,
                quantity_minimum = settings.quantity_minimum.toString(),
                quantity_step = settings.quantity_step.toString(),

                cost_multiplier = settings.cost_multiplier.toString(),




                starting_price_announcement_mins = de.settings.starting_price_announcement_mins,

                starting_time = starting_date_time?.let { DateTimeValue.create(it) },

                round_red_secs = de.settings.round_red_secs,
                round_orange_secs = de.settings.round_orange_secs,
                round_open_min_secs = de.settings.round_open_min_secs,
                round_closed_min_secs = de.settings.round_closed_min_secs
                )
        }
    }
}

================
File: main/kotlin/au21/engine/domain/de/viewmodel/DeTraderHistoryRowElement.kt
================
package au21.engine.domain.de.viewmodel

import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.OrderSubmissionType
import au21.engine.domain.common.model.OrderType
import au21.engine.domain.common.model.PriceDirection
import au21.engine.domain.de.model.*
import au21.engine.framework.client.StoreElement
import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.thousands

data class DeTraderHistoryRowElement(
    override val id: String,



    val auction_id: String,
    val bid_constraints: DeBidConstraints?,
    val company_id: String,
    val excess_side: OrderType?,
    val excess_level: String,
    val order_submitted_by: String,
    val order_submission_type: OrderSubmissionType,
    val order_type: OrderType?,
    val price_direction: PriceDirection?,
    val price_has_reversed: Boolean,
    val price_suffix: String,
    val quantity: String,
    val round_number: String,
    val round_price: String,
    val value: String,
) : StoreElement {

    companion object {








        fun create(
            a: DeAuction,
            r: DeRound,
            t: DeTradingCompany
        ): DeTraderHistoryRowElement {

            val o: DeOrder = r.get_order(t)
            val constraints: DeBidConstraints? = r.get_rti(t)?.constraints

            return DeTraderHistoryRowElement(
                id = "ROUND.${r.number}",
                auction_id = a.id_str(),
                bid_constraints = constraints,
                company_id = t.company_id.toString(),


                price_has_reversed = r.has_reversed,
                price_direction = r.price_direction,
                price_suffix =
                when (o.type) {
                    OrderType.BUY -> "or lower"
                    OrderType.SELL -> "or higher"
                    OrderType.NONE -> "none"
                },
                round_number = r.number.toString(),
                round_price = a.format_round_price(AuUserRole.TRADER, r),

                excess_level =
                when (a.show_trader_excess(r)) {
                    true -> a.excess_level(r, AuUserRole.TRADER)
                    false -> ""
                },
                excess_side =
                when (a.show_trader_excess(r)) {
                    true -> a.excess_side(r)
                    false -> null
                },
                order_submission_type = o.submission_type,
                order_submitted_by = when (o.submission_type) {
                    OrderSubmissionType.MANUAL -> o.user?.username_at_auction_time
                        ?: throw AlertException("Manual order must have a username")
                    OrderSubmissionType.DEFAULT -> "(default)"
                    OrderSubmissionType.MANDATORY -> "(manual)"
                },
                order_type = o.type,

                value = a.order_value_str(o),
                quantity = o.quantity.thousands(),
            )
        }
    }


}

================
File: main/kotlin/au21/engine/domain/de/viewmodel/DeTraderInfoValue.kt
================
package au21.engine.domain.de.viewmodel

import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.OrderSubmissionType
import au21.engine.domain.common.model.OrderType
import au21.engine.domain.de.model.*
import au21.engine.framework.client.StoreValue
import au21.engine.framework.commands.AlertException
import au21.engine.framework.utils.thousands


data class DeTraderInfoValue(
    val auction_id: String,
    val award_direction: String,
    val award_line: String?,
    val awarded_price: String,
    val awarded_round_number: String,
    val awarded_quantity: String,
    val awarded_value: String,
    val initial_limits: DeInitialLimits,
    val bid_constraints: DeBidConstraints,
    val company_id: String,
    val order_submission_type: OrderSubmissionType,
    val order_type: OrderType,
    val order_quantity: Int,
    val price_label: String,
    val quantity_label: String,
    val round_number: Int,
    val round_price: String,
    val value:String,
) : StoreValue {













    companion object {

        fun create(
            a: DeAuction,
            t: DeTradingCompany
        ): DeTraderInfoValue {

            val awarded_De_round: DeRound? = a.awarded_round
            val n: DeRound = a.lastround()


            val rti: DeRoundTraderInfo = n.get_rti(t) ?: throw AlertException("No trader info for round ${n.number}")

            val o: DeOrder = n.get_order(t)

            return DeTraderInfoValue(
               auction_id = a.id_str(),
               quantity_label = a.settings.quantity_units,
                price_label = a.settings.price_units,



                company_id = t.company_id.toString(),

                round_number = a.lastround().number,


                round_price = n.let { a.format_round_price(AuUserRole.TRADER, n) },

                initial_limits = t.initial_limits,
                bid_constraints = rti.constraints,

                order_submission_type = o.submission_type,
                order_quantity = o.quantity,
                order_type = o.type,

                awarded_price = awarded_De_round?.let {
                    "${a.format_round_price(AuUserRole.AUCTIONEER, it)} ${a.settings.price_units}"
                } ?: "---",

                awarded_round_number = awarded_De_round?.let {
                    "round ${it.number}"
                } ?: "---",

                awarded_quantity = awarded_De_round?.let {
                    t.awarded_quantity.thousands()
                } ?: "---",

                awarded_value =
                if (a.closed)
                    "$ TODO"
                else "---",

                award_direction = "TODO",

                award_line = a.awarded_round?.let {
                    if (t.awarded_quantity == 0)
                        "You were not awarded any volume"

                    else
                        "TODO"
                },

                value = a.order_value_str(o),
            )
        }

    }
}

================
File: main/kotlin/au21/engine/framework/client/client-command-types.kt
================
package au21.engine.framework.client








import net.pearx.kasechange.toSnakeCase


class EngineTransaction(
    val client_command_maps: MutableList<ClientCommandSessionsMap>,
    val engine_command_json: String?,
    val engine_command_name: String,
    val isHeartbeat: Boolean,
    val session_id: String?,
    var duration_ms: Int,
    var has_alert: Boolean,
    var has_session_differ_error: Boolean,
    var requests_since_last_restart: Int
)

class ClientCommandSessionsMap(
    val command: ClientCommand,
    val sessionIds: List<String>
)




























enum class CommandType {
    CommandSucceeded,
    ShowMessage,
    TerminateSession,
    NetworkDown,
    NetworkUp,
    SetLiveStore,
    AddElements
}

sealed class ClientCommand {
    abstract val command: CommandType

    class CommandSucceeded : ClientCommand() {
        override val command = CommandType.CommandSucceeded
    }

    class ShowMessage(
        val browser_message_kind: BrowserMessageKind,
        val message: List<String>
    ) : ClientCommand() {
        override val command = CommandType.ShowMessage
    }

    class TerminateSession(
        val message: String?
    ) : ClientCommand() {
        override val command = CommandType.TerminateSession
    }

    class NetworkDown : ClientCommand() {
        override val command = CommandType.NetworkDown
    }

    class NetworkUp : ClientCommand() {
        override val command = CommandType.NetworkUp
    }

    sealed class StoreCommand : ClientCommand() {
        abstract override val command: CommandType

        class SetLiveStore(
            val store: LiveClientStore
        ) : StoreCommand() {
            override val command = CommandType.SetLiveStore
        }

        class AddElements(
            element_class: Class<out StoreElement>,
            val elements: List<StoreElement>?
        ) : StoreCommand() {
            override val command = CommandType.AddElements
            val path: String = element_class.simpleName
        }
    }
}







interface StoreValue

interface StoreElement {
    val id: String
}


fun Class<out StoreElement>?.element_path(): String =
    this?.simpleName?.removeSuffix("Element")?.toSnakeCase()?.let {
        if (it.endsWith("y"))
            it.removeSuffix("y") + "ies"
        else
            it + "s"
    } ?: ""

fun Class<out StoreValue>?.value_path(): String =
    this?.simpleName?.removeSuffix("Value")?.toSnakeCase() ?: ""

enum class BrowserMessageKind {
    ALERT, NOTIFICATION
}

enum class BrowserMessageIcon {
    SUCCESS,
    INFO,
    WARNING,
    AUCTIONEER_MESSAGE,
    TRADER_MESSAGE,
    SYSTEM_MESSAGE,
    ORDER_CONFIRMATION
}

================
File: main/kotlin/au21/engine/framework/client/ClientsManager.kt
================
package au21.engine.framework.client



import au21.engine.domain.common.commands.PageSetAction
import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.services.sessions_logged_in
import au21.engine.domain.common.viewmodel.MessageElement
import au21.engine.domain.de.commands.DeRoundControllerAction
import au21.engine.domain.de.viewmodel.DeMatrixRoundElement
import au21.engine.framework.PageName
import au21.engine.framework.client.ClientCommand.*
import au21.engine.framework.client.ClientCommand.StoreCommand.AddElements
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.HeartbeatAction
import au21.engine.framework.commands.interfaces.IAuctionMessage
import au21.engine.framework.commands.interfaces.ISessionsTerminated
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.gzip
import au21.engine.framework.utils.gzipClientCommand
import au21.engine.framework.utils.to_json
import io.quarkus.logging.Log
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.nio.ByteBuffer










@ApplicationScoped
class ClientsManager {

    @Inject
    lateinit var socket_handler: SocketHandler


    var last_action: EngineAction? = null
    var last_stores: List<StoreCommand.SetLiveStore> = emptyList()





    fun handle(db: AuEntityManager, action: EngineAction) {

        last_action = action

        val is_not_heartbeat: Boolean = action !is HeartbeatAction

        val should_update = true

        if (should_update) {




            last_stores = session_live_stores(db)


            if (is_not_heartbeat) {
                Log.info(action.command::class.java.simpleName + ":" + action.command.to_json())

            }


            last_stores.forEach {
                it.store.session_user?.session_id?.let { sid -> socket_handler.publish(sid, gzip(it.to_json())) }
            }
        }




        fun auction_sessions(a: Auction): List<AuSession> =
            db.sessions_logged_in().filter { a == it.auction }

        when (action) {

            is DeRoundControllerAction -> {

                val add_matrix: AddElements =
                    DeMatrixRoundElement.add_rounds(action.de, listOf(action.deRound))
                socket_handler.publish(
                    action.session.session_id,
                    gzip(add_matrix.to_json())
                )
            }
            is PageSetAction -> {

                if (action.page == PageName.DE_AUCTIONEER_PAGE) {
                    socket_handler.publish(
                        action.session.session_id,
                        gzip(DeMatrixRoundElement.clear_rounds().to_json())
                    )
                }
            }
            else -> {


            }

        }


        if (action is ISessionsTerminated) {
            action.sessions_terminated.forEach {
                socket_handler.publish(it.sid, gzipClientCommand(TerminateSession(it.reason)))
            }
        }

        if (action is IAuctionMessage) {
            notify(
                socket_handler,
                MessageElement.recipient_sids_for_message(
                    auction_sessions(action.auction),
                    action.auction,
                    action.message
                ),
                MessageElement.create(action.message)
            )
        }


        action.session?.let {
            succeeded(socket_handler, listOf(it.session_id))
        }

    }







    fun succeeded(socket: SocketHandler, sids: List<String>) {
        gzipClientCommand(CommandSucceeded()).also { buf ->
            sids.forEach { socket.publish(it, buf) }
        }
    }

    fun notify(
        socket: SocketHandler,
        recipient_sids: List<String>,
        m: MessageElement

    ) {
        if (recipient_sids.isEmpty())
            return
        gzipClientCommand(ShowMessage(BrowserMessageKind.NOTIFICATION, listOf(m.message))).also { buf: ByteBuffer ->
            recipient_sids.forEach { socket.publish(it, buf) }
        }

    }

}

================
File: main/kotlin/au21/engine/framework/client/CommandBuffer.kt
================
package au21.engine.framework.client

import au21.engine.framework.utils.gzip
import au21.engine.framework.utils.to_json
import java.nio.ByteBuffer

open class CommandBuffer<T : ClientCommand> {

    var json: String? = null
        private set

    var buffer: ByteBuffer? = null
        private set

    fun set_buffer(cmd: ClientCommand?): Boolean =
        when (val new_json = cmd.to_json()) {
            json -> false
            else -> {
                json = new_json
                buffer = gzip(new_json)
                true
            }
        }





    fun init_session(
        socket: SocketHandler,
        session_id: String?,
        cmd_provider: (() -> T)? = null,
    ): Boolean =


        session_id?.let { sid ->
            when (cmd_provider) {
                null -> {
                    buffer?.let { buf -> socket.publish(sid, buf) }
                        ?: throw Error("ERROR: Init expected buffer not to be null")
                    false
                }
                else -> {
                    set_buffer(cmd_provider()).also { changed ->
                        if (changed) {
                            buffer?.let { socket.publish(sid, it) }
                        }
                    }
                }
            }
        } ?: false






    open fun update_sessions(
        socket: SocketHandler,
        sids: Array<String>,
        force: Boolean,
        cmd_provider: () -> T,
    ) {
        fun publish(){
            sids.forEach { buffer?.let { buff -> socket.publish(it, buff) } }
        }


        when {
            sids.isEmpty() -> return
            set_buffer(cmd_provider()) -> publish()
            force -> publish()
        }
    }
}

================
File: main/kotlin/au21/engine/framework/client/CommandsHelper.kt
================
package au21.engine.framework.client

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.model.Company
import au21.engine.domain.common.services.sessions_non_terminated
import au21.engine.domain.common.viewmodel.*
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeRound
import au21.engine.domain.de.viewmodel.DeMatrixRoundElement
import au21.engine.framework.client.ClientCommand.StoreCommand.SetLiveStore
import au21.engine.framework.database.AuEntityManager

class DbSessionCache(db: AuEntityManager) {
    val sessions_non_terminated: List<AuSession> by lazy {
        db.sessions_non_terminated()
    }

    val sessions_logged_in: List<AuSession> by lazy {
        sessions_non_terminated.filter { !it.isTerminated() }
    }

    val time: TimeValue = TimeValue.now("Houston")



    val auctions: List<Auction> by lazy {
        db.findAll()
    }


    val auctioneer_row_elements: List<AuctionRowElement> by lazy {
        auctions.map { AuctionRowElement.create(it) }
    }

    val companies: List<Company> by lazy { db.findAll() }

    val company_elements: List<CompanyElement> by lazy {
        companies.map { CompanyElement.create(it) }
    }

    val counterpartyCredits: List<CounterpartyCreditElement> by lazy {
        CounterpartyCreditElement.all(companies)
    }

    val user_elements: List<UserElement> by lazy {
        UserElement.user_elements(db, sessions_logged_in)
    }



    val de_matrix_rounds: MutableMap<DeAuction, MutableList<DeMatrixRoundElement>> = mutableMapOf()


    fun get_matrix_round(de: DeAuction, round_number: Int): DeMatrixRoundElement =
        de.rounds.find { it.number == round_number }?.let { r: DeRound ->
            de_matrix_rounds.getOrPut(de) {
                mutableListOf(DeMatrixRoundElement.create(de.lastround(), de.settings.cost_multiplier))
            }.let { matrix_rounds ->
                matrix_rounds.find { it.round_number == round_number }
                    ?: run {
                        DeMatrixRoundElement.create(r, de.settings.cost_multiplier).also {
                            matrix_rounds.add(it)
                        }
                    }
            }
        } ?: throw Error("get_matrix_round(): No round found with number = $round_number")
}

fun session_live_stores(db: AuEntityManager): List<SetLiveStore> {

    val cache = DbSessionCache(db)

    return cache.sessions_non_terminated.map { s: AuSession ->
        SetLiveStore(LiveClientStore.create(s, cache))
    }

}

================
File: main/kotlin/au21/engine/framework/client/LiveClientStore.kt
================
package au21.engine.framework.client

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.viewmodel.*
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.get_trader
import au21.engine.domain.de.viewmodel.DeAuctionValue
import au21.engine.domain.de.viewmodel.DeMatrixRoundElement
import au21.engine.framework.PageName
























data class LiveClientStore(
    val auction_rows: List<AuctionRowElement>,
    val companies: List<CompanyElement>,
    val counterparty_credits: List<CounterpartyCreditElement>,
    val de_auction: DeAuctionValue?,
    val session_user: SessionUserValue?,
    val time: TimeValue?,
    val users: List<UserElement>,
) {
    companion object {
        val debug = false

        fun create(s: AuSession, cache: DbSessionCache): LiveClientStore {
            val role: AuUserRole? = s.user?.role

            return LiveClientStore(
                auction_rows = when (s.page) {
                    PageName.HOME_PAGE ->
                        when (role) {
                            AuUserRole.AUCTIONEER -> cache.auctioneer_row_elements
                            AuUserRole.TRADER -> AuctionRowElement.elements_for_session(
                                cache.auctions,
                                s
                            )
                            else -> emptyList()
                        }
                    else -> emptyList()
                },
                companies = when (role) {
                    AuUserRole.AUCTIONEER -> cache.company_elements
                    else -> emptyList()
                },
                counterparty_credits = when (role) {
                    AuUserRole.AUCTIONEER -> cache.counterpartyCredits
                    AuUserRole.TRADER -> cache.counterpartyCredits.filter { it.seller_id == s.user?.company?.id_str() }
                    else -> emptyList()
                },
                de_auction = when (val de: Auction? = s.auction) {
                    is DeAuction ->
                        when (s.page) {
                            PageName.DE_AUCTIONEER_PAGE -> DeAuctionValue.value_for_auctioneer(
                                de,
                                s,
                                cache.counterpartyCredits.filter { c ->
                                    de.de_trading_companies
                                        .map { it.shortname_at_auction_time }
                                        .contains(c.seller_shortname)
                                }
                            )
                            PageName.DE_TRADER_PAGE ->
                                when (val t = de.get_trader(s)) {
                                    null -> DeAuctionValue.value_for_null_auction
                                    else -> DeAuctionValue.value_for_trader(de, s, t)
                                }
                            else -> DeAuctionValue.value_for_null_auction
                        }
                    else -> DeAuctionValue.value_for_null_auction
                },
                session_user = SessionUserValue.create(s),
                time = cache.time,
                users = when (role) {
                    AuUserRole.AUCTIONEER -> cache.user_elements
                    else -> emptyList()
                }
            ).also {
                if (debug) println()
            }
        }

    }
}

class StaleClientStore {
    val stale_de_matrix_rounds: MutableList<DeMatrixRoundElement> = mutableListOf()
}

================
File: main/kotlin/au21/engine/framework/client/SocketHandler.kt
================
package au21.engine.framework.client

import au21.engine.domain.common.commands.ClientSocketCommand
import au21.engine.domain.common.model.AuSession
import au21.engine.framework.commands.EngineCommandEnvelope
import au21.engine.framework.utils.to_json
import io.quarkus.logging.Log
import io.vertx.core.eventbus.EventBus
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.websocket.*
import jakarta.websocket.server.PathParam
import jakarta.websocket.server.ServerEndpoint
import java.nio.ByteBuffer





@ServerEndpoint("/socket/{session_id}")
@ApplicationScoped
class SocketHandler {

    companion object {
        const val TOPIC_SOCKET_COMMAND = "TOPIC_SOCKET_COMMAND"
    }




    @Inject
    lateinit var bus: EventBus

    val sessions: MutableMap<String, Session> = mutableMapOf()

    fun publish(session_id: String?, zipped: ByteBuffer?) {
        if (session_id == null || zipped == null)
            return
        try {
            sessions[session_id]
                ?.asyncRemote
                ?.sendBinary(zipped)
        } catch (e: Throwable) {
            e.printStackTrace()
        }
    }

    @OnOpen
    fun onOpen(s: Session, @PathParam("session_id") session_id: String) {
        Log.info("Socket opened, session_id = $session_id")
        sessions[session_id] = s
        val params: Map<String, List<String>> = s.requestParameterMap

        val browser_name: String = params["browser_name"]?.get(0) ?: ""
        val browser_version: String = params["browser_version"]?.get(0) ?: ""
        val browser_os: String = params["browser_os"]?.get(0) ?: ""

        bus.send(
            TOPIC_SOCKET_COMMAND,
            EngineCommandEnvelope(
                session_id = session_id,
                command = ClientSocketCommand(
                    sid = session_id,
                    state = AuSession.ClientSocketState.OPENED,
                    browser_name = browser_name,
                    browser_version = browser_version,
                    browser_os = browser_os
                )
            ).to_json()
        )
    }

    @OnClose
    fun onClose(session: Session?, @PathParam("session_id") session_id: String) {
        Log.info("Socket closed, session_id = $session_id")
        sessions.remove(session_id)
        bus.send(
            TOPIC_SOCKET_COMMAND,
            EngineCommandEnvelope(
                session_id = session_id,
                command = ClientSocketCommand(
                    sid = session_id,
                    state = AuSession.ClientSocketState.CLOSED)
            ).to_json()
        )

    }

    @OnError
    fun onError(session: Session?, @PathParam("session_id") session_id: String, throwable: Throwable) {
        Log.error("Socket error, session_id = $session_id, error = ${throwable.message}")



        println("error: ${throwable.message}")
    }

    @OnMessage
    fun onMessage(message: String, @PathParam("session_id") session_id: String) {

        Log.info("Socket message, message =$message")
        bus.send(TOPIC_SOCKET_COMMAND, message)
    }

}

================
File: main/kotlin/au21/engine/framework/commands/interfaces/IAuctionMessage.kt
================
package au21.engine.framework.commands.interfaces

import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.model.AuctionMessage

interface IAuctionMessage {
    val auction:Auction
    val message:AuctionMessage
}

================
File: main/kotlin/au21/engine/framework/commands/interfaces/ISessionsTerminated.kt
================
package au21.engine.framework.commands.interfaces

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuSession.SessionTerminationReason
import au21.engine.framework.commands.SOCKET_CLOSED_TIMEOUT_SECONDS
import au21.engine.framework.database.AuEntityManager

class SessionTermination(
    s: AuSession,
    val reason:String
){
    val sid:String = s.session_id
}

interface ISessionsTerminated{

    val sessions_terminated:MutableList<SessionTermination>

    fun get_reason_string(tr: SessionTerminationReason):String =
        when(tr){
            SessionTerminationReason.BROWSER_UNLOADED -> TODO()
            SessionTerminationReason.COMPANY_DELETED -> "Company deleted"
            SessionTerminationReason.COMPANY_NAME_EDITED -> "Company name edited"
            SessionTerminationReason.FORCED_OFF -> TODO()
            SessionTerminationReason.LOGIN_FROM_ANOTHER_BROWSER -> "You have logged in from another browser or device."
            SessionTerminationReason.SERVER_REBOOT -> TODO()
            SessionTerminationReason.SERVER_SWEPT_STALE_SESSION -> "Session swept due to > $SOCKET_CLOSED_TIMEOUT_SECONDS seconds of inactivity"
            SessionTerminationReason.SIGNED_OFF -> ""
            SessionTerminationReason.USER_EDITED -> "User edited"
            SessionTerminationReason.USER_DELETED -> "User deleted"
        }


    fun terminate_session(db: AuEntityManager, s:AuSession, tr: SessionTerminationReason){
        s.terminate(tr)
        db.save(s)
        sessions_terminated.add(SessionTermination(s, get_reason_string(tr)))
    }
}

================
File: main/kotlin/au21/engine/framework/commands/AuSessionAction.kt
================
package au21.engine.framework.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.session_by_sid
import au21.engine.framework.database.AuEntityManager
import jakarta.inject.Inject

abstract class AuSessionAction {

    @Inject
    lateinit var db: AuEntityManager

    val errors: MutableList<String> = mutableListOf()

    abstract fun mutate()



    abstract val session_id: String

    open val session: AuSession by lazy {
        db.session_by_sid(session_id)
            ?: throw AlertException("session not found: $session_id.")
    }
}

================
File: main/kotlin/au21/engine/framework/commands/command-validators.kt
================
package au21.engine.framework.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuUserRole
import au21.engine.domain.common.viewmodel.DateTimeValue
import au21.engine.framework.utils.is_blank
import java.util.*
import kotlin.reflect.KProperty0

fun humanize(s: String): String = s.replace("_", " ").replaceFirstChar { it.uppercase() }

fun EngineCommand.err_if(pred: Boolean, err: String) {
    if (pred) this.errors().add(err)
}

fun EngineCommand.err_unless(pred: Boolean, err: String) {
    if (!pred) errors().add(err)
}


fun EngineCommand.err_if_blank(prop: KProperty0<String?>, name: String? = null): String? =
    prop.get().also {
        if (it.is_blank())
            errors().add("${humanize(name ?: prop.name)} cannot be blank.")
    }?.trim()

fun EngineCommand.err_if_null(o: Any?, err: String) {
    if (o == null) errors().add(err)
}


fun EngineCommand.fail_if_errors() {
    if (this.errors().isNotEmpty()) {


        throw AlertException(errors().joinToString(separator = "\n"))
    }
}

fun fail(err: String): Nothing =
    throw AlertException(err)

fun fail_if(check: Boolean, err: String) {
    if (check) fail(err)
}

fun fail_if_not(check: Boolean, err: String) {
    if (!check) fail(err)
}

fun fail_unless(check: Boolean, err: String) =
    fail_if_not(check, err)

fun fail_if_null(o: Any?, err: String) {
    if (o == null) fail(err)
}

inline fun <reified T : Enum<T>> fail_if_not_contains(item: T, vararg items: T, message: () -> String) {
    if (items.none { it == item })
        throw AlertException(message())
}

fun fail_if_not_auctioneer(session: AuSession) {
    if (!session.inRole(AuUserRole.AUCTIONEER))
        fail("Only auctioneers can do this.")
}








fun String.trim_commas_and_underscores(): String = trim()
    .replace(",", "")
    .replace("_", "")

fun to_int_or_err(
    s: String?,
    name: String,
    pred: ((Int) -> Boolean) = { true },
    err_fn: ((String?) -> String)
): Int =
    s?.trim_commas_and_underscores()
        ?.toIntOrNull()
        ?.takeIf(pred)
        ?: throw AlertException(err_fn(name))

fun to_int_gt0_or_err(s: String?, name: String): Int =
    to_int_or_err(s, name, ::is_GT_zero, ::add_GT_zero_suffix)

fun to_int_ge0_or_err(s: String?, name: String): Int =
    to_int_or_err(s, name, ::is_GE_zero, ::add_GE_zero_suffix)

fun to_double_or_err(
    s: String?,
    name: String,
    pred: ((Double) -> Boolean) = { true },
    err_fn: ((String?) -> String)
): Double =
    s?.trim_commas_and_underscores()
        ?.toDoubleOrNull()
        ?.takeIf(pred)
        ?: throw AlertException(err_fn(name))


fun EngineCommand.toIntOrError(
    prop: KProperty0<String?>,
    pred: ((Int?) -> Boolean)? = null,
    err_fn: (String) -> String
): Int? =

    prop.get()?.trim_commas_and_underscores()?.toIntOrNull()?.takeIf {
        if (pred != null) pred(it)
        else true
    } ?: // TODO: humanize the property name
    run {
        errors().add(humanize(err_fn(prop.name))) // TODO: humanize the property name
        // TODO: humanize the property name
        null
    }

fun EngineCommand.toLongOrError(
    prop: KProperty0<String?>,
    pred: ((Long?) -> Boolean)? = null,
    err_fn: (String) -> String
): Long? =

    prop.get()?.trim_commas_and_underscores()?.toLongOrNull()?.takeIf {
        if (pred != null) pred(it)
        else true
    } ?: // TODO: humanize the property name
    run {
        errors().add(humanize(err_fn(prop.name))) // TODO: humanize the property name
        // TODO: humanize the property name
        null
    }

fun EngineCommand.toDoubleOrError(
    prop: KProperty0<String?>,
    pred: ((Double?) -> Boolean)? = null,
    err_fn: (String) -> String
): Double? =

    prop.get()?.trim_commas_and_underscores()?.toDoubleOrNull()?.takeIf {
        if (pred != null) pred(it)
        else true
    } ?: // TODO: humanize the property name
    run {
        errors().add(err_fn(humanize(prop.name))) // TODO: humanize the property name
        // TODO: humanize the property name
        null
    }

fun add_GT_zero_suffix(s: String?): String = (s ?: "").plus(" must be a number greater than zero.")
fun add_GE_zero_suffix(s: String?): String = (s ?: "").plus(" must be a number greater than or equal to zero.")

fun is_GT_zero(i: Int?): Boolean = i?.let { it > 0 } ?: false
fun is_GT_zero(i: Long?): Boolean = i?.let { it > 0 } ?: false
fun is_GT_zero(i: Double?): Boolean = i?.let { it > 0 } ?: false

fun is_GE_zero(i: Int?): Boolean = i?.let { it >= 0 } ?: false
fun is_GE_zero(i: Long?): Boolean = i?.let { it >= 0 } ?: false
fun is_GE_zero(i: Double?): Boolean = i?.let { it >= 0 } ?: false


fun EngineCommand.err_unless_Int_GT_zero(prop: KProperty0<String?>): Int? =
    toIntOrError(prop, ::is_GT_zero, ::add_GT_zero_suffix)

fun EngineCommand.err_unless_Long_GT_zero(prop: KProperty0<String?>): Long? =
    toLongOrError(prop, ::is_GT_zero, ::add_GT_zero_suffix)

fun EngineCommand.err_unless_Double_GT_zero(prop: KProperty0<String?>): Double? =
    toDoubleOrError(prop, ::is_GT_zero, ::add_GT_zero_suffix)

fun EngineCommand.err_unless_Int_GE_zero(prop: KProperty0<String?>): Int? =
    toIntOrError(prop, ::is_GE_zero, ::add_GE_zero_suffix)

fun EngineCommand.err_unless_Long_GE_zero(prop: KProperty0<String?>): Long? =
    toLongOrError(prop, ::is_GE_zero, ::add_GE_zero_suffix)

fun EngineCommand.err_unless_Double_GE_zero(prop: KProperty0<String?>): Double? =
    toDoubleOrError(prop, ::is_GE_zero, ::add_GE_zero_suffix)

//inline fun <reified T : Enum<T>?> EngineCommand.enumValueOrErr(prop: KProperty0<String>): T? =
//    toEnumOrNull<T>(prop.get()) ?: run {
//        errors().add(humanize(prop.name) + " not understood.")





fun EngineCommand.require_int_GT_zero(s: String, err: String, default: Int = 0): Int =
    s.trim_commas_and_underscores().toIntOrNull()?.takeIf { it > 0 } ?: run {
        errors().add(err)
        default
    }

fun EngineCommand.require_int_GE_zero(s: String, err: String, default: Int = 0): Int =
    s.trim_commas_and_underscores().toIntOrNull()?.takeIf { it >= 0 } ?: run {
        errors().add(err)
        default
    }

fun EngineCommand.require_double_GT_zero(s: String, err: String, default: Double = 0.0): Double =
    s.trim_commas_and_underscores().toDoubleOrNull()?.takeIf { it > 0.0 } ?: run {
        errors().add(err)
        default
    }

fun EngineCommand.require_double_GE_zero(s: String, err: String, default: Double = 0.0): Double =
    s.trim_commas_and_underscores().toDoubleOrNull()?.takeIf { it >= 0.0 } ?: run {
        errors().add(err)
        default
    }



fun EngineCommand.to_date_or_err(dt: DateTimeValue?): Date? =
    try {
        dt?.toDate()
    } catch (e: Throwable) {
        e.printStackTrace()
        null
    }

================
File: main/kotlin/au21/engine/framework/commands/controller.kt
================
package au21.engine.framework.commands





import au21.engine.framework.database.AuEntityManager
import io.quarkus.logging.Log
import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.QueryParam





















@Path("/ENGINE_INPUT_CHANNEL")
class EngineCommandController {




    @Inject
    lateinit var handler: EngineCommandHandler





    @Inject
    lateinit var db: AuEntityManager






    @GET
    @Synchronized
    fun handle(@QueryParam("message") message: String): String {

        try {

            return when (message) {
                "TICK" -> handler.handle(db, message)
                else -> {

                        handler.handle(db, message)

                }
            }

        } catch (t: Throwable) {
            t.printStackTrace()
            t.message?.let {
                Log.error(it)

            }
            t.cause?.message?.let {
                Log.error("caused by: $it")

            }
            return t.cause?.message ?: t.message ?: "Error"
        }
    }

}

================
File: main/kotlin/au21/engine/framework/commands/deserializer.kt
================
package au21.engine.framework.commands

import com.google.gson.Gson
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject

@ApplicationScoped
class EngineCommandDeserializer {




    val gson = Gson()




























    fun to_command(command_json: Any, classname: String): EngineCommand {


        return gson.fromJson(command_json.toString(), Class.forName(classname)) as EngineCommand
    }





















































}

================
File: main/kotlin/au21/engine/framework/commands/dispatcher.kt
================
package au21.engine.framework.commands

import au21.engine.framework.client.SocketHandler.Companion.TOPIC_SOCKET_COMMAND
import au21.engine.framework.features.AuFeatures
import au21.engine.framework.utils.HeartbeatConfig
import io.quarkus.logging.Log
import io.quarkus.vertx.ConsumeEvent
import io.vertx.core.eventbus.EventBus
import jakarta.annotation.PostConstruct
import jakarta.annotation.PreDestroy
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import kong.unirest.Unirest
import org.eclipse.microprofile.config.ConfigProvider
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.concurrent.thread











@ApplicationScoped
class Dispatcher {

    @Inject
    lateinit var hearbeatConfig: HeartbeatConfig

    @Inject
    lateinit var handler: EngineCommandHandler

    @Inject
    lateinit var bus: EventBus

    @Inject
    lateinit var features: AuFeatures

    val HEARTBEAT_SECS: Long = 1
    val TICK = "TICK"

    private val destroyed = AtomicBoolean(false)
    private val request_queue = LinkedBlockingQueue<String>()

    fun send_message(message:String){

        request_queue.add(message)
    }

    final val port: String = ConfigProvider.getConfig().getValue("quarkus.http.port", String::class.java)


    @Suppress("HttpUrlsUsage")
    val internal_url: String = "http://127.0.0.1:$port/ENGINE_INPUT_CHANNEL"

    init {
        Log.info { "Internal url = $internal_url" }
    }

    @ConsumeEvent(TOPIC_SOCKET_COMMAND)
    fun addCommand(msg: String) {

        this.request_queue.add(msg)
    }

    @PostConstruct
    fun postConstruct() {
        try {
            handler_thread.start()
            if(hearbeatConfig.heartbeat_enabled) {
                heartbeat_thread.start()
            }
        } catch (e: Throwable) {
            e.printStackTrace()
        }
    }

    @PreDestroy
    fun preDestroy() {
        Log.info("Stopping threads")
        destroyed.set(true)
    }

    val heartbeat_thread: Thread = thread(
        name = "Heartbeat thread",
        start = false
    ) {
        Log.info("${this}: Heartbeat started.")
        while (!destroyed.get()) {
            try {
                if (features.heartbeat) {




                    request_queue.find { it == TICK } ?: run {
                        request_queue.add(TICK)
                    }
                }
                Thread.sleep(HEARTBEAT_SECS * 1_000)
            } catch (e: Throwable) {
                e.printStackTrace()
            }
        }
        Log.info("destroying:${this}")
    }

    val handler_thread: Thread = thread(
        name = "Handler thread",
        start = false
    ) {
        Log.info("$this Handler started.")
        while (!destroyed.get()) {
            try {
                val msg = request_queue.take()



                when {
                    request_queue.size > 1 -> Log.info("tasks remaining: ${request_queue.size}")
                }

                if (!destroyed.get()) {
                    Unirest.get(internal_url)
                        .queryString(mapOf(Pair("message", msg)))
                        .asJson()
                }

            } catch (t: Throwable) {
                t.printStackTrace()
            }
        }
        Log.info("destroying:${this}")
    }

}

================
File: main/kotlin/au21/engine/framework/commands/EngineAction.kt
================
package au21.engine.framework.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.framework.database.AuEntityManager


interface EngineAction {
    val command: EngineCommand
    val db: AuEntityManager
    val session: AuSession?


    fun mutate()
}

================
File: main/kotlin/au21/engine/framework/commands/EngineCommand.kt
================
package au21.engine.framework.commands

import au21.engine.framework.database.AuEntity
import au21.engine.framework.database.AuEntityManager
import com.fasterxml.jackson.annotation.JsonIgnore
import org.joda.time.DateTime
import java.util.*
import javax.persistence.Entity

abstract class EngineCommand {




    @Transient
    @JsonIgnore
    private var _errors: MutableList<String>? = null

    fun errors(): MutableList<String> =
        _errors ?: run {
            mutableListOf<String>().also {
                _errors = it
            }
        }


    abstract fun validate(db: AuEntityManager, session_id: String? = null): EngineAction

}








@Entity
class CommandJson(
    var command_json: String,
    var timestamp: Date = DateTime().toDate()
) : AuEntity()

================
File: main/kotlin/au21/engine/framework/commands/EngineCommandEnvelope.kt
================
package au21.engine.framework.commands








class EngineCommandEnvelope(
    val session_id: String,
    val command: EngineCommand
) {
    val simplename:String = command.javaClass.simpleName
    val classname:String = command.javaClass.canonicalName
}

================
File: main/kotlin/au21/engine/framework/commands/exceptions.kt.kt
================
package au21.engine.framework.commands

class AlertException(error: String) :

    Error(error) {



























}





class LogException(error: String) : Error(error)

================
File: main/kotlin/au21/engine/framework/commands/handler.kt
================
package au21.engine.framework.commands





import au21.engine.domain.common.commands.ErrorsSendCommand
import au21.engine.domain.common.model.AuSession.SessionTerminationReason.SERVER_REBOOT
import au21.engine.domain.common.services.sessions_non_terminated
import au21.engine.domain.de.commands.DeOrderSubmitCommand
import au21.engine.framework.client.BrowserMessageKind
import au21.engine.framework.client.ClientCommand
import au21.engine.framework.client.ClientsManager
import au21.engine.framework.client.SocketHandler
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.metrics.AuMetrics
import au21.engine.framework.utils.gzipClientCommand
import au21.engine.framework.utils.jsonToPrettyFormat
import au21.engine.framework.utils.objToPrettyFormat
import au21.engine.framework.utils.to_json
import com.jsoniter.JsonIterator
import io.quarkus.logging.Log
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import java.util.concurrent.atomic.AtomicBoolean














class CommandParams(
    val session_id: String?,
    val command_json: Any?,
    val simplename: String,
    val classname: String,
)


@ApplicationScoped
class EngineCommandHandler {

    var trace_heartbeat = false

    @Inject
    lateinit var metrics: AuMetrics





    val heartbeat_simplename: String = HeartbeatCommand::class.java.simpleName
    val heartbeat_classname: String = HeartbeatCommand::class.java.canonicalName

    @Inject
    lateinit var session_manager: ClientsManager

    @Inject
    lateinit var session_socket: SocketHandler

    @Inject
    lateinit var command_deserializer: EngineCommandDeserializer

    @Inject
    lateinit var dispatcher: Dispatcher

    private val has_run = AtomicBoolean()








    var error: Throwable? = null


    @Synchronized
    @Transactional
    fun handle(
        db: AuEntityManager,

        json: String,


    ): String {

        error = null

        val start_command = System.nanoTime()

        val is_heartbeat = json == "TICK"

        if (!is_heartbeat) {
            Log.info(jsonToPrettyFormat(json))
        }

        val commandParams: CommandParams = commandParameters(is_heartbeat, json)

        val command: EngineCommand =
            deserializeToCommand(is_heartbeat, commandParams)

        if (!is_heartbeat) {
            Log.info(command.to_json())
        }

        val cmd_json = saveCommandJson(command, is_heartbeat, commandParams, db)


        try {











            val action = validateCommand(command, db, commandParams.session_id)



            mutateAction(db, action)

            val start_stores = System.nanoTime()


            updateStore(is_heartbeat, db, action, commandParams)
            metrics.add_request_latency(cmd_json, start_command, start_stores)

            return "Succeeded"
        } catch (e: Throwable) {

            error = e



            if (e is AlertException) {
                Log.error("AlertException: ${e.message}")
                handle_alert(e, commandParams, command)
                return "AlertException handled: ${e.message}"
            } else {
                Log.error("Error: ${e.message}", e)
            }

            e.cause?.let { ee: Throwable ->
                if (ee is AlertException) {
                    Log.error("AlertException cause: ${ee.message}")
                    handle_alert(ee, commandParams, command)
                    return "AlertException cause handler: ${ee.message}"
                } else {
                    Log.error("Error cause: ${e.message}", ee)
                    throw ee
                }
            }

            throw e

        }
    }


    fun updateStore(
        is_heartbeat: Boolean,
        db: AuEntityManager,
        action: EngineAction,
        commandParams: CommandParams,
    ) {
        when {
            is_heartbeat -> {
                session_manager.handle(db, action)
            }

            else -> {
                Log.info(">>>")
                Log.info("session_id = " + commandParams.session_id)
                session_manager.handle(db, action)
                Log.info("<<<")

            }
        }
    }


    fun mutateAction(
        db: AuEntityManager,
        action: EngineAction,
    ) {
        db.transact {
            action.mutate()
        }
    }


    fun validateCommand(
        command: EngineCommand,
        db: AuEntityManager,
        session_id: String?,
    ): EngineAction {
        val action = command.validate(db, session_id)

        if (!has_run.get()) {
            has_run.set(true)
            db.transact {
                db.sessions_non_terminated().forEach {
                    it.terminate(SERVER_REBOOT)
                    db.save(it)
                }
            }
        }
        return action
    }


    fun saveCommandJson(
        command: EngineCommand,
        is_heartbeat: Boolean,
        commandParams: CommandParams,
        db: AuEntityManager,
    ): String {
        val cmd_json = command::class.java.simpleName +
                if (is_heartbeat) ""
                else ": " + commandParams.command_json!!.toString()

        if (!is_heartbeat) {
            try {
                db.transact {
                    db.save(CommandJson(cmd_json))
                }
            } catch (t: Throwable) {
                t.printStackTrace()
            }
        }
        return cmd_json
    }

    // @WithSpan
    fun deserializeToCommand(
        is_heartbeat: Boolean,
        commandParams: CommandParams,
    ): EngineCommand {
        val command: EngineCommand = when {
            is_heartbeat -> HeartbeatCommand.instance
            else -> try {
                command_deserializer.to_command(
                    commandParams.command_json!!,
                    commandParams.classname
                )
            } catch (t: Throwable) {
                t.printStackTrace()
                throw t
            }
        }
        return command
    }

    // @WithSpan
    fun commandParameters(
        is_heartbeat: Boolean,
        json: String,
    ): CommandParams {
        val commandParams: CommandParams =
            when {
                is_heartbeat -> CommandParams(
                    session_id = null,
                    command_json = null,
                    simplename = heartbeat_simplename,
                    classname = heartbeat_classname
                )

                else -> {
                    val any = JsonIterator.deserialize(json)
                    CommandParams(
                        session_id = any.toString("session_id"),
                        command_json = any.get("command"),
                        simplename = any.toString("simplename"),
                        classname = any.toString("classname")
                    )
                }
            }
        return commandParams
    }

    fun handle_alert(
        e: AlertException,
        commandParams: CommandParams,
        command: EngineCommand
    ) {
        when (val sid = commandParams.session_id) {
            null -> {
                e.printStackTrace()
                throw e
            }
            else -> {
                session_socket.publish(
                    sid, gzipClientCommand(
                        ClientCommand.ShowMessage(
                            BrowserMessageKind.ALERT,
                            listOf(e.message!!)
                        )
                    )
                )

                e.message.let {
                    if (it?.trim() != "" && command is DeOrderSubmitCommand) {
                        dispatcher.send_message(
                            mapOf(
                                "session_id" to null,
                                "simplename" to ErrorsSendCommand::class.simpleName,
                                "classname" to ErrorsSendCommand::class.qualifiedName,
                                "command" to mapOf(
                                    "auction_id" to command.auction_id,
                                    "error" to e.message,
                                    "trader_session_id" to sid
                                )
                            ).to_json()
                        )
                    }
                }
            }
        }
    }

}

================
File: main/kotlin/au21/engine/framework/commands/HeartbeatAction.kt
================
package au21.engine.framework.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.model.AuSession.SessionTerminationReason.SERVER_SWEPT_STALE_SESSION
import au21.engine.domain.common.model.Auction
import au21.engine.domain.common.services.open_auctions
import au21.engine.domain.common.services.sessions_non_terminated
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeFlowControlType
import au21.engine.domain.de.services.state.DeControlValidator
import au21.engine.domain.de.services.state.DeControlValidator.MUTATE
import au21.engine.domain.de.services.state.DeMutator
import au21.engine.framework.commands.interfaces.ISessionsTerminated
import au21.engine.framework.commands.interfaces.SessionTermination
import au21.engine.framework.database.AuEntityManager
import org.joda.time.DateTime

const val SOCKET_CLOSED_TIMEOUT_SECONDS = 15














interface IOpenAuctions {
    val open_auctions: List<Auction>
}

class HeartbeatCommand : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?) =

        HeartbeatAction(this, db, null, db.open_auctions())

    companion object {
        val instance = HeartbeatCommand()
    }
}

class HeartbeatAction(
    override val command: HeartbeatCommand,
    override val db: AuEntityManager,
    override val session: AuSession? = null,
    override val open_auctions: List<Auction>
) : EngineAction, ISessionsTerminated, IOpenAuctions {

    override val sessions_terminated = mutableListOf<SessionTermination>()

    val de_auctions_ticked: MutableList<DeAuction> = mutableListOf()
    val de_auctions_round_closed: MutableList<DeAuction> = mutableListOf()

    override fun mutate() {


        val threshold = DateTime().toDate().time - (SOCKET_CLOSED_TIMEOUT_SECONDS * 1_000)

        db.sessions_non_terminated()
            .filter {
                when (val t = it.socket_last_closed) {
                    null -> false
                    else -> t.time < threshold
                }
            }
            .onEach { s: AuSession ->
                terminate_session(db, s, SERVER_SWEPT_STALE_SESSION)
            }

        open_auctions.forEach { a: Auction ->
            when(a){
                is DeAuction ->
                    if (DeControlValidator.validate(a, DeFlowControlType.HEARTBEAT) == MUTATE) {
                        DeMutator.mutate(a, DeFlowControlType.HEARTBEAT, null)
                        db.save(a)
                    }
            }
        }

    }

}

================
File: main/kotlin/au21/engine/framework/database/AuEntity.kt
================
package au21.engine.framework.database

import java.io.Serializable
import javax.jdo.annotations.Index
import javax.persistence.Entity
import javax.persistence.GeneratedValue
import javax.persistence.Id





@Entity

open class AuEntity : Serializable {
    @Id
    @GeneratedValue
    var id: Long = 0



    fun id_str() =
        id.toString()

    @Index
    var deleted: Boolean = false



    fun checkConstraints() {



    }
}


fun is_same_entity(a: AuEntity?, b: AuEntity?): Boolean =
    when {
        a == null -> false
        b == null -> false
        a.id == 0L && b.id == 0L -> a.hashCode() == b.hashCode()
        else -> a.id == b.id
    }

================
File: main/kotlin/au21/engine/framework/database/AuEntityManager.kt
================
package au21.engine.framework.database


import io.quarkus.logging.Log
import jakarta.annotation.PostConstruct
import jakarta.annotation.PreDestroy
import jakarta.enterprise.context.RequestScoped
import jakarta.inject.Inject
import javax.persistence.EntityManager
import javax.persistence.EntityManagerFactory
import javax.persistence.EntityTransaction

@RequestScoped
class AuEntityManager @Inject constructor(val emf: EntityManagerFactory) {

    companion object {
        var class_count = 0
    }

    var instance_count = 0

    init {
        instance_count = ++class_count
    }

    val em: EntityManager = emf.createEntityManager()



    fun getId(o:AuEntity?): String =
        o.let {

            emf.persistenceUnitUtil.getIdentifier(o)?.toString() ?: ""
        }
    @PostConstruct
    fun postConstruct() {
       // Log.info("POST_CONSTRUCT: " + this::class.java.simpleName + " #${instance_count} created")
    }

    @PreDestroy
    fun preDestroy() {

        if (em.isOpen) {

            em.close()
        }
    }


    @Synchronized
    fun <T> transact(logging: Boolean = true, block: () -> T): T {




        val tx: EntityTransaction = em.transaction
        try {
            tx.begin()
            return block().apply {
                tx.commit()
            }
        } catch (e: Throwable) {
            e.printStackTrace()
            throw e
        } finally {
            if (tx.isActive) {
                Log.error("TRANSACTION ROLLING BACK")
                tx.rollback()
                Log.error("TRANSACTION ROLLED BACK")
            }
        }
    }


    fun <E : AuEntity> save(e: E): E =
        e.also {


            em.persist(it)
        }


    fun <E : AuEntity> refresh(e: E) =
        e.also { em.refresh(it) }


    fun <E : AuEntity> delete(e: E) =
        e.also { em.remove(it) }



    @Suppress("JpaQlInspection")
    inline fun <reified E> deleteAllIncDeleted() =

        try {


            em.createQuery("DELETE FROM ${E::class.simpleName}").executeUpdate()
        } catch (e:Exception){
            e.printStackTrace()
        }


    fun commit_and_begin(){
        em.transaction.commit()
        em.transaction.begin()
    }


    fun <E : AuEntity> deleteAllExcept(vararg except: AuEntity) =



        findAll<AuEntity>(include_deleted = true).forEach {
            if (!except.contains(it)) {
                delete(it)
            } else {
                Log.info("not deleting: ${it::class.simpleName} with id: ${it.id_str()}")
            }
        }


    inline fun <reified T : AuEntity> findAll(include_deleted: Boolean = false): List<T> =
        try {
            val query: String = "SELECT t FROM ${T::class.java.name} t" + (
                    if (!include_deleted) " WHERE t.deleted=false"
                    else "")
            em.createQuery(query, T::class.java).resultList
        } catch (e: Exception) {
            Log.info(e.message)


            listOf()
        }




    inline fun <reified T : AuEntity> query(query: String): List<T> =
        try {
            em.createQuery(query, T::class.java).resultList
        } catch (e: Exception) {
            Log.info(e.message)
            emptyList<T>()
        }




    inline fun <reified T : AuEntity> filter(include_deleted: Boolean = false, pred: (T) -> Boolean): List<T> =
        findAll<T>(include_deleted).filter { pred(it) }


    inline fun <reified T : AuEntity> findFirst(include_deleted: Boolean = false, pred: (T) -> Boolean): T? =
        filter(include_deleted, pred).firstOrNull()



    inline fun <reified T : AuEntity> byId(oid: String?, include_deleted: Boolean = false): T? =
        byId(oid?.toLongOrNull(), include_deleted)


    inline fun <reified T : AuEntity> byId(oid: Long?, include_deleted: Boolean = false): T? =
        oid?.let { em.find(T::class.java, oid) }?.takeIf { include_deleted || !it.deleted }


}

================
File: main/kotlin/au21/engine/framework/database/AuEntityManagerFactory.kt
================
package au21.engine.framework.database

import io.quarkus.logging.Log
import jakarta.annotation.PostConstruct
import jakarta.enterprise.context.ApplicationScoped
import jakarta.ws.rs.Produces
import org.eclipse.microprofile.config.inject.ConfigProperty
import java.io.File
import javax.persistence.EntityManagerFactory
import javax.persistence.Persistence


@ApplicationScoped
class AuEntityManagerFactory {

    @ConfigProperty(name = "OBJECTDB_ACTIVATION_CODE")
    lateinit var OBJECTDB_ACTIVATION_CODE: String

    @ConfigProperty(name = "OBJECTDB_ADMIN_USER", defaultValue = "admin")
    lateinit var OBJECTDB_ADMIN_USER: String

    @ConfigProperty(name = "OBJECTDB_ADMIN_PASSWORD", defaultValue = "admin")
    lateinit var OBJECTDB_ADMIN_PASSWORD: String

    @ConfigProperty(name = "OBJECTDB_CONFIG_TEMPLATE_PATH")
    lateinit var OBJECTDB_CONFIG_TEMPLATE_PATH: String

    @ConfigProperty(name = "OBJECTDB_CONFIG_GENERATED_PATH")
    lateinit var OBJECTDB_CONFIG_GENERATED_PATH: String

    @ConfigProperty(name = "OBJECTDB_DB_HOME")
    lateinit var OBJECTDB_DB_HOME: String

    @ConfigProperty(name = "OBJECTDB_URL")
    lateinit var OBJECTDB_URL: String

    @PostConstruct
    fun init(){
        Log.info("OBJECTDB_ACTIVATION_CODE: $OBJECTDB_ACTIVATION_CODE")
        Log.info("OBJECTDB_ADMIN_USER: $OBJECTDB_ADMIN_USER")
        Log.info("OBJECTDB_ADMIN_PASSWORD: $OBJECTDB_ADMIN_PASSWORD")
        Log.info("OBJECTDB_CONFIG_TEMPLATE_PATH: $OBJECTDB_CONFIG_TEMPLATE_PATH")
        Log.info("OBJECTDB_CONFIG_GENERATED_PATH: $OBJECTDB_CONFIG_GENERATED_PATH")
        Log.info("OBJECTDB_DB_HOME: $OBJECTDB_DB_HOME")
        Log.info("OBJECTDB_URL: $OBJECTDB_URL")
    }

    val BASE_DIR: String = System.getProperty("user.dir").let {
        println("user.dir: $it")
        val index = it.indexOf("/build/classes/kotlin/main")
        if (index > -1)
            it.substring(0, index)
        else
            it
    }

    fun to_absolute_path(relative_path: String): String =
        "$BASE_DIR/$relative_path".let { s: String ->
            s.replace("\\\\", "\\")
        }

    val config_logging = false







    private fun getTemplateFileFromClasspath(): File {

        val resource = javaClass.classLoader.getResource("au21-engine.objectdb.template.xml")
            ?: throw IllegalStateException("ObjectDB template file not found in classpath")
        return File(resource.toURI())
    }


    @ApplicationScoped
    @Produces
    fun createEntityManagerFactory(): EntityManagerFactory {


        Log.info("\nOBJECTDB_CONFIG_TEMPLATE_PATH: $OBJECTDB_CONFIG_TEMPLATE_PATH")








        val template: String = getTemplateFileFromClasspath().readText()

        Log.info("NB: Objectdb config template: \n$template")


        File(to_absolute_path(OBJECTDB_CONFIG_GENERATED_PATH)).also { f ->
            val path = f.canonicalPath
            Log.info("Location to write Objectdb config file: $path")
            if (f.exists()) {
                Log.info("Deleting existing objectdb config file at: $path")
                f.delete()
            } else {
                Log.info("Creating directories")
                f.parentFile.mkdirs()
            }
            f.createNewFile()
            Log.info("Created empty objectdb config file: $path")

            val generated = template
                .replace("OBJECTDB_ACTIVATION_CODE", OBJECTDB_ACTIVATION_CODE)
                .replace("OBJECTDB_ADMIN_PASSWORD", OBJECTDB_ADMIN_PASSWORD)
                .replace("OBJECTDB_ADMIN_USER", OBJECTDB_ADMIN_USER)
                .replace("OBJECTDB_URL", OBJECTDB_URL)

            f.writeText(generated)
            Log.info("\nConfig file written at: $path")
            Log.info(f.readText())


            Log.info("Setting objectdb.conf to: $path")
            System.setProperty("objectdb.conf", path)
        }


        val objectdHomeDir: File = File(to_absolute_path(OBJECTDB_DB_HOME)).also {
            Log.info("Setting objectdb.home to: ${it.canonicalPath}")
            System.setProperty("objectdb.home", it.canonicalPath)
            it.mkdirs()
        }



        Log.info("Objectdb url: \n$OBJECTDB_URL")








        return if (OBJECTDB_URL.endsWith(".mem")) {
            Persistence.createEntityManagerFactory(OBJECTDB_URL)

        } else if (OBJECTDB_URL.startsWith("objectdb://")) {
            Persistence.createEntityManagerFactory(OBJECTDB_URL,
                HashMap<String, String>().apply {
                    put("javax.persistence.jdbc.user", OBJECTDB_ADMIN_USER)
                    put("javax.persistence.jdbc.password", OBJECTDB_ADMIN_PASSWORD)
                })
        } else {

            File(objectdHomeDir, OBJECTDB_URL).let { odb_file ->
                val path = odb_file.canonicalPath
                Log.info("DB Location: $path")
                Persistence.createEntityManagerFactory(path)
            }
        }
    }
}

================
File: main/kotlin/au21/engine/framework/database/IAuctionState.kt
================
package au21.engine.framework.database

interface IAuctionState {
    fun oneOf(vararg states: IAuctionState): Boolean
}

================
File: main/kotlin/au21/engine/framework/features/AuFeatures.kt
================
package au21.engine.framework.features

import jakarta.enterprise.context.ApplicationScoped


@ApplicationScoped
class AuFeatures {




    var heartbeat = true
}

================
File: main/kotlin/au21/engine/framework/graphql/GraphqlApi.kt
================
package au21.engine.framework.graphql

import au21.engine.domain.common.commands.*
import au21.engine.domain.common.model.*
import au21.engine.domain.common.services.sessions_logged_in
import au21.engine.domain.common.services.sessions_non_terminated
import au21.engine.domain.de.commands.*
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeFlowControlType
import au21.engine.framework.commands.AlertException
import au21.engine.framework.commands.EngineCommandEnvelope
import au21.engine.framework.commands.EngineCommandHandler
import au21.engine.framework.database.AuEntity
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.features.AuFeatures
import au21.engine.framework.utils.to_json
import com.google.common.collect.ImmutableList
import jakarta.inject.Inject
import org.eclipse.microprofile.graphql.GraphQLApi
import org.eclipse.microprofile.graphql.Mutation
import org.eclipse.microprofile.graphql.Query
import org.joda.time.DateTime
import java.util.*











@GraphQLApi

class MutationApi1 {
    @Mutation
    fun mutation1(msg: String): String {
        println(msg)
        return msg
    }
}


@GraphQLApi
class GraphqlApi {









    @Inject
    lateinit var handler: EngineCommandHandler

    @Inject
    lateinit var db: AuEntityManager

    @Inject
    lateinit var features: AuFeatures

    @Query
    fun sessions(include_terminated: Boolean, logged_in_only: Boolean): List<AuSession> =
        when {
            include_terminated && logged_in_only -> throw AlertException("Cannot search for terminated sessions that are logged in.")
            include_terminated -> db.findAll()

            logged_in_only -> db.sessions_logged_in()
            else -> db.sessions_non_terminated()
        }

    @Query
    fun people(): List<Person> = db.findAll<Person>()

    @Query
    fun companies(): List<Company> = db.findAll()

    @Query
    fun users(): List<Person> = db.findAll()

    @Query
    fun auctions(): List<Auction> = db.findAll()

    @Query
    fun de_auctions(): List<DeAuction> = db.findAll()

    @Query
    fun entities(): List<AuEntity> = db.findAll()


    @Mutation
    fun initDb(): String {
        handler.handle(
            db,
            EngineCommandEnvelope(
                session_id = "",
                command = DbInitCommand()
            ).to_json()
        )
        return "send DbInitCommand to handler."
    }

    @Mutation
    fun deleteAuctions(id: String): String {
        return handler.handle(
            db,
            EngineCommandEnvelope(
                session_id = "",
                command = DbDeleteAuctionsCommand()
            ).to_json()
        )
    }

    /**
     * Microprofile API:
     * https://download.eclipse.org/microprofile/microprofile-graphql-1.0/microprofile-graphql.html#mutations
     */
    @Mutation
    fun reCreateDummyDb(
        auction_count: Int = 1,
        auctioneer_count: Int = 2,
        trader_count: Int = 4,
        round_count: Int = 1,
        close_last_round: Boolean = false,
        use_counterparty_credits: Boolean = false,
    ): String {
//        Unirest.get("http:

        handler.handle(
            db, EngineCommandEnvelope(
                session_id = "",
                command = DeCreateSampleDbCommand(
                    auction_count = auction_count,
                    auctioneer_count = auctioneer_count,
                    round_count = round_count,
                    trader_count = trader_count,
                    close_last_round = close_last_round,
                    use_counterparty_credits = use_counterparty_credits
                )
            ).to_json()
        )
        return "command sent to engine command handler via REST api"
    }


    @Mutation
    fun createAuction(name: String): String {
        val d = DateTime()
        val sessionId = create_session_and_login("a1", "1")
        return handler.handle(
            db,
            EngineCommandEnvelope(
                session_id = sessionId,
                command = DeAuctionSaveCommand(
                    auction_id = "", // If exists then update else create new
                    auction_name = name,
                    use_counterparty_credits = "false",
                    quantity_label = "MMlb",
                    quantity_minimum = "1",
                    quantity_step = "1",
                    price_change_initial = "0.5",
                    price_change_post_reversal = "0.125",
                    price_label = "cpp",
                    price_decimal_places = "3",
                    cost_multiplier = "10000",



                    excess_level_0_label = "+",
                    excess_level_1_label = "++",
                    excess_level_2_label = "+++",
                    excess_level_3_label = "++++",
                    excess_level_4_label = "+++++",
                    excess_level_1_quantity = "10",
                    excess_level_2_quantity = "20",
                    excess_level_3_quantity = "30",
                    excess_level_4_quantity = "40",



                    starting_price_announcement_mins = "5",
                    month_is_1_based = true,
                    starting_year = d.year.toString(),
                    starting_month = d.monthOfYear.toString(),
                    starting_day = d.dayOfMonth.toString(),
                    starting_hour = d.hourOfDay.toString(),
                    starting_mins = d.minuteOfHour.toString(),



                    round_red_secs = "15",
                    round_orange_secs = "30",
                    round_open_min_seconds = "15",
                    round_closed_min_secs = "5"
                )
            ).to_json()
        )
    }

    @Mutation
    fun heartbeat(on: Boolean): String {
        features.heartbeat = on
        return "Heartbeat " + if (on) "on" else "off"
    }

    @Mutation
    fun trace_heartbeat(on: Boolean): String {
        handler.trace_heartbeat = on
        return "Heartbeat Trace: " + if (on) "on" else "off"
    }

    @Mutation
    fun broker_mode(on: Boolean): String {
        handler.trace_heartbeat = on
        return "Heartbeat Trace: " + if (on) "on" else "off"
    }

    @Mutation
    fun create_company(shortName: String): String {
        val sessionId = create_session_and_login("a1", "1")
        return handler.handle(
            db,
            EngineCommandEnvelope(
                session_id = sessionId,
                command = CompanySaveCommand("", shortName, "${shortName}_long")
            ).to_json()
        )
    }

    @Mutation
    fun flowControlMutation(auction_id: String, flowControlType: DeFlowControlType): String {
        val sessionId = create_session_and_login("a1", "1")
        return handler.handle(
            db,
            EngineCommandEnvelope(
                session_id = sessionId,
                command = DeFlowControlCommand(
                    auction_id,
                    flowControlType,
                    "10"
                )
            ).to_json()
        )
    }

    @Mutation
    fun addTrader(auction_id: String, traderId: String): String {
        val sessionId = create_session_and_login("a1", "1")
        return handler.handle(
            db,
            EngineCommandEnvelope(
                session_id = sessionId,
                command = DeTradersAddCommand(
                    auction_id,
                    ImmutableList.of(traderId)
                )
            ).to_json()
        )
    }

    @Mutation
    fun orderSubmit(
        auction_id: String,
        traderId: String,
        orderType: OrderType,
        round: String,
        quantity: String,
    ): String {
        val sessionId = create_session_and_login("a1", "1")

        return handler.handle(
            db,
            EngineCommandEnvelope(
                session_id = sessionId,
                command = DeOrderSubmitCommand(
                    auction_id,
                    traderId,
                    orderType,
                    round,
                    quantity
                )
            ).to_json()
        )
    }

    private fun create_session_and_login(userName: String, password: String): String {
        val sessionId = UUID.randomUUID().toString()
        handler.handle(
            db,
            EngineCommandEnvelope(
                session_id = "",
                ClientSocketCommand(sessionId, AuSession.ClientSocketState.OPENED)
            ).to_json()
        )

        handler.handle(
            db,
            EngineCommandEnvelope(
                session_id = sessionId,
                LoginCommand(
                    "a1",
                    "1"
                )
            ).to_json()
        )
        return sessionId
    }
}

================
File: main/kotlin/au21/engine/framework/observability/GelfLoggingResource.kt
================
package au21.engine.framework.observability

import io.quarkus.logging.Log
import jakarta.enterprise.context.ApplicationScoped
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path

@Path("/gelf-logging")
@ApplicationScoped
class GelfLoggingResource {

    @GET
    fun log():String {
        Log.info("Some useful log message")
        return "logged"
    }
}

================
File: main/kotlin/au21/engine/framework/observability/metrics.kt
================
package au21.engine.framework.metrics

import au21.engine.framework.utils.thousands
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path

@ApplicationScoped
class AuMetrics {

    class RequestLatency(
        val cmd:String,
        val command_handling_full: String,
        val stores_json_zip_send: String
    )

    var request_latencies: MutableList<RequestLatency> = mutableListOf()

    fun add_request_latency(cmd:String, start_command: Long, start_stores: Long) {
        val now = System.nanoTime()
        this.request_latencies.add(
            RequestLatency(
                cmd,
                ((now - start_command) / 1_000).thousands() + " us",
                ((now - start_stores) / 1_000).thousands() + " us"
            )
        )
        while (this.request_latencies.size > 30) {
            this.request_latencies.removeFirst()
        }
    }
}


@Path("/au-metrics")
class MetricsController {

    @Inject
    lateinit var metrics: AuMetrics

    @GET
    fun toggle_session_differ(): String {
        var r = "<meta http-equiv='refresh' content='1'>"
        r += "<table border='1'>"
        r += "<thead><tr><th>command</th><th>full</th><th>stores</th></tr></thead>"
        metrics.request_latencies.forEach {
            r += "<tr>"
            r += "<td><div style='width:400px; overflow:auto'>${it.cmd}</div></td>"
            r += "<td>${it.command_handling_full}</td>"
            r += "<td>${it.stores_json_zip_send}</td>"
            r += "</tr>"
        }
        r += "</table>"
        return r
    }
}

================
File: main/kotlin/au21/engine/framework/observability/tracing.kt
================
package au21.engine.framework.observability


import jakarta.enterprise.context.ApplicationScoped




@ApplicationScoped
class AuTracer {













































}

================
File: main/kotlin/au21/engine/framework/utils/au-format-utils.kt
================
package au21.engine.framework.utils

import io.quarkus.logging.Log
import org.joda.time.DateTime
import org.joda.time.DateTimeZone
import org.joda.time.Interval
import org.joda.time.Period
import org.joda.time.format.DateTimeFormat
import org.joda.time.format.DateTimeFormatter
import org.joda.time.format.ISODateTimeFormat
import java.text.DecimalFormat
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.max



private val thousands_formatter = DecimalFormat("#,###")
fun Int?.thousands(): String = this?.toLong().thousands()
fun Long?.thousands(): String =
    if (this == null) ""
    else thousands_formatter.format(this)
// val mem_format_thousands = Memoize1(::format_thousands)


object AuFormatter {

    ///////////////////////////////////////////////////
    // TODO: add memoization
    // from: https://gist.github.com/sureshg/9aa4bed513f8d4179ad2893ecb7886f4

//    val memIsTextPresent = Memoize2(isTextPresent)
//
//    fun isTextPresent(path: Path, text: String) = path.toFile().readText().contains(text, true)
//
//    class Memoize2<A, B, C>(val func: (A, B) -> C) : (A, B) -> C {
//        val cache = hashMapOf<String, C>()
//        override fun invoke(p1: A, p2: B) = cache.getOrPut(p1.toString(), { func(p1, p2) })
//    }
//
//    class Memoize1<A, C>(val func: (A) -> C) : (A) -> C {
//        val cache = hashMapOf<String, C>()
//        override fun invoke(p1: A) = cache.getOrPut(p1.toString(), { func(p1) })
//    }

    ////////////////////////////////////////////////////

    val fmt: DateTimeFormatter? = ISODateTimeFormat.dateTime()
    val bid_table_formatter: DateTimeFormatter? = DateTimeFormat.forPattern("MMM dd, HH:mm:ss")
    val flatpickr_fmt: DateTimeFormatter? =
        DateTimeFormat.forPattern("yyyy-MM-dd'T'HH:mm:ss'Z'").withZone(DateTimeZone.UTC)
    val place_formatters = LinkedHashMap<Int, DecimalFormat>()
    val simple_date: DateTimeFormatter = DateTimeFormat.forPattern("MMM dd")
    val simple_time: DateTimeFormatter = DateTimeFormat.forPattern("HH:mm:ss")

    val auction_row_date_formatter = SimpleDateFormat("EEE, d MMM yyyy")
    val auction_row_time_formatter = SimpleDateFormat("HH:mm:ss zzz")

    val order_time_format = SimpleDateFormat("HH:mm:ss")
    val months = arrayOf("Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec")

    fun parseIsoDateString(s: String): Date? =
        try {
            fmt!!.parseDateTime(s).toDate()
        } catch (e: Exception) {
            Log.info(e.message)
            null
        }

    fun date_time_format(d: Date): String =
        "${simple_date.print(d.time)}, ${simple_time.print(d.time)}"


    fun formatDateTime_for_flatpickr(date: Date): String =
        flatpickr_fmt!!.print(date.time)

    fun bid_datetime_format(date: Date?): String =
        date?.let { bid_table_formatter!!.print(it.time) } ?: ""

    fun formatDateTime_for_te_status(date: Date): String =
        simple_time.print(date.time) + " on " + simple_date.print(date.time)

    // @Memoized
    fun toShortMonth(m: Int): String = months[m]

    //@Memoized
    fun to_duration(start: DateTime, end: DateTime): String {
        val interval = Interval(start, end)
        //val d: Duration = interval.toDuration()
        val p: Period = interval.toPeriod()

        return (if (p.years > 0) p.years.toString() + " years, " else "") +
                (if (p.months > 0) p.months.toString() + " months, " else "") +
                (if (p.days > 0) p.days.toString() + " days" else "") +
                (if (p.hours > 0) p.hours.toString() + " hours, " else "") +
                (if (p.minutes > 0) p.minutes.toString() + " minutes, " else "") +
                p.seconds + " seconds"
    }







    fun format_to_places(d: Double, decimal_places: Int): String {
        val places: Int = max(0, decimal_places)

        var format = "#,##0"
        if (places > 0) {
            format += "."
            repeat(places) { format += "0" }
        }

        var df: DecimalFormat? = place_formatters[decimal_places]
        if (df == null) {
            df = DecimalFormat(format)
            place_formatters[decimal_places] = df
        }

        return df.format(d)
    }

    fun format_currency(d: Double?): String = when(d){
        null -> ""
        else -> "$" + format_to_places(d, 2)
    }

    fun format_order_time(d: Date?): String =  // String fmt_str = "HH:mm:ss.SSS"
        d?.let { order_time_format.format(it) } ?: ""

    fun ltrim(s: String): String =
        s.replace("^\\s+".toRegex(), "")

    fun rtrim(s: String): String =
        s.replace("\\s+\$".toRegex(), "")

    fun seconds_until(d: Date?): Int? = d?.let {
        Period(DateTime(d), DateTime.now()).seconds
    }


}

================
File: main/kotlin/au21/engine/framework/utils/compression.kt
================
package au21.engine.framework.utils

import au21.engine.framework.client.ClientCommand
import java.io.ByteArrayOutputStream
import java.nio.ByteBuffer
import java.nio.charset.StandardCharsets.UTF_8
import java.util.zip.GZIPOutputStream





fun gzip(content: String): ByteBuffer =
    ByteArrayOutputStream().let { bos ->
        GZIPOutputStream(bos).bufferedWriter(UTF_8).use { it.write(content) }
        ByteBuffer.wrap(bos.toByteArray())
    }

fun gzipClientCommand(c: ClientCommand): ByteBuffer =
    gzip(c.to_json())

================
File: main/kotlin/au21/engine/framework/utils/config.kt
================
package au21.engine.framework.utils

import io.quarkus.logging.Log
import jakarta.annotation.PostConstruct
import jakarta.enterprise.context.ApplicationScoped
import org.eclipse.microprofile.config.inject.ConfigProperty

@ApplicationScoped
class HeartbeatConfig {

    @ConfigProperty(name = "HEARTBEAT", defaultValue = "ON")
    private lateinit var heartbeatStatus: String

    val heartbeat_enabled: Boolean
        get() = heartbeatStatus != "OFF"

    @PostConstruct
    fun init() {
        Log.info("HeartbeatConfig initialized with:$heartbeatStatus")
    }

}

================
File: main/kotlin/au21/engine/framework/utils/enum-utils.kt
================
package au21.engine.framework.utils

























inline fun <reified T : Enum<T>> toEnumOrNull(name: String): T? {
    return T::class.java.enumConstants.firstOrNull { it.name == name }
}









inline fun <reified T : Enum<T>> toEnumWithDefault(name: String, default: T): T {
    return toEnumOrNull<T>(name) ?: default
}








inline fun <reified T : Enum<T>> toEnumOrError(name: String): T {
    return toEnumOrNull<T>(name) ?: throw IllegalArgumentException("Invalid enum value: $name")
}

================
File: main/kotlin/au21/engine/framework/utils/json-utils.kt
================
package au21.engine.framework.utils


import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.google.gson.GsonBuilder
import com.google.gson.JsonObject
import com.google.gson.JsonParser
import io.micrometer.core.annotation.Timed


val mapper = jacksonObjectMapper()























private val gson = GsonBuilder().setPrettyPrinting().create()

fun jsonToPrettyFormat(jsonString: String?): String? {
    val json: JsonObject = JsonParser.parseString(jsonString).asJsonObject
    return gson.toJson(json)
}

fun objToPrettyFormat(o:Any):String {
    return gson.toJson(o)
}







@Timed("to_json")
fun Any?.to_json(): String =
    try {



        mapper.writeValueAsString(this)

    } catch (e: Throwable) {
        e.printStackTrace()
        throw e
    }





fun Any.to_json_with_args(message: String, vararg args: Any?): String {
    try {
        val jsonNode = mapper.readTree(message) as ObjectNode
        require(args.size % 2 == 0) { "Vararg arguments must be provided in key-value pairs" }
        for (i in args.indices step 2) {
            val key = args[i].toString()
            val value = args[i + 1].toString()
            jsonNode.put(key, value)
        }
        val formattedJson = ObjectMapper().writerWithDefaultPrettyPrinter()
        return formattedJson.writeValueAsString(jsonNode)
    } catch (e: Exception) {
        throw e
    }
}

================
File: main/kotlin/au21/engine/framework/utils/logging.kt
================
package au21.engine.framework.utils

import io.vertx.core.json.JsonObject
import java.util.logging.ConsoleHandler
import java.util.logging.LogRecord

class CustomJsonLogHandler : ConsoleHandler() {

    override fun publish(record: LogRecord) {
        if (!isLoggable(record)) {
            return
        }

        val json = JsonObject()
        json.put("timestamp", java.time.Instant.ofEpochMilli(record.millis).toString())
        json.put("level", record.level.name)
        json.put("message", record.message)
        json.put("class", record.sourceClassName.substringAfterLast('.'))
        json.put("line", record.sourceMethodName)


        println(json.encodePrettily())
    }
}

================
File: main/kotlin/au21/engine/framework/utils/misc-utils.kt
================
package au21.engine.framework.utils

import io.quarkus.logging.Log
import org.joda.time.format.DateTimeFormatter
import org.joda.time.format.ISODateTimeFormat
import java.util.*
import kotlin.math.floor
import kotlin.math.pow

fun String?.is_blank(): Boolean =
    this?.trim()?.isEmpty() ?: true




inline fun <reified T : Enum<T>> printAllValues() {
    print(enumValues<T>().joinToString { it.name })
}





















fun Double.round(precision: Int): Double =
    floor(this * 10.0.pow(precision.toDouble()) + 0.5) /
            10.0.pow(precision.toDouble())

fun main() {
    (0..10).forEach {
        Log.info(Math.PI.round(it))
    }

}

val iso_date_time_formatter: DateTimeFormatter =
    ISODateTimeFormat.dateTime()

fun String.iso_to_date(): Date? =
    try {
        iso_date_time_formatter.parseDateTime(this).toDate()
    } catch (e: Exception) {
        Log.info(e.message)
        null
    }

fun Date.date_to_iso(): String =
    iso_date_time_formatter.print(this.time)



interface Ideable {
    val id: String
}



sealed class CBResult<out T> {
    data class CBSuccess<out T>(val data: T) : CBResult<T>()
    data class CBError<out T>(val throwable: Throwable) : CBResult<T>()
}

fun <T> T.asResult() =
    CBResult.CBSuccess(data = this)

fun <T> Throwable.asErrorResult() =
    CBResult.CBError<T>(throwable = this)

================
File: main/kotlin/au21/engine/framework/utils/string-utils.kt
================
package au21.engine.framework.utils


private val charPool : List<Char> = ('a'..'z') + ('A'..'Z') + ('0'..'9')


fun randomString(string_length:Int):String = (1..string_length)
    .map { i -> kotlin.random.Random.nextInt(0, charPool.size) }
    .map(charPool::get)
    .joinToString("")


fun main(){
    println(randomString(4))
}

================
File: main/kotlin/au21/engine/framework/utils/table-formatter.kt
================
package au21.engine.framework.utils

import de.vandermeer.asciitable.AT_Row
import de.vandermeer.asciitable.AsciiTable
import de.vandermeer.skb.interfaces.transformers.textformat.TextAlignment
import io.quarkus.logging.Log
import kotlin.reflect.KCallable
import kotlin.reflect.full.declaredMembers

inline fun <reified T> format_table(
    collection: Collection<T>,
    field_names: List<String>,
    print_json: Boolean = false
): String {



    val members: List<KCallable<*>> =
        field_names.mapNotNull { name ->
            try {
                T::class.declaredMembers.find { m -> m.name == name }
                    ?: run {
                        println("field not found: $name")
                        null
                    }
            } catch (e: Throwable) {
                println("field not found: $name")
                null
            }
        }

    if (members.isEmpty()) {
        println("NO FIELDS FOUND")
        return ""
    }

    try {
        AsciiTable().apply {
            addRule()
            addRow(members.map { it.name })
            addRule()
            collection.forEach { item ->
                addRow(members.map { it.call(item) })
                    .also { row: AT_Row ->
                        members.forEachIndexed { index, m ->
                            when (m.call(item)) {
                                is Int ->
                                    row.cells[index].context.textAlignment = TextAlignment.RIGHT
                            }
                        }
                    }
            }
            addRule()
            return "\n + ${T::class.java.name}:\n" + render()
        }
    } catch (t: Throwable) {
        Log.error(t.localizedMessage)
        return ""
    }

//    if (print_json) {
//        // TODO: does this work with primitives?
//        println(objToPrettyFormat(collection.forEach { println(it.to_json()) }))
//    }

    // 2) with javaclass fields: not working only get companion

//    val fields:List<Field> = field_names.mapNotNull { name ->
//        try {
//            T::class.java.fields.find { f:Field ->
//                f.name == name
//            }
//                ?: run {
//                    println("field not found: $name")



































}

================
File: main/kotlin/au21/engine/framework/utils/TimeFormatter.kt
================
package au21.engine.framework.utils

import org.joda.time.DateTime
import java.util.*





object TimeFormatter {

    fun formatDuration(toDate: Date): String? {
        val secs: Int = (toDate.time - DateTime().toDate().time).toInt() / 1_000
        return if (secs < 0)
            null
        else formatDuration(secs)
    }

    fun formatDuration(seconds: Int): String {
        var _secs = seconds
        var res = ""
        val units = intArrayOf(31536000, 86400, 3600, 60, 1)
        val labels = arrayOf("year", "day", "hour", "minute", "second")
        if (_secs == 0) return "now"
        for (i in 0..4) {
            if (_secs >= units[i]) {
                val q = _secs / units[i]
                _secs %= units[i]
                res += ((if (res == "") "" else if (_secs == 0) " and " else ", ")
                        + q + " " + labels[i] + if (q > 1) "s" else "")
            }
        }
        return res
    }

}

================
File: main/kotlin/au21/engine/framework/enums.kt
================
package au21.engine.framework



























enum class PageName {

    CREDITOR_AUCTIONEER_PAGE,
    CREDITOR_TRADER_PAGE,
    HOME_PAGE,
    LOGIN_PAGE,
    SESSION_PAGE,
    USER_PAGE,

    BH_AUCTIONEER_PAGE,
    BH_SETUP_PAGE,
    BH_TRADER_PAGE,

    DE_AUCTIONEER_PAGE,
    DE_SETUP_PAGE,
    DE_TRADER_PAGE,

    MR_AUCTIONEER_PAGE,
    MR_SETUP_PAGE,
    MR_TRADER_PAGE,

    TE_AUCTIONEER_PAGE,
    TE_SETUP_PAGE,
    TE_TRADER_PAGE,

    TO_AUCTIONEER_PAGE,
    TO_SETUP_PAGE,
    TO_TRADER_PAGE
}

================
File: main/kotlin/au21/engine/generators/mermaid/mermaid-writer.kt
================
package au21.engine.generators.mermaid

import au21.engine.framework.database.AuEntity
import au21.engine.generators.get_sub_classes
import au21.engine.generators.resolve_directory
import gg.jte.CodeResolver
import gg.jte.ContentType
import gg.jte.TemplateEngine
import gg.jte.TemplateOutput
import gg.jte.output.StringOutput


class MermaidHelper(
    val description: String?,
    val title: String,
    val entity_classes: List<Class<*>>
)

fun main() {

    val codeResolver: CodeResolver = resolve_directory(MermaidHelper::class)
    val templateEngine: TemplateEngine = TemplateEngine.create(codeResolver, ContentType.Plain)
    val out: TemplateOutput = StringOutput()

    templateEngine.render(
        "mermaid-entities.jte",
        MermaidHelper(
            "desc",
            "title",
            get_sub_classes<AuEntity>()
        ),
        out
    )
    println(out)
}

================
File: main/kotlin/au21/engine/generators/plantuml/plantuml-writer.kt
================
package au21.engine.generators.plantuml

import au21.engine.framework.database.AuEntity
import au21.engine.generators.get_classes_with_annotation
import au21.engine.generators.get_enums_in_package
import au21.engine.generators.get_sub_classes
import ch.ifocusit.plantuml.classdiagram.ClassDiagramBuilder
import java.io.File
import javax.persistence.Embeddable

fun diagram(file: File, vararg classes: Class<*>) {
    val diagram: String = ClassDiagramBuilder()
        .addClasses(*classes)
        .build()
        .lines()
        .mapNotNull {
            when {
                it.trim().startsWith("_") -> null
                else -> it
            }
        }
        .joinToString("\n")


    println(file.absoluteFile)
    file.apply {

        writeText(diagram)
    }

}

fun main() {























    diagram(
        File("docs/diagrams/model/entities.puml"),
        *get_sub_classes<AuEntity>().toTypedArray()
    )

    diagram(
        File("docs/diagrams/model/entities_and_embedded.puml"),
        *listOf(
            get_sub_classes<AuEntity>(),
            get_classes_with_annotation(Embeddable::class.java)
        ).flatten().distinct().toTypedArray()
    )

    diagram(
        File("docs/diagrams/enums/enums.puml"),
        *get_enums_in_package().toTypedArray()
    )
}

================
File: main/kotlin/au21/engine/generators/typescript/exp/ts-generator-jte.kt
================
package au21.engine.generators.typescript.exp


import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.viewmodel.DateTimeValue
import au21.engine.framework.client.ClientCommand
import au21.engine.framework.client.LiveClientStore
import au21.engine.framework.client.StaleClientStore
import au21.engine.framework.client.StoreValue
import au21.engine.framework.commands.EngineCommand
import au21.engine.generators.get_sub_classes
import au21.engine.generators.resolve_directory
import au21.engine.generators.typescript.StoreClassHelper
import gg.jte.CodeResolver
import gg.jte.ContentType
import gg.jte.TemplateEngine
import gg.jte.output.StringOutput
import io.github.classgraph.ClassGraph
import io.github.classgraph.ClassInfo
import io.quarkus.logging.Log
import me.ntrrgc.tsGenerator.ClassTransformer
import me.ntrrgc.tsGenerator.TypeScriptGenerator
import me.ntrrgc.tsGenerator.onlyOnSubclassesOf
import net.pearx.kasechange.toSnakeCase
import java.io.File
import java.lang.reflect.Field
import java.lang.reflect.ParameterizedType
import kotlin.reflect.KClass
import kotlin.reflect.KProperty
import kotlin.reflect.KVisibility
import kotlin.reflect.full.isSubclassOf
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.javaField







private fun outputToFile(templateEngine: TemplateEngine, templateParams: TsGeneratorParams, to_file: Boolean = false) {
    val output = StringOutput()
    templateEngine.render("ts-generator.jte", templateParams, output)
    println(output)

    if (to_file) {
        File("generated", "generated-jte.ts").apply {
            if (exists()) {
                Log.info("deleting:$canonicalPath")
                delete()
            }
            Log.info("creating: $canonicalPath")
            createNewFile()
            writeText(output.toString())
        }










    }
}


class TsGeneratorParams(
    val version: String
) {
    val pkg = "au21.engine"



    class EnumFields(val name: String, val fields: List<String>) {
        companion object {
            fun create(): List<EnumFields> =
                ClassGraph()
                    .verbose()
                    .enableAllInfo()
                    .acceptPackages(TsGeneratorJte.pkg)
                    .scan()
                    .allEnums.map { info: ClassInfo ->
                        EnumFields(
                            name = info.simpleName,
                            fields = info.fieldInfo.filter { it.name != "\$VALUES" }.map { it.name }
                        )
                    }
        }
    }

    val enums: List<EnumFields> = EnumFields.create()


    val request_classes: List<Class<EngineCommand>> = get_sub_classes()


    val commands: List<String> = request_classes.map {
        val name = it.simpleName
        when (it.kotlin.memberProperties.size) {
            0 -> "\nexport const ${name.toSnakeCase()} = () => \n\tcreate_command('${name}', '${it.canonicalName}', {})\n"
            else -> "\nexport const ${name.toSnakeCase()} = (req: $name) => \n\tcreate_command('${name}', '${it.canonicalName}', req)\n"
        }
    }

    val request_ts_generator =
        TypeScriptGenerator(rootClasses = request_classes.map { Class.forName(it.name).kotlin }
            .filter { it.isSubclassOf(EngineCommand::class) }, classTransformers = listOf(
            object : ClassTransformer {
                override fun transformPropertyList(
                    properties: List<KProperty<*>>, klass: KClass<*>
                ): List<KProperty<*>> = properties.filter { property ->

                    !listOf(
                        KVisibility.PRIVATE, KVisibility.PROTECTED
                    ).contains(property.visibility) && !listOf(
                        AuSession::class,
                        DateTimeValue::class
                    ).contains(
                        klass
                    ) && !listOf("session").contains(property.name)

                }
            }.onlyOnSubclassesOf(EngineCommand::class)
        )
        )

    val request_classes_str: String = request_ts_generator

        .individualDefinitions.map {
            Log.info(it)
            it
        }.filterNot { it.startsWith("type") }.filterNot { it.startsWith("interface Companion") }
        .filterNot { it.contains("Handler") }


        .sorted()
        .joinToString(separator = "\n\nexport ")


    val client_command_classes: String = "export " + TypeScriptGenerator(
        rootClasses = get_sub_classes<List<Class<ClientCommand>>>().map { Class.forName(it.name).kotlin },
        ignoreSuperclasses = setOf(Cloneable::class),
        classTransformers = listOf(object : ClassTransformer {
            override fun transformPropertyList(
                properties: List<KProperty<*>>, klass: KClass<*>
            ): List<KProperty<*>> = properties.filter { property ->

                property.visibility != KVisibility.PRIVATE
            }
        })
    )

        .individualDefinitions.filter {

            !it.startsWith("interface LiveClientStore")
        }.sorted().filterNot { it.startsWith("type") }.joinToString(separator = "\n\nexport ")




    var count = 0

    fun heading(heading: String): String {
        count++
        return """





        """.trimIndent()
    }

}











fun main() {








    val codeResolver: CodeResolver = resolve_directory(TsGeneratorParams::class)
    val templateEngine: TemplateEngine = TemplateEngine
        .create(codeResolver, ContentType.Plain)
        .apply { setTrimControlStructures(true) }
    val params = TsGeneratorParams("0.0.0")

    outputToFile(templateEngine, params, true)



    if (true) return












}






object TsGeneratorJte {





    val pkg = "au21.engine"

    fun generate(f: File) {

        f.apply {
















            StoreClassHelper.write(f, LiveClientStore::class, "AuStore")





            StoreClassHelper.write(f, StaleClientStore::class)


        }



        Log.info(f.readText())
    }

    fun validate(f: File) {
        var failed = false
        fun duplicates(search: String, err_msg: String) =
            f.readLines().filter { it.startsWith(search) }.groupingBy { it }.eachCount().filter { it.value > 1 }
                .takeIf { it.isNotEmpty() }?.let { duplicates ->
                    Log.error("ERROR: duplicate $err_msg found: $duplicates")
                    failed = true
                }
        duplicates("export interface", "interfaces")
        duplicates("export class", "classes")
        if (failed) {
            throw Error("Duplicates detected")
        }
    }
}


class StoreClassHelperJte(
    val prop: String, val type: String, val default: String?
) {
    companion object {

        fun write(f: File, t: KClass<out Any>, extends: String? = null) {

            f.appendText("\nexport class ${t.simpleName}")
            extends?.let { f.appendText(" extends $extends ") }
            f.appendText("{\n")

            to_store_helpers(t).apply {
                forEach { h: StoreClassHelper ->
                    f.appendText("\t${h.prop}: ${h.type}")
                    h.default?.let {
                        f.appendText(" = $it")
                    }
                    f.appendText("\n")
                }
                f.appendText("}\n")
            }

        }

        fun to_store_helpers(k: KClass<out Any>): List<StoreClassHelper> =

            mutableListOf<StoreClassHelper>().apply {

                k.memberProperties.forEach { p ->
                    val ff: Field = p.javaField!!
                    val type: Class<*> = ff.type
                    val is_nullable = p.returnType.isMarkedNullable

                    type.genericInterfaces.forEach {

                        if (it.typeName == StoreValue::class.java.name) {
                            add(
                                StoreClassHelper(
                                    prop = p.name, type = type.simpleName + when {
                                        is_nullable -> " | null"
                                        else -> ""
                                    }, default = when {
                                        is_nullable -> "null"
                                        else -> null
                                    }
                                )
                            )
                        }
                    }

                    if (type.toString() == List::class.java.toString()) {
                        (ff.genericType as ParameterizedType).actualTypeArguments.forEach {
                            it.typeName.let { elem ->
                                println(elem)
                                val elem_name = elem.substring(elem.lastIndexOf(".") + 1, elem.length)
                                add(
                                    StoreClassHelper(
                                        prop = p.name, type = "$elem_name[]", default = "[]"
                                    )
                                )
                            }
                        }
                    }
                }
            }.toList().also {
                println(it)
            }
    }
}

================
File: main/kotlin/au21/engine/generators/typescript/ts-generator-2-gpt4o.kt
================
package au21.engine.generators.typescript

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.viewmodel.DateTimeValue
import au21.engine.framework.client.ClientCommand
import au21.engine.framework.client.LiveClientStore
import au21.engine.framework.client.StaleClientStore
import au21.engine.framework.client.StoreValue
import au21.engine.framework.commands.EngineCommand
import au21.engine.generators.get_sub_classes
import io.github.classgraph.ClassGraph
import io.github.classgraph.ClassInfo
import io.github.classgraph.ScanResult
import io.quarkus.logging.Log
import me.ntrrgc.tsGenerator.ClassTransformer
import me.ntrrgc.tsGenerator.TypeScriptGenerator
import me.ntrrgc.tsGenerator.onlyOnSubclassesOf
import net.pearx.kasechange.toSnakeCase
import java.io.File
import java.lang.reflect.Field
import java.lang.reflect.ParameterizedType
import kotlin.reflect.KClass
import kotlin.reflect.KProperty
import kotlin.reflect.KVisibility
import kotlin.reflect.full.isSubclassOf
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.javaField


const val TARGET_DIR_PATH = "temp"
const val TARGET_FILE_NAME = "generated.ts"
const val PACKAGE_NAME = "au21.engine"

fun main() {
    val targetDir = File(TARGET_DIR_PATH)
    val targetFile = File(targetDir, TARGET_FILE_NAME)

    try {
        createTargetDirectory(targetDir)
        generateAndValidate(targetFile)
    } catch (e: Throwable) {
        Log.error("Error during TypeScript generation", e)
    }
}

fun createTargetDirectory(dir: File) {
    if (!dir.exists()) {
        dir.mkdirs()
    }
}

fun generateAndValidate(file: File) {
    file.also {
        TsGenerator.generate(it)
        TsGenerator.validate(it)
    }
}

object TsGenerator {

    val pkg = "au21.engine"
    fun generate(file: File) {
        resetFile(file)

        file.apply {
            writeText("")
            //appendSectionHeader("IMPORTS")
            appendSectionHeader("ENUMS")
            generateEnums(this)

            appendSectionHeader("ENGINE COMMAND (aka Requests) HELPERS")
            generateCommandHelpers(this)

            appendSectionHeader("ENGINE COMMAND (REQUEST) CLASSES")
            generateCommandClasses(this)

            appendSectionHeader("CLIENT STORE COMMANDS (aka Results)")
            generateClientStoreCommands(this)

            writeStoreClasses(this)
        }

        println(file.readText())
    }

    private fun resetFile(file: File) {
        if (file.exists()) {

            file.delete()
        }
        println("Creating: ${file.canonicalPath}")
        file.createNewFile()
    }

    private fun File.appendSectionHeader(heading: String) {
        appendText(
            """







            """.trimIndent()
        )
    }

    private fun generateEnums(file: File) {
        try {
            val scanResult: ScanResult = ClassGraph()
                .verbose()
                .enableAllInfo()
                .acceptPackages(PACKAGE_NAME)
                .scan()

            scanResult.allEnums.forEach { info: ClassInfo ->
                file.appendText("\nexport enum ${info.simpleName} {\n")
                val fields = info.fieldInfo
                    .filter { it.name != "\$VALUES" }
                    .filter { it.name != "\$ENTRIES" }
                    .map { it.name }
                    .joinToString(separator = ", \n") { value ->
                        "\t$value = '$value'"
                    }
                file.appendText(fields)
                file.appendText("\n}\n")
            }
        } catch (e: Exception) {
            Log.error("Error generating enums", e)
        }
    }

    private fun generateCommandHelpers(file: File) {
        file.appendText(
            """
                export interface EngineCommandEnvelope {
                    session_id: string
                    readonly simplename: string
                    readonly classname: string
                    readonly command: EngineCommand
                }

                function create_command<T extends EngineCommand>(
                    simplename: string,
                    classname: string,
                    command: T
                ): EngineCommandEnvelope {
                    return {
                        session_id: "",
                        simplename,
                        classname,
                        command
                    } as EngineCommandEnvelope
                }
            """.trimIndent()
        )

        val requestClasses = get_sub_classes<EngineCommand>()

        requestClasses.forEach {
            println(it.simpleName)
        }

        requestClasses.forEach {
            val name = it.simpleName
            val definition = when (it.kotlin.memberProperties.size) {
                0 -> "export const ${name.toSnakeCase()} = () => create_command('$name', '${it.canonicalName}', {})"
                else -> "export const ${name.toSnakeCase()} = (req: $name) => create_command('$name', '${it.canonicalName}', req)"
            }
            file.appendText("\n$definition\n")
        }
    }

    private fun generateCommandClasses(file: File) {
        val requestClasses = get_sub_classes<EngineCommand>()

        val requestTsGenerator = TypeScriptGenerator(
            rootClasses = requestClasses.map { Class.forName(it.name).kotlin }
                .filter { it.isSubclassOf(EngineCommand::class) },
            classTransformers = listOf(
                object : ClassTransformer {
                    override fun transformPropertyList(
                        properties: List<KProperty<*>>,
                        klass: KClass<*>
                    ): List<KProperty<*>> =
                        properties.filter { property ->
                            !listOf(KVisibility.PRIVATE, KVisibility.PROTECTED).contains(property.visibility) &&
                                    !listOf(AuSession::class, DateTimeValue::class).contains(klass) &&
                                    !listOf("session").contains(property.name)
                        }
                }.onlyOnSubclassesOf(EngineCommand::class)
            )
        )

        val requestDefinitions = requestTsGenerator.individualDefinitions
            .filterNot { it.startsWith("type") || it.startsWith("interface Companion") || it.contains("Handler") }
            .sorted()

        file.appendText("export ${requestDefinitions.joinToString(separator = "\n\nexport ")}")
    }


    private fun generateClientStoreCommands(file: File) {
        val clientCommands = get_sub_classes<ClientCommand>()

        file.appendText("export ${
            TypeScriptGenerator(
                rootClasses = clientCommands.map { Class.forName(it.name).kotlin },
                ignoreSuperclasses = setOf(Cloneable::class),
                classTransformers = listOf(
                    object : ClassTransformer {
                        override fun transformPropertyList(
                            properties: List<KProperty<*>>,
                            klass: KClass<*>
                        ): List<KProperty<*>> =
                            properties.filter { property ->
                                property.visibility != KVisibility.PRIVATE
                            }
                    }
                )
            ).individualDefinitions
                .filterNot { it.startsWith("type") || it.startsWith("interface LiveClientStore") }
                .sorted()
                .joinToString(separator = "\n\nexport ")
        }")

        file.appendText("\n\n
        file.appendText("\n\n// - eg: properties are always given default values for reactivity to work")
    }

    private fun writeStoreClasses(file: File) {
        file.appendText("\n\nexport class AuStore{\n")
        file.appendText("\ttime: TimeValue | null = null\n")
        file.appendText("\tseconds_since_last_message_received: number = 0\n")
        file.appendText("}\n\n")

        StoreClassHelper.write(file, LiveClientStore::class, "AuStore")
        StoreClassHelper.write(file, StaleClientStore::class)
    }

    fun validate(file: File) {
        var failed = false

        fun checkDuplicates(search: String, errMsg: String) {
            file.readLines()
                .filter { it.startsWith(search) }
                .groupingBy { it }
                .eachCount()
                .filter { it.value > 1 }
                .takeIf { it.isNotEmpty() }
                ?.let { duplicates ->
                    Log.error("ERROR: duplicate $errMsg found: $duplicates")
                    failed = true
                }
        }

        checkDuplicates("export interface", "interfaces")
        checkDuplicates("export class", "classes")

        if (failed) {
            throw Error("Duplicates detected")
        }
    }
}

class StoreClassHelper(
    val prop: String,
    val type: String,
    val default: String?
) {
    companion object {
        fun write(file: File, kClass: KClass<out Any>, extends: String? = null) {
            file.appendText("\nexport class ${kClass.simpleName}")
            extends?.let { file.appendText(" extends $extends ") }
            file.appendText("{\n")

            toStoreHelpers(kClass).forEach { helper ->
                file.appendText("\t${helper.prop}: ${helper.type}")
                helper.default?.let { file.appendText(" = $it") }
                file.appendText("\n")
            }
            file.appendText("}\n")
        }

        private fun toStoreHelpers(kClass: KClass<out Any>): List<StoreClassHelper> {
            val helpers = mutableListOf<StoreClassHelper>()

            kClass.memberProperties.forEach { prop ->
                val field: Field = prop.javaField!!
                val type: Class<*> = field.type
                val isNullable = prop.returnType.isMarkedNullable

                if (StoreValue::class.java.isAssignableFrom(type)) {
                    helpers.add(
                        StoreClassHelper(
                            prop.name,
                            type.simpleName + if (isNullable) " | null" else "",
                            if (isNullable) "null" else null
                        )
                    )
                } else if (type == List::class.java) {
                    (field.genericType as ParameterizedType).actualTypeArguments.forEach {
                        val elemName = it.typeName.substringAfterLast(".")
                        helpers.add(StoreClassHelper(prop.name, "$elemName[]", "[]"))
                    }
                }
            }

            return helpers
        }
    }
}

================
File: main/kotlin/au21/engine/generators/typescript/TsGeneratorVersionInfo.kt
================
package au21.engine.generators.typescript

import au21.engine.generators.getCurrentGitBranch
import au21.engine.generators.getCurrentGitCommit

data class TsGeneratorVersionInfo(

    val branch: String = getCurrentGitBranch(),

    val current_commit: List<String> = getCurrentGitCommit(),



    val version: String = ""
)

//fun updateVersion(filePath: String): String {
//    val mapper = jacksonObjectMapper()
//
//    val jsonFile = File(filePath)
//    val jsonMap: Map<String, String> = mapper.readValue(jsonFile)
//
//    val versionString = jsonMap["version"]

================
File: main/kotlin/au21/engine/generators/GitHelper.kt
================
package au21.engine.generators

import java.io.BufferedReader
import java.io.InputStreamReader

object GitHelper {
    fun getCurrentGitBranch(): String =
        Runtime.getRuntime()
            .exec("git rev-parse --abbrev-ref HEAD")
            .let {
                it.waitFor()
                BufferedReader(InputStreamReader(it.inputStream))
                    .readLine()
            }

    fun getCurrentGitCommit(): List<String> =
        Runtime.getRuntime()
            .exec("git log -1")
            .let {
                it.waitFor()
                BufferedReader(InputStreamReader(it.inputStream))
                    .readLines()
            }
}

fun main(args: Array<String>) {
    val commit = GitHelper.getCurrentGitCommit()
    for (s in commit) {
        println("s=${s}")
    }
    println(commit)
}

================
File: main/kotlin/au21/engine/generators/helpers.kt
================
package au21.engine.generators

import au21.engine.generators.typescript.TsGenerator
import io.github.classgraph.ClassGraph


@Suppress("UNCHECKED_CAST")
inline fun <reified T> get_sub_interfaces(pkg: String = "au21.engine"): List<Class<T>> =

    ClassGraph()
        .verbose()
        .enableAllInfo()
        .acceptPackages(pkg)
        .scan()
        .allClasses
        .filter {
            it.interfaces.any {
                it.simpleName == T::class.simpleName
            }
        }
        .map {
            Class.forName(it.name)
        } as List<Class<T>>

@Suppress("UNCHECKED_CAST")
inline fun <reified T> get_sub_classes(pkg: String = TsGenerator.pkg): List<Class<T>> {
    return ClassGraph()

        .acceptPackages(pkg)

        .scan()
        .allClasses
        .filter {
            it.extendsSuperclass(T::class.java.canonicalName)
        }
        .map {
            Class.forName(it.name)
        } as List<Class<T>>
}

fun get_classes_with_annotation(clazz: Class<out Annotation>, pkg: String = TsGenerator.pkg): List<Class<*>> =
    ClassGraph()
        .enableAnnotationInfo()

        .acceptPackages(pkg)

        .scan()
        .allClasses

        .filter {
            it.hasAnnotation(clazz)
        }
        .map { Class.forName(it.name) }

fun get_enums_in_package(pkg: String = TsGenerator.pkg): List<Class<*>> =
    ClassGraph()

        .acceptPackages(pkg)

        .scan()
        .allClasses

        .filter {
            it.isEnum
        }
        .map { Class.forName(it.name) }

================
File: main/kotlin/au21/engine/generators/template_helpers.kt
================
package au21.engine.generators

import gg.jte.resolve.DirectoryCodeResolver
import java.io.BufferedReader
import java.io.IOException
import java.io.InputStreamReader
import java.nio.file.Path
import kotlin.reflect.KClass





fun resolve_directory(clazz: KClass<*>, is_test: Boolean = false) =
    DirectoryCodeResolver(
        Path.of(
            "src",
            *listOf(
                listOf(
                    if (is_test) "test" else "main",
                    "kotlin"
                ),
                clazz
                    .toString()
                    .removePrefix("class ")
                    .trim()
                    .split(".")
                    .dropLast(1)
            ).flatten().toTypedArray()
        )
    )



@Throws(IOException::class, InterruptedException::class)
fun getCurrentGitCommit(): List<String> =
    Runtime.getRuntime()
        .exec("git log -1")
        .let {
            it.waitFor()
            BufferedReader(InputStreamReader(it.inputStream))
                .readLines()
        }

@Throws(IOException::class, InterruptedException::class)
fun getCurrentGitBranch(): String =
    Runtime.getRuntime()
        .exec("git rev-parse --abbrev-ref HEAD")
        .let {
            it.waitFor()
            BufferedReader(InputStreamReader(it.inputStream))
                .readLine()
        }

================
File: main/kotlin/au21/engine/Main.kt
================
package au21.engine

import au21.engine.framework.commands.EngineCommandHandler
import io.quarkus.logging.Log
import io.quarkus.runtime.Quarkus
import io.quarkus.runtime.QuarkusApplication
import io.quarkus.runtime.ShutdownEvent
import io.quarkus.runtime.StartupEvent
import io.quarkus.runtime.annotations.QuarkusMain
import jakarta.enterprise.context.ApplicationScoped
import jakarta.enterprise.event.Observes
import jakarta.inject.Inject
import java.lang.management.ManagementFactory
import javax.persistence.EntityManagerFactory


@ApplicationScoped
class Au21Engine : QuarkusApplication {










    @Inject
    lateinit var emf: EntityManagerFactory

    @Inject
    lateinit var handler: EngineCommandHandler

    @Throws(Exception::class)
    override fun run(vararg args: String): Int {
        Log.info("created: $emf")















        Quarkus.waitForExit()
        return 0
    }

    fun onStart(@Observes ev: StartupEvent?) {

    }

    fun onStop(@Observes ev: ShutdownEvent?) {


    }
}

fun show_memory() {

    val mb = 1024 * 1024
    val memoryBean = ManagementFactory.getMemoryMXBean()
    val xmx = memoryBean.heapMemoryUsage.max / mb
    val xms = memoryBean.heapMemoryUsage.init / mb
    Log.info("Initial Memory (xms) : ${xms}mb, Max Memory (xmx) : ${xmx}mb")
}

@QuarkusMain
class Main {


    companion object {


        @JvmStatic
        fun main(args: Array<String>) {



            show_memory()





            System
                .getProperties()
                .keys
                .map { it.toString() }
                .sortedBy { it }
                .joinToString("\n") { it + ":" + System.getProperty(it) }.also {
                    Log.info(it)
                }

            Quarkus.run(Au21Engine::class.java, *args)
        }

    }

}

================
File: main/kotlin/exp/otel/TracedResource.kt
================
package exp.otel

import au21.engine.framework.utils.date_to_iso
import io.opentelemetry.api.trace.Span
import io.opentelemetry.api.trace.SpanKind
import io.opentelemetry.api.trace.Tracer
import io.opentelemetry.context.Context
import io.quarkus.logging.Log
import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.Produces
import jakarta.ws.rs.core.MediaType
import java.util.*

@Path("/hello-otel")
class TracedResource {


    @Inject
    lateinit var tracer: Tracer

    val attr1 = "Hello Dave " + Date().toLocaleString()

    @GET
    @Produces(MediaType.TEXT_PLAIN)
    fun hello(): String {
        Log.info(attr1)
        method1()
        return attr1
    }

    fun method1() {
        val span: Span = tracer.spanBuilder("My custom span")
            .setAttribute("attr", Date().date_to_iso())
            .setAttribute("name", attr1)
            .setParent(Context.current().with(Span.current()))
            .setSpanKind(SpanKind.INTERNAL)
            .startSpan()

        Log.warn("method1")
        span.end()
    }

}

================
File: main/kotlin/exp/otel/tracing.kt
================
package exp.otel

import io.opentelemetry.api.trace.Tracer
import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import io.quarkus.logging.Log


@Path("/test")
class TestResource {
    @Inject
    lateinit var tracer: Tracer

    @GET
    @Path("/telemetry")
    fun testTelemetry(): String {

        val parentSpan = tracer!!.spanBuilder("test-parent-operation")
            .setAttribute("custom.attribute", "parent-value")
            .startSpan()

        try {
            parentSpan.makeCurrent().use { scope ->

                parentSpan.addEvent("Starting parent operation")



                val childSpan = tracer!!.spanBuilder("test-child-operation")
                    .setAttribute("custom.attribute", "child-value")
                    .startSpan()
                try {

                    childSpan.addEvent("Processing in child span")
                    Thread.sleep(100)
                    childSpan.addEvent("Child operation complete")

                    return "Telemetry test " + System.currentTimeMillis()
                } finally {
                    childSpan.end()
                }
            }
        } finally {
            parentSpan.end()
        }
    }

    @GET
    @Path("/logs")
    fun testLogs(): String {
        Log.trace("This is a TRACE message")
        Log.debug("This is a DEBUG message")
        Log.info("This is an INFO message")
        Log.warn("This is a WARN message")
        Log.error("This is an ERROR message")

        try {
            throw RuntimeException("Test exception")
        } catch (e: Exception) {
            Log.error("Caught test exception", e)
        }

        return "Logs generated"
    }
}

================
File: main/kotlin/exp/template/TemplateExp.kt
================
package exp.template

import au21.engine.generators.resolve_directory
import gg.jte.CodeResolver
import gg.jte.ContentType
import gg.jte.TemplateEngine
import gg.jte.TemplateOutput
import gg.jte.output.StringOutput

class ExpPage(
    val description: String?,
    val title: String
)

fun main() {


    val codeResolver: CodeResolver = resolve_directory(ExpPage::class)

    val templateEngine: TemplateEngine = TemplateEngine.create(codeResolver, ContentType.Plain)

    val output: TemplateOutput = StringOutput()

    templateEngine.render(
        "example.jte",
        ExpPage("desc", "title"),
        output
    )
    println(output)
}

================
File: main/kotlin/exp/opentelemetry-exp.kt
================
package exp


fun main() {

}

================
File: main/kotlin/exp/TracedResource.kt
================
package exp

import au21.engine.framework.utils.date_to_iso
import io.opentelemetry.api.trace.*
import io.opentelemetry.context.Context
import io.quarkus.logging.Log
import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.Produces
import jakarta.ws.rs.core.MediaType
import java.util.*


@Path("/hello")
class TracedResource {


    @Inject
    lateinit var tracer: Tracer

    val attr1 = "Hello Dave 11"

    @GET
    @Produces(MediaType.TEXT_PLAIN)
    fun hello(): String {
        Log.info(attr1)
        method1()
        return attr1
    }

    fun method1() {
        val span: Span = tracer.spanBuilder("My custom span")
            .setAttribute("attr", Date().date_to_iso())
            .setAttribute("name", attr1)
            .setParent(Context.current().with(Span.current()))
            .setSpanKind(SpanKind.INTERNAL)
            .startSpan()

        Log.warn("method1")
        span.end()
    }

}

================
File: main/kotlin/optimizationExps/NetworkResourceExp1.kt
================
package optimizationExps

import jakarta.ws.rs.GET
import jakarta.ws.rs.Path

@Path("/network-exp-1")
class NetworkResourceExp1 {

    @GET
    fun calc():String {
        return "calc3!"
    }
}

================
File: main/kotlin/org/acme/AcmeWebSocket.kt
================
package org.acme

import com.fasterxml.jackson.databind.ObjectMapper
import io.quarkus.logging.Log
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.websocket.*
import jakarta.websocket.server.PathParam
import jakarta.websocket.server.ServerEndpoint
import java.util.concurrent.ConcurrentHashMap
@ServerEndpoint("/acme-socket/{clientId}")
@ApplicationScoped
class AcmeWebSocket {


    enum class Type { JOIN, LEAVE, MESSAGE }
    data class Event(val type: Type, val from: String, val message: String? = null)


    @Inject
    lateinit var objectMapper: ObjectMapper



    private val sessions = ConcurrentHashMap<String, Session>()
    private val clientIds = ConcurrentHashMap<String, String>()

    @OnOpen
    fun onOpen(session: Session, @PathParam("clientId") clientId: String) {
        sessions[session.id] = session
        clientIds[session.id] = clientId
        Log.info("WebSocket opened for client: $clientId (Session ID: ${session.id})")
        val event = Event(Type.JOIN, clientId)
        broadcast(event)
    }

    @OnMessage
    fun onMessage(session: Session, message: String) {
        val clientId = clientIds[session.id] ?: "unknown"
        try {


            val incomingEvent = objectMapper.readValue(message, Event::class.java)




            val eventToBroadcast = incomingEvent.copy(from = clientId)

            Log.info("Received message from $clientId: $eventToBroadcast")
            broadcast(eventToBroadcast)

        } catch (e: Exception) {
            Log.error("Failed to parse message from $clientId: $message", e)

            val errorEvent = Event(Type.MESSAGE, "server", "ERROR: Could not process message - ${e.message}")
            sendToSession(session, errorEvent)
        }
    }

    @OnClose
    fun onClose(session: Session, closeReason: CloseReason) {
        val clientId = clientIds.remove(session.id)
        sessions.remove(session.id)
        if (clientId != null) {
            Log.info("WebSocket closed for client: $clientId (Reason: ${closeReason.reasonPhrase})")
            val event = Event(Type.LEAVE, clientId)
            broadcast(event)
        } else {
            Log.warn("WebSocket closed for unknown client (Session ID: ${session.id})")
        }
    }

    @OnError
    fun onError(session: Session, throwable: Throwable) {
        val clientId = clientIds[session.id] ?: "unknown"
        Log.error("WebSocket error for client $clientId (Session ID: ${session.id})", throwable)


        val errorEvent = Event(Type.MESSAGE, "server", "ERROR: ${throwable.message}")
        sendToSession(session, errorEvent)



        clientIds.remove(session.id)
        sessions.remove(session.id)

    }


    private fun broadcast(event: Event) {
        val message = objectMapper.writeValueAsString(event)
        sessions.values.forEach { session ->
            session.asyncRemote.sendText(message) { result ->
                if (!result.isOK) {
                    Log.error("Failed to send message to session ${session.id}", result.exception)

                }
            }
        }
    }


    private fun sendToSession(session: Session, event: Event) {
        try {
            val message = objectMapper.writeValueAsString(event)
            session.asyncRemote.sendText(message) { result ->
                if (!result.isOK) {
                    Log.error( "Failed to send error message to session ${session.id}", result.exception)
                }
            }
        } catch (e: Exception) {
             Log.error("Failed to serialize or send error message to session ${session.id}", e)
        }
    }
}

================
File: main/kotlin/simulator/CalculateMaxFlow.kt
================
package simulator

import org.psjava.algo.graph.flownetwork.FordFulkersonAlgorithm
import org.psjava.algo.graph.flownetwork.MaximumFlowAlgorithmResult
import org.psjava.algo.graph.pathfinder.BFSPathFinder
import org.psjava.algo.graph.pathfinder.DFSPathFinder
import org.psjava.ds.graph.CapacityEdge
import org.psjava.ds.graph.MutableCapacityGraph
import org.psjava.ds.numbersystrem.IntegerNumberSystem

fun calculateMaxFlow(edges: List<Edge>, isDFS: Boolean = true): Pair<Int, List<FlowResult>> {
    val nodes: Set<String> = mutableSetOf<String>().apply {

        add(Edge.SELL_SOURCE)
        add(Edge.BUY_SINK)
        edges.forEach { c: Edge ->
            add(c.from)
            add(c.to)
        }
    }.toSet()

    when {
        !nodes.contains(Edge.SELL_SOURCE) -> throw Error("SOURCE node not found.")
        !nodes.contains(Edge.BUY_SINK) -> throw Error("SINK node not found.")
    }

    val capacityGraph = MutableCapacityGraph.create<String, Int>().apply {
        nodes.forEach { n -> insertVertex(n) }
        edges.forEach { e -> addEdge(e.from, e.to, e.capacity) }
    }

    val result: MaximumFlowAlgorithmResult<Int, CapacityEdge<String, Int>> = FordFulkersonAlgorithm
        .getInstance(if (isDFS) DFSPathFinder.getInstance() else BFSPathFinder.getInstance())
        .calc(
            capacityGraph,
            Edge.SELL_SOURCE,
            Edge.BUY_SINK,
            IntegerNumberSystem.getInstance()
        )

    val maxFlow: Int = result.calcTotalFlow()
    val maxFlowFunction = result.calcFlowFunction()

    val flowResults: List<FlowResult> = nodes
        .filter { it != Edge.SELL_SOURCE && it != Edge.BUY_SINK }
        .flatMap { trader ->
            capacityGraph.getEdges(trader)
                .filter { it.to() != Edge.BUY_SINK }
                .map { e: CapacityEdge<String, Int> ->
                    FlowResult(
                        from_seller = e.from(),
                        to_buyer = e.to(),
                        flow = maxFlowFunction.get(e)
                    )
                }
        }

    return Pair(maxFlow,flowResults)
}

data class Edge(
    val from: String,
    val to: String,
    val capacity: Int
) {
    companion object {
        const val SELL_SOURCE = "SELL_SOURCE"
        const val BUY_SINK = "BUY_SINK"
    }
}

data class FlowResult(
    val from_seller: String,
    val to_buyer: String,
    val flow: Int
)

================
File: main/kotlin/simulator/Runner.kt
================
package simulator

import au21.engine.domain.common.model.OrderType
import au21.engine.domain.common.model.PriceDirection
import au21.engine.domain.de.model.DeBidConstraints
import au21.engine.domain.de.services.constraints.calculate_subsequent_round_constraints
import au21.engine.domain.de.validations.validate_de_order_against_constraints_and_throw_if_fails
import com.opencsv.CSVParser
import com.opencsv.CSVParserBuilder
import com.opencsv.CSVReader
import com.opencsv.CSVReaderBuilder
import gg.jte.ContentType
import gg.jte.TemplateEngine
import gg.jte.output.StringOutput
import gg.jte.resolve.DirectoryCodeResolver
import java.io.File
import java.nio.charset.Charset
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.StandardOpenOption

typealias Buyer = String
typealias Sellers = Map<String, String>

const val MAX_BUY_QUANTITY = 50
const val MAX_SELL_QUANTITY = 50

fun main() {
    val path = Path.of("src", "main", "jte")
    val codeResolver = DirectoryCodeResolver(path)
    val templateEngine = TemplateEngine.create(codeResolver, ContentType.Html)

    val (buyerSellerMap, sellerBuyerMap) = readCreditsCsv()
    val credits = Credits(buyerSellerMap, sellerBuyerMap)
    val edges = edgesFromCounterPartyLimits(sellerBuyerMap)

    val scenarios = readOrdersCsv()
    val roundInfoMap = scenarios.map {
        it.key to it.value.first
    }.toMap()

    val constraintsMap = scenarios.map {
        it.key to it.value.second
    }.toMap()

    val scenarioToRoundAttributes = scenarioRoundAttributes(roundInfoMap, edges)
    val templateParams = TemplateParams(
        credits,
        scenarioToRoundAttributes,
        constraintsMap
    )
    outputToFile(templateEngine, templateParams)
}

private fun scenarioRoundAttributes(
    scenarioToRoundInfo: Map<String, List<RoundInfo>>,
    edges: MutableList<Edge>
): Map<String, List<RoundAttributes>> {
    return scenarioToRoundInfo.map { m ->
        m.key to m.value.map { r ->
            val bids = r.bids
            val orderEdges = bids.flatMap {
                val edgeList = mutableListOf<Edge>()
                when (it.order) {
                    "BUY" -> {
                        edgeList.add(Edge(Edge.SELL_SOURCE, it.company, 0))
                        edgeList.add(Edge(it.company, Edge.BUY_SINK, it.quantity.toInt()))
                    }
                    "SELL" -> {
                        edgeList.add(Edge(Edge.SELL_SOURCE, it.company, it.quantity.toInt()))
                        edgeList.add(Edge(it.company, Edge.BUY_SINK, 0))
                    }
                    else -> throw RuntimeException("Invalid Order Type")
                }
                edgeList
            }.toList()
            val flow = calculateMaxFlow(edges + orderEdges)




            val totalDemand = bids.calculateTotalDemand()
            val totalSupply = bids.calculateTotalSupply()
            val absoluteDemandSupply = totalDemand.minus(totalSupply)

            val bidsMap = linkedMapOf<String, OrderInfo>()
            bids.forEach { bidsMap[it.company] = OrderInfo(it.quantity, it.order) }

            RoundAttributes(
                r.roundNumber, bidsMap, flow.first.toString(), totalDemand.toString(),
                totalSupply.toString(),
                absoluteDemandSupply.toString(),
                r.priceDirection.toString()
            )
        }.toList()
    }.toMap()
}

private fun edgesFromCounterPartyLimits(sellerBuyerMap: MutableMap<String, MutableMap<String, String>>): MutableList<Edge> {
    val edges = sellerBuyerMap.flatMap { company ->
        company.value.map {
            Edge(company.key, it.key, it.value.toInt())
        }.toMutableList()
    }.toMutableList()
    return edges
}

private fun outputToFile(templateEngine: TemplateEngine, templateParams: TemplateParams) {
    val output = StringOutput()
    templateEngine.render("simulator-template.jte", templateParams, output)
    val newBufferedWriter = Files.newBufferedWriter(
        Path.of("src", "main", "jte", "rendered.html"),
        Charset.defaultCharset(),
        StandardOpenOption.WRITE,
        StandardOpenOption.TRUNCATE_EXISTING,
        StandardOpenOption.CREATE
    )
    newBufferedWriter.write(output.toString())
    newBufferedWriter.close()
}

private fun readOrdersCsv(): Map<String, Pair<List<RoundInfo>, Map<String, List<DeBidConstraints>>>> {
    return File("src/main/jte/csv/scenarios/").walk().filter { it.isDirectory.not() }.sorted().map { file ->
        val reader =
            Files.newBufferedReader(Path.of(file.path))
        val parser: CSVParser = CSVParserBuilder().withSeparator(',').withIgnoreQuotations(true).build()
        val csvReader: CSVReader = CSVReaderBuilder(reader).withSkipLines(1).withCSVParser(parser).build()

        val roundOrders = csvReader.groupBy {
            it.first()
        }.map {
            Pair(it.key, it.value.map { arr -> Bid(arr[1], arr[2], arr[3]) }.toList())
        }.map {
            RoundInfo(
                it.first,
                it.second,
                calculatePriceDirection(it.second.calculateTotalDemand(), it.second.calculateTotalSupply())
            )
        }.toList()
        val scenarioName = file.name.removeSuffix(".csv")
        val validatedOrders = validateOrders(scenarioName, roundOrders)
        scenarioName to Pair(roundOrders,validatedOrders)
    }.toMap()
}

private fun calculatePriceDirection(totalDemand: Double, totalSupply: Double): PriceDirection =
    if (totalDemand > totalSupply) PriceDirection.UP else PriceDirection.DOWN

private fun validateOrders(scenarioName: String, roundOrders: List<RoundInfo>): Map<String, List<DeBidConstraints>> {
    val ordersPerCompany = ordersPerCompany(roundOrders)
    return ordersPerCompany.map { eachCompany ->

        var previousOrderQuantity = 0
        var previousOrderConstraints = DeBidConstraints(MAX_BUY_QUANTITY, 0, 0, MAX_SELL_QUANTITY)
        var previousOrderType = OrderType.NONE

        val constraints = eachCompany.value.mapIndexed { index, orderDetails ->
            val logConstraints: DeBidConstraints
            if (index == 0) {

                previousOrderQuantity = orderDetails.second
                previousOrderType = OrderType.valueOf(orderDetails.first)
                previousOrderConstraints = calculate_subsequent_round_constraints(
                    previousOrderConstraints,
                    previousOrderType,
                    previousOrderQuantity,
                    orderDetails.third
                )
                logConstraints = previousOrderConstraints
            } else {

                val orderQuantity = orderDetails.second
                val orderType = OrderType.valueOf(orderDetails.first)

                validateOrderConstraints(
                    scenarioName,
                    previousOrderConstraints,
                    orderType,
                    orderQuantity,
                    index,
                    eachCompany
                )
                val bidConstraints = calculate_subsequent_round_constraints(
                    previousOrderConstraints,
                    previousOrderType,
                    previousOrderQuantity,
                    orderDetails.third
                )
                previousOrderConstraints = bidConstraints
                previousOrderType = orderType
                previousOrderQuantity = orderQuantity
                logConstraints = bidConstraints
            }
            logConstraints
        }.toList()
        eachCompany.key to constraints
    }.toMap()
}

private fun ordersPerCompany(roundOrders: List<RoundInfo>): MutableMap<String, MutableList<Triple<String, Int, PriceDirection>>> {
    val transformedMap = mutableMapOf<String, MutableList<Triple<String, Int, PriceDirection>>>()
    roundOrders.forEach {
        val bids = it.bids
        bids.forEach { bid ->
            if (transformedMap.containsKey(bid.company)) {
                transformedMap[bid.company] = transformedMap[bid.company]?.apply {
                    this.add(Triple(bid.order, bid.quantity.toInt(), it.priceDirection))
                } ?: throw RuntimeException("Unexpected error")
            } else {
                transformedMap[bid.company] = mutableListOf(Triple(bid.order, bid.quantity.toInt(), it.priceDirection))
            }
        }
    }
    return transformedMap
}

private fun validateOrderConstraints(
    scenarioName: String,
    bidConstraints: DeBidConstraints,
    orderType: OrderType,
    orderQuantity: Int,
    index: Int,
    eachCompany: Map.Entry<String, MutableList<Triple<String, Int, PriceDirection>>>,
) {
    try {
        validate_de_order_against_constraints_and_throw_if_fails(
            bidConstraints,
            orderType,
            orderQuantity,
            "MMLB"
        )

    } catch (ex: Error) {
        throw RuntimeException("For Scenario $scenarioName, In Round ${index + 1}, For company ${eachCompany.key} with order ${eachCompany.value} got validation error : ${ex.message}")
    }
}


private fun readCreditsCsv(): Pair<MutableMap<String, MutableMap<String, String>>, MutableMap<String, MutableMap<String, String>>> {
    val reader = Files.newBufferedReader(Path.of("src", "main", "jte", "csv", "credits.csv"))

    val parser: CSVParser = CSVParserBuilder().withSeparator(',').withIgnoreQuotations(true).build()

    val csvReader: CSVReader = CSVReaderBuilder(reader).withSkipLines(1).withCSVParser(parser).build()

    val buyerSellerMap = mutableMapOf<String, MutableMap<String, String>>()
    val sellerBuyerMap = mutableMapOf<String, MutableMap<String, String>>()
    csvReader.onEach { arr ->
        generateCreditsFromCsv(arr, buyerSellerMap, sellerBuyerMap)
    }
    return Pair(buyerSellerMap, sellerBuyerMap)
}

private fun generateCreditsFromCsv(
    arr: Array<out String>,
    buyerSellerMap: MutableMap<String, MutableMap<String, String>>,
    sellerBuyerMap: MutableMap<String, MutableMap<String, String>>,
) {
    if (arr.size != 3) throw RuntimeException("invalid csv")
    if (buyerSellerMap.containsKey(arr.first())) {
        buyerSellerMap[arr.first()] = buyerSellerMap[arr.first()]?.apply {
            this[arr[1]] = arr[2]
        } ?: throw RuntimeException("invalid csv")
    } else {
        buyerSellerMap[arr.first()] = mutableMapOf(arr[1] to arr[2])
    }
    if (sellerBuyerMap.containsKey(arr[1])) {
        sellerBuyerMap[arr[1]] = sellerBuyerMap[arr[1]]?.apply {
            this[arr.first()] = arr[2]
        } ?: throw RuntimeException("invalid csv")
    } else {
        sellerBuyerMap[arr[1]] = mutableMapOf(arr.first() to arr[2])
    }
}

fun List<Bid>.calculateTotalDemand() =
    this.filter { it.order == "BUY" }.map { it.quantity.toInt() }.fold(0.0) { t, y -> (t + y) }

fun List<Bid>.calculateTotalSupply() =
    this.filter { it.order == "SELL" }.map { it.quantity.toInt() }.fold(0.0) { t, y -> t + y }

data class Credits(val buyerSellerCredits: Map<Buyer, Sellers>, val sellerBuyerCredits: Map<Buyer, Sellers>)

data class RoundInfo(val roundNumber: String, val bids: List<Bid>, val priceDirection: PriceDirection)

data class Bid(val company: String, val order: String, val quantity: String)

data class TemplateParams(
    val credits: Credits,
    val scenarioToRoundAttributes: Map<String, List<RoundAttributes>>,
    val constraintsMap: Map<String, Map<String, List<DeBidConstraints>>>
)

data class RoundAttributes(
    val roundNumber: String,
    val bids: LinkedHashMap<String, OrderInfo>,
    val maxFlow: String,
    val demand: String,
    val supply: String,
    val absoluteSupplyDemand: String,
    val priceDirection: String
)

data class OrderInfo(
    val quantity: String,
    val orderType: String
)

================
File: native-test/kotlin/org/acme/GreetingResourceIT.kt
================
package org.acme

import io.quarkus.test.junit.QuarkusIntegrationTest

@QuarkusIntegrationTest
class GreetingResourceIT : GreetingResourceTest()



================================================================
End of Codebase
================================================================
