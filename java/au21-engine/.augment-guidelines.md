# Augment Guidelines for Auction System

## Critical Security Rule: Test Files Only

**NEVER edit production code without explicit permission - editing production code in auction systems can cause security vulnerabilities where users see competitor bids.**

### Rules:
1. **Only edit test files** - Files in `src/test/` directories are safe to modify
2. **Never edit production code** - Files in `src/main/` contain business logic that handles multi-million dollar auctions
3. **Report production issues** - If tests fail due to production code issues, report them to the user
4. **Wait for permission** - Only edit production code after explicit user approval
5. **User determines root cause** - The user will determine if issues are with tests or production code

### Why This Matters:
- Auction systems handle sensitive financial data
- Incorrect role validation could expose competitor bids
- Security vulnerabilities in auctions can result in lawsuits
- Production code changes require careful review and testing

### When Tests Fail:
1. ✅ Fix test setup, data, or assertions
2. ✅ Fix test configuration issues  
3. ✅ Fix test environment problems
4. ❌ Do NOT fix production code without permission
5. 📋 Report production code issues to user

### Test Patterns:
- All commands have corresponding helpers in CommandTestBase
- Tests use in-memory database with clean state per test
- State is created by running actual commands using CommandTestBase helper methods
- GraphQL API can also be used to set state
- Use `login_command(session, username, password)` from CommandTestBase to login users
- Never call non-existent methods like `login()` - always use CommandTestBase helpers
- Tests share in-memory database across process space - parallel execution disabled for safety
- Use fixed ports (not random) since tests run sequentially

### Example Safe Edits:
- Test data setup
- Test configuration
- Test assertions
- Test helper methods
- Build configuration for tests

### Example Dangerous Edits:
- Role validation logic
- Authentication/authorization
- Business logic
- Database entities
- Security-related code
