# au21-engine

2025-04-14
- installed:  sdk install java 21.0.6-tem
- note <PERSON> says <PERSON><PERSON><PERSON> is the recommended jdk
- see: https://whichjdk.com/
-----

Feb 5, 2025:
- https://openrouter.ai/chat?room=orc-1738804405-TEx8a69dk4XJZOk6FY2k



---

Important links:
- http://localhost:4040/q/graphql-ui/?query=query%20%7B%0A%20%20users%20%7B%0A%20%20%20%20id%0A%20%20%20%20username%0A%20%20%20%20password%0A%20%20%7D%0A%7D

May 11, 2024
Graphql:
- NOTE: for .http and insomnia etc this is the endpoint:
  - http://localhost:4040/graphql
- docs: 
  - https://www.jetbrains.com/help/idea/http-client-in-product-code-editor.html#create-graphql-from-spring-controller
- nice client:
  -  https://studio.apollographql.com/sandbox/explorer

June 11, 2024:
- NB, INK:Gelf and Seq setup: https://community.inkdrop.app/note/c47fbe830e04dc02b1f684240da7971c/note:CkwtWeSp3

 
March 25, 2024:
- to upgrade gradle: `just upgrade_gradle`, or
```sh
upgrade_gradle:
./gradlew wrapper --gradle-version=8.7
```

- to upgrade quarkus
  - for now, got to gradle.properties and change it there
  - command line version doesn't currently work with kts gradle file.
```sh 
# TRYING WITH COMMAND LINE,
# - using same version of quarkus I want to upgrade to
# - failed
sdk upgrade quarkus
quarkus upgrade # NOTE: THIS FAILED needs build.gradle ie: not kotlin
  - tried to convert, but it failed
```

Feb 19, 2024:
- This project runs and build docker images
- But now I'm leaving it as a template,
  - and moving work to a monorepo with the frontend
  
-To run:
```shell
docker login registry.gitlab.com
docker pull registry.gitlab.com/auctionologies/2024/2024-02-19-code-with-quarkus:COMMIT_3f99f61b
docker run -p 8080:8080 registry.gitlab.com/auctionologies/2024/2024-02-19-code-with-quarkus:COMMIT_3f99f61b registry.gitlab.com/auctionologies/2024/2024-02-19-code-with-quarkus:COMMIT_3f99f6a1b
```
- TODO:
  - test reports and coverage
  - caching doesn't seem to work
  - docker should only build on manual push
  - need to review Dhiren's gitlab-ci.yml file


---

This project uses Quarkus, the Supersonic Subatomic Java Framework.

If you want to learn more about Quarkus, please visit its website: https://quarkus.io/ .

## Running the application in dev mode

You can run your application in dev mode that enables live coding using:
```shell script
./gradlew quarkusDev
```

> **_NOTE:_**  Quarkus now ships with a Dev UI, which is available in dev mode only at http://localhost:8080/q/dev/.

## Packaging and running the application

The application can be packaged using:
```shell script
./gradlew build
```
It produces the `quarkus-run.jar` file in the `build/quarkus-app/` directory.
Be aware that it’s not an _über-jar_ as the dependencies are copied into the `build/quarkus-app/lib/` directory.

The application is now runnable using `java -jar build/quarkus-app/quarkus-run.jar`.

If you want to build an _über-jar_, execute the following command:
```shell script
./gradlew build -Dquarkus.package.jar.type=uber-jar
```

The application, packaged as an _über-jar_, is now runnable using `java -jar build/*-runner.jar`.

## Creating a native executable

You can create a native executable using:
```shell script
./gradlew build -Dquarkus.native.enabled=true
```

Or, if you don't have GraalVM installed, you can run the native executable build in a container using:
```shell script
./gradlew build -Dquarkus.native.enabled=true -Dquarkus.native.container-build=true
```

You can then execute your native executable with: `./build/au24.engine-1.0.0-SNAPSHOT-runner`

If you want to learn more about building native executables, please consult https://quarkus.io/guides/gradle-tooling.

## Related Guides

- WebSockets Client ([guide](https://quarkus.io/guides/websockets)): Client for WebSocket communication channel
- Kotlin ([guide](https://quarkus.io/guides/kotlin)): Write your services in Kotlin
- OpenTelemetry ([guide](https://quarkus.io/guides/opentelemetry)): Use OpenTelemetry to trace services
- WebSockets ([guide](https://quarkus.io/guides/websockets)): WebSocket communication channel support
- SmallRye GraphQL ([guide](https://quarkus.io/guides/smallrye-graphql)): Create GraphQL Endpoints using the code-first approach from MicroProfile GraphQL

## Provided Code

### REST

Easily start your REST Web Services

[Related guide section...](https://quarkus.io/guides/getting-started-reactive#reactive-jax-rs-resources)

### SmallRye GraphQL

Start coding with this Hello GraphQL Query

[Related guide section...](https://quarkus.io/guides/smallrye-graphql)

### WebSockets

WebSocket communication channel starter code

[Related guide section...](https://quarkus.io/guides/websockets)

----
